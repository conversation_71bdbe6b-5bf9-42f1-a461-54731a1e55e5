package net.lab1024.sa.admin.module.business.mes.factory.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 车间信息 实体类
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:31:51
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_workshop")
public class WorkshopEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 所属工厂id
     */
    private Long factoryId;

    /**
     * 车间名称
     */
    private String name;

    /**
     * 车间位置
     */
    private String location;

    /**
     * 负责人id
     */
    private Long managerId;

}
