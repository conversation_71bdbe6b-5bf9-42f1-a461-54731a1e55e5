package net.lab1024.sa.admin.module.business.mes.salary.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Data;

/**
 * 薪酬发放记录表 实体类
 *
 * <AUTHOR>
 * @Date 2024-12-03 21:37:41
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_salary_payoff_record")
public class PayoffRecordEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 员工id
     */
    private Long employeeId;

    /**
     * 员工姓名
     */
    private String actualName;

    /**
     * 归属月份
     */
    private Date belongMonth;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 计件金额
     */
    private BigDecimal pieceAmount;

    /**
     * 其他项金额
     */
    private BigDecimal otherAmount;

    /**
     * 发放时间
     */
    private LocalDateTime payoffTime;

    /**
     * 其他项金额数据
     */
    private String otherAmountData;

}
