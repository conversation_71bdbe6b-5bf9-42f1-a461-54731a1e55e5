package net.lab1024.sa.admin.module.business.mes.stock.out.controller;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.common.bill.constant.BillType;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockBillStatusEnum;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.form.StkOutStockQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.form.StkProdOutStockAddForm;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.form.StkProdOutStockUpdateForm;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.vo.StkOutStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.vo.StkProdOutStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.out.service.StkProdOutStockService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 生产退库单 Controller
 *
 * <AUTHOR>
 * @Date 2025-02-03 15:55:23
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class StkProdOutStockController {

    @Resource
    private StkProdOutStockService stkProdOutStockService;

    @Resource
    private SerialNumberService serialNumberService;


    /**
     * 分页查询
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/stkProdOut/queryPage")
    public ResponseDTO<PageResult<StkOutStockVO>> queryPage(@RequestBody StkOutStockQueryForm queryForm) {
        queryForm.setType(BillType.STOCK_PRODUCE_OUT.getValue());
        return stkProdOutStockService.queryPage(queryForm);
    }


    /**
     * 添加
     *
     * @param form
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/stkProdOut/add")
    public ResponseDTO<String> otherInStock(@RequestBody @Valid StkProdOutStockAddForm form) {
        form.setType(BillType.STOCK_PRODUCE_OUT.getValue());
        if (StrUtil.isBlank(form.getStatus())) {
            form.setStatus(StockBillStatusEnum.UN_AUDIT.getValue());
        }
        if (StrUtil.isBlank(form.getNumber())) {
            form.setNumber(serialNumberService.generate(SerialNumberIdEnum.STOCK_OUT_PRODUCE));
        }
        return stkProdOutStockService.add(form);
    }

    /**
     * 修改
     *
     * @param form
     * @return
     */
    @Operation(summary = "修改 <AUTHOR>
    @PostMapping("/stkProdOut/update")
    public ResponseDTO<String> update(@RequestBody @Valid StkProdOutStockUpdateForm form) {
        return stkProdOutStockService.update(form);
    }

    /**
     * 根据id
     *
     * @param id
     * @return
     */
    @Operation(summary = "根据id查询 <AUTHOR>
    @GetMapping("stkProdOut/byId")
    public ResponseDTO<StkProdOutStockVO> queryById(@RequestParam("id") Long id) {
        return stkProdOutStockService.queryById(id);
    }


    /**
     * 修改单据状态
     *
     * @param id
     * @return
     */
    @Operation(summary = "修改单据状态 <AUTHOR>
    @GetMapping("stkProdOut/status")
    public ResponseDTO<String> updateStatus(@RequestParam("id") Long id) {
        return stkProdOutStockService.updateStatus(id);
    }

    /*
    * 删除
     */
    @Operation(summary = "删除 <AUTHOR>
    @GetMapping("stkProdOut/delete")
    public ResponseDTO<String> delete(@RequestParam("id") Long id) {
        return stkProdOutStockService.delete(id);
    }
}
