package net.lab1024.sa.admin.module.business.mes.part.station.inventory.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 操作类型枚举
 */
@Getter
@AllArgsConstructor
public enum PartStationInventoryOptTypeEnum implements BaseEnum {

    /**
     * 入库
     */
    IN("IN","入库"),

    /**
     * 出库
     */
    OUT("OUT","出库"),

    /**
     * 移库
     */
    MOVE("MOVE","移库"),

    /**
     * 库存盘点
     */
    TAKE("TAKE","盘库");

    private final String value;

    private final String desc;



}
