package net.lab1024.sa.admin.module.business.mes.base.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.lab1024.sa.base.common.domain.PageParam;

/**
 * 部位表 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-06-23 19:56:55
 * @Copyright zscbdic
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class PartQueryForm extends PageParam {

    @Schema(description = "关键字查询")
    private String queryKey;

}
