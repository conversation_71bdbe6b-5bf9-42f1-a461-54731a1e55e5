package net.lab1024.sa.admin.module.business.mes.part.dispatch.controller;

import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.entity.PartDispatchReceiveForm;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.form.PartDispatchQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.form.PartDispatchSendForm;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.vo.PartDispatchLogVO;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.service.PartDispatchLogService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 裁片收发日志 Controller
 *
 * <AUTHOR>
 * @Date 2024-11-06 15:25:13
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class PartDispatchLogController {

    @Resource
    private PartDispatchLogService partDispatchLogService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/partDispatchLog/queryPage")
    public ResponseDTO<PageResult<PartDispatchLogVO>> queryPage(@RequestBody @Valid PartDispatchQueryForm queryForm) {
        return ResponseDTO.ok(partDispatchLogService.queryPage(queryForm));
    }


    /**
     * 下发
     * @param sendForm
     * @return
     */
    @Operation(summary = "下发 <AUTHOR>
    @PostMapping("/partDispatchLog/send")
    public ResponseDTO<String> send(@RequestBody @Valid PartDispatchSendForm sendForm) {
        return partDispatchLogService.send(sendForm);
    }

    /**
     * 回收
     * @param receiveForm
     * @return
     */
    @Operation(summary = "收回 <AUTHOR>
    @PostMapping("/partDispatchLog/receive")
    public ResponseDTO<String>  receive(@RequestBody @Valid PartDispatchReceiveForm receiveForm) {
        return partDispatchLogService.receive(receiveForm);
    }



//    @Operation(summary = "添加 <AUTHOR>
//    @PostMapping("/partDispatchLog/add")
//    public ResponseDTO<String> add(@RequestBody @Valid PartDispatchLogAddForm addForm) {
//        return partDispatchLogService.add(addForm);
//    }
//
//    @Operation(summary = "更新 <AUTHOR>
//    @PostMapping("/partDispatchLog/update")
//    public ResponseDTO<String> update(@RequestBody @Valid PartDispatchLogUpdateForm updateForm) {
//        return partDispatchLogService.update(updateForm);
//    }
//
//    @Operation(summary = "批量删除 <AUTHOR>
//    @PostMapping("/partDispatchLog/batchDelete")
//    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Integer> idList) {
//        return partDispatchLogService.batchDelete(idList);
//    }
//
//    @Operation(summary = "单个删除 <AUTHOR>
//    @GetMapping("/partDispatchLog/delete/{id}")
//    public ResponseDTO<String> batchDelete(@PathVariable Integer id) {
//        return partDispatchLogService.delete(id);
//    }
}
