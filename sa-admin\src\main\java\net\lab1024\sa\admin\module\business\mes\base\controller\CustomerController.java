package net.lab1024.sa.admin.module.business.mes.base.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.CustomerAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.CustomerQuery;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.CustomerQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.CustomerUpdateForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.CustomerVO;
import net.lab1024.sa.admin.module.business.mes.base.service.CustomerService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户表 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-03 09:43:14
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class CustomerController {

    @Resource
    private CustomerService customerService;

    //编号生成器
    @Resource
    SerialNumberService serialNumberService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/customer/queryPage")
    public ResponseDTO<PageResult<CustomerVO>> queryPage(@RequestBody @Valid CustomerQueryForm queryForm) {
        return ResponseDTO.ok(customerService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/customer/add")
    public ResponseDTO<String> add(@RequestBody @Valid CustomerAddForm addForm) {
        return customerService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/customer/update")
    public ResponseDTO<String> update(@RequestBody @Valid CustomerUpdateForm updateForm) {
        return customerService.update(updateForm);
    }

    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/customer/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return customerService.delete(id);
    }

    /**
     * 自动生成客户编号
     * @return
     */
    @Operation(summary = "自动生成客户编号 <AUTHOR>
    @GetMapping("/customer/getClientNo")
    public ResponseDTO<String> getClientNo() {
        return ResponseDTO.ok(serialNumberService.generate(SerialNumberIdEnum.CLIENT));
    }

    /**
     * 客户名称下拉
     * @param query
     * @return
     */
    @Operation(summary = "客户名称下拉 <AUTHOR>
    @PostMapping("/customer/queryList")
    public ResponseDTO<List<CustomerVO>> queryList( @RequestBody @Valid CustomerQuery query) {
        return customerService.queryList(query);
    }
}
