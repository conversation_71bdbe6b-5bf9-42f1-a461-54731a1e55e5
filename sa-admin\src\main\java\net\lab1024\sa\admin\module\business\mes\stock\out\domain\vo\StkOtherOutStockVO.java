package net.lab1024.sa.admin.module.business.mes.stock.out.domain.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class StkOtherOutStockVO extends StkOutStockVO {

    private List<StkOtherOutStockVO.DetailVO> details;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DetailVO extends StkOutStockDetailVO {

    }
}
