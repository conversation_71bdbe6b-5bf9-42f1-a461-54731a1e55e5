package net.lab1024.sa.admin.module.business.mes.stock.out.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity.StkOutStockDetailEntity;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.form.StkOutStockDetailQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.vo.StkOutStockDetailVO;

import java.util.List;

/**
 * 出库单详情 Dao
 *
 * <AUTHOR>
 * @Date 2025-02-09 19:59:35
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface StkOutStockDetailDao extends BaseMapper<StkOutStockDetailEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<StkOutStockDetailVO> queryPage(Page page, @Param("queryForm") StkOutStockDetailQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
