package net.lab1024.sa.admin.module.business.mes.equip.equipment.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.entity.EquipmentEntity;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentAddForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentQueryForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.vo.EquipmentVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 设备 Dao
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:12:13
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface EquipmentDao extends BaseMapper<EquipmentEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<EquipmentVO> queryPage(Page page, @Param("queryForm") EquipmentQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 查询启用的IOT设备id集合
     * @return
     */
    List<EquipmentVO> queryOpenIotEquipIdsAndPlatform(Page page, @Param("queryForm") EquipmentQueryForm queryForm);

}
