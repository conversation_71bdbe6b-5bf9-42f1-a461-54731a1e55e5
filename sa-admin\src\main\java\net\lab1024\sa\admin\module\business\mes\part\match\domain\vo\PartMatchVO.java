package net.lab1024.sa.admin.module.business.mes.part.match.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 裁片配扎情况 列表VO
 *
 * <AUTHOR>
 * @Date 2024-11-07 15:17:40
 * @Copyright zscbdic
 */

@Data
public class PartMatchVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "菲票id")
    private Long ticketId;

}
