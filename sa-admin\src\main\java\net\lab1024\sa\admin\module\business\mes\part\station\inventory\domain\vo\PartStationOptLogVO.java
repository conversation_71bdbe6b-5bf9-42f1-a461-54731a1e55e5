package net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 裁片驿站操作日志 列表VO
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:47:06
 * @Copyright zscbdic
 */

@Data
public class PartStationOptLogVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "菲票id")
    private Long feTicketId;

    /**
     * 指令单号
     */
    private String instructOrderNumber;

    /**
     * 指令单id
     */
    private Long instructOrderId;

    /**
     * 裁床单号
     */
    private String cutBedSheetNumber;

    /**
     * 裁床单id
     */
    private Long cutBedSheetId;

    /**
     * 床次
     */
    private Integer cutNum;

    /**
     * 物料id
     */
    private Long itemId;

    /**
     * 物料编号
     */
    private String itemNumber;

    /**
     * 物料名称
     */
    private String itemName;

    /**
     * 扎号
     */
    private Integer tieNum;

    /**
     * 款式颜色
     */
    private String styleColor;

    /**
     * 尺寸
     */
    private String size;

    /**
     * 部位
     */
    private String positions;

    /**
     * 数量
     */
    private Integer num;

    @Schema(description = "操作类型")
    private String optType;

    @Schema(description = "操作描述")
    private String optDesc;

    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "操作人id")
    private Long operatorId;

    @Schema(description = "操作时间")
    private String optTime;

    /**
     * 货位id
     */
    private Long binId;

    /**
     * 货位编码
     */
    private String binCode;

    /**
     * 货架id
     */
    private Long rackId;

    /**
     * 货架编码
     */
    private String rackCode;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;


}
