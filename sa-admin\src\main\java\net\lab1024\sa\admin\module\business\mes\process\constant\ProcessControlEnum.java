package net.lab1024.sa.admin.module.business.mes.process.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 工序控制枚举
 */
@Getter
@AllArgsConstructor
public enum ProcessControlEnum implements BaseEnum {

    /**
     * 自制
     */
    SELF_MADE("0", "自制"),


    /**
     * 委外
     */
    OUTSOURCING("1", "委外"),

    /**
     * 不限
     */
    UNLIMITED("2", "不限");

    private final String value;

    private final String desc;

    public static ProcessControlEnum getByValue(String value) {
        for (ProcessControlEnum item : values()){
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }

    public static ProcessControlEnum getByDesc(String desc) {
        for (ProcessControlEnum item : values()){
            if (item.getDesc().equals(desc)) {
                return item;
            }
        }
        return null;
    }
}
