package net.lab1024.sa.admin.module.business.mes.salary.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 薪酬发放记录详情 实体类
 *
 * <AUTHOR>
 * @Date 2024-12-03 21:37:54
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_salary_payoff_record_detail")
public class PayoffRecordDetailEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 关联主表id
     */
    private Long mainId;

    /**
     * 结算记录id
     */
    private Long settlementRecordId;

}
