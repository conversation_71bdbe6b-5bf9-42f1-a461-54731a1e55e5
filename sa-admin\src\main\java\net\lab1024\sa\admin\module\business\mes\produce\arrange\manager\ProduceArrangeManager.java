package net.lab1024.sa.admin.module.business.mes.produce.arrange.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.dao.ProduceArrangeDao;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.dao.ProduceArrangeDetailDao;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.entity.ProduceArrangeDetailEntity;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.entity.ProduceArrangeEntity;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.form.ProduceArrangeDetailAddForm;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.vo.ProduceArrangeDetailVO;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.vo.ProduceArrangeVO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.util.SmartBeanUtil;

import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 生产安排信息  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-22 18:44:38
 * @Copyright zscbdic
 */
@Service
public class ProduceArrangeManager extends ServiceImpl<ProduceArrangeDao, ProduceArrangeEntity> {

    @Resource
    private ProduceArrangeDao produceArrangeDao;
    @Resource
    private ProduceArrangeDetailDao produceArrangeDetailDao;

    /**
     * 新增生产安排
     *
     * @param produceArrangeEntity
     * @param produceArrangeDetailAddForms
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(ProduceArrangeEntity produceArrangeEntity, List<ProduceArrangeDetailAddForm> produceArrangeDetailAddForms) {
        produceArrangeDao.insert(produceArrangeEntity);
        //返回id
        Long id = produceArrangeEntity.getId();

        if (CollectionUtils.isEmpty(produceArrangeDetailAddForms)) {
            return;
        }
        List<ProduceArrangeDetailAddForm> addForms = produceArrangeDetailAddForms.stream().
                sorted(Comparator.comparing(ProduceArrangeDetailAddForm::getSerialNumber)).collect(Collectors.toList());

        addForms.forEach(e -> {
            ProduceArrangeDetailEntity entity = SmartBeanUtil.copy(e, ProduceArrangeDetailEntity.class);
            entity.setArrangeId(id);
            //设置是否为末道节点
            entity.setEndFlag(addForms.indexOf(e)==addForms.size()-1);
            produceArrangeDetailDao.insert(entity);
        });
    }

    /**
     * 删除生产安排
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        //查询id的详细生产安排
        List<ProduceArrangeDetailEntity> produceArrangeDetailEntities = produceArrangeDetailDao
                .selectList(new LambdaQueryWrapper<>(ProduceArrangeDetailEntity.class)
                        .eq(ProduceArrangeDetailEntity::getArrangeId, id));
        //删除详细安排
        if (CollectionUtils.isNotEmpty(produceArrangeDetailEntities)) {
            produceArrangeDetailEntities.forEach(e -> {
                produceArrangeDetailDao.updateDeleted(e.getId(), true);
            });
        }
        //删除安排
        produceArrangeDao.deleteById(id);
    }

    /**
     * 更新生产安排
     *
     * @param produceArrangeEntity
     * @param produceArrangeDetailAddForms
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateProduceArrange(ProduceArrangeEntity produceArrangeEntity, List<ProduceArrangeDetailAddForm> produceArrangeDetailAddForms) {
        Long id = produceArrangeEntity.getId();

        List<ProduceArrangeDetailEntity> produceArrangeDetailEntities = produceArrangeDetailDao
                .selectList(new LambdaQueryWrapper<ProduceArrangeDetailEntity>().eq(ProduceArrangeDetailEntity::getArrangeId, id));

        //删除原有详细工序
        if (CollectionUtils.isNotEmpty(produceArrangeDetailEntities)) {
            produceArrangeDetailEntities.forEach(e -> {
                produceArrangeDetailDao.updateDeleted(e.getId(), true);
            });
        }

        if (CollectionUtils.isNotEmpty(produceArrangeDetailAddForms)) {
            //排序
            List<ProduceArrangeDetailAddForm> addForms = produceArrangeDetailAddForms.stream()
                    .sorted(Comparator.comparing(ProduceArrangeDetailAddForm::getSerialNumber)).collect(Collectors.toList());
            addForms.forEach(e -> {
                ProduceArrangeDetailEntity entity = SmartBeanUtil.copy(e, ProduceArrangeDetailEntity.class);
                entity.setArrangeId(id);
                //设置是否为末道节点
                entity.setEndFlag(addForms.indexOf(e)==addForms.size()-1);
                produceArrangeDetailDao.insert(entity);
            });
        }

        produceArrangeDao.updateById(produceArrangeEntity);
    }

    /**
     * 根据生产安排id查询详细安排
     *
     * @param id
     * @return
     */
    public ProduceArrangeVO queryById(Long id) {
        ProduceArrangeEntity produceArrangeEntity = produceArrangeDao.selectById(id);
        ProduceArrangeVO produceArrangeVO = SmartBeanUtil.copy(produceArrangeEntity, ProduceArrangeVO.class);

        List<ProduceArrangeDetailEntity> produceArrangeDetailEntities = produceArrangeDetailDao
                .selectList(new LambdaQueryWrapper<ProduceArrangeDetailEntity>().eq(ProduceArrangeDetailEntity::getArrangeId, id));

        if (CollectionUtils.isNotEmpty(produceArrangeDetailEntities)) {
            produceArrangeDetailEntities = produceArrangeDetailEntities.stream().sorted(Comparator.comparing(ProduceArrangeDetailEntity::getSerialNumber)).collect(Collectors.toList());
            List<ProduceArrangeDetailVO> produceArrangeDetailVOS = SmartBeanUtil.copyList(produceArrangeDetailEntities, ProduceArrangeDetailVO.class);
            produceArrangeVO.setProduceArrangeDetailVOS(produceArrangeDetailVOS);
        }
        return produceArrangeVO;
    }
}
