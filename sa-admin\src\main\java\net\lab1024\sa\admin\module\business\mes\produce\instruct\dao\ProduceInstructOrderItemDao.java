package net.lab1024.sa.admin.module.business.mes.produce.instruct.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderItemEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderItemQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderItemVO;

import java.util.List;

/**
 * 指令单用料信息 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:24:54
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface ProduceInstructOrderItemDao extends BaseMapper<ProduceInstructOrderItemEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<ProduceInstructOrderItemVO> queryPage(Page page, @Param("queryForm") ProduceInstructOrderItemQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Integer id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
