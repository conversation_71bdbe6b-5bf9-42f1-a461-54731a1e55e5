package net.lab1024.sa.admin.module.business.mes.report.stock.inout.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.report.stock.inout.domain.form.ProInAndOutStockReportQueryForm;
import net.lab1024.sa.admin.module.business.mes.report.stock.inout.domain.vo.ProInAndOutStockReportVO;

import java.util.List;

@Mapper
@Component
public interface ProInAndOutStockReportDao {

    /**
     * 查询生产物料分页
     * @param page
     * @param queryForm
     * @return
     */
    List<ProInAndOutStockReportVO> queryProduceMaterialPage(Page<?> page, @Param("queryForm") ProInAndOutStockReportQueryForm queryForm);
}
