package net.lab1024.sa.admin.module.business.mes.salary.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 薪酬结算记录 列表VO
 *
 * <AUTHOR>
 * @Date 2024-11-20 20:48:59
 * @Copyright zscbdic
 */

@Data
public class SettlementRecordVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "员工id")
    private Long employeeId;

    @Schema(description = "员工姓名")
    private String actualName;

    @Schema(description = "归属月份")
    private LocalDate belongMonth;

    @Schema(description = "总金额")
    private BigDecimal totalAmount;

    @Schema(description = "总件数")
    private Integer totalNum;

    @Schema(description = "总次数")
    private Integer totalCount;

    @Schema(description = "结算方式")
    private String settlementWay;

    @Schema(description = "发放标识;0未发放，1已发放")
    private Boolean payoffFlag;


    @Schema(description = "结算时间")
    private LocalDateTime settlementTime;

}
