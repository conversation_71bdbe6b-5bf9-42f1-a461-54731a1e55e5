package net.lab1024.sa.admin.module.business.mes.part.station.in.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.admin.module.business.mes.part.station.in.domain.form.PartStationStockInByBoxForm;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.annotation.PartStationStoreManageMode;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.constant.StorageManageModeConfigEnum;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import net.lab1024.sa.admin.module.business.mes.part.station.in.domain.form.PartStationStockInByTicketForm;
import net.lab1024.sa.admin.module.business.mes.part.station.in.service.PartStationStockInService;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 裁片驿站入库 Controller
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:44:34
 * @Copyright zscbdic
 */
@RestController
@Tag(name = "")
public class PartStationStockInController {

    @Resource
    private PartStationStockInService partStationStockInService;


    /**
     * 单扎裁片入库
     *
     * @param form
     * @return
     */
    @Operation(summary = "单扎裁片入库")
    @PostMapping("/partStationStockIn/stockIn")
    @PartStationStoreManageMode(mode = StorageManageModeConfigEnum.FE_TICKET_MANAGE_MODE)
    public ResponseDTO<String> stockIn(@RequestBody @Valid PartStationStockInByTicketForm form) {
        return partStationStockInService.stockIn(form);
    }

    /**
     * 裁片入库（周转箱）
     * @param form
     * @return
     */
    @Operation(summary = "裁片入库（周转箱）")
    @PostMapping("/partStationStockIn/stockInByBox")
    @PartStationStoreManageMode(mode = StorageManageModeConfigEnum.TURN_BOX_MANAGE_MODE)
    public ResponseDTO<String> stockInByBox(@RequestBody @Valid PartStationStockInByBoxForm form) {
        return partStationStockInService.stockInByBox(form);
    }
}
