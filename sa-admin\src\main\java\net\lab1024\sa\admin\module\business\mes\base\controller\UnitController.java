package net.lab1024.sa.admin.module.business.mes.base.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.UnitAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.UnitQuery;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.UnitQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.UnitUpdateForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.UnitVO;
import net.lab1024.sa.admin.module.business.mes.base.service.UnitService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 单位表 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-04 11:12:45
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class UnitController {

    @Resource
    private UnitService unitService;

    @Resource
    private SerialNumberService serialNumberService;
    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/unit/queryPage")
    public ResponseDTO<PageResult<UnitVO>> queryPage(@RequestBody @Valid UnitQueryForm queryForm) {
        return ResponseDTO.ok(unitService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/unit/add")
    public ResponseDTO<String> add(@RequestBody @Valid UnitAddForm addForm) {
        return unitService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/unit/update")
    public ResponseDTO<String> update(@RequestBody @Valid UnitUpdateForm updateForm) {
        return unitService.update(updateForm);
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/unit/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return unitService.delete(id);
    }

    /**
     * 下拉查询所有
     * @param queryForm
     * @return
     */
    @Operation(summary = "下拉查询所有 <AUTHOR>
    @PostMapping("/unit/queryAll")
    public ResponseDTO<List<UnitVO>> queryAll(@RequestBody @Valid UnitQuery queryForm){
        return ResponseDTO.ok(unitService.queryAll(queryForm));
    }

    /**
     * 通过id查询
     * @param id
     * @return
     */
    @Operation(summary = "通过id查询")
    @GetMapping("/unit/get/{id}")
    public ResponseDTO<UnitVO> getById(@PathVariable Long id) {
        return ResponseDTO.ok(unitService.getById(id));
    }


    @Operation(summary = "自动生成单位编号 <AUTHOR>
    @GetMapping("/unit/getUnitNo")
    public ResponseDTO<String> getUnitNo() {
        return ResponseDTO.ok(serialNumberService.generate(SerialNumberIdEnum.UNIT));
    }
}
