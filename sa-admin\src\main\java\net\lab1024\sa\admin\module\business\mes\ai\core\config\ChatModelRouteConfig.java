package net.lab1024.sa.admin.module.business.mes.ai.core.config;

import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.ai.core.constant.LLMConfigConstant;
import net.lab1024.sa.base.module.support.config.ConfigService;
import net.lab1024.sa.base.module.support.config.domain.ConfigAddForm;
import net.lab1024.sa.base.module.support.config.domain.ConfigUpdateForm;
import net.lab1024.sa.base.module.support.config.domain.ConfigVO;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class ChatModelRouteConfig {

    @Resource
    private ConfigService configService;

    public static final String DEFAULT_PROMPT = """
            ### 角色
            你是一位杰出的意图识别专家，具备极为敏锐的洞察力，能够迅速且精准地判断用户问题的意图类型。在接收到用户问题时，需紧密结合当前用户输入以及历史消息，全面且深入地剖析问题的核心内涵。
                        
            ### 回复格式
            - 仅回复意图对应的意图值
                        
            ### 技能
            #### 技能 1：精准识别用户意图
            依据以下意图列表，仅返回与之对应的意图值。
                        
            意图描述: 对智裁MES系统的使用问题，使用系统的咨询，针对“智裁”的产品问题
            意图值:1
                        
            --------------------
                        
            意图描述:针对“智裁”的业务数据相关查询
            意图值:2
                        
            --------------------
            """;

    public Config getConfig() {
        ConfigVO config = configService.getConfig(LLMConfigConstant.CHAT_ROUTE);
        if (config == null) {
            Config configDTO = new Config();
            configDTO.setEnableFlag(false);
            configDTO.setPrompt(DEFAULT_PROMPT);

            // 添加配置
            ConfigAddForm addForm = new ConfigAddForm();
            addForm.setConfigKey(LLMConfigConstant.CHAT_ROUTE);
            addForm.setConfigValue(JSON.toJSONString(configDTO));
            addForm.setConfigName("LLM配置-模型路由");
            addForm.setRemark("LLM配置-模型路由");
            configService.add(addForm);

            return configDTO;
        }
        String configValue = config.getConfigValue();
        Config configDTO = JSON.parseObject(configValue, Config.class);
        configDTO.setConfigId(config.getConfigId());
        return configDTO;
    }


    public void updateConfig(Config config) {
        String configValue = JSON.toJSONString(config);

        ConfigUpdateForm updateForm = new ConfigUpdateForm();
        updateForm.setConfigKey(LLMConfigConstant.CHAT_ROUTE);
        updateForm.setConfigValue(configValue);
        updateForm.setRemark("LLM配置-模型路由");
        updateForm.setConfigName("LLM配置-模型路由");
        updateForm.setConfigId(config.getConfigId());
        configService.updateConfig(updateForm);
    }


    @Data
    public static class Config {


        private Long configId;

        /**
         * 提示
         */
        private String prompt;

        /**
         * 决策模型id
         */
        private Long modelId;

        /**
         * 是否启用
         */
        private Boolean enableFlag;

        /**
         * 配置
         */
        private List<Item> items;


        @Data
        public static class Item {

            /**
             * 值
             */
            private String value;

            /**
             * 选择的模型id
             */
            private Long modelId;
        }
    }

}
