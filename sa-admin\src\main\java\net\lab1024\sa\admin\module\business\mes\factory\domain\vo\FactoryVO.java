package net.lab1024.sa.admin.module.business.mes.factory.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 工厂信息表 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-04 11:11:50
 * @Copyright zscbdic
 */

@Data
public class FactoryVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "工厂名称")
    private String name;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "邮政编码")
    private String postalCode;

    @Schema(description = "联系人")
    private String contact;

    @Schema(description = "电话")
    private String telephone;

}
