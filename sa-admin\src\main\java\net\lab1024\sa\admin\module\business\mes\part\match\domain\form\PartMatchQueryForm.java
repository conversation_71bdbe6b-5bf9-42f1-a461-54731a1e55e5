package net.lab1024.sa.admin.module.business.mes.part.match.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

import java.util.List;

/**
 * 裁片配扎情况 分页查询表单
 *
 * <AUTHOR>
 * @Date 2024-11-07 15:17:40
 * @Copyright zscbdic
 */

@Data
public class PartMatchQueryForm extends PageParam{
    /**
     * 物料编号
     */
    @Schema(description = "物料编号")
    private String itemNumber;

    /**
     * 生产指令单号
     */
    @Schema(description = "生产指令单号")
    private String instructOrderNumber;

    @Schema(description = "颜色")
    private List<String> colors;

    @Schema(description = "尺码")
    private List<String> sizes;

    @Schema(description = "扎号")
    private String tieNum;

}
