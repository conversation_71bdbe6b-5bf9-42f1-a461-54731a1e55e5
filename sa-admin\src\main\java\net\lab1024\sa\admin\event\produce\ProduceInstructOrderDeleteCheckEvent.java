package net.lab1024.sa.admin.event.produce;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 生产指令单删除校验事件
 * <AUTHOR>
 */
@Getter
public class ProduceInstructOrderDeleteCheckEvent extends ApplicationEvent {

    /**
     * 生产指令单id
     */
    private final Long id;

    public ProduceInstructOrderDeleteCheckEvent(Object source, Long id) {
        super(source);
        this.id = id;
    }
}
