package net.lab1024.sa.admin.module.business.mes.base.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 尺码表 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-05 15:45:35
 * @Copyright zscbdic
 */

@Data
public class SizeInfoVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "尺码编号（如：001上衣、002衬衫等）")
    private String sizeCode;

    @Schema(description = "尺码模板名称")
    private String templateName;

    @Schema(description = "尺码信息（如：童装 66,73,80,90,100,110）")
    private String sizeInfo;

}
