package net.lab1024.sa.admin.module.business.mes.stock.warehouse.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.dao.StkRackDao;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkLocationEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkRackEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkRackAddForm;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkRackQuery;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkRackQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkRackUpdateForm;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.vo.StkRackVO;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.manager.StkLocationManager;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.manager.StkRackManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 货架 Service
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:13:43
 * @Copyright zscbdic
 */

@Service
public class StkRackService {

    @Resource
    private StkRackDao stkRackDao;

    @Resource
    private StkRackManager stkRackManager;

    @Resource
    private StkLocationManager stkLocationManager;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<StkRackVO> queryPage(StkRackQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<StkRackVO> list = stkRackDao.queryPage(page, queryForm);
        PageResult<StkRackVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(StkRackAddForm addForm) {
        stkRackManager.addCheck(addForm);
        StkRackEntity stkRackEntity = SmartBeanUtil.copy(addForm, StkRackEntity.class);
        stkRackDao.insert(stkRackEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(StkRackUpdateForm updateForm) {
        stkRackManager.updateCheck(updateForm);
        StkRackEntity stkRackEntity = SmartBeanUtil.copy(updateForm, StkRackEntity.class);
        stkRackDao.updateById(stkRackEntity);
        return ResponseDTO.ok();
    }


    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }
        // 判断货架下是否存在库位
        List<StkLocationEntity> locations = stkLocationManager.list(new LambdaQueryWrapper<StkLocationEntity>()
                .eq(StkLocationEntity::getRackId, id)
                .select(StkLocationEntity::getId));
        if (CollUtil.isNotEmpty(locations)) {
            // 货架下的库位删除校验
            List<Long> locationIds = locations.stream().map(StkLocationEntity::getId).collect(Collectors.toList());
            stkLocationManager.deleteCheck(locationIds);
        }


        stkRackManager.deletedRack(id);
        return ResponseDTO.ok();
    }

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    public ResponseDTO<List<StkRackVO>> queryList(StkRackQuery query) {
        List<StkRackEntity> list = stkRackDao.selectList(null);
        return ResponseDTO.ok(SmartBeanUtil.copyList(list, StkRackVO.class));
    }
}
