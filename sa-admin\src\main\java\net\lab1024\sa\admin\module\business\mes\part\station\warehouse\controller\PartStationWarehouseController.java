package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationWarehouseAddForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationWarehouseQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationWarehouseQuickAddForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationWarehouseUpdateForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.vo.PartStationWarehouseVO;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.service.PartStationWarehouseService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 裁片仓库表 Controller
 *
 * <AUTHOR>
 * @Date 2024-10-06 16:48:11
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class PartStationWarehouseController {

    @Resource
    private PartStationWarehouseService partStationWarehouseService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/partStationWarehouse/queryPage")
    public ResponseDTO<PageResult<PartStationWarehouseVO>> queryPage(@RequestBody @Valid PartStationWarehouseQueryForm queryForm) {
        return ResponseDTO.ok(partStationWarehouseService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/partStationWarehouse/add")
    public ResponseDTO<String> add(@RequestBody @Valid PartStationWarehouseAddForm addForm) {
        return partStationWarehouseService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/partStationWarehouse/update")
    public ResponseDTO<String> update(@RequestBody @Valid PartStationWarehouseUpdateForm updateForm) {
        return partStationWarehouseService.update(updateForm);
    }


    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/partStationWarehouse/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return partStationWarehouseService.delete(id);
    }

    /**
     * 获取所有
     * @return
     */
    @Operation(summary = "获取所有 <AUTHOR>
    @PostMapping("/partStationWarehouse/getAll")
    public ResponseDTO<List<PartStationWarehouseVO>> getAll() {
        return partStationWarehouseService.getAll();
    }


    /**
     * 快速建仓
     */
    @Operation(summary = "快速建仓 <AUTHOR>
    @PostMapping("/partStationRack/quickAdd")
    public ResponseDTO<String> quickAdd(@RequestBody @Valid PartStationWarehouseQuickAddForm addForm) {
        return partStationWarehouseService.quickAdd(addForm);
    }

    /**
     * 根据id获取仓库
     * @param id
     * @return
     */
    @Operation(summary = "根据id获取仓库 <AUTHOR>
    @GetMapping("/partStationWarehouse/getById/{id}")
    public ResponseDTO<PartStationWarehouseVO> getById(@PathVariable Long id) {
        return ResponseDTO.ok(partStationWarehouseService.getById(id));
    }
}
