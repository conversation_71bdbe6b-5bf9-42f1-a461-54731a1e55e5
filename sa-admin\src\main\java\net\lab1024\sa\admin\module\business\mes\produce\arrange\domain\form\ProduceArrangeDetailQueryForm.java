package net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.form;

import net.lab1024.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 生产安排信息 分页查询表单
 *
 * <AUTHOR>
 * @Date 2024-07-22 18:44:38
 * @Copyright zscbdic
 */

@Data
public class ProduceArrangeDetailQueryForm extends PageParam{

    @Schema(description = "关键字查询")
    private String queryKey;

}
