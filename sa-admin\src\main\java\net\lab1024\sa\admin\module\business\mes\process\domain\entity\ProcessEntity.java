package net.lab1024.sa.admin.module.business.mes.process.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 工序信息 实体类
 *
 * <AUTHOR>
 * @Date 2024-07-13 15:19:57
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_process")
public class ProcessEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 工序编号
     */
    private String processNumber;

    /**
     * 工序名称
     */
    private String name;

    /**
     * 部位
     */
    private String position;

    /**
     * 工序类型
     */
    private String processType;

    /**
     * 标准工时;单位
     */
    private Integer standardTime;

    /**
     * 工价一
     */
    private BigDecimal unitPrice1;

    /**
     * 工价二
     */
    private BigDecimal unitPrice2;

    /**
     * 工价三
     */
    private BigDecimal unitPrice3;

    /**
     * 工价四
     */
    private BigDecimal unitPrice4;

    /**
     * 工价五
     */
    private BigDecimal unitPrice5;

    /**
     * 工价六
     */
    private BigDecimal unitPrice6;

    /**
     * 工序控制;0自制 1委外 2不限
     */
    private String processControl;

    /**
     * sopId;保留
     */
    private Long sopId;

}
