package net.lab1024.sa.admin.module.business.mes.equip.equipment.service;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.dao.EquipmentDao;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.bo.EquipmentBO;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.entity.EquipmentEntity;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.entity.EquipmentScadaEntity;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentAddForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentQuery;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentQueryForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentUpdateForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.vo.EquipmentVO;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.manager.EquipmentManager;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.manager.EquipmentScadaManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备 Service
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:12:13
 * @Copyright zscbdic
 */

@Service
public class EquipmentService {

    @Resource
    private EquipmentDao equipmentDao;

    @Resource
    private EquipmentManager equipmentManager;

    @Resource
    private EquipmentScadaManager equipmentScadaManager;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<EquipmentVO> queryPage(EquipmentQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<EquipmentVO> list = equipmentDao.queryPage(page, queryForm);
        PageResult<EquipmentVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(EquipmentAddForm addForm) {
        // 校验
        equipmentManager.addCheck(addForm);

        EquipmentEntity equipmentEntity = SmartBeanUtil.copy(addForm, EquipmentEntity.class);
        EquipmentScadaEntity equipmentScada = SmartBeanUtil.copy(addForm, EquipmentScadaEntity.class);

        // scada校验
        equipmentScadaManager.check(equipmentScada);

        EquipmentBO equipmentBO = new EquipmentBO();
        equipmentBO.setEquipment(equipmentEntity);
        equipmentBO.setEquipmentScada(equipmentScada);
        equipmentManager.saveEquipment(equipmentBO);

        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(EquipmentUpdateForm updateForm) {
        equipmentManager.updateCheck(updateForm);

        EquipmentEntity equipmentEntity = SmartBeanUtil.copy(updateForm, EquipmentEntity.class);
        EquipmentScadaEntity equipmentScada = SmartBeanUtil.copy(updateForm, EquipmentScadaEntity.class);

        equipmentScadaManager.check(equipmentScada);

        EquipmentBO equipmentBO = new EquipmentBO();
        equipmentBO.setEquipment(equipmentEntity);
        equipmentBO.setEquipmentScada(equipmentScada);
        equipmentManager.updateEquipment(equipmentBO);
        return ResponseDTO.ok();
    }


    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }

        equipmentManager.deleteEquipment(id);
        return ResponseDTO.ok();
    }

    /**
     * 查询
     *
     * @param id
     * @return
     */
    public ResponseDTO<EquipmentVO> queryById(Long id) {
        EquipmentEntity equipmentEntity = equipmentManager.getById(id);
        if (equipmentEntity == null) {
            return ResponseDTO.userErrorParam("设备不存在");
        }
        EquipmentVO vo = SmartBeanUtil.copy(equipmentEntity, EquipmentVO.class);
        EquipmentScadaEntity scada = equipmentScadaManager.lambdaQuery()
                .eq(EquipmentScadaEntity::getEquipmentId, id)
                .last("limit 1")
                .one();
        if (scada == null) {
            return ResponseDTO.ok(vo);
        }

        vo.setIotNetworkFlag(scada.getIotNetworkFlag());
        vo.setIotNetworkPlatform(scada.getIotNetworkPlatform());
        vo.setScadaEquipmentId(scada.getScadaEquipmentId());
        vo.setScadaProductCode(scada.getScadaProductCode());
        vo.setScadaEquipmentCode(scada.getScadaEquipmentCode());
        vo.setIotEquipmentType(scada.getIotEquipmentType());
        return ResponseDTO.ok(vo);


    }

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    public List<EquipmentVO> queryList(EquipmentQuery query) {
        List<EquipmentEntity> list = equipmentManager.lambdaQuery()
                .and(CharSequenceUtil.isNotBlank(query.getQueryKey()),
                        w -> w.like(EquipmentEntity::getNumber, query.getQueryKey())
                        .or()
                        .like(EquipmentEntity::getName, query.getQueryKey())).list();
        return SmartBeanUtil.copyList(list, EquipmentVO.class);

    }
}
