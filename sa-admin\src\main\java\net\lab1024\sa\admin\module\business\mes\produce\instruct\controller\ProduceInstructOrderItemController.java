package net.lab1024.sa.admin.module.business.mes.produce.instruct.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderItemQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderItemVO;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.service.ProduceInstructOrderItemService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 指令单用料信息 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:24:54
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class ProduceInstructOrderItemController {

    @Resource
    private ProduceInstructOrderItemService produceInstructOrderItemService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/produceInstructOrderItem/queryPage")
    public ResponseDTO<PageResult<ProduceInstructOrderItemVO>> queryPage(@RequestBody @Valid ProduceInstructOrderItemQueryForm queryForm) {
        return ResponseDTO.ok(produceInstructOrderItemService.queryPage(queryForm));
    }
//
//    @Operation(summary = "添加 <AUTHOR>
//    @PostMapping("/produceInstructOrderItem/add")
//    public ResponseDTO<String> add(@RequestBody @Valid ProduceInstructOrderItemAddForm addForm) {
//        return produceInstructOrderItemService.add(addForm);
//    }
//
//    @Operation(summary = "更新 <AUTHOR>
//    @PostMapping("/produceInstructOrderItem/update")
//    public ResponseDTO<String> update(@RequestBody @Valid ProduceInstructOrderItemUpdateForm updateForm) {
//        return produceInstructOrderItemService.update(updateForm);
//    }
//
//    @Operation(summary = "批量删除 <AUTHOR>
//    @PostMapping("/produceInstructOrderItem/batchDelete")
//    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Integer> idList) {
//        return produceInstructOrderItemService.batchDelete(idList);
//    }
//
//    @Operation(summary = "单个删除 <AUTHOR>
//    @GetMapping("/produceInstructOrderItem/delete/{id}")
//    public ResponseDTO<String> batchDelete(@PathVariable Integer id) {
//        return produceInstructOrderItemService.delete(id);
//    }
}
