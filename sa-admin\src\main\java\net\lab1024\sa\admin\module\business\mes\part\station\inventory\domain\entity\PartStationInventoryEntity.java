package net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.checkerframework.checker.units.qual.A;

/**
 * 裁片驿站库存表 实体类
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:44:34
 * @Copyright zscbdic
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_mes_part_station_inventory")
public class PartStationInventoryEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
//    @TableLogic
//    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 菲票id
     */
    private Long feTicketId;

    /**
     * 货位id
     */
    private Long binId;

    /**
     * 最后盘库时间
     */
    private LocalDateTime lastCheckTime;

    /**
     * 入库时间
     */
    private LocalDateTime stockInTime;

    public PartStationInventoryEntity(Long feTicketId, Long binId) {
        this.feTicketId = feTicketId;
        this.binId = binId;
        this.stockInTime = LocalDateTime.now();
    }

}
