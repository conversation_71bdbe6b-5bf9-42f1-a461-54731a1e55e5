package net.lab1024.sa.admin.module.business.mes.factory.controller;

import net.lab1024.sa.admin.module.business.mes.factory.domain.form.ProduceTeamQuery;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.ProduceTeamEntity;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.ProduceTeamAddForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.ProduceTeamQueryForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.ProduceTeamUpdateForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.vo.ProduceTeamVO;
import net.lab1024.sa.admin.module.business.mes.factory.manager.ProduceTeamManager;
import net.lab1024.sa.admin.module.business.mes.factory.service.ProduceTeamService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 生产小组 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:32:46
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "生产小组信息接口")
public class ProduceTeamController {


    @Resource
    private ProduceTeamService produceTeamService;

    /**
     * 查询列表
     * @param query
     * @return
     */
    @PostMapping("/produceTeam/queryList")
    public ResponseDTO<List<ProduceTeamVO>> queryList(@RequestBody @Valid ProduceTeamQuery query) {
        return produceTeamService.queryList(query);
    }

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/produceTeam/queryPage")
    public ResponseDTO<PageResult<ProduceTeamVO>> queryPage(@RequestBody @Valid ProduceTeamQueryForm queryForm) {
        return ResponseDTO.ok(produceTeamService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/produceTeam/add")
    public ResponseDTO<String> add(@RequestBody @Valid ProduceTeamAddForm addForm) {
        return produceTeamService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/produceTeam/update")
    public ResponseDTO<String> update(@RequestBody @Valid ProduceTeamUpdateForm updateForm) {
        return produceTeamService.update(updateForm);
    }


    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/produceTeam/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return produceTeamService.delete(id);
    }


    /**
     * 单个查询
     * @param id
     * @return
     */
    @Operation(summary = "单个查询 <AUTHOR>
    @GetMapping("/produceTeam/{id}")
    public ResponseDTO<ProduceTeamVO> getById(@PathVariable("id") Long id){
        return produceTeamService.getById(id);
    }

}
