package net.lab1024.sa.admin.module.business.mes.factory.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.WorkshopEntity;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.WorkshopQueryForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.vo.WorkshopVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 车间信息 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:31:51
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface WorkshopDao extends BaseMapper<WorkshopEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<WorkshopVO> queryPage(Page page, @Param("queryForm") WorkshopQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 根据车间id查询成员id
     * @param id
     * @return
     */
    List<Long> queryMemberIdsById(@Param("id")Long id);

}
