package net.lab1024.sa.admin.module.business.mes.base.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

/**
 * 款式品类表 分页查询表单
 *
 * <AUTHOR>
 * @Date 2024-07-05 15:45:34
 * @Copyright zscbdic
 */

@Data
public class StyleQueryForm extends PageParam{

    @Schema(description = "尺码表查询",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "尺码表名称 不能为空")
    private String queryKey;

    @Schema(description = "备注")
//    @NotNull(message = "默认尺码表 不能为空")
    private String remark;

}
