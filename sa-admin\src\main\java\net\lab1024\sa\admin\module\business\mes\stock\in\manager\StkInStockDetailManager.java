package net.lab1024.sa.admin.module.business.mes.stock.in.manager;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.stock.in.dao.StkInStockDetailDao;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.entity.StkInStockDetailEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.dao.StkWarehouseDao;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkWarehouseEntity;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 入库单详情  Manager
 *
 * <AUTHOR>
 * @Date 2025-02-03 15:55:23
 * @Copyright zscbdic
 */
@Service
public class StkInStockDetailManager extends ServiceImpl<StkInStockDetailDao, StkInStockDetailEntity> {

    @Resource
    private StkWarehouseDao stkWarehouseDao;

    /**
     * 检查是否需要库位
     * @param details
     */
    public void checkNeedLocation(List<StkInStockDetailEntity> details){
        details.forEach(e->{
            if(e.getLocationId()==null){
                throw new BusinessException("第"+e.getSeq()+"行请选择库位");
            }
        });

    }

    /**
     * 检查是否需要库位
     * @param warehouseId
     * @param details
     */
    public void checkNeedLocation(Long warehouseId, List<StkInStockDetailEntity> details) {
        StkWarehouseEntity warehouse = stkWarehouseDao.selectById(warehouseId);
        if(warehouse==null){
            throw new BusinessException("仓库不存在");
        }
        if(warehouse.getOpenLocationFlag()){
            details.forEach(e->{
                if(e.getLocationId()==null){
                    throw new BusinessException("第"+e.getSeq()+"行请选择库位");
                }
            });
        }else {
            details.forEach(e->{
                // 没有库位，则设置为空
                e.setLocationId(null);
                e.setLocationNumber(null);
            });
        }
    }
}
