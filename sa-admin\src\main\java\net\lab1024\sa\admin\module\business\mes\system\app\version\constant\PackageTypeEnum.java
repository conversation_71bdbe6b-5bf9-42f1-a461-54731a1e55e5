package net.lab1024.sa.admin.module.business.mes.system.app.version.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

@Getter
@AllArgsConstructor
public enum PackageTypeEnum implements BaseEnum {

    /**
     * 整包升级
     */
    FULL_UPGRADE("0", "整包升级"),
    /**
     * wgt升级
     */
//    WGT_UPGRADE("wgt升级", "1");

    ;



    private String value;

    private String desc;
}
