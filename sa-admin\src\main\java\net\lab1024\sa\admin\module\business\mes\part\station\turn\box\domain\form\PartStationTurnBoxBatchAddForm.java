package net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class PartStationTurnBoxBatchAddForm {

    @Schema(description = "周转箱编号前缀", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "周转箱编号前缀 不能为空")
    private String numberPrefix;

    @Schema(description = "周转箱名称前缀", requiredMode = Schema.RequiredMode.REQUIRED)
    private String namePrefix;

    @Schema(description = "容量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "容量 不能为空")
    @Min(value = 1, message = "容量 不能小于1")
    private Integer capacity;

    @Min(value = 1, message = "数量 不能小于1")
    @Max(value = 50, message = "数量 不能大于50")
    private Integer num;
}
