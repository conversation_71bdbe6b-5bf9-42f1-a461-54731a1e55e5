package net.lab1024.sa.admin.module.business.mes.stock.setting.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.manager.StkInventoryManager;
import net.lab1024.sa.admin.module.business.mes.stock.setting.dao.StkMaterialStockDao;
import net.lab1024.sa.admin.module.business.mes.stock.setting.domain.entity.StkMaterialStockEntity;
import net.lab1024.sa.admin.module.business.mes.stock.setting.domain.form.StkMaterialStockAddForm;
import net.lab1024.sa.admin.module.business.mes.stock.setting.domain.form.StkMaterialStockQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.setting.domain.form.StkMaterialStockUpdateForm;
import net.lab1024.sa.admin.module.business.mes.stock.setting.domain.vo.StkMaterialStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.setting.manager.StkMaterialStockManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 物料库存属性 Service
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:18:19
 * @Copyright zscbdic
 */

@Service
public class StkMaterialStockService {

    @Resource
    private StkMaterialStockDao stkMaterialStockDao;

    @Resource
    private StkMaterialStockManager stkMaterialStockManager;

    @Resource
    private StkInventoryManager stkInventoryManager;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<StkMaterialStockVO> queryPage(StkMaterialStockQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<StkMaterialStockVO> list = stkMaterialStockDao.queryPage(page, queryForm);
        PageResult<StkMaterialStockVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(StkMaterialStockAddForm addForm) {
        stkMaterialStockManager.addCheck(addForm);
        if(addForm.getLotManageFlag()){
            //启用批次管理校验
            stkMaterialStockManager.openLotManageCheck(addForm.getMaterialId());
        }

        StkMaterialStockEntity stkMaterialStockEntity = SmartBeanUtil.copy(addForm, StkMaterialStockEntity.class);
        stkMaterialStockDao.insert(stkMaterialStockEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(StkMaterialStockUpdateForm updateForm) {
        StkMaterialStockEntity stkMaterialStockEntity = SmartBeanUtil.copy(updateForm, StkMaterialStockEntity.class);

        StkMaterialStockEntity materialStock = stkMaterialStockDao.selectById(updateForm.getId());
        //仓位设置有不同


        Boolean lotManageFlag = updateForm.getLotManageFlag();
        if (!lotManageFlag.equals(materialStock.getLotManageFlag())) {
            if (lotManageFlag) {
                stkMaterialStockManager.openLotManageCheck(materialStock.getMaterialId());

            } else {
                //合并批次库存
                stkInventoryManager.mergeLotInventory(materialStock.getMaterialId());
            }
        }



        stkMaterialStockDao.updateById(stkMaterialStockEntity);
        return ResponseDTO.ok();
    }



}
