package net.lab1024.sa.admin.event.tailor;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import org.springframework.context.ApplicationEvent;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 菲票追踪日志事件
 */
@Getter
public class FeTicketTraceLogEvent extends ApplicationEvent {

    private final List<Payload> payloads;

    public FeTicketTraceLogEvent(Object source, List<FeTicketTraceLogEvent.Payload> payloads) {
        super(source);
        this.payloads = payloads;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Payload implements Serializable {

        public Payload(Long feTicketId, String optContent, String optDesc, String bizType, String bizNumber, Long bizId){
            this.feTicketId = feTicketId;
            this.optContent = optContent;
            this.optDesc = optDesc;
            this.bizType = bizType;
            this.bizNumber = bizNumber;
            this.bizId = bizId;
            this.optTime = LocalDateTime.now();
            RequestUser user = SmartRequestUtil.getRequestUser();
            if(user != null){
                this.operatorId = user.getUserId();
                this.operatorName = user.getUserName();
            }
        }

        /**
         * 菲票ID
         */
        private Long feTicketId;

        /**
         * 操作时间
         */
        private LocalDateTime optTime;

        /**
         * 操作人ID
         */
        private Long operatorId;

        /**
         * 操作人名称
         */
        private String operatorName;

        /**
         * 操作内容
         */
        private String optContent;

        /**
         * 操作描述
         */
        private String optDesc;

        /**
         * 关联单据类型
         */
        private String bizType;

        /**
         * 关联单据编号
         */
        private String bizNumber;

        /**
         * 关联单据ID
         */
        private Long bizId;

        /**
         * 关联单据详情序号
         */
        private Integer bizDetailSeq;

        /**
         * 关联单据详情ID
         */
        private Long bizDetailId;
    }
}
