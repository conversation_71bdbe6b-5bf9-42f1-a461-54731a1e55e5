package net.lab1024.sa.admin.module.business.mes.part.station.inventory.controller;

import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.form.PartStationOptLogQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.vo.PartStationOptLogVO;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.service.PartStationOptLogService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 裁片驿站操作日志 Controller
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:47:06
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class PartStationOptLogController {

    @Resource
    private PartStationOptLogService partStationOptLogService;

    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/partStationOptLog/queryPage")
    public ResponseDTO<PageResult<PartStationOptLogVO>> queryPage(@RequestBody @Valid PartStationOptLogQueryForm queryForm) {
        return ResponseDTO.ok(partStationOptLogService.queryPage(queryForm));
    }

//    @Operation(summary = "添加 <AUTHOR>
//    @PostMapping("/partStationOptLog/add")
//    public ResponseDTO<String> add(@RequestBody @Valid PartStationOptLogAddForm addForm) {
//        return partStationOptLogService.add(addForm);
//    }
//
//    @Operation(summary = "更新 <AUTHOR>
//    @PostMapping("/partStationOptLog/update")
//    public ResponseDTO<String> update(@RequestBody @Valid PartStationOptLogUpdateForm updateForm) {
//        return partStationOptLogService.update(updateForm);
//    }
//
//    @Operation(summary = "批量删除 <AUTHOR>
//    @PostMapping("/partStationOptLog/batchDelete")
//    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> idList) {
//        return partStationOptLogService.batchDelete(idList);
//    }
//
//    @Operation(summary = "单个删除 <AUTHOR>
//    @GetMapping("/partStationOptLog/delete/{id}")
//    public ResponseDTO<String> batchDelete(@PathVariable Long id) {
//        return partStationOptLogService.delete(id);
//    }
}
