package net.lab1024.sa.admin.module.business.mes.produce.follow.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.ValidateList;

import jakarta.validation.constraints.NotNull;

/**
 * 我的生产跟单 查询表单
 *
 * <AUTHOR>
 * @Date 2024-07-26 21:36:36
 * @Copyright zscbdic
 */

@Data
public class ProduceInstructInfoQueryForm {

    @Schema(description = "跟单员id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "跟单员id 不能为空")
    private Long employeeId;

    @Schema(description = "指令单id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "指令单id 不能为空")
    private Long instructOrderId;



}
