package net.lab1024.sa.admin.module.business.mes.stock.out.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockBillStatusEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class StkOtherOutStockUpdateForm extends StkOutStockUpdateForm {

    @Schema(description = "单据状态;", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "单据状态; 不能为空")
    @CheckEnum(value = StockBillStatusEnum.class, message = "单据状态; 值非法")
    private String status;

    @Valid
    @NotEmpty(message = "入库单详情 不能为空")
    private List<DetailUpdateForm> details;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DetailUpdateForm extends StkOutStockDetailUpdateForm {

        @Schema(description = "单据来源详情类型")
        private String originDetailType;

        @Schema(description = "单据来源详情ID")
        private Long originDetailId;

        @Schema(description = "单据来源详情行号")
        private Integer originDetailSeq;
    }
}
