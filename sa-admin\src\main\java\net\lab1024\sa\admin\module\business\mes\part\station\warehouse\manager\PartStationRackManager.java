package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationBinDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationRackDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationBinEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationRackEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationRackAddForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationRackUpdateForm;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;

/**
 * 裁片货架  Manager
 *
 * <AUTHOR>
 * @Date 2024-10-06 16:49:27
 * @Copyright zscbdic
 */
@Service
public class PartStationRackManager extends ServiceImpl<PartStationRackDao, PartStationRackEntity> {

    @Resource
    private PartStationBinDao partStationBinDao;

    /**
     * 删除前校验
     *
     * @param id
     */
    public void deleteCheck(Long id) {
        Long count = partStationBinDao.selectCount(new LambdaQueryWrapper<PartStationBinEntity>()
                .eq(PartStationBinEntity::getRackId, id));
        if (count > 0) {
            throw new BusinessException("该货架存在货位，无法删除");
        }

    }

    /**
     * 添加前校验
     *
     * @param addForm
     */
    public void addCheck(PartStationRackAddForm addForm) {
        String rackCode = addForm.getRackCode();
//        if(rackCode.contains(QrCodeTypeUtil.SEPARATOR)){
//            throw new BusinessException("货架编码不能包含"+QrCodeTypeUtil.SEPARATOR);
//        }

        long count = this.count(new LambdaQueryWrapper<PartStationRackEntity>()
                .eq(PartStationRackEntity::getRackCode, rackCode));
        if (count > 0) {
            throw new BusinessException("货架编号已存在");
        }
    }

    /**
     * 更新前校验
     *
     * @param updateForm
     */
    public void updateCheck(PartStationRackUpdateForm updateForm) {
        String rackCode = updateForm.getRackCode();
//        if(rackCode.contains(QrCodeTypeUtil.SEPARATOR)){
//            throw new BusinessException("货架编码不能包含"+QrCodeTypeUtil.SEPARATOR);
//        }
        long count = this.count(new LambdaQueryWrapper<PartStationRackEntity>()
                .eq(PartStationRackEntity::getRackCode, rackCode)
                .ne(PartStationRackEntity::getId, updateForm.getId()));
        if (count > 0) {
            throw new BusinessException("货架编号已存在");
        }
    }
}
