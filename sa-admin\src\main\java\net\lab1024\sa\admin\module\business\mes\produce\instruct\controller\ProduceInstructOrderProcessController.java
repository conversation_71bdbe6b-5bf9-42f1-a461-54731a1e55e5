package net.lab1024.sa.admin.module.business.mes.produce.instruct.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderProcessVO;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderVO;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructProcessVO;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.service.ProduceInstructOrderProcessService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 生产指令工序信息 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:24:08
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class ProduceInstructOrderProcessController {

    @Resource
    private ProduceInstructOrderProcessService produceInstructOrderProcessService;

    /**
     * 工序列表
     * @param orderId 指令单id
     * @return
     */
    @Operation(summary = "工序列表 <AUTHOR>
    @GetMapping("/produceInstructOrderProcess/list/{orderId}")
    public ResponseDTO<List<ProduceInstructOrderProcessVO>> queryList(@PathVariable Long orderId) {
        return ResponseDTO.ok(produceInstructOrderProcessService.queryList(orderId));
    }


    /**
     * 指令单工序分页查询
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/produceInstructOrderProcess/produceInstructOrder/queryPage")
    public ResponseDTO<PageResult<ProduceInstructOrderVO>> queryProduceInstructOrderPage(@RequestBody @Valid ProduceInstructOrderQueryForm queryForm) {
        return ResponseDTO.ok(produceInstructOrderProcessService.queryProduceInstructOrderPage(queryForm));
    }

    @Operation(summary = "下拉根据指令单id获取所有工序信息接口 <AUTHOR>
    @GetMapping("/produceInstructOrderProcess/process/{id}")
    public ResponseDTO<List<ProduceInstructProcessVO>> getProcessInfoById(@PathVariable Long id) {
        return produceInstructOrderProcessService.getProcessInfoById(id);
    }

//    @Operation(summary = "分页查询 <AUTHOR>
//    @PostMapping("/produceInstructOrderProcess/queryPage")
//    public ResponseDTO<PageResult<ProduceInstructOrderProcessVO>> queryPage(@RequestBody @Valid ProduceInstructOrderProcessQueryForm queryForm) {
//        return ResponseDTO.ok(produceInstructOrderProcessService.queryPage(queryForm));
//    }
//
//    @Operation(summary = "添加 <AUTHOR>
//    @PostMapping("/produceInstructOrderProcess/add")
//    public ResponseDTO<String> add(@RequestBody @Valid ProduceInstructOrderProcessAddForm addForm) {
//        return produceInstructOrderProcessService.add(addForm);
//    }
//
//    @Operation(summary = "更新 <AUTHOR>
//    @PostMapping("/produceInstructOrderProcess/update")
//    public ResponseDTO<String> update(@RequestBody @Valid ProduceInstructOrderProcessUpdateForm updateForm) {
//        return produceInstructOrderProcessService.update(updateForm);
//    }
//
//    @Operation(summary = "批量删除 <AUTHOR>
//    @PostMapping("/produceInstructOrderProcess/batchDelete")
//    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Integer> idList) {
//        return produceInstructOrderProcessService.batchDelete(idList);
//    }
//
//    @Operation(summary = "单个删除 <AUTHOR>
//    @GetMapping("/produceInstructOrderProcess/delete/{id}")
//    public ResponseDTO<String> batchDelete(@PathVariable Integer id) {
//        return produceInstructOrderProcessService.delete(id);
//    }
}
