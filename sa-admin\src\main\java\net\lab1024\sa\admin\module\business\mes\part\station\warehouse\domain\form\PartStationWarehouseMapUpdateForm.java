package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 裁片仓库地图 更新表单
 *
 * <AUTHOR>
 * @Date 2024-11-05 16:55:45
 * @Copyright zscbdic
 */

@Data
public class PartStationWarehouseMapUpdateForm {

    @Schema(description = "仓库id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "仓库id 不能为空")
    private Long warehouseId;

    @Schema(description = "地图数据字符串", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "地图数据字符串 不能为空")
    private String mapDataStr;

    @Schema(description = "是否可见")
    @NotNull(message = "是否可见 不能为空")
    private Boolean visibleFlag;

}
