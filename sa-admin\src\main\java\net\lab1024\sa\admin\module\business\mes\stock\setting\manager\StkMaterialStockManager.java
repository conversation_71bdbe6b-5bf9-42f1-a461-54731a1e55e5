package net.lab1024.sa.admin.module.business.mes.stock.setting.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.dao.StkInventoryDao;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.entity.StkInventoryEntity;
import net.lab1024.sa.admin.module.business.mes.stock.setting.dao.StkMaterialStockDao;
import net.lab1024.sa.admin.module.business.mes.stock.setting.domain.entity.StkMaterialStockEntity;
import net.lab1024.sa.admin.module.business.mes.stock.setting.domain.form.StkMaterialStockAddForm;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;

/**
 * 物料库存属性  Manager
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:18:19
 * @Copyright zscbdic
 */
@Service
public class StkMaterialStockManager extends ServiceImpl<StkMaterialStockDao, StkMaterialStockEntity> {

    @Resource
    private StkInventoryDao stkInventoryDao;


    /**
     * 添加校验
     *
     * @param addForm
     */
    public void addCheck(StkMaterialStockAddForm addForm) {
        Long materialId = addForm.getMaterialId();
        Long count = this.lambdaQuery()
                .eq(StkMaterialStockEntity::getMaterialId, materialId)
                .count();
        if (count > 0) {
            throw new RuntimeException("物料已存在库存属性");
        }
    }

    /**
     * 打开批次管理校验
     *
     * @param materialId
     */
    public void openLotManageCheck(Long materialId) {
        Long count = stkInventoryDao.selectCount(new LambdaQueryWrapper<StkInventoryEntity>()
                .eq(StkInventoryEntity::getMaterielId, materialId));
        if (count > 0) {
            throw new BusinessException("该物料存在库存，无法启用批次");
        }
    }
}
