package net.lab1024.sa.admin.module.business.mes.part.station.setting.service;

import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.domain.dto.StorageManageModeConfigDTO;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.domain.dto.StoragePressureConfigDTO;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.domain.dto.StorageTimeConfigDTO;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.domain.form.PartStationConfigForm;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.domain.vo.PartStationConfigVO;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.manager.PartStationConfigManager;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 裁片驿站配置
 */
@Service
public class PartStationConfigService {

    @Resource
    private PartStationConfigManager partStationConfigManager;

    /**
     * 查询
     *
     * @return
     */
    public ResponseDTO<PartStationConfigVO> query() {
        PartStationConfigVO configVO = new PartStationConfigVO();
        // 存储时间
        StorageTimeConfigDTO storageTimeConfig = partStationConfigManager.getStorageTimeConfig();
        PartStationConfigVO.StorageTimeConfigVO storageTimeConfigVO = SmartBeanUtil.copy(storageTimeConfig, PartStationConfigVO.StorageTimeConfigVO.class);
        configVO.setStorageTimeConfig(storageTimeConfigVO);
        // 存储压力
        StoragePressureConfigDTO storagePressureConfig = partStationConfigManager.getStoragePressureConfig();
        PartStationConfigVO.StoragePressureConfigVO storagePressureConfigVO = SmartBeanUtil.copy(storagePressureConfig, PartStationConfigVO.StoragePressureConfigVO.class);
        configVO.setStoragePressureConfig(storagePressureConfigVO);
        // 存储管理方式
        StorageManageModeConfigDTO storageManageModeConfig = partStationConfigManager.getStorageManageModeConfig();
        PartStationConfigVO.StorageManageModeConfigVO storageManageModeConfigVO = SmartBeanUtil.copy(storageManageModeConfig, PartStationConfigVO.StorageManageModeConfigVO.class);
        configVO.setStorageManageModeConfig(storageManageModeConfigVO);
        return ResponseDTO.ok(configVO);
    }

    /**
     * 更新
     *
     * @param form
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(PartStationConfigForm form) {
        PartStationConfigForm.StorageTimeConfigForm storageTimeConfig = form.getStorageTimeConfig();

        StorageTimeConfigDTO storageTimeConfigDTO = SmartBeanUtil.copy(storageTimeConfig, StorageTimeConfigDTO.class);
        partStationConfigManager.updateStorageTimeConfig(storageTimeConfigDTO);

        StoragePressureConfigDTO storagePressureConfigDTO = SmartBeanUtil.copy(form.getStoragePressureConfig(), StoragePressureConfigDTO.class);
        partStationConfigManager.updateStoragePressureConfig(storagePressureConfigDTO);

        StorageManageModeConfigDTO storageManageModeConfigDTO = SmartBeanUtil.copy(form.getStorageManageModeConfig(), StorageManageModeConfigDTO.class);
        partStationConfigManager.updateStorageManageModeConfig(storageManageModeConfigDTO);
        return ResponseDTO.ok();
    }
}
