package net.lab1024.sa.admin.module.business.mes.part.station.setting.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 存放时间配置
 */
@Data
public class StorageTimeConfigDTO {

    /**
     * 主键
     */
    private Long configId;

    /**
     * 是否启用
     */
    private Boolean enableFlag;

    /**
     * 最小存放时间
     */
    private Integer minDay;

    /**
     * 最大存放时间
     */
    private Integer maxDay;

    /**
     * 通知员工id
     */
    private List<Long> employeeIds;
}
