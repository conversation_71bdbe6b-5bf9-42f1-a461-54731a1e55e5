package net.lab1024.sa.admin.module.business.mes.part.station.inventory.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.dao.PartStationInventoryDao;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.entity.PartStationInventoryEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.form.PartStationInventoryQuery;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.form.PartStationInventoryQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.form.PartStationInventoryUsageSituationQuery;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.vo.PartStationInventoryUsageSituationVO;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.vo.PartStationInventoryVO;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationBinDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationRackDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationWarehouseDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationBinEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationRackEntity;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.dao.FeTicketDao;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.domain.entity.FeTicketEntity;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 裁片驿站库存表 Service
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:44:34
 * @Copyright zscbdic
 */

@Service
public class PartStationInventoryService {

    @Resource
    private PartStationInventoryDao partStationInventoryDao;

    @Resource
    private FeTicketDao feTicketDao;

    @Resource
    private PartStationBinDao partStationBinDao;

    @Resource
    private PartStationRackDao partStationRackDao;

    @Resource
    private PartStationWarehouseDao partStationWarehouseDao;


    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<PartStationInventoryVO> queryPage(PartStationInventoryQueryForm queryForm) {
        LambdaQueryWrapper<FeTicketEntity> lq = new LambdaQueryWrapper<>();
        lq.eq(CharSequenceUtil.isNotEmpty(queryForm.getProduceInstructOrderNumber()), FeTicketEntity::getInstructOrderNumber, queryForm.getProduceInstructOrderNumber())
                .eq(CharSequenceUtil.isNotEmpty(queryForm.getItemNumber()), FeTicketEntity::getItemNumber, queryForm.getItemNumber())
                .in(CollUtil.isNotEmpty(queryForm.getColors()), FeTicketEntity::getStyleColor, queryForm.getColors())
                .in(CollUtil.isNotEmpty(queryForm.getSizes()), FeTicketEntity::getSize, queryForm.getSizes());

        List<FeTicketEntity> feTicketList = feTicketDao.selectList(lq);
        // 没有 FeTicket 数据，直接返回空
        if (CollectionUtils.isEmpty(feTicketList)) {
            return SmartPageUtil.convert2PageResult(new Page<>(), Collections.emptyList());
        }

        List<Long> feTicketIds = feTicketList.stream().map(FeTicketEntity::getId).toList();
        // 分页查询库存信息
        Page<PartStationInventoryEntity> tempPage = new Page<>(queryForm.getPageNum(), queryForm.getPageSize());
        partStationInventoryDao.selectPage(tempPage, new LambdaQueryWrapper<PartStationInventoryEntity>()
                .in(PartStationInventoryEntity::getFeTicketId, feTicketIds));
        if (CollUtil.isEmpty(tempPage.getRecords())) {
            // 没有数据
            return SmartPageUtil.convert2PageResult(tempPage, Collections.emptyList());
        }

        //查询货位信息与菲票信息
        List<Long> binIds = tempPage.getRecords().stream().map(PartStationInventoryEntity::getBinId).toList();
        Map<Long, FeTicketEntity> feTicketEntityMap = feTicketList.stream()
                .collect(Collectors.toMap(FeTicketEntity::getId, e -> e));
        List<PartStationBinEntity> binEntities = partStationBinDao.selectList(new LambdaQueryWrapper<PartStationBinEntity>().in(PartStationBinEntity::getId, binIds));
        Map<Long, PartStationBinEntity> binEntityMap = binEntities.stream()
                .collect(Collectors.toMap(PartStationBinEntity::getId, e -> e));

        // 封装数据
        List<PartStationInventoryVO> vos = new ArrayList<>((int) tempPage.getTotal());
        for (PartStationInventoryEntity e : tempPage.getRecords()) {
            if (feTicketEntityMap.containsKey(e.getFeTicketId()) && binEntityMap.containsKey(e.getBinId())) {
                FeTicketEntity ticket = feTicketEntityMap.get(e.getFeTicketId());
                PartStationInventoryVO vo = SmartBeanUtil.copy(ticket, PartStationInventoryVO.class);
                vo.setBinCode(binEntityMap.get(e.getBinId()).getBinCode());
                vo.setBinId(e.getBinId());
                vo.setFeTicketId(e.getFeTicketId());
                vo.setLastCheckTime(e.getLastCheckTime());
                vo.setStockInTime(e.getStockInTime());
                vos.add(vo);
            }
        }

        Page<PartStationInventoryVO> page = new Page<>();
        page.setCurrent(tempPage.getCurrent());
        page.setSize(tempPage.getSize());
        page.setTotal(tempPage.getTotal());
        page.setRecords(vos);
        return SmartPageUtil.convert2PageResult(page, vos);
    }


    /**
     * 查询
     *
     * @param queryForm
     * @return
     */
    public List<PartStationInventoryVO> query(PartStationInventoryQuery queryForm) {
        if (CharSequenceUtil.isEmpty(queryForm.getItemNumber()) && CharSequenceUtil.isEmpty(queryForm.getProduceInstructOrderNumber())) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<FeTicketEntity> lq = new LambdaQueryWrapper<>();
        lq.eq(CharSequenceUtil.isNotEmpty(queryForm.getProduceInstructOrderNumber()), FeTicketEntity::getInstructOrderNumber, queryForm.getProduceInstructOrderNumber())
                .eq(CharSequenceUtil.isNotEmpty(queryForm.getItemNumber()), FeTicketEntity::getItemNumber, queryForm.getItemNumber())
                .in(CollUtil.isNotEmpty(queryForm.getColors()), FeTicketEntity::getStyleColor, queryForm.getColors())
                .in(CollUtil.isNotEmpty(queryForm.getSizes()), FeTicketEntity::getSize, queryForm.getSizes());

        List<FeTicketEntity> feTicketList = feTicketDao.selectList(lq);

        // 没有 FeTicket 数据，直接返回空
        if (CollectionUtils.isEmpty(feTicketList)) {
            return Collections.emptyList();
        }

        List<Long> feTicketIds = feTicketList.stream().map(FeTicketEntity::getId).toList();
        // 分页查询库存信息

        List<PartStationInventoryEntity> inventoryList = partStationInventoryDao.selectList(new LambdaQueryWrapper<PartStationInventoryEntity>()
                .in(PartStationInventoryEntity::getFeTicketId, feTicketIds));
        if (CollUtil.isEmpty(inventoryList)) {
            // 没有数据
            return Collections.emptyList();
        }


        List<Long> binIds = inventoryList.stream().map(PartStationInventoryEntity::getBinId).toList();
        Map<Long, FeTicketEntity> feTicketEntityMap = feTicketList.stream()
                .collect(Collectors.toMap(FeTicketEntity::getId, e -> e));
        List<PartStationBinEntity> binEntities = partStationBinDao.selectList(new LambdaQueryWrapper<PartStationBinEntity>().in(PartStationBinEntity::getId, binIds));
        Map<Long, PartStationBinEntity> binEntityMap = binEntities.stream()
                .collect(Collectors.toMap(PartStationBinEntity::getId, e -> e));


        // 封装数据
        List<PartStationInventoryVO> vos = new ArrayList<>(inventoryList.size());
        for (PartStationInventoryEntity e : inventoryList) {
            if (feTicketEntityMap.containsKey(e.getFeTicketId()) && binEntityMap.containsKey(e.getBinId())) {
                FeTicketEntity ticket = feTicketEntityMap.get(e.getFeTicketId());
                PartStationInventoryVO vo = SmartBeanUtil.copy(ticket, PartStationInventoryVO.class);
                vo.setBinCode(binEntityMap.get(e.getBinId()).getBinCode());
                vo.setBinId(e.getBinId());
                vo.setFeTicketId(e.getFeTicketId());
                vo.setLastCheckTime(e.getLastCheckTime());
                vo.setStockInTime(e.getStockInTime());
                vos.add(vo);
            }
        }

        return vos;

    }

    /**
     * 库存使用情况
     *
     * @param queryForm
     * @return
     */
    public List<PartStationInventoryUsageSituationVO> usageSituation(PartStationInventoryUsageSituationQuery queryForm) {
        Long warehouseId = queryForm.getWarehouseId();
        // 查询所有货架
        List<PartStationRackEntity> rackEntities = partStationRackDao.selectList(new LambdaQueryWrapper<PartStationRackEntity>()
                .eq(PartStationRackEntity::getWarehouseId, warehouseId));
        if (CollUtil.isEmpty(rackEntities)) {
            return Collections.emptyList();
        }
        Map<Long, PartStationRackEntity> rackEntityMap = rackEntities.stream().collect(Collectors.toMap(PartStationRackEntity::getId, e -> e));

        // 查询所有库位
        List<Long> rackIds = rackEntities.stream().map(PartStationRackEntity::getId).toList();
        List<PartStationBinEntity> binEntities = partStationBinDao.selectList(new LambdaQueryWrapper<PartStationBinEntity>()
                .in(PartStationBinEntity::getRackId, rackIds));
        if (CollUtil.isEmpty(binEntities)) {
            return Collections.emptyList();
        }

        // 封装数据
        List<PartStationInventoryUsageSituationVO> vos = binEntities.stream().map(e -> {
            PartStationInventoryUsageSituationVO vo = new PartStationInventoryUsageSituationVO();
            vo.setBinCode(e.getBinCode());
            vo.setBinId(e.getId());
            vo.setTotalCapacity(e.getCapacity());
            vo.setUsedCapacity(0);
            if (rackEntityMap.containsKey(e.getRackId())) {
                vo.setRackCode(rackEntityMap.get(e.getRackId()).getRackCode());
                vo.setRackId(rackEntityMap.get(e.getRackId()).getId());
            }
            return vo;
        }).toList();

        List<Long> binIds = binEntities.stream().map(PartStationBinEntity::getId).toList();
        List<PartStationInventoryEntity> inventoryEntities = partStationInventoryDao.selectList(new LambdaQueryWrapper<PartStationInventoryEntity>()
                .in(PartStationInventoryEntity::getBinId, binIds));
        if (CollUtil.isEmpty(inventoryEntities)) {
            //如果没有库存数据，直接返回所有库位的信息
            return vos;
        }
        // 根据货位分组
        Map<Long, List<PartStationInventoryEntity>> inventoryGroupMap = inventoryEntities.stream()
                .collect(Collectors.groupingBy(PartStationInventoryEntity::getBinId));
        List<Long> ticketIds = inventoryEntities.stream()
                .map(PartStationInventoryEntity::getFeTicketId).toList();

        List<FeTicketEntity> feTicketList = feTicketDao.selectList(new LambdaQueryWrapper<FeTicketEntity>()
                .in(FeTicketEntity::getId, ticketIds));
        if (CollUtil.isEmpty(feTicketList)) {
            //没有 FeTicket 数据，直接返回
            return vos;
        }
        // 获取 菲票 数量
        Map<Long, Integer> ticketCountMap = feTicketList.stream().collect(Collectors.toMap(FeTicketEntity::getId, FeTicketEntity::getNum));

        for (PartStationInventoryUsageSituationVO vo : vos) {
            if (inventoryGroupMap.containsKey(vo.getBinId())) {
                //如果有库存数据，则计算占用容量
                // 获取该货位数据
                List<PartStationInventoryEntity> inventoryList = inventoryGroupMap.get(vo.getBinId());
                for (PartStationInventoryEntity e : inventoryList) {
                    // 菲票数量
                    vo.setUsedCapacity(vo.getUsedCapacity() + ticketCountMap.get(e.getFeTicketId()));
                }
            }
        }

        return vos;

    }

    /**
     * 库存汇总分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<PartStationInventoryVO> summaryPage(PartStationInventoryQueryForm queryForm) {
        Long pageNum = queryForm.getPageNum();
        Long pageSize = queryForm.getPageSize();
        Long offset = (pageNum - 1) * pageSize;
        List<PartStationInventoryVO> vos = partStationInventoryDao.querySummary(queryForm, offset, pageSize);
        Long total = partStationInventoryDao.querySummaryTotal(queryForm);
        PageResult<PartStationInventoryVO> result = new PageResult<>();
        result.setList(vos);
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);
        result.setTotal(total);
        result.setPages(total % pageSize == 0 ? total / pageSize : total / pageSize + 1);
        result.setEmptyFlag(CollUtil.isEmpty(vos));
        return result;

    }
}
