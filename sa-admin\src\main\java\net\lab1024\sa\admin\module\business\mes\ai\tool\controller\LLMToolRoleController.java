package net.lab1024.sa.admin.module.business.mes.ai.tool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.form.LLMToolRoleUpdateForm;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.vo.LLMToolRoleVO;
import net.lab1024.sa.admin.module.business.mes.ai.tool.service.LLMToolRoleService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.*;

/**
 * 大模型工具角色权限表 Controller
 *
 * <AUTHOR>
 * @Date 2025-04-06 15:15:25
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "大模型工具角色权限表")
public class LLMToolRoleController {

    @Resource
    private LLMToolRoleService lLMToolRoleService;

    /**
     * 更新角色权限
     * @param updateDTO
     * @return
     */
    @Operation(summary = "更新角色权限 <AUTHOR>
    @PostMapping("/ai/LLMTool/role/updateRoleMenu")
//    @SaCheckPermission("system:role:menu:update")
    public ResponseDTO<String> updateRoleMenu(@Valid @RequestBody LLMToolRoleUpdateForm updateDTO) {
        return lLMToolRoleService.updateRoleTool(updateDTO);
    }

    /**
     * 获取角色关联大模型工具权限
     * @param roleId
     * @return
     */
    @Operation(summary = "获取角色关联大模型工具权限 <AUTHOR>
    @GetMapping("/ai/LLMTool/role/getRoleSelectedMenu/{roleId}")
    public ResponseDTO<LLMToolRoleVO> getRoleSelectedMenu(@PathVariable Long roleId) {
        return lLMToolRoleService.getRoleSelectedTool(roleId);
    }
}
