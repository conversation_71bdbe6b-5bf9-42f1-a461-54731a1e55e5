package net.lab1024.sa.admin.module.business.mes.part.station.inventory.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.service.PartStationInventoryStatsService;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;

/**
 * 裁片驿站库存统计Controller
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:44:34
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class PartStationInventoryStatsController {

    @Resource
    private PartStationInventoryStatsService partStationInventoryStatsService;

    /**
     * 总库存数 <AUTHOR>
     *
     * @param warehouseId
     * @return
     */
    @Operation(summary = "总库存数")
    @GetMapping("/partStationInventoryStats/totalInventoryNum")
    public ResponseDTO<Long> totalInventoryNum(@RequestParam(required = false, value = "warehouseId") Long warehouseId) {
        return partStationInventoryStatsService.totalInventoryNum(warehouseId);
    }

    /**
     * 总扎数 <AUTHOR>
     *
     * @param warehouseId
     * @return
     */
    @Operation(summary = "总扎数")
    @GetMapping("/partStationInventoryStats/totalTieNum")
    public ResponseDTO<Long> totalTieNum(@RequestParam(required = false, value = "warehouseId") Long warehouseId) {
        return partStationInventoryStatsService.totalTieNum(warehouseId);
    }


    /**
     * 平均存放天数 <AUTHOR>
     *
     * @param warehouseId
     * @return
     */
    @Operation(summary = "平均存放天数")
    @GetMapping("/partStationInventoryStats/averageStoredDays")
    public ResponseDTO<Double> averageStoredDays(@RequestParam(required = false, value = "warehouseId") Long warehouseId) {
        return partStationInventoryStatsService.averageStoredDays(warehouseId);
    }

    /**
     * 库位利用率 <AUTHOR>
     *
     * @param warehouseId
     * @return
     */
    @Operation(summary = "库位利用率")
    @GetMapping("/partStationInventoryStats/binUsageRate")
    public ResponseDTO<Double> binUsageRate(@RequestParam(required = false, value = "warehouseId") Long warehouseId) {
        return partStationInventoryStatsService.binUsageRate(warehouseId);
    }


}
