package net.lab1024.sa.admin.module.business.mes.item.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

@Getter
@AllArgsConstructor
public enum ItemAttributeEnum implements BaseEnum {

    /**
     * 面料
     */
    CLOTH("0", "面料"),


    /**
     * 其他
     */
    OTHER("1", "其他"),

    /**
     * 成衣
     */
    FINISHED_CLOTHES("2", "成衣");

    private final String value;

    private final String desc;
}
