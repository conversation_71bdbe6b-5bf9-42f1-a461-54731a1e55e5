package net.lab1024.sa.admin.module.business.mes.factory.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 车间信息 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:31:51
 * @Copyright zscbdic
 */

@Data
public class WorkshopVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "所属工厂id")
    private Long factoryId;

    @Schema(description = "所属工厂名称")
    private String factoryName;

    @Schema(description = "车间名称")
    private String name;

    @Schema(description = "车间位置")
    private String location;

    @Schema(description = "负责人id")
    private Long managerId;

    @Schema(description = "负责人名称")
    private String manager;

}
