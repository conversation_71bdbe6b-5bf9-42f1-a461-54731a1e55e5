package net.lab1024.sa.admin.module.business.mes.stock.inventory.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.form.StkInventoryLogQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.vo.StkInventoryLogVO;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.service.StkInventoryLogService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 即时库存更新日志 Controller
 *
 * <AUTHOR>
 * @Date 2025-01-15 11:20:19
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class StkInventoryLogController {

    @Resource
    private StkInventoryLogService stkInventoryLogService;

    /**
     * 分页查询
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/stkInventoryLog/queryPage")
    public ResponseDTO<PageResult<StkInventoryLogVO>> queryPage(@RequestBody @Valid StkInventoryLogQueryForm queryForm) {
        return stkInventoryLogService.queryPage(queryForm);
    }


}
