package net.lab1024.sa.admin.module.business.mes.produce.arrange.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.entity.ProduceArrangeDetailEntity;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.form.ProduceArrangeDetailQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.vo.ProduceArrangeDetailVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 生产安排信息 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-22 18:44:38
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface ProduceArrangeDetailDao extends BaseMapper<ProduceArrangeDetailEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<ProduceArrangeDetailVO> queryPage(Page page, @Param("queryForm") ProduceArrangeDetailQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);


}
