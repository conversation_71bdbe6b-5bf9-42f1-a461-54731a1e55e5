package net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * 裁片收发日志 实体类
 *
 * <AUTHOR>
 * @Date 2024-11-06 15:25:13
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_part_dispatch_log")
public class PartDispatchLogEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 菲票id
     */
    private Long ticketId;

    /**
     * 生产指令单编号
     */
    private String instructOrderNumber;

    /**
     * 生产指令单id
     */
    private Long instructOrderId;

    /**
     * 裁床单id
     */
    private Long cutBedSheetId;

    /**
     * 裁床单编号
     */
    private String cutBedSheetNumber;

    /**
     * 物料id
     */
    private Long itemId;

    /**
     * 物料编号
     */
    private String itemNumber;

    /**
     * 规格型号
     */
    private String model;

    /**
     * 物料名称
     */
    private String itemName;

    /**
     * 物料单位id
     */
    private Long unitId;

    /**
     * 物料单位名称
     */
    private String unitName;

    /**
     * 扎号
     */
    private Integer tieNum;

    /**
     * 款式颜色
     */
    private String styleColor;

    /**
     * 尺码
     */
    private String size;

    /**
     * 床次
     */
    private Integer cutNum;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 缸号
     */
    private String lotNo;

    /**
     * 部位
     */
    private String positions;

    /**
     * 收发范围
     */
    private String dispatchRange;

    /**
     * 收或发
     */
    private String actionStatus;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 工厂id
     */
    private Long factoryId;

    /**
     * 工厂名称
     */
    private String factoryName;

    /**
     * 车间id
     */
    private Long workshopId;

    /**
     * 车间名称
     */
    private String workshopName;

    /**
     * 生产组id
     */
    private Long produceTeamId;

    /**
     * 生产组名称
     */
    private String produceTeamName;


    /**
     * 下发时间
     */
    private LocalDateTime sendTime;

    /**
     * 下发人id
     */
    private Long senderId;

    /**
     * 下发人名称
     */
    private String senderName;

    /**
     * 下发描述
     */
    private String sendDesc;

    /**
     * 回收时间
     */
    private LocalDateTime receiveTime;

    /**
     * 回收人id
     */
    private Long receiverId;

    /**
     * 回收人名称
     */
    private String receiverName;

    /**
     * 回收描述
     */
    private String receiveDesc;





}
