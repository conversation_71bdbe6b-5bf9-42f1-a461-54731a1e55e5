package net.lab1024.sa.admin.module.business.mes.stock.out.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockBillStatusEnum;
import net.lab1024.sa.admin.module.business.mes.stock.lot.manager.StkLotMasterManager;
import net.lab1024.sa.admin.module.business.mes.stock.out.dao.StkOutStockDao;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.bo.StkOutStockBO;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity.StkOutStockDetailEntity;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity.StkOutStockEntity;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.form.StkOtherOutStockAddForm;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.form.StkOtherOutStockUpdateForm;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.form.StkOutStockQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.vo.StkOtherOutStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.vo.StkOutStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.out.manager.StkOutStockDetailManager;
import net.lab1024.sa.admin.module.business.mes.stock.out.manager.StkOutStockManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 其他出库单服务
 */
@Service
public class StkOtherOutStockService {


    @Resource
    private StkOutStockDao stkOutStockDao;

    @Resource
    private StkOutStockManager stkOutStockManager;

    @Resource
    private StkOutStockDetailManager stkOutStockDetailManager;

    @Resource
    private StkLotMasterManager stkLotMasterManager;

    @Resource
    private StkOutStockService stkOutStockService;

    public ResponseDTO<PageResult<StkOutStockVO>> queryPage(StkOutStockQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<StkOutStockVO> list = stkOutStockDao.queryPage(page, queryForm);
        PageResult<StkOutStockVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return ResponseDTO.ok(pageResult);

    }

    public ResponseDTO<String> add(StkOtherOutStockAddForm form) {
        StkOutStockEntity outStock = SmartBeanUtil.copy(form, StkOutStockEntity.class);
        List<StkOutStockDetailEntity> details = SmartBeanUtil.copyList(form.getDetails(), StkOutStockDetailEntity.class);
        StkOutStockBO stockBO = new StkOutStockBO();
        stockBO.setStkOutStock(outStock);
        stockBO.setDetails(details);

        if (StockBillStatusEnum.UN_AUDIT.getValue().equals(form.getStatus())) {
            //保存单据
            stkOutStockService.saveStockOutBill(stockBO);
        } else if (StockBillStatusEnum.AUDIT.getValue().equals(form.getStatus())) {
            //保存并出库
            stkOutStockService.saveAndStockOut(stockBO);

        }
        return ResponseDTO.ok();
    }


    public ResponseDTO<String> update(StkOtherOutStockUpdateForm form) {
        Long id = form.getId();
        StkOutStockEntity stockEntity = stkOutStockManager.getById(id);
        if (stockEntity == null) {
            return ResponseDTO.userErrorParam("单据不存在");
        }
        String status = stockEntity.getStatus();
        stkOutStockManager.updateBillStatusCheck(status);


        StkOutStockBO stockBO = new StkOutStockBO();
        stockBO.setStkOutStock(SmartBeanUtil.copy(form, StkOutStockEntity.class));
        stockBO.setDetails(SmartBeanUtil.copyList(form.getDetails(), StkOutStockDetailEntity.class));
        if (StockBillStatusEnum.AUDIT.getValue().equals(form.getStatus())) {
            stkOutStockService.updateAndStockOut(stockBO);
        } else if (StockBillStatusEnum.UN_AUDIT.getValue().equals(form.getStatus())) {
            stkOutStockService.updateStockOutBill(stockBO);
        }
        return ResponseDTO.ok();
    }

    public ResponseDTO<StkOtherOutStockVO> queryById(Long id) {
        StkOutStockEntity stockEntity = stkOutStockManager.getById(id);
        if (stockEntity == null) {
            return ResponseDTO.userErrorParam("单据不存在");
        }
        StkOtherOutStockVO vo = SmartBeanUtil.copy(stockEntity, StkOtherOutStockVO.class);
        List<StkOutStockDetailEntity> detailEntities = stkOutStockDetailManager.lambdaQuery()
                .eq(StkOutStockDetailEntity::getOutStockId, id)
                .list();
        List<StkOtherOutStockVO.DetailVO> detailVOS = SmartBeanUtil.copyList(detailEntities, StkOtherOutStockVO.DetailVO.class);
        vo.setDetails(detailVOS);
        return ResponseDTO.ok(vo);
    }

    public ResponseDTO<String> updateStatus(Long id) {
        StkOutStockEntity stockEntity = stkOutStockManager.getById(id);
        if (stockEntity == null) {
            return ResponseDTO.userErrorParam("单据不存在");
        }
        String status = stockEntity.getStatus();
        stkOutStockManager.updateBillStatusCheck(status);

        if (StockBillStatusEnum.UN_AUDIT.getValue().equals(status)) {
            List<StkOutStockDetailEntity> detailEntities = stkOutStockDetailManager.lambdaQuery()
                    .eq(StkOutStockDetailEntity::getOutStockId, id)
                    .list();
            StkOutStockBO stockBO = new StkOutStockBO();
            stockBO.setStkOutStock(stockEntity);
            stockBO.setDetails(detailEntities);
            //出库
            stkOutStockService.stockOut(stockBO);

        }
        return ResponseDTO.ok();
    }

    /**
     * 删除
     * @param id
     * @return
     */
    public ResponseDTO<String> delete(Long id) {
        StkOutStockEntity stockEntity = stkOutStockManager.getById(id);
        if (stockEntity == null) {
            return ResponseDTO.userErrorParam("单据不存在");
        }
        String status = stockEntity.getStatus();
        stkOutStockManager.updateBillStatusCheck(status);

        stkOutStockManager.deleteBill(id);

        return ResponseDTO.ok();
    }
}
