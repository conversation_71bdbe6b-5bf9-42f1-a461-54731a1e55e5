package net.lab1024.sa.admin.module.business.mes.item.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

@Getter
@AllArgsConstructor
public enum ItemCategoryEnum implements BaseEnum {


    /**
     * 半成品
     */
    HALF_PRODUCT("0", "半成品"),

    /**
     * 成品
     */
    FINISHED_PRODUCT("1", "成品");

    private final String value;

    private final String desc;
}
