package net.lab1024.sa.admin.module.business.mes.base.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.SizeTemplateEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SizeTemplateQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.SizeTemplateVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 尺寸模板表 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-05 15:45:35
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface SizeTemplateDao extends BaseMapper<SizeTemplateEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<SizeTemplateVO> queryPage(Page page, @Param("queryForm") SizeTemplateQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

}
