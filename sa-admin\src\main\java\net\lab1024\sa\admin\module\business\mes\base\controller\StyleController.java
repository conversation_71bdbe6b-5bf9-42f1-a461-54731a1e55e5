package net.lab1024.sa.admin.module.business.mes.base.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.StyleAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.StyleTreeQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.StyleUpdateForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.StyleTreeVO;
import net.lab1024.sa.admin.module.business.mes.base.service.StyleService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 款式品类表 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-05 15:45:34
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "款式品类管理")
public class StyleController {

    @Resource
    private StyleService styleService;

    /**
     * 添加
     *
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/style/add")
    public ResponseDTO<String> add(@RequestBody @Valid StyleAddForm addForm) {
        return styleService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/style/update")
    public ResponseDTO<String> update(@RequestBody @Valid StyleUpdateForm updateForm) {
        return styleService.update(updateForm);
    }

    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/style/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return styleService.delete(id);
    }

    /**
     * 样式层级树查询
     * @param styleTreeQueryForm
     * @return
     */
    @Operation(summary = "样式层级树查询 <AUTHOR>
    @PostMapping("/style/queryTree")
    public ResponseDTO<List<StyleTreeVO>> queryTree(@RequestBody @Valid StyleTreeQueryForm styleTreeQueryForm) {
        return styleService.queryTree(styleTreeQueryForm);
    }

}
