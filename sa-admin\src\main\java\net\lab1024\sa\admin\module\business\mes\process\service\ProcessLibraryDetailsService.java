package net.lab1024.sa.admin.module.business.mes.process.service;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.process.dao.ProcessLibraryDetailsDao;
import net.lab1024.sa.admin.module.business.mes.process.domain.entity.ProcessLibraryDetailsEntity;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessLibraryDetailsAddForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessLibraryDetailsQueryForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessLibraryDetailsUpdateForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.vo.ProcessLibraryDetailsVO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 工序库详情工序 Service
 *
 * <AUTHOR>
 * @Date 2024-07-13 15:19:57
 * @Copyright zscbdic
 */

@Service
public class ProcessLibraryDetailsService {

    @Resource
    private ProcessLibraryDetailsDao processLibraryDetailsDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<ProcessLibraryDetailsVO> queryPage(ProcessLibraryDetailsQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ProcessLibraryDetailsVO> list = processLibraryDetailsDao.queryPage(page, queryForm);
        PageResult<ProcessLibraryDetailsVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(ProcessLibraryDetailsAddForm addForm) {
        ProcessLibraryDetailsEntity processLibraryDetailsEntity = SmartBeanUtil.copy(addForm, ProcessLibraryDetailsEntity.class);
        processLibraryDetailsDao.insert(processLibraryDetailsEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(ProcessLibraryDetailsUpdateForm updateForm) {
        ProcessLibraryDetailsEntity processLibraryDetailsEntity = SmartBeanUtil.copy(updateForm, ProcessLibraryDetailsEntity.class);
        processLibraryDetailsDao.updateById(processLibraryDetailsEntity);
        return ResponseDTO.ok();
    }
    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id){
            return ResponseDTO.ok();
        }
        processLibraryDetailsDao.updateDeleted(id,true);
        return ResponseDTO.ok();
    }
}
