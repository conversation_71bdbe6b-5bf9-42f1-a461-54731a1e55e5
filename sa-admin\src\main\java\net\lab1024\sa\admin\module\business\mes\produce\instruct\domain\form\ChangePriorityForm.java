package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.constant.ProduceInstructOrderPrioityEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;

import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class ChangePriorityForm {

    /**
     * 指令id
     */
    @NotNull
    @Schema(description = "指令id")
    private List<Long> id;

    /**
     * 优先级
     */
    @NotNull
    @Schema(description = "优先级")
    @CheckEnum(value = ProduceInstructOrderPrioityEnum.class, required = true, message = "优先级不存在")
    private String value;

}
