package net.lab1024.sa.admin.module.business.mes.item.service;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.item.dao.ItemTypeDao;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemEntity;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemTypeEntity;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemTypeAddForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemTypeQueryTreeForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemTypeUpdateForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.vo.ItemTypeTreeVO;
import net.lab1024.sa.admin.module.business.mes.item.manager.ItemManager;
import net.lab1024.sa.admin.module.business.mes.item.manager.ItemTypeManager;
import net.lab1024.sa.base.common.code.UserErrorCode;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 物料分类表 Service
 *
 * <AUTHOR>
 * @Date 2024-07-02 09:54:23
 * @Copyright zscbdic
 */

@Service
public class ItemTypeService {

    @Resource
    private ItemTypeDao itemTypeDao;

    @Resource
    private ItemTypeManager itemTypeManager;

    @Resource
    private ItemManager itemManager;


    /**
     * 添加
     */
    public ResponseDTO<String> add(ItemTypeAddForm addForm) {
        ItemTypeEntity itemTypeEntity = SmartBeanUtil.copy(addForm, ItemTypeEntity.class);
        ResponseDTO<String> res = itemTypeManager.checkItemType(itemTypeEntity, false);
        if (!res.getOk()) {
            return res;
        }
        // 没有父类则使用默认父类
        Long parentId = null == addForm.getParentId() ? NumberUtils.LONG_ZERO : addForm.getParentId();
        itemTypeEntity.setParentId(parentId);
        itemTypeEntity.setSort(null == addForm.getSort() ? 0 : addForm.getSort());
        itemTypeEntity.setDeletedFlag(false);

        // 保存数据
        itemTypeDao.insert(itemTypeEntity);

        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(ItemTypeUpdateForm updateForm) {
        // 校验类目
        Long id = updateForm.getId();
        ItemTypeEntity originEntity = itemTypeManager.getById(id);

        if (originEntity == null) {
            return ResponseDTO.error(UserErrorCode.DATA_NOT_EXIST);
        }
        ItemTypeEntity itemType = SmartBeanUtil.copy(updateForm, ItemTypeEntity.class);

        /*
          不更新父类id
         */
        itemType.setParentId(originEntity.getParentId());

        ResponseDTO<String> responseDTO = itemTypeManager.checkItemType(itemType, true);
        if (!responseDTO.getOk()) {
            return responseDTO;
        }
        itemTypeDao.updateById(itemType);
        return ResponseDTO.ok();
    }


    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        ItemTypeEntity itemTypeEntity = itemTypeDao.selectById(id);
        if (itemTypeEntity == null) {
            return ResponseDTO.error(UserErrorCode.DATA_NOT_EXIST);
        }

        List<Long> categorySubId = itemTypeManager.queryTypeSubId(Lists.newArrayList(id));
        if (CollectionUtils.isNotEmpty(categorySubId)) {
            return ResponseDTO.userErrorParam("请先删除子级类目");
        }

        Long count = itemManager.lambdaQuery().eq(ItemEntity::getTypeId, id).count();
        if (count > 0) {
            return ResponseDTO.userErrorParam("请先删除该分类下的物料");
        }


        itemTypeDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }

    /**
     * 树形查询
     *
     * @param queryForm
     * @return
     */
    public ResponseDTO<List<ItemTypeTreeVO>> queryTree(ItemTypeQueryTreeForm queryForm) {
        if (null == queryForm.getParentId()) {
            queryForm.setParentId(NumberUtils.LONG_ZERO);
        }
        List<ItemTypeTreeVO> treeList = itemTypeManager.queryItemTypeTree(queryForm.getParentId(), queryForm.getQueryKey());
        return ResponseDTO.ok(treeList);
    }
}
