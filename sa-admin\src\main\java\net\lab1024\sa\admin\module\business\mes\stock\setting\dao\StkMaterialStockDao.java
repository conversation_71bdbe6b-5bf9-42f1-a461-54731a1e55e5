package net.lab1024.sa.admin.module.business.mes.stock.setting.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.stock.setting.domain.entity.StkMaterialStockEntity;
import net.lab1024.sa.admin.module.business.mes.stock.setting.domain.form.StkMaterialStockQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.setting.domain.vo.StkMaterialStockVO;

import java.util.List;

/**
 * 物料库存属性 Dao
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:18:19
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface StkMaterialStockDao extends BaseMapper<StkMaterialStockEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<StkMaterialStockVO> queryPage(Page page, @Param("queryForm") StkMaterialStockQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
