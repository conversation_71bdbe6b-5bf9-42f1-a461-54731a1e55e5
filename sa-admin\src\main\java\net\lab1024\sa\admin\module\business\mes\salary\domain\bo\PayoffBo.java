package net.lab1024.sa.admin.module.business.mes.salary.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.PayoffRecordDetailEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.PayoffRecordEntity;

import java.util.List;

@Data
public class PayoffBo {

    private List<RecordBo> payoffRecords;

    private List<Long> settlementRecordIds;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RecordBo {

        private PayoffRecordEntity record;

        private List<PayoffRecordDetailEntity> details;
    }
}
