package net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 裁片周转箱 更新表单
 *
 * <AUTHOR>
 * @Date 2025-03-14 11:36:51
 * @Copyright zscbdic
 */

@Data
public class PartStationTurnBoxUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "周转箱编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "周转箱编号 不能为空")
    private String number;

    /**
     * 周转箱名称
     */
    private String name;

    @Schema(description = "容量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "容量 不能为空")
    @Min(value = 1, message = "容量 不能小于1")
    private Integer capacity;

}
