package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

/**
 * 裁片仓库表 列表VO
 *
 * <AUTHOR>
 * @Date 2024-10-06 16:48:11
 * @Copyright zscbdic
 */

@Data
public class PartStationWarehouseVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "仓库名称")
    private String name;

    /**
     * 仓库编码
     */
    @Schema(description = "仓库编码")
    private String warehouseCode;

    @Schema(description = "仓库地址")
    private String address;

    @Schema(description = "仓库任务员")
    private String taskerIds;

    @Schema(description = "仓库任务员")
    private List<Long> taskerIdList;

}
