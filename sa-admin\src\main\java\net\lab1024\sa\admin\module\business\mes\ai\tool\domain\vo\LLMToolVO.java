package net.lab1024.sa.admin.module.business.mes.ai.tool.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 大模型工具表 列表VO
 *
 * <AUTHOR>
 * @Date 2025-04-05 14:27:51
 * @Copyright zscbdic
 */

@Data
public class LLMToolVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "bean名称")
    private String beanName;

    @Schema(description = "工具描述")
    private String description;

    @Schema(description = "启用状态;0禁用 1启用")
    private Boolean enableFlag;

    @Schema(description = "工具类型")
    private String type;

}
