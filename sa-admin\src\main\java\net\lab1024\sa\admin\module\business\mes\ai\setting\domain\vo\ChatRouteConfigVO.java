package net.lab1024.sa.admin.module.business.mes.ai.setting.domain.vo;

import lombok.Data;

import java.util.List;

@Data
public class ChatRouteConfigVO {

    private Long configId;

    /**
     * 提示
     */
    private String prompt;

    /**
     * 决策模型id
     */
    private Long modelId;

    /**
     * 是否启用
     */
    private Boolean enableFlag;

    /**
     * 配置
     */
    private List<Item> items;

    @Data
    public static class Item {

        /**
         * 值
         */
        private String value;

        /**
         * 选择的模型id
         */
        private Long modelId;

    }


}
