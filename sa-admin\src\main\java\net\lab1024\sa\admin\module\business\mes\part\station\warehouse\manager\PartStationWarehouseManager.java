package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationRackDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationWarehouseDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationRackEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationWarehouseEntity;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationWarehouseAddForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationWarehouseUpdateForm;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;

/**
 * 裁片仓库表  Manager
 *
 * <AUTHOR>
 * @Date 2024-10-06 16:48:11
 * @Copyright zscbdic
 */
@Service
public class PartStationWarehouseManager extends ServiceImpl<PartStationWarehouseDao, PartStationWarehouseEntity> {

    @Resource
    private PartStationRackDao partStationRackDao;

    /**
     * 删除前校验
     *
     * @param id
     */
    public void deleteCheck(Long id) {
        Long count = partStationRackDao.selectCount(new LambdaQueryWrapper<PartStationRackEntity>()
                .eq(PartStationRackEntity::getWarehouseId, id));
        if (count > 0) {
            throw new BusinessException("该仓库存在货架，无法删除");
        }
    }

    /**
     * 新增前校验
     *
     * @param addForm
     */
    public void addCheck(PartStationWarehouseAddForm addForm) {
        String warehouseCode = addForm.getWarehouseCode();
//        if(warehouseCode.contains(QrCodeTypeUtil.SEPARATOR)){
//            throw new BusinessException("仓库编码不能包含"+QrCodeTypeUtil.SEPARATOR);
//        }

        long count = this.count(new LambdaQueryWrapper<PartStationWarehouseEntity>()
                .eq(PartStationWarehouseEntity::getWarehouseCode, warehouseCode));
        if (count > 0) {
            throw new BusinessException("仓库编码已存在");
        }
    }

    /**
     * 更新前校验
     *
     * @param updateForm
     */
    public void updateCheck(PartStationWarehouseUpdateForm updateForm) {
        String warehouseCode = updateForm.getWarehouseCode();
//        if(warehouseCode.contains(QrCodeTypeUtil.SEPARATOR)){
//            throw new BusinessException("仓库编码不能包含"+QrCodeTypeUtil.SEPARATOR);
//        }

        long count = this.count(new LambdaQueryWrapper<PartStationWarehouseEntity>()
                .eq(PartStationWarehouseEntity::getWarehouseCode, warehouseCode)
                .ne(PartStationWarehouseEntity::getId, updateForm.getId()));
        if (count > 0) {
            throw new BusinessException("仓库编码已存在");
        }
    }
}
