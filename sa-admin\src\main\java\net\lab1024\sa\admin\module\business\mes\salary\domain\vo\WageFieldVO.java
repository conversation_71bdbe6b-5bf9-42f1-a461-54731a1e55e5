package net.lab1024.sa.admin.module.business.mes.salary.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 工资字段 列表VO
 *
 * <AUTHOR>
 * @Date 2024-11-25 20:20:26
 * @Copyright zscbdic
 */

@Data
public class WageFieldVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

//    @Schema(description = "删除标识;0未删除，1删除")
//    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "字段名称")
    private String fieldName;

    @Schema(description = "运算类型;0 加项,1减项")
    private String type;

    @Schema(description = "配置值（保留）")
    private BigDecimal value;

}
