package net.lab1024.sa.admin.module.business.mes.base.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.StyleColorEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.StyleColorQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.StyleColorVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 款式颜色表 Dao
 *
 * <AUTHOR>
 * @Date 2024-11-03 20:49:04
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface StyleColorDao extends BaseMapper<StyleColorEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<StyleColorVO> queryPage(Page page, @Param("queryForm") StyleColorQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Integer id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Integer> idList,@Param("deletedFlag")boolean deletedFlag);

}
