package net.lab1024.sa.admin.module.business.mes.item.controller;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import net.lab1024.sa.admin.module.business.mes.common.excel.utils.ExcelUtils;
import net.lab1024.sa.admin.module.business.mes.item.constant.ItemAttributeEnum;
import net.lab1024.sa.admin.module.business.mes.item.constant.ItemCategoryEnum;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemClothesAddForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemClothesQueryForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemClothesUpdateForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.vo.ItemClothesVO;
import net.lab1024.sa.admin.module.business.mes.item.service.ItemClothesService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

import java.io.IOException;
import java.util.List;

/**
 * 成衣信息 Controller
 */
@RestController
public class ItemClothesController {


    @Resource
    private ItemClothesService itemClothesService;

    @Resource
    private SerialNumberService serialNumberService;

    /**
     * 成衣添加
     *
     * @param addForm
     * @return
     */
    @Operation(summary = "成衣添加")
    @PostMapping("/item/clothes/add")
    public ResponseDTO<String> add(@RequestBody @Valid ItemClothesAddForm addForm) {
        addForm.setAttribute(ItemAttributeEnum.FINISHED_CLOTHES.getValue());
        addForm.setCategory(ItemCategoryEnum.FINISHED_PRODUCT.getValue());
        if (StrUtil.isEmpty(addForm.getNumber())) {
            addForm.setNumber(serialNumberService.generate(SerialNumberIdEnum.ITEM));
        }
        return itemClothesService.add(addForm);
    }

    /**
     * 成衣详情
     *
     * @param id 物料id
     * @return
     */
    @Operation(summary = "详情")
    @GetMapping("/item/clothes/byId")
    public ResponseDTO<ItemClothesVO> getById(@RequestParam("id") Long id) {
        return itemClothesService.getById(id);
    }

    /**
     * 成衣更新
     *
     * @return
     */
    @Operation(summary = "成衣更新")
    @PostMapping("/item/clothes/update")
    public ResponseDTO<String> update(@RequestBody @Valid ItemClothesUpdateForm updateForm) {
        updateForm.setAttribute(ItemAttributeEnum.FINISHED_CLOTHES.getValue());
        updateForm.setCategory(ItemCategoryEnum.FINISHED_PRODUCT.getValue());
        return itemClothesService.update(updateForm);
    }

    /**
     * 成衣分页
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页")
    @PostMapping("/item/clothes/queryPage")
    public ResponseDTO<PageResult<ItemClothesVO>> queryPage(@RequestBody @Valid ItemClothesQueryForm queryForm) {
        return itemClothesService.queryPage(queryForm);
    }


    @Operation(summary = "删除")
    @GetMapping("/item/clothes/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return itemClothesService.delete(id);
    }


    /**
     * 查询sku列表
     *
     * @param spuNumber
     * @return
     */
    @GetMapping("/query/sku/list/{spuNumber}")
    public ResponseDTO<List<ItemClothesVO>> querySkuList(@PathVariable("spuNumber") String spuNumber) {
        return itemClothesService.querySkuListBySpuNumber(spuNumber);
    }

    /**
     * 下载模板
     * @param response
     * @throws IOException
     */
    @Operation(summary = "下载模板")
    @GetMapping("/item/clothes/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        ExcelUtils.excelTemplateExport(response,"file-template/excel/item/import_item_clothes_template.xlsx","导入成衣模板.xlsx");
    }

    /**
     * 导入
     * @param file
     * @return
     */
    @Operation(summary = "导入")
    @PostMapping("/item/clothes/import")
    public ResponseDTO<String> importExcel(@RequestParam("file") MultipartFile file) {
        return itemClothesService.importExcel(file);
    }
}
