package net.lab1024.sa.admin.module.business.mes.bom.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.mes.bom.domain.entity.BomEntity;
import net.lab1024.sa.admin.module.business.mes.bom.domain.form.BomQueryForm;
import net.lab1024.sa.admin.module.business.mes.bom.domain.vo.BomVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 物料BOM表 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:25:22
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface BomDao extends BaseMapper<BomEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<BomVO> queryPage(Page page, @Param("queryForm") BomQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

}
