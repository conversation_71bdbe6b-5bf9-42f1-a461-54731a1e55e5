package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationRackEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationRackQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.vo.PartStationRackVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 裁片货架 Dao
 *
 * <AUTHOR>
 * @Date 2024-10-06 16:49:27
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface PartStationRackDao extends BaseMapper<PartStationRackEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<PartStationRackVO> queryPage(Page page, @Param("queryForm") PartStationRackQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

}
