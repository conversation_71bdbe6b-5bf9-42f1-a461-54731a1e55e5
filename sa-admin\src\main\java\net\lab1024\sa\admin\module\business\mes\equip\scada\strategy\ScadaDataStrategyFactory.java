package net.lab1024.sa.admin.module.business.mes.equip.scada.strategy;

import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.constant.IotPlatformEnum;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * ScadaDataStrategy工厂
 */
@Component
public class ScadaDataStrategyFactory {

    private Map<String, ScadaDataStrategy> scadaDataStrategyMap = new ConcurrentHashMap<>();

    public ScadaDataStrategy getStrategy(String iotPlatform) {
        return scadaDataStrategyMap.get(iotPlatform);
    }

    public void register(IotPlatformEnum platformEnum, ScadaDataStrategy strategy) {
        scadaDataStrategyMap.put(platformEnum.getValue(), strategy);
    }
}
