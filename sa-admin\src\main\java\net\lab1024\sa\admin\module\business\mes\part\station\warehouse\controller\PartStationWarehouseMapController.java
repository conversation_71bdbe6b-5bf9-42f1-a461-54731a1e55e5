package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.controller;

import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationWarehouseMapUpdateForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.vo.PartStationWarehouseMapVO;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.service.PartStationWarehouseMapService;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 裁片仓库地图 Controller
 *
 * <AUTHOR>
 * @Date 2024-11-05 16:55:45
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class PartStationWarehouseMapController {

    @Resource
    private PartStationWarehouseMapService partStationWarehouseMapService;

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/partStationWarehouseMap/update")
    public ResponseDTO<String> update(@RequestBody @Valid PartStationWarehouseMapUpdateForm updateForm) {
        return partStationWarehouseMapService.update(updateForm);
    }

    /**
     * 根据仓库id获取
     * @param warehouseId
     * @return
     */
    @Operation(summary = "根据仓库id获取 <AUTHOR>
    @GetMapping("/partStationWarehouseMap/getMapByWarehouseId/{warehouseId}")
    public ResponseDTO<PartStationWarehouseMapVO> getMapDataByWarehouseId(@PathVariable("warehouseId") Long warehouseId) {
        return partStationWarehouseMapService.getMapDataByWarehouseId(warehouseId);
    }

}
