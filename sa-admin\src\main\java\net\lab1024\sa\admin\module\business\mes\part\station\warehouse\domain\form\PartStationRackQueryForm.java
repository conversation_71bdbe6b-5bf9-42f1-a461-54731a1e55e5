package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form;

import net.lab1024.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 裁片货架 分页查询表单
 *
 * <AUTHOR>
 * @Date 2024-10-06 16:49:27
 * @Copyright zscbdic
 */

@Data
public class PartStationRackQueryForm extends PageParam{

    @Schema(description = "所属仓库id")
    private Long warehouseId;

    @Schema(description = "货架编号")
    private String queryKey;

}
