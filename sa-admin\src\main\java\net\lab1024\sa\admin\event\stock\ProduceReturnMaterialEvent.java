package net.lab1024.sa.admin.event.stock;

import lombok.Data;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 生产退料事件
 */
@Getter
public class ProduceReturnMaterialEvent extends ApplicationEvent {

    private List<Material> materials;


    public ProduceReturnMaterialEvent(final Object source, List<Material> materials) {
        super(source);
        this.materials = materials;
    }

    @Data
    public static class Material implements Serializable {

        /**
         * 用料清单id
         */
        private Long produceInstructOrderMaterielId;

        /**
         * 物料ID
         */
        private Long materielId;
        /**
         * 实际领料数量
         */
        private BigDecimal qty = BigDecimal.ZERO;
    }
}
