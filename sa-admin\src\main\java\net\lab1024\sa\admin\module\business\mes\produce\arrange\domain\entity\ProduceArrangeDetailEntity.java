package net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 生产安排信息 实体类
 *
 * <AUTHOR>
 * @Date 2024-07-22 18:44:38
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_produce_arrange_detail")
public class ProduceArrangeDetailEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 主表id
     */
    private Long arrangeId;

    /**
     * 序号
     */
    private Integer serialNumber;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 负责人id
     */
    private Long headId;

    /**
     * 负责人名称
     */
    private String headName;

    /**
     * 末道节点;0否 1是
     */
    private Boolean endFlag;

}
