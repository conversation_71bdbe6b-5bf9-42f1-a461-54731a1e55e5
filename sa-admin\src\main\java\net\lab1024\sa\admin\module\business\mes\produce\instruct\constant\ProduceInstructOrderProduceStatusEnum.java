package net.lab1024.sa.admin.module.business.mes.produce.instruct.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 生产业务状态;0计划，1下达，2开工，3完工
 */
@Getter
@AllArgsConstructor
public enum ProduceInstructOrderProduceStatusEnum implements BaseEnum {

    /**
     * 计划
     */
    PLAN("0", "计划"),
    /**
     * 下达
     */
    ISSUED("1", "下达"),
    /**
     * 开工
     */
    START("2", "开工"),
    /**
     * 完工
     */
    FINISH("3", "完工");


    private final String value;

    private final String desc;

    /**
     * 根据value获取枚举
     * @param value
     * @return 枚举 可能为null
     */
    public static ProduceInstructOrderProduceStatusEnum getByValue(String value) {
        for (ProduceInstructOrderProduceStatusEnum item : values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }
}
