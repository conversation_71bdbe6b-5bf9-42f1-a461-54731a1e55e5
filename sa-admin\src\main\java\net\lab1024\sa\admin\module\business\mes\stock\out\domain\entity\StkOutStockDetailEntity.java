package net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 出库单详情 实体类
 *
 * <AUTHOR>
 * @Date 2025-02-09 19:59:35
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_stk_out_stock_detail")
public class StkOutStockDetailEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 出库单ID
     */
    private Long outStockId;

    /**
     * 来源单类型
     */
    private String originOrderBillType;

    /**
     * 来源单ID
     */
    private Long originOrderBillId;

    /**
     * 来源单号
     */
    private String originOrderBillNumber;

    /**
     * 单据来源详情类型
     */
    private String originDetailType;

    /**
     * 单据来源详情ID
     */
    private Long originDetailId;

    /**
     * 单据来源详情行号
     */
    private Integer originDetailSeq;

    /**
     * 行号
     */
    private Integer seq;

    /**
     * 物料ID
     */
    private Long materielId;

    /**
     * 批次ID
     */
    private Long lotId;

    /**
     * 批次编号
     */
    private String lotNumber;

    /**
     * SN集合
     */
    private String sns;

    /**
     * 仓位id
     */
    private Long locationId;

    /**
     * 仓位编号
     */
    private String locationNumber;

    /**
     * 单位id
     */
    private Long unitId;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 实收数量
     */
    private BigDecimal qty;

    /**
     * 关联数量
     */
    private BigDecimal joinQty;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 出库类型
     */
    private String outType;

    /**
     * 出库原因
     */
    private String outReason;

}
