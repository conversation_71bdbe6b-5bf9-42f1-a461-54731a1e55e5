package net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 即时库存 列表VO
 *
 * <AUTHOR>
 * @Date 2025-01-14 15:20:23
 * @Copyright zscbdic
 */

@Data
public class StkInventoryVO {

    @Schema(description = "仓库id")
    private Long warehouseId;

    @Schema(description = "仓库名称")
    private String warehouseName;

    @Schema(description = "仓库编号")
    private String warehouseNumber;

    @Schema(description = "货位id")
    private Long locationId;

    @Schema(description = "货位编号")
    private String locationNumber;

    @Schema(description = "物料id")
    private Long materielId;

    @Schema(description = "物料名称")
    private String materielName;

    @Schema(description = "物料规格型号")
    private String materielModel;

    @Schema(description = "物料spu编号")
    private String materielSpuNumber;

    @Schema(description = "物料sku编号")
    private String materielSkuNumber;

    @Schema(description = "批次号")
    private String lotNumber;

    @Schema(description = "批次id")
    private Long lotId;

    @Schema(description = "sn ID")
    private Long snId;

    @Schema(description = "货主类型;保留")
    private String ownerType;

    @Schema(description = "货主id;保留")
    private Long ownerId;

    @Schema(description = "单位id")
    private Long unitId;

    @Schema(description = "单位名称")
    private String unitName;

    @Schema(description = "实际库存")
    private BigDecimal qty;

    @Schema(description = "可用库存")
    private BigDecimal avbQty;

    @Schema(description = "锁定库存")
    private BigDecimal lockQty;

    @Schema(description = "预计库存")
    private BigDecimal predictQty;

}
