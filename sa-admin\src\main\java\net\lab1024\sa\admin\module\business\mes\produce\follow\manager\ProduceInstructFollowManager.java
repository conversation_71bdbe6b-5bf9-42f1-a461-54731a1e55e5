package net.lab1024.sa.admin.module.business.mes.produce.follow.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.CustomerEntity;
import net.lab1024.sa.admin.module.business.mes.base.manager.CustomerManager;
import net.lab1024.sa.admin.module.business.mes.produce.follow.dao.ProduceInstructFollowDao;
import net.lab1024.sa.admin.module.business.mes.produce.follow.domain.entity.ProduceInstructFollowEntity;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.produce.follow.domain.vo.ProduceInstructFollowVO;
import net.lab1024.sa.admin.module.system.employee.domain.entity.EmployeeEntity;
import net.lab1024.sa.admin.module.system.employee.manager.EmployeeManager;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 生产跟单  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-26 21:36:36
 * @Copyright zscbdic
 */
@Service
public class ProduceInstructFollowManager extends ServiceImpl<ProduceInstructFollowDao, ProduceInstructFollowEntity> {

    @Resource
    ProduceInstructFollowDao followDao;

    @Resource
    CustomerManager customerManager;


    @Resource
    EmployeeManager employeeManager;



    /**
     * 设置客户电话，已派员工，委派人信息
     * @param list
     */
    public void handleInfo(List<ProduceInstructFollowVO> list){
        Map<Long, List<CustomerEntity>> customerMap = customerManager.processMap();
        Map<Long, List<EmployeeEntity>> employeeMap = employeeManager.lambdaQuery().list()
                .stream().collect(Collectors.groupingBy(EmployeeEntity::getEmployeeId));;
        list.forEach(e->{
            Long customerId = e.getCustomerId();
            Long instructOrderId = e.getInstructOrderId();
            //设置电话信息
            if (customerId!=null) {
                e.setTelephone(customerMap.get(customerId).get(0).getTelephone());
            }
            //根据指令单id设置所有员工姓名和id
            LambdaQueryWrapper<ProduceInstructFollowEntity> wrapper = new LambdaQueryWrapper<>();
            List<ProduceInstructFollowEntity> produceInstructFollowEntities = followDao.selectList(wrapper.eq(ProduceInstructFollowEntity::getInstructOrderId, instructOrderId));
            List<Long> empIds = produceInstructFollowEntities.stream().map(ProduceInstructFollowEntity::getEmployeeId).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(empIds)) {
                e.setEmployeeName(Collections.emptyList());
            }else {
                List<String> empNames = new ArrayList<>();
                empIds.forEach(emp->{
                    String actualName = employeeMap.get(emp).get(0).getActualName();
                    empNames.add(actualName);
                });
                e.setEmployeeName(empNames);
                e.setEmployeeId(empIds);
            }
            //根据指令单id设置委派人名称
            if (CollectionUtil.isNotEmpty(produceInstructFollowEntities)) {
                Long delegateId = produceInstructFollowEntities.get(0).getDelegateId();
                String actualName = employeeMap.get(delegateId).get(0).getActualName();
                e.setDelegateName(actualName);
                e.setDelegateId(delegateId);
            }
        });
    }
}
