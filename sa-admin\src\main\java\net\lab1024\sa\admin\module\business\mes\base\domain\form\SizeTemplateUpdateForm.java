package net.lab1024.sa.admin.module.business.mes.base.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 尺寸模板表 更新表单
 *
 * <AUTHOR>
 * @Date 2024-07-05 15:45:35
 * @Copyright zscbdic
 */

@Data
public class SizeTemplateUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

    @Schema(description = "尺码编号（如：001上衣、002衬衫等）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "尺码编号（如：001上衣、002衬衫等） 不能为空")
    private String sizeCode;

    @Schema(description = "尺码模板名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "尺码模板名称 不能为空")
    private String templateName;

    @Schema(description = "尺码标准（如：国际标准GB/T2664-2001等）")
    private String standard;

}
