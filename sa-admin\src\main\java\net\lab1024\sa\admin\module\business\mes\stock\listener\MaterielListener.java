package net.lab1024.sa.admin.module.business.mes.stock.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.event.item.ItemDeleteCheckEvent;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.dao.StkInventoryDao;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.entity.StkInventoryEntity;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;

/**
 * 物料删除事件监听器
 */
@Component("stkMaterielListener")
public class MaterielListener {

    @Resource
    private StkInventoryDao stkInventoryDao;

    @EventListener(ItemDeleteCheckEvent.class)
    public void materielDeleteCheck(ItemDeleteCheckEvent event) {
        Long id = event.getId();

        Long count = stkInventoryDao.selectCount(new LambdaQueryWrapper<StkInventoryEntity>()
                .eq(StkInventoryEntity::getMaterielId, id));
        if (count > 0) {
            throw new BusinessException("该物料正在被库存使用中，无法删除");
        }
    }

}
