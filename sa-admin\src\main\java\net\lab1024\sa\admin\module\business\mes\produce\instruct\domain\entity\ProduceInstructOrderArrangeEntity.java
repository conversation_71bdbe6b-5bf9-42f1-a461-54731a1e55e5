package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 指令单安排信息 实体类
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:26:34
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_produce_instruct_order_arrange")
public class ProduceInstructOrderArrangeEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 指令单id
     */
    private Long orderId;

    /**
     * 序号
     */
    private Integer serialNumber;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 负责人id
     */
    private Long headId;

    /**
     * 负责人名称
     */
    private String headName;

    /**
     * 计划开始时间
     */
    private LocalDate planBeginTime;

    /**
     * 计划结束时间
     */
    private LocalDate planEndTime;

    /**
     * 实际开始时间
     */
    private LocalDateTime realBeginTime;

    /**
     * 实际结束时间
     */
    private LocalDateTime realEndTime;

    /**
     * 完成人id
     */
    private Long finishId;

    /**
     * 完成人名称
     */
    private String finishName;

    /**
     * 末道节点;0否 1是
     */
    private Boolean endFlag;

    /**
     * 完成标识;0未完成，1完成
     */
    private Boolean finishFlag;

}
