package net.lab1024.sa.admin.module.business.mes.factory.manager;

import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.ProduceTeamEntity;
import net.lab1024.sa.admin.module.business.mes.factory.dao.ProduceTeamDao;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 生产小组  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:32:46
 * @Copyright zscbdic
 */
@Service
public class ProduceTeamManager extends ServiceImpl<ProduceTeamDao, ProduceTeamEntity> {


}
