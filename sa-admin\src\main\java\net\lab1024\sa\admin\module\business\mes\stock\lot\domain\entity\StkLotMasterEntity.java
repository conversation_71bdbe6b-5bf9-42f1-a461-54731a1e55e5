package net.lab1024.sa.admin.module.business.mes.stock.lot.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 批号主档 实体类
 *
 * <AUTHOR>
 * @Date 2025-02-03 19:28:09
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_stk_lot_master")
public class StkLotMasterEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 批次编号
     */
    private String number;

    /**
     * 物料ID
     */
    private Long materielId;

    /**
     * 批号状态;0 未生效 1 生效 2 断号（保留）
     */
    private String lotStatus;

    /**
     * 业务类型;保留
     */
    private String bizType;

    /**
     * 货主类型;保留
     */
    private String ownerType;

    /**
     * 货主id;保留
     */
    private Long ownerId;

    /**
     * 货主名称;保留
     */
    private String ownerName;

}
