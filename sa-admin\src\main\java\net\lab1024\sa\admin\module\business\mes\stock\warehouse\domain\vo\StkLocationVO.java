package net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 货位 列表VO
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:14:20
 * @Copyright zscbdic
 */

@Data
public class StkLocationVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "货架id")
    private Long rackId;

    @Schema(description = "货位编号")
    private String number;

    @Schema(description = "拣货优先级;拣货优先级 保留")
    private Integer pickPriority;

}
