package net.lab1024.sa.admin.module.business.mes.ai.tool.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.entity.LLMToolEntity;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.form.LLMToolQueryForm;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.vo.LLMToolVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 大模型工具表 Dao
 *
 * <AUTHOR>
 * @Date 2025-04-05 14:27:51
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface LLMToolDao extends BaseMapper<LLMToolEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<LLMToolVO> queryPage(Page page, @Param("queryForm") LLMToolQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
