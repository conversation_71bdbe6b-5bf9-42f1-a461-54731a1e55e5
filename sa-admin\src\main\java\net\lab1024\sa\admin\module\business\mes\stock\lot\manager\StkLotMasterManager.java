package net.lab1024.sa.admin.module.business.mes.stock.lot.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.entity.StkInStockDetailEntity;
import net.lab1024.sa.admin.module.business.mes.stock.lot.dao.StkLotMasterDao;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.entity.StkLotMasterEntity;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity.StkOutStockDetailEntity;
import net.lab1024.sa.admin.module.business.mes.stock.setting.dao.StkMaterialStockDao;
import net.lab1024.sa.admin.module.business.mes.stock.setting.domain.entity.StkMaterialStockEntity;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 批号主档  Manager
 *
 * <AUTHOR>
 * @Date 2025-02-03 19:28:09
 * @Copyright zscbdic
 */
@Service
public class StkLotMasterManager extends ServiceImpl<StkLotMasterDao, StkLotMasterEntity> {

    @Resource
    private StkMaterialStockDao stkMaterialStockDao;

    /**
     * 根据物料id和批号查询批号主档
     * 无批号则插入
     *
     * @param lots
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<StkLotMasterEntity> queryLotsWithId(List<StkLotMasterEntity> lots) {
        if (CollUtil.isEmpty(lots)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<StkLotMasterEntity> lq = new LambdaQueryWrapper<>();
        for (StkLotMasterEntity lot : lots) {
            Long materialId = lot.getMaterielId();
            String number = lot.getNumber();
            lq.or(w -> w.eq(StkLotMasterEntity::getMaterielId, materialId)
                    .eq(StkLotMasterEntity::getNumber, number));
        }
        // 查询数据库中的批次信息
        List<StkLotMasterEntity> list = this.list(lq);
        if (list.size() == lots.size()) {
            // 如果查询结果与输入数量一致，直接返回
            return list;
        }


        // 将已记录的批次生成唯一标识
        Set<String> recordedKeys = list.stream()
                .map(lot -> lot.getMaterielId() + "-" + lot.getNumber())
                .collect(Collectors.toSet());
        // 找出未记录的批次
        List<StkLotMasterEntity> lotsToInsert = lots.stream()
                .filter(lot -> !recordedKeys.contains(lot.getMaterielId() + "-" + lot.getNumber()))
                .toList();

        if (!lotsToInsert.isEmpty()) {
            // 批量插入未记录的批次
            this.saveBatch(lotsToInsert);
        }

        return ListUtil.toList(CollUtil.union(list, lotsToInsert));


    }

    /**
     * 检查入库单明细是否需要批次
     *
     * @param warehouseId
     * @param materielIds
     * @param details
     */
    public void checkInNeedLot(Long warehouseId, List<Long> materielIds, List<StkInStockDetailEntity> details) {
        List<StkMaterialStockEntity> materialStocks = stkMaterialStockDao.selectList(new LambdaQueryWrapper<StkMaterialStockEntity>()
                .in(StkMaterialStockEntity::getMaterialId, materielIds));
        if (CollUtil.isEmpty(materialStocks)) {
            details.forEach(d->{
                d.setLotNumber(null);
                d.setLotId(null);
            });
            return;
        }
        Map<Long, StkMaterialStockEntity> materialStockMap = materialStocks.stream()
                .collect(Collectors.toMap(StkMaterialStockEntity::getMaterialId, item -> item));

        for (StkInStockDetailEntity e : details) {
            Long materielId = e.getMaterielId();
            StkMaterialStockEntity stock = materialStockMap.get(materielId);
            if (stock == null) {
                // 没有库存配置，则不处理，而外设置批次为空
                e.setLotNumber(null);
                e.setLotId(null);
                continue;
            }
            if (Boolean.TRUE.equals(stock.getLotManageFlag()) && StrUtil.isBlank(e.getLotNumber())) {
                throw new BusinessException("第" + e.getSeq() + "行请选择或输入批号");
            } else if (Boolean.FALSE.equals(stock.getLotManageFlag())) {
                // 没有批次管理，则设置批号为空
                e.setLotNumber(null);
                e.setLotId(null);
            }
        }
    }

    /**
     * 检查出库单明细是否需要批次
     *
     * @param warehouseId
     * @param materielIds
     * @param details
     */
    public void checkOutNeedLot(Long warehouseId, List<Long> materielIds, List<StkOutStockDetailEntity> details) {
        List<StkMaterialStockEntity> materialStocks = stkMaterialStockDao.selectList(new LambdaQueryWrapper<StkMaterialStockEntity>()
                .in(StkMaterialStockEntity::getMaterialId, materielIds));
        if (CollUtil.isEmpty(materialStocks)) {
            details.forEach(d->{
                d.setLotNumber(null);
                d.setLotId(null);
            });
            return;
        }
        Map<Long, StkMaterialStockEntity> materialStockMap = materialStocks.stream()
                .collect(Collectors.toMap(StkMaterialStockEntity::getMaterialId, item -> item));

        for (StkOutStockDetailEntity e : details) {
            Long materielId = e.getMaterielId();
            StkMaterialStockEntity stock = materialStockMap.get(materielId);
            if (stock == null) {
                // 没有库存配置，则不处理，而外设置批次为空
                e.setLotNumber(null);
                e.setLotId(null);
                continue;
            }
            if (Boolean.TRUE.equals(stock.getLotManageFlag()) && StrUtil.isBlank(e.getLotNumber())) {
                throw new BusinessException("第" + e.getSeq() + "行请选择或输入批号");
            } else if (Boolean.FALSE.equals(stock.getLotManageFlag())) {
                // 没有批次管理，则设置批号为空
                e.setLotNumber(null);
                e.setLotId(null);
            }
        }
    }
}
