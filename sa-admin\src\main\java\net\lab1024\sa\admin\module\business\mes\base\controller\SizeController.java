package net.lab1024.sa.admin.module.business.mes.base.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SizeAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SizeQuery;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SizeQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SizeUpdateForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.SizeVO;
import net.lab1024.sa.admin.module.business.mes.base.service.SizeService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 尺码表 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-05 22:37:21
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class SizeController {

    @Resource
    private SizeService sizeService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/size/queryPage")
    public ResponseDTO<PageResult<SizeVO>> queryPage(@RequestBody @Valid SizeQueryForm queryForm) {
        return ResponseDTO.ok(sizeService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/size/add")
    public ResponseDTO<String> add(@RequestBody @Valid SizeAddForm addForm) {
        return sizeService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/size/update")
    public ResponseDTO<String> update(@RequestBody @Valid SizeUpdateForm updateForm) {
        return sizeService.update(updateForm);
    }

    /**
     * 批量删除
     * @param idList
     * @return
     */
    @Operation(summary = "批量删除 <AUTHOR>
    @PostMapping("/size/batchDelete")
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> idList) {
        return sizeService.batchDelete(idList);
    }

    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/size/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return sizeService.delete(id);
    }

    /**
     * 下拉全部
     * @param query
     * @return
     */
    @Operation(summary = "下拉全部 <AUTHOR>
    @PostMapping("/size/queryAll")
    public ResponseDTO<List<SizeVO>> queryAll(@RequestBody @Valid SizeQuery query) {
        return sizeService.queryAll(query);
    }
}
