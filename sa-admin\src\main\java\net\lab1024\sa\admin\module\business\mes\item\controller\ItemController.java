package net.lab1024.sa.admin.module.business.mes.item.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import net.lab1024.sa.admin.constant.AdminSwaggerTagConst;
import net.lab1024.sa.admin.module.business.mes.common.excel.utils.ExcelUtils;
import net.lab1024.sa.admin.module.business.mes.item.constant.ItemAttributeEnum;
import net.lab1024.sa.admin.module.business.mes.item.constant.ItemCategoryEnum;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemAddForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemQuery;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemQueryForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemUpdateForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.vo.ItemVO;
import net.lab1024.sa.admin.module.business.mes.item.service.ItemService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 物料 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-02 08:33:07
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class ItemController {

    @Resource
    private ItemService itemService;

    @Resource
    private SerialNumberService serialNumberService;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/item/queryPage")
    public ResponseDTO<PageResult<ItemVO>> queryPage(@RequestBody @Valid ItemQueryForm queryForm) {
        if (StrUtil.isEmpty(queryForm.getAttribute()) && CollUtil.isEmpty(queryForm.getAttributeList())) {
            queryForm.setAttribute(ItemAttributeEnum.OTHER.getValue());
            queryForm.setAttributeList(CollUtil.newArrayList(ItemAttributeEnum.OTHER.getValue()));
        }

        return ResponseDTO.ok(itemService.queryPage(queryForm));
    }

    /**
     * 全查询
     *
     * @return
     */
    @Operation(summary = "全查询 <AUTHOR>
    @PostMapping("/item/queryList")
    public ResponseDTO<List<ItemVO>> queryList(@RequestBody @Valid ItemQuery query) {
        return ResponseDTO.ok(itemService.queryList(query));
    }

    /**
     * 添加
     *
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/item/add")
    public ResponseDTO<String> add(@RequestBody @Valid ItemAddForm addForm) {
        addForm.setCategory(ItemCategoryEnum.FINISHED_PRODUCT.getValue());
        addForm.setAttribute(ItemAttributeEnum.OTHER.getValue());
        if (StrUtil.isBlank(addForm.getSkuNumber())) {
            String number = serialNumberService.generate(SerialNumberIdEnum.ITEM);
            addForm.setNumber(number);
            addForm.setSkuNumber(number);
        }
        return itemService.add(addForm);
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/item/update")
    public ResponseDTO<String> update(@RequestBody @Valid ItemUpdateForm updateForm) {
        updateForm.setCategory(ItemCategoryEnum.FINISHED_PRODUCT.getValue());
        updateForm.setAttribute(ItemAttributeEnum.OTHER.getValue());
        updateForm.setSkuNumber(updateForm.getNumber());
        return itemService.update(updateForm);
    }


    /**
     * 单个删除
     *
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/item/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return itemService.delete(id);
    }

    /**
     * 详情
     * @param id
     * @return
     */
    @Operation(summary = "详情 <AUTHOR>
    @GetMapping("/item/byId/{id}")
    public ResponseDTO<ItemVO> getById(@PathVariable Long id) {
        return ResponseDTO.ok(itemService.getById(id));
    }

    /**
     * 下载基础物料导入模板
     *
     * @param response
     * @throws IOException
     */
    @Operation(summary = "下载基础物料导入模板")
    @GetMapping("/item/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        ExcelUtils.excelTemplateExport(response, "file-template/excel/item/import_item_template.xlsx", "导入辅料模板.xlsx");
    }

    /**
     * 导入
     * @param file 文件名称
     * @return
     */
    @Operation(summary = "导入")
    @PostMapping("/item/import")
    public ResponseDTO<String> importExcel(@RequestParam("file") MultipartFile file) {
        return itemService.importExcel(file);
    }

}
