package net.lab1024.sa.admin.module.business.mes.report.stock.inout.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.constant.ProduceInstructOrderPrioityEnum;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.constant.ProduceInstructOrderProduceStatusEnum;
import net.lab1024.sa.base.common.domain.PageParam;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;

import java.time.LocalDate;

@Data
public class ProInAndOutStockReportQueryForm extends PageParam {

    /**
     * 生产指令单号
     */
    @Schema(description = "生产指令单号")
    private String produceInstructOrderNumber;

//    @Schema(description = "生产指令单下发时间")
//    private String produceIssuedTimeBegin;
//
//    @Schema(description = "生产指令单下发时间")
//    private String produceIssuedTimeEnd;

    /**
     * 交货日期 开始
     */
    @Schema(description = "交货日期")
    private LocalDate produceDeliverTimeBegin;

    /**
     * 交货日期 结束
     */
    @Schema(description = "交货日期")
    private LocalDate produceDeliverTimeEnd;

    /**
     * 生产业务状态
     */
    @Schema(description = "生产业务状态;0计划，1下达，2开工，3完工")
    @CheckEnum(message = "生产业务状态错误值非法", value = ProduceInstructOrderProduceStatusEnum.class, required = false)
    private String produceStatus;

    /**
     * 优先级
     */
    @Schema(description = "优先级;0一般,1紧急,2非常紧急")
    @CheckEnum(message = "优先级错误值非法", value = ProduceInstructOrderPrioityEnum.class, required = false)
    private String producePriority;

    /*
     *物料spu编码
     */
    @Schema(description = "物料spu编码")
    private String materialSpuNumber;

    /**
     * 物料sku编码
     */
    @Schema(description = "物料sku编码")
    private String materialSkuNumber;

    /**
     * 物料名称
     */
    @Schema(description = "物料名称")
    private String materialName;


}
