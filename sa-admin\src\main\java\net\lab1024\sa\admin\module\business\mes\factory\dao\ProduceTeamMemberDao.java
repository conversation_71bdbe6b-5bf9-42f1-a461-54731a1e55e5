package net.lab1024.sa.admin.module.business.mes.factory.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.ProduceTeamMemberEntity;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.ProduceTeamMemberQueryForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.vo.ProduceTeamMemberVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 生产小组成员 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:33:31
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface ProduceTeamMemberDao extends BaseMapper<ProduceTeamMemberEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<ProduceTeamMemberVO> queryPage(Page page, @Param("queryForm") ProduceTeamMemberQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
