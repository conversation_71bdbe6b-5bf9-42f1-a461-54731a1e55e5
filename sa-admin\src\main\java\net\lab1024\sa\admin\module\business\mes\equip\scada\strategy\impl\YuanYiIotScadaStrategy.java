package net.lab1024.sa.admin.module.business.mes.equip.scada.strategy.impl;

import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.constant.IotPlatformEnum;
import net.lab1024.sa.admin.module.business.mes.equip.scada.constant.EquipScadaOnlineEnum;
import net.lab1024.sa.admin.module.business.mes.equip.scada.domain.dto.EquipScadaDataDTO;
import net.lab1024.sa.admin.module.business.mes.equip.scada.domain.dto.EquipScadaPropertyDTO;
import net.lab1024.sa.admin.module.business.mes.equip.scada.strategy.ScadaDataStrategy;
import net.lab1024.sa.admin.module.business.mes.equip.scada.strategy.ScadaDataStrategyFactory;
import net.lab1024.sa.admin.module.business.mes.system.third.yuanyi.adapter.YuanYiApiAdapter;
import net.lab1024.sa.admin.module.business.mes.system.third.yuanyi.constant.FieldValueConstant;
import net.lab1024.sa.admin.module.business.mes.system.third.yuanyi.domain.dto.DeviceDetailDTO;
import net.lab1024.sa.admin.module.business.mes.system.third.yuanyi.domain.dto.DevicePropertyDetailDTO;
import net.lab1024.sa.admin.module.business.mes.system.third.yuanyi.domain.form.YuanYiDeviceQuery;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class YuanYiIotScadaStrategy extends ScadaDataStrategy {

    @Resource
    private ScadaDataStrategyFactory scadaDataStrategyFactory;

    @Resource
    private YuanYiApiAdapter yuanYiApiAdapter;

    @Override
    protected List<EquipScadaDataDTO> requestScadaData(List<EquipScadaDataDTO> queryDTO) {
        List<EquipScadaDataDTO> list = queryDTO.stream().map(q -> {
            String scadaEquipmentCode = q.getScadaEquipmentCode();
            String scadaProductCode = q.getScadaProductCode();
            // 查询设备详情
            YuanYiDeviceQuery yiDeviceQuery = new YuanYiDeviceQuery();
            yiDeviceQuery.setEquipmentCode(scadaEquipmentCode);
            yiDeviceQuery.setProductCode(scadaProductCode);
            DevicePropertyDetailDTO resultDto = yuanYiApiAdapter.queryDevicePropertyDetail(yiDeviceQuery);
            DeviceDetailDTO resultDeviceInfoDto = yuanYiApiAdapter.queryDeviceDetail(yiDeviceQuery);
            // 封装返回值
            EquipScadaDataDTO dto = new EquipScadaDataDTO();
            dto.setScadaEquipmentId(q.getScadaEquipmentId());
            dto.setScadaProductCode(scadaProductCode);
            dto.setScadaEquipmentCode(scadaEquipmentCode);
            dto.setLastScadaEquipmentUpTime(resultDeviceInfoDto.getLastOnlineTime());
            dto.setLastScadaEquipmentDownTime(resultDeviceInfoDto.getLastOnlineTime());

            FieldValueConstant.DeviceStatus deviceStatus = FieldValueConstant.DeviceStatus.getByValue(resultDto.getDeviceStatus());
            FieldValueConstant.OnlineStatus onlineStatus = FieldValueConstant.OnlineStatus.getByValue(resultDto.getOnlineStatus());
            // 设备状态翻译
            dto.setEquipmentScadaOnlineStatus(Optional
                    .ofNullable(onlineStatus)
                    .map(FieldValueConstant.OnlineStatus::getDesc)
                    .orElse(FieldValueConstant.OnlineStatus.UNKNOWN.getDesc()));
            // 设备运行状态翻译
            dto.setEquipmentScadaRunStatus(Optional
                    .ofNullable(deviceStatus)
                    .map(FieldValueConstant.DeviceStatus::getDesc)
                    .orElse(FieldValueConstant.DeviceStatus.FAILURE.getDesc()));

            //设置系统内在线状态
            if (onlineStatus == null) {
                dto.setEquipmentOnlineStatus(EquipScadaOnlineEnum.UNKNOWN.getValue());
            } else if (FieldValueConstant.OnlineStatus.ONLINE.equals(onlineStatus)) {
                dto.setEquipmentOnlineStatus(EquipScadaOnlineEnum.ONLINE.getValue());
            } else if (FieldValueConstant.OnlineStatus.OFFLINE.equals(onlineStatus)) {
                dto.setEquipmentOnlineStatus(EquipScadaOnlineEnum.OFFLINE.getValue());
            } else {
                dto.setEquipmentOnlineStatus(EquipScadaOnlineEnum.UNKNOWN.getValue());
            }

            dto.setLastScadaEquipmentUpTime(resultDto.getUpTime());
            dto.setLastScadaEquipmentDownTime(resultDto.getDownTime());
            dto.setLastScadaDataUpdateTime(resultDto.getLastDataTime());
            return dto;
        }).collect(Collectors.toList());
        return list;
    }

    /**
     * 查询设备属性
     *
     * @param queryParam
     * @return
     */
    @Override
    protected EquipScadaPropertyDTO requestScadaProperty(EquipScadaPropertyDTO queryParam) {
        String scadaProductCode = queryParam.getScadaProductCode();
        String scadaEquipmentCode = queryParam.getScadaEquipmentCode();

        YuanYiDeviceQuery yuanYiDeviceQuery = new YuanYiDeviceQuery();
        yuanYiDeviceQuery.setProductCode(scadaProductCode);
        yuanYiDeviceQuery.setEquipmentCode(scadaEquipmentCode);
        DevicePropertyDetailDTO devicePropertyDetailDTO = yuanYiApiAdapter.queryDevicePropertyDetail(yuanYiDeviceQuery);

        EquipScadaPropertyDTO dto = new EquipScadaPropertyDTO();
        dto.setScadaEquipmentId(queryParam.getScadaEquipmentId());
        dto.setScadaProductCode(scadaProductCode);
        dto.setScadaEquipmentCode(scadaEquipmentCode);

        if (devicePropertyDetailDTO == null) {
            return dto;
        }

        JSONObject propertyMap = devicePropertyDetailDTO.getPropertyMap();
        if (propertyMap == null) {
            dto.setProperties(Collections.emptyList());
            return dto;
        }

        // 封装属性
        Date now = new Date();
        List<EquipScadaPropertyDTO.Property> properties = propertyMap.entrySet().stream().map(objectEntry -> {
            String fieldKey = objectEntry.getKey();
            JSONObject value = (JSONObject) objectEntry.getValue();

            EquipScadaPropertyDTO.Property property = new EquipScadaPropertyDTO.Property();
            property.setFieldKey(fieldKey);
            property.setFieldName(value.getString("name"));
            property.setFieldValueStr(value.getString("curVal"));
            property.setUnitName(value.getString("specsUnitname"));
            property.setDataType(value.getString("dtp"));
            property.setRequestTime(now);
            return property;
        }).collect(Collectors.toList());

        dto.setProperties(properties);

        return dto;
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        scadaDataStrategyFactory.register(IotPlatformEnum.YUAN_YI_IOT, this);
    }


}
