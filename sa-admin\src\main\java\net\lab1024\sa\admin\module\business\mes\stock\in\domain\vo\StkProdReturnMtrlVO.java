package net.lab1024.sa.admin.module.business.mes.stock.in.domain.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 生产退料
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StkProdReturnMtrlVO extends StkInStockVO{

    private List<DetailVO> details;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DetailVO extends StkInStockDetailVO{

    }
}
