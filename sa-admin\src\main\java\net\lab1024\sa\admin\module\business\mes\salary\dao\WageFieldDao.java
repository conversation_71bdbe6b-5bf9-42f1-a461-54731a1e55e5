package net.lab1024.sa.admin.module.business.mes.salary.dao;

import java.util.List;

import net.lab1024.sa.admin.module.business.mes.salary.domain.dto.WageFieldValueDto;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.WageFieldEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.WageFieldQueryForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.vo.WageFieldVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 工资字段 Dao
 *
 * <AUTHOR>
 * @Date 2024-11-25 20:20:26
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface WageFieldDao extends BaseMapper<WageFieldEntity> {

    /**
     * 查询工资字段值
     * @param employeeIds
     * @return
     */
    List<WageFieldValueDto> queryWageFieldValue(@Param("employeeIds") List<Long> employeeIds);

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<WageFieldVO> queryPage(Page page, @Param("queryForm") WageFieldQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
