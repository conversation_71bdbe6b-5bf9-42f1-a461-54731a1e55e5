package net.lab1024.sa.admin.module.business.mes.salary.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.SettlementRecordDetailEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 薪酬结算记录详情 Dao
 *
 * <AUTHOR>
 * @Date 2024-11-20 20:49:55
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface SettlementRecordDetailDao extends BaseMapper<SettlementRecordDetailEntity> {



    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

    void batchInsert(@Param("list") List<SettlementRecordDetailEntity> details);
}
