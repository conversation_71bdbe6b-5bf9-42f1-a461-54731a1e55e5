package net.lab1024.sa.admin.module.business.mes.item.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemEntity;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemQueryForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.vo.ItemVO;

import java.util.List;

/**
 * 主物料表 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-02 08:33:07
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface ItemDao extends BaseMapper<ItemEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<ItemVO> queryPage(Page page, @Param("queryForm") ItemQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 根据id查询带单位
     * @param id
     * @return
     */
    List<ItemVO> queryByIdsWithUnit(@Param("ids")List<Long> id);
}
