package net.lab1024.sa.admin.module.business.mes.process.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * 工序信息 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-13 15:19:57
 * @Copyright zscbdic
 */

@Data
public class ProcessVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    /**
     * 工序编号
     */
    @Schema(description = "工序编号")
    private String processNumber;

    @Schema(description = "工序名称")
    private String name;

    @Schema(description = "部位")
    private String position;

    @Schema(description = "工序类型")
    private String processType;

    @Schema(description = "标准工时;单位")
    private Integer standardTime;

    @Schema(description = "工价一")
    private BigDecimal unitPrice1;

    @Schema(description = "工价二")
    private BigDecimal unitPrice2;

    @Schema(description = "工价三")
    private BigDecimal unitPrice3;

    @Schema(description = "工价四")
    private BigDecimal unitPrice4;

    @Schema(description = "工价五")
    private BigDecimal unitPrice5;

    @Schema(description = "工价六")
    private BigDecimal unitPrice6;

    @Schema(description = "工序控制;0自制 1委外 2不限")
    private String processControl;

    @Schema(description = "sopId;保留")
    private Long sopId;

}
