package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 裁片货架 新建表单
 *
 * <AUTHOR>
 * @Date 2024-10-06 16:49:27
 * @Copyright zscbdic
 */

@Data
public class PartStationRackAddForm {

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "所属仓库id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "所属仓库id 不能为空")
    private Long warehouseId;

    @Schema(description = "货架编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "货架编码 不能为空")
    private String rackCode;

    @Schema(description = "位置")
    private String location;

}
