package net.lab1024.sa.admin.module.business.mes.stock.setting.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 物料库存属性作用范围枚举
 */
@Getter
@AllArgsConstructor
public enum StockScopeEnum implements BaseEnum {

    ALL("ALL", "所有仓库"),
    ONE("ONE", "单一仓库");

    private String value;

    private String desc;

    public static StockScopeEnum getByValue(String value) {
        for (StockScopeEnum item : values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }
}
