package net.lab1024.sa.admin.module.business.mes.salary.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.salary.dao.*;
import net.lab1024.sa.admin.module.business.mes.salary.domain.bo.PayoffBo;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.*;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PayOffManager {

    @Resource
    private WageFieldDao wageFieldDao;

    @Resource
    private WageFieldValueDao wageFieldValueDao;

    @Resource
    private SettlementRecordDao settlementRecordDao;

    @Resource
    private PayoffRecordDao payoffRecordDao;

    @Resource
    private PayoffRecordDetailDao payoffRecordDetailDao;

    @Transactional(rollbackFor = Exception.class)
    public void checkAndUpdateWageFieldValue(List<Long> employeeIds) {
        if (CollUtil.isEmpty(employeeIds)) {
            return;
        }

        // 查询所有工资项
        List<WageFieldEntity> fields = wageFieldDao.selectList(null);
        if (CollUtil.isEmpty(fields)) {
            return;
        }
        List<Long> fieldIds = fields.stream()
                .map(WageFieldEntity::getId)
                .collect(Collectors.toList());
        List<WageFieldValueEntity> values = wageFieldValueDao.selectList(new LambdaQueryWrapper<WageFieldValueEntity>()
                .in(WageFieldValueEntity::getFieldId, fieldIds)
                .in(WageFieldValueEntity::getEmployeeId, employeeIds));

        // 没有记录，则新增
        if (CollUtil.isEmpty(values)) {
            List<WageFieldValueEntity> addValues = new ArrayList<>(employeeIds.size() * fields.size());
            for (Long eId : employeeIds) {
                for (WageFieldEntity field : fields) {
                    WageFieldValueEntity value = new WageFieldValueEntity(field.getId(), eId, BigDecimal.ZERO);
                    addValues.add(value);
                }
            }
            wageFieldValueDao.batchInsert(addValues);
            return;
        }

        // 有记录，则比较补充
        List<WageFieldValueEntity> addValues = new ArrayList<>();
        HashSet<Long> fieldIdSet = Sets.newHashSet(fieldIds);
        Map<Long, List<WageFieldValueEntity>> valueMap = values.stream().collect(Collectors.groupingBy(WageFieldValueEntity::getEmployeeId));
        for (Long eId : employeeIds) {
            List<WageFieldValueEntity> eValues = valueMap.get(eId);

            if(CollUtil.isEmpty(eValues)){
                //没有该用户所有工资项
                for (WageFieldEntity field : fields) {
                    WageFieldValueEntity value = new WageFieldValueEntity(field.getId(), eId, BigDecimal.ZERO);
                    addValues.add(value);
                }
                continue;
            }

            if (CollUtil.isNotEmpty(eValues) && eValues.size() == fields.size()) {
                // 该用户工资项已经存在并且完整，跳过
                continue;
            }


            HashSet<Long> eFieldIdSet = eValues.stream()
                    .map(WageFieldValueEntity::getFieldId)
                    .collect(Collectors.toCollection(Sets::newHashSet));
            // 计算出该用户没有的工资项
            Sets.SetView<Long> difference = Sets.difference(fieldIdSet, eFieldIdSet);
            if (CollUtil.isEmpty(difference)) {
                // 该用户工资项全部存在，跳过
                continue;
            }
            for (Long fieldId : difference) {
                WageFieldValueEntity value = new WageFieldValueEntity(fieldId, eId, BigDecimal.ZERO);
                addValues.add(value);
            }
        }
        if (CollUtil.isNotEmpty(addValues)) {
            wageFieldValueDao.batchInsert(addValues);
        }
    }

    /**
     * 保存发放记录
     *
     * @param payoffBo
     */
    @Transactional(rollbackFor = Exception.class)
    public void savePayoff(PayoffBo payoffBo) {
        List<PayoffBo.RecordBo> payoffRecords = payoffBo.getPayoffRecords();
        List<Long> settlementRecordIds = payoffBo.getSettlementRecordIds();

        //设置结算记录为发放
        settlementRecordDao.update(null, new LambdaUpdateWrapper<SettlementRecordEntity>()
                .in(SettlementRecordEntity::getId, settlementRecordIds)
                .set(SettlementRecordEntity::getPayoffFlag, true));
        //保存发放记录
        payoffRecords.forEach(recordBo -> {
            PayoffRecordEntity record = recordBo.getRecord();
            payoffRecordDao.insert(record);
            Long mainId = record.getId();
            List<PayoffRecordDetailEntity> details = recordBo.getDetails();
            details.forEach(detail -> {
                detail.setMainId(mainId);
            });
            payoffRecordDetailDao.batchInsert(details);

        });
        //        System.out.println("1");
    }

    /**
     * 取消发放
     *
     * @param ids       发放记录ids
     * @param settleIds 结算记录ids
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelPayoff(ValidateList<Long> ids, List<Long> settleIds) {
        payoffRecordDao.batchUpdateDeleted(ids, true);
        payoffRecordDetailDao.delete(new LambdaQueryWrapper<PayoffRecordDetailEntity>()
                .in(PayoffRecordDetailEntity::getMainId, ids));
        settlementRecordDao.update(null, new LambdaUpdateWrapper<SettlementRecordEntity>()
                .in(SettlementRecordEntity::getId, settleIds)
                .set(SettlementRecordEntity::getPayoffFlag, false));
    }
}
