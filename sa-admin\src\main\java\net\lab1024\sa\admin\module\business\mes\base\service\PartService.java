package net.lab1024.sa.admin.module.business.mes.base.service;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.base.dao.PartDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.PartEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.PartAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.PartQuery;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.PartQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.PartUpdateForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.PartVO;
import net.lab1024.sa.admin.module.business.mes.base.manager.PartManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 部位表 Service
 *
 * <AUTHOR>
 * @Date 2025-06-23 19:56:55
 * @Copyright zscbdic
 */

@Service
public class PartService {

    @Resource
    private PartDao partDao;

    @Resource
    private PartManager partManager;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<PartVO> queryPage(PartQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<PartVO> list = partDao.queryPage(page, queryForm);
        return SmartPageUtil.convert2PageResult(page, list);
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(PartAddForm addForm) {
        partManager.addCheck(addForm);
        PartEntity partEntity = SmartBeanUtil.copy(addForm, PartEntity.class);
        partDao.insert(partEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(PartUpdateForm updateForm) {
        partManager.updateCheck(updateForm);
        PartEntity partEntity = SmartBeanUtil.copy(updateForm, PartEntity.class);
        partDao.updateById(partEntity);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    public ResponseDTO<String> batchDelete(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return ResponseDTO.ok();
        }

        partDao.batchUpdateDeleted(idList, true);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id){
            return ResponseDTO.ok();
        }

        partDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }

    /**
     *
     * @param query
     * @return
     */
    public List<PartVO> queryList(PartQuery query) {
        List<PartEntity> list = partManager.lambdaQuery()
                .like(CharSequenceUtil.isNotBlank(query.getQueryKey()), PartEntity::getName, query.getQueryKey())
                .list();
        return SmartBeanUtil.copyList(list, PartVO.class);
    }
}
