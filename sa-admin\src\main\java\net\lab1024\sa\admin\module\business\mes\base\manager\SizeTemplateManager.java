package net.lab1024.sa.admin.module.business.mes.base.manager;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.base.dao.SizeDao;
import net.lab1024.sa.admin.module.business.mes.base.dao.SizeTemplateDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.SizeEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.SizeTemplateEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.SizeInfoVO;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 尺寸模板表  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-05 15:45:35
 * @Copyright zscbdic
 */
@Service
public class SizeTemplateManager extends ServiceImpl<SizeTemplateDao, SizeTemplateEntity> {

    @Resource
    private SizeDao sizeDao;

    @Resource
    private SizeManager sizeManager;
    @Resource
    private SizeTemplateDao sizeTemplateDao;

    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(Long id) {
        LambdaQueryWrapper<SizeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SizeEntity::getTemplateId, id);
        sizeDao.delete(queryWrapper);
        sizeTemplateDao.deleteById(id);

  }

    public ResponseDTO<List<SizeInfoVO>> iterList(List<SizeTemplateEntity> sizeTList) {
        List<SizeInfoVO> voList = new ArrayList<>();

        sizeTList.forEach(e-> {
            //获得该模板下的所有尺码对象
            List<SizeEntity> list = sizeManager.query().eq("template_id", e.getId()).list();
            //对尺码对象单独提取尺码名称
            List<String> messages = list.stream().map(SizeEntity::getSizeMessage).toList();
            //拼接
            String strMessage = String.join(",", messages);

            SizeInfoVO sizeTemplateVO = BeanUtil.copyProperties(e, SizeInfoVO.class);
            if(strMessage.isEmpty()){
                strMessage = "暂无添加尺寸信息 ~";
            }
            sizeTemplateVO.setSizeInfo(strMessage);
            voList.add(sizeTemplateVO);
        });
        return ResponseDTO.ok(voList);
    }



}
