package net.lab1024.sa.admin.module.business.mes.base.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.SeasonEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SeasonQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.SeasonVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 季度表 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-05 15:45:34
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface SeasonDao extends BaseMapper<SeasonEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<SeasonVO> queryPage(Page page, @Param("queryForm") SeasonQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 某个类型的所有
     */
    List<SeasonEntity> queryByKey(@Param("queryKey") String queryKey, @Param("deletedFlag")Boolean deletedFlag);

    List<SeasonEntity> queryByParentId(@Param("parentIds") List<Long> parentIds,@Param("deletedFlag") Boolean deletedFlag);
}
