package net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form;

import net.lab1024.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 裁片周转箱 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-03-14 11:36:51
 * @Copyright zscbdic
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class PartStationTurnBoxQueryForm extends PageParam {

    @Schema(description = "关键字查询")
    private String queryKey;

}
