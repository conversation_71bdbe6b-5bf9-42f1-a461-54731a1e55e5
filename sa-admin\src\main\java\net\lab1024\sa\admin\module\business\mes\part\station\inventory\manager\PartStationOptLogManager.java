package net.lab1024.sa.admin.module.business.mes.part.station.inventory.manager;

import cn.hutool.core.bean.BeanUtil;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.constant.PartStationInventoryOptTypeEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationBinDao;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.dao.PartStationOptLogDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationRackDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationWarehouseDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationBinEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.entity.PartStationOptLogEntity;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationRackEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationWarehouseEntity;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.dao.FeTicketDao;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.domain.entity.FeTicketEntity;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 裁片驿站操作日志  Manager
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:47:06
 * @Copyright zscbdic
 */
@Service
public class PartStationOptLogManager extends ServiceImpl<PartStationOptLogDao, PartStationOptLogEntity> {

    @Resource
    private FeTicketDao feTicketDao;

    @Resource
    private PartStationWarehouseDao warehouseDao;

    @Resource
    private PartStationRackDao rackDao;

    @Resource
    private PartStationBinDao binDao;


    public void insertLog(Long feTicketId, Long binId, String userName, Long userId, String optDesc, PartStationInventoryOptTypeEnum typeEnum) {
        FeTicketEntity ticket = feTicketDao.selectById(feTicketId);
        if (ticket == null) {
            return;
        }
        PartStationBinEntity bin = binDao.selectById(binId);
        if (bin == null) {
            return;
        }
        PartStationRackEntity rack = rackDao.selectById(bin.getRackId());
        if (rack == null) {
            return;
        }
        PartStationWarehouseEntity warehouse = warehouseDao.selectById(rack.getWarehouseId());
        if (warehouse == null) {
            return;
        }

        PartStationOptLogEntity entity = new PartStationOptLogEntity();
        entity.setFeTicketId(feTicketId);
        entity.setOptType(typeEnum.getValue());
        entity.setOptDesc(optDesc);
        entity.setOperator(userName);
        entity.setOperatorId(userId);
        entity.setOptTime(LocalDateTime.now());
        entity.setWarehouseId(warehouse.getId());
        entity.setWarehouseName(warehouse.getName());
        entity.setRackId(rack.getId());
        entity.setRackCode(rack.getRackCode());
        entity.setBinId(bin.getId());
        entity.setBinCode(bin.getBinCode());
        BeanUtil.copyProperties(ticket, entity, "id");

        this.save(entity);
    }
}
