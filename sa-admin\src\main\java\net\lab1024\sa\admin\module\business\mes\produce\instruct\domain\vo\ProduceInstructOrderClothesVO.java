package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 指令单成衣信息 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:25:32
 * @Copyright zscbdic
 */

@Data
public class ProduceInstructOrderClothesVO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "指令单id")
    private Long orderId;

    @Schema(description = "指令单编号")
    private String orderNumber;

    @Schema(description = "指令单生产状态")
    private String orderProduceStatus;

    @Schema(description = "物料id")
    private Long itemId;

    @Schema(description = "物料名称")
    private String itemName;

    @Schema(description = "物料spu编号")
    private String itemNumber;

    @Schema(description = "物料sku编号")
    private String itemSkuNumber;

    @Schema(description = "款式颜色")
    private String styleColor;

    @Schema(description = "尺码")
    private String size;

    @Schema(description = "单位id")
    private Long unitId;

    @Schema(description = "单位名称")
    private String unitName;

    @Schema(description = "数量")
    private Integer num;

}
