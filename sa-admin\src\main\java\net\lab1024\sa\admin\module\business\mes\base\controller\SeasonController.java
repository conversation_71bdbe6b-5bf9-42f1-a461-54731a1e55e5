package net.lab1024.sa.admin.module.business.mes.base.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SeasonAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SeasonQueryTreeForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SeasonUpdateForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.SeasonTreeVO;
import net.lab1024.sa.admin.module.business.mes.base.service.SeasonService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 季度表 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-05 15:45:34
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class SeasonController {

    @Resource
    private SeasonService seasonService;


    /**
     * 获取层级树
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询层级树 <AUTHOR>
    @PostMapping("/season/queryTree")
    public ResponseDTO<List<SeasonTreeVO>> queryTree(@RequestBody @Valid SeasonQueryTreeForm queryForm) {
        return seasonService.queryTree(queryForm);
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/season/add")
    public ResponseDTO<String> add(@RequestBody @Valid SeasonAddForm addForm) {
        return seasonService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/season/update")
    public ResponseDTO<String> update(@RequestBody @Valid SeasonUpdateForm updateForm) {
        return seasonService.update(updateForm);
    }

    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/season/delete/{id}")
    public ResponseDTO<String> batchDelete(@PathVariable Long id) {
        return seasonService.delete(id);
    }


}
