package net.lab1024.sa.admin.module.business.mes.process.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 工序信息 更新表单
 *
 * <AUTHOR>
 * @Date 2024-07-13 15:19:57
 * @Copyright zscbdic
 */

@Data
public class ProcessUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

    /**
     * 工序编号
     */
    @Schema(description = "工序编号")
    @NotNull(message = "工序编号 不能为空")
    private String processNumber;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "工序名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "工序名称 不能为空")
    private String name;

    @Schema(description = "部位")
    private String position;

    @Schema(description = "工序类型")
    private String processType;

    @Schema(description = "标准工时;单位")
    private Integer standardTime;

    @Schema(description = "工价一", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "工价一 不能为空")
    private BigDecimal unitPrice1;

    @Schema(description = "工价二", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "工价二 不能为空")
    private BigDecimal unitPrice2;

    @Schema(description = "工价三", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "工价三 不能为空")
    private BigDecimal unitPrice3;

    @Schema(description = "工价四", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "工价四 不能为空")
    private BigDecimal unitPrice4;

    @Schema(description = "工价五", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "工价五 不能为空")
    private BigDecimal unitPrice5;

    @Schema(description = "工价六", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "工价六 不能为空")
    private BigDecimal unitPrice6;

    @Schema(description = "工序控制;0自制 1委外 2不限", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "工序控制;0自制 1委外 2不限 不能为空")
    private String processControl;

    @Schema(description = "sopId;保留")
    private Long sopId;

}
