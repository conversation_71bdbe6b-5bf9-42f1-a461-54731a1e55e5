package net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 生产安排信息 新建表单
 *
 * <AUTHOR>
 * @Date 2024-07-22 18:44:38
 * @Copyright zscbdic
 */

@Data
public class ProduceArrangeDetailAddForm {

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 序号
     */
    @Schema(description = "序号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "序号 不能为空")
    private Integer serialNumber;

    /**
     * 节点名称
     */
    @Schema(description = "节点名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "节点名称 不能为空")
    private String nodeName;

    /**
     * 负责人id
     */
    @Schema(description = "负责人id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "负责人id 不能为空")
    private Long headId;

    /**
     * 负责人名称
     */
    @Schema(description = "负责人名称")
    private String headName;


    @Schema(description = "末道节点;0否 1是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "末道节点;0否 1是 不能为空")
    private Boolean endFlag;

}
