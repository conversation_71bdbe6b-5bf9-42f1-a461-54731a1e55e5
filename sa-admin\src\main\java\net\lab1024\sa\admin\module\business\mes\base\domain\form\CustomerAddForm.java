package net.lab1024.sa.admin.module.business.mes.base.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 客户表 新建表单
 *
 * <AUTHOR>
 * @Date 2024-07-03 09:43:14
 * @Copyright zscbdic
 */

@Data
public class CustomerAddForm {

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "停用标识;0启用，1停用")
    private Boolean enableFlag;

    @Schema(description = "客户编号")
    private String number;

    @Schema(description = "客户名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "客户名称 不能为空")
    private String name;

    @Schema(description = "公司名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "公司名称 不能为空")
    private String company;

    @Schema(description = "联系人")
    private String contact;

    @Schema(description = "电话")
    private String telephone;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "等级;5星最高，无半星")
    private String level;

}
