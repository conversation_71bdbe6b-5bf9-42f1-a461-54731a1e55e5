package net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.domain.vo.FeTicketVO;

/**
 * 周转箱内容 列表VO
 *
 * <AUTHOR>
 * @Date 2025-03-14 15:03:20
 * @Copyright zscbdic
 */

@Data
public class PartStationTurnBoxInsideVO extends FeTicketVO {


    @Schema(description = "周转箱ID")
    private Long turnBoxId;

//    @Schema(description = "菲票ID")
//    private Long feTicketId;

    @Schema(description = "入箱时间")
    private LocalDateTime inTime;

}
