package net.lab1024.sa.admin.module.business.mes.salary.domain.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class PayoffQueryForm extends PageParam {

    /**
     * 员工id
     */
    private List<Long> employeeIds;

    /**
     * 归属月份 YYYY-MM
     */
    @NotNull(message = "归属月份 不能为空")
    @JsonFormat(pattern = "yyyy-MM")
    private Date belongMonth;

    private Boolean payoffFlag;
}
