package net.lab1024.sa.admin.module.business.mes.produce.instruct.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderItemDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderItemQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderItemVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 指令单用料信息 Service
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:24:54
 * @Copyright zscbdic
 */

@Service
public class ProduceInstructOrderItemService {

    @Resource
    private ProduceInstructOrderItemDao produceInstructOrderItemDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<ProduceInstructOrderItemVO> queryPage(ProduceInstructOrderItemQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ProduceInstructOrderItemVO> list = produceInstructOrderItemDao.queryPage(page, queryForm);
        PageResult<ProduceInstructOrderItemVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }


}
