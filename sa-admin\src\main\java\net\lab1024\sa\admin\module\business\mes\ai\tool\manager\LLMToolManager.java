package net.lab1024.sa.admin.module.business.mes.ai.tool.manager;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.lab1024.sa.admin.module.business.mes.ai.tool.dao.LLMToolDao;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.entity.LLMToolEntity;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.form.LLMToolAddForm;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.form.LLMToolUpdateForm;
import net.lab1024.sa.base.common.exception.BusinessException;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 大模型工具表  Manager
 *
 * <AUTHOR>
 * @Date 2025-04-05 14:27:51
 * @Copyright zscbdic
 */
@Service
public class LLMToolManager extends ServiceImpl<LLMToolDao, LLMToolEntity> {

    /*
     * 获取所有工具
     */
    public Object[] getAllTools() {
        List<LLMToolEntity> list = this.lambdaQuery().list();
        if (list.isEmpty()) {
            return Collections.emptyList().toArray();
        }
        return list.stream().map(item -> {
            try {
                return SpringUtil.getBean(item.getBeanName());
            } catch (NoSuchBeanDefinitionException e) {
                return null;
            }
        }).filter(Objects::nonNull).toArray();
    }


    /**
     * 添加校验
     *
     * @param addForm
     */
    public void addCheck(LLMToolAddForm addForm) {
        String beanName = addForm.getBeanName();
        beanNameCheck(beanName, null);
        beanExistsCheck(beanName);
    }

    /**
     * 更新校验
     *
     * @param updateForm
     */
    public void updateCheck(LLMToolUpdateForm updateForm) {
        String beanName = updateForm.getBeanName();
        beanNameCheck(beanName, updateForm.getId());
        beanExistsCheck(beanName);
    }


    private void beanNameCheck(String beanName, Long excludeId) {
        Long count = this.lambdaQuery()
                .eq(LLMToolEntity::getBeanName, beanName)
                .ne(excludeId != null, LLMToolEntity::getId, excludeId)
                .count();
        if (count > 0) {
            throw new BusinessException("beanName已存在");
        }
    }


    private void beanExistsCheck(String beanName) {
        try {
            SpringUtil.getBean(beanName);
        } catch (NoSuchBeanDefinitionException e) {
            throw new BusinessException("beanName不存在：" + beanName);
        }
    }


}
