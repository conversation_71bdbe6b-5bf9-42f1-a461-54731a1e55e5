package net.lab1024.sa.admin.module.business.mes.process.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.process.domain.entity.ProcessLibraryDetailsEntity;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessLibraryDetailsQueryForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.vo.ProcessLibraryDetailsVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 工序库详情工序 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-13 15:19:57
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface ProcessLibraryDetailsDao extends BaseMapper<ProcessLibraryDetailsEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<ProcessLibraryDetailsVO> queryPage(Page page, @Param("queryForm") ProcessLibraryDetailsQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

}
