package net.lab1024.sa.admin.module.business.mes.produce.instruct.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 指令单来源
 */
@Getter
@AllArgsConstructor
public enum ProduceInstructOrderOriginEnum implements BaseEnum {

    /**
     * 直接下达
     */
    DIRECT("0", "直接下达");


    private final String value;

    private final String desc;
}
