package net.lab1024.sa.admin.module.business.mes.stock.warehouse.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.dao.StkLocationDao;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.dao.StkRackDao;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkLocationEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkRackEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkRackAddForm;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkRackUpdateForm;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;

/**
 * 货架  Manager
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:13:43
 * @Copyright zscbdic
 */
@Service
public class StkRackManager extends ServiceImpl<StkRackDao, StkRackEntity> {

    @Resource
    private StkLocationDao stkLocationDao;

    /**
     * 添加校验
     *
     * @param addForm
     */
    public void addCheck(StkRackAddForm addForm) {
        String number = addForm.getNumber();
        long count = this.count(new LambdaQueryWrapper<StkRackEntity>()
                .eq(StkRackEntity::getNumber, number));
        if (count > 0) {
            throw new BusinessException("货架编号已存在");
        }
    }

    /**
     * 更新校验
     *
     * @param updateForm
     */
    public void updateCheck(StkRackUpdateForm updateForm) {
        String number = updateForm.getNumber();
        long count = this.count(new LambdaQueryWrapper<StkRackEntity>()
                .eq(StkRackEntity::getNumber, number)
                .ne(StkRackEntity::getId, updateForm.getId()));
        if (count > 0) {
            throw new BusinessException("货架编号已存在");
        }
    }

    /**
     * 删除货架
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deletedRack(Long id) {
        // 删除货架
        this.removeById(id);
        // 删除货架下的库位
        stkLocationDao.delete(new LambdaQueryWrapper<StkLocationEntity>()
                .eq(StkLocationEntity::getRackId, id));

    }
}
