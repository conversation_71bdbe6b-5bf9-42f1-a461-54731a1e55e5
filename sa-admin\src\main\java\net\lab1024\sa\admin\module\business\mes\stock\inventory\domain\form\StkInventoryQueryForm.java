package net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.form;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.lab1024.sa.base.common.domain.PageParam;

/**
 * 即时库存 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-01-14 15:20:23
 * @Copyright zscbdic
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class StkInventoryQueryForm extends PageParam{

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 批次号
     */
    private String lotNumber;

    /**
     * 物料spu编码
     */
    private String materialSpuNumber;

    /**
     * 物料sku编码
     */
    private String materialSkuNumber;

    /**
     * 物料名称
     */
    private String materialName;


}
