package net.lab1024.sa.admin.module.business.mes.item.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.json.serializer.FileKeyVoSerializer;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 主物料表 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-02 08:33:07
 * @Copyright zscbdic
 */

@Data
public class ItemVO {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 物料分类id;关联t_item_type
     */
    @Schema(description = "物料分类id;关联t_item_type")
    private Long typeId;

    /**
     * 物料分类名称
     */
    @Schema(description = "物料分类名称")
    private String typeName;

    /**
     * 物料名称
     */
    @Schema(description = "物料名称")
    private String name;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id;关联t_item_supplier")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    private String supplierName;

    /**
     * 规格型号
     */
    @Schema(description = "规格型号")
    private String model;

    /**
     * 物料编号 spu
     */
    @Schema(description = "物料编号")
    private String number;

    /**
     * sku编号
     */
    @Schema(description = "sku编号")
    private String skuNumber;

    /**
     * 单位id;关联t_unit
     */
    @Schema(description = "单位id;关联t_unit")
    private Long unitId;

    /**
     * 单位名称
     */
    @Schema(description = "单位名称")
    private String unitName;

    /**
     * 价格
     */
    @Schema(description = "价格")
    private BigDecimal price;

    /**
     * 图片url
     */
    @Schema(description = "图片url")
    @JsonSerialize(using = FileKeyVoSerializer.class)
    private String imgUrl;

    /**
     * 停用标识;0启用，1停用
     */
    @Schema(description = "停用标识;0启用，1停用")
    private Boolean enableFlag;

    /**
     * 类型;0半成品 1成品
     */
    @Schema(description = "类型;0半成品 1成品")
    private String category;

    /**
     * 属性;0面料，1其他，2成衣
     */
    @Schema(description = "属性;0面料，1其他，2成衣")
    private String attribute;

}
