package net.lab1024.sa.admin.module.business.mes.salary.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SettlementVO {


    private Long employeeId;

    /**
     * 员工名称
     */
    private String actualName;


    /**
     * 手机号码
     */
    private String phone;


    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 未结算工资
     */
    private BigDecimal unSettledSalary;

    /**
     * 未结算条数
     */
    private Integer unSettledCount;

    /**
     * 未结算件数
     */
    private Integer unSettledNum;

    /**
     * 已结算工资
     */
    private BigDecimal settledSalary;

    /**
     * 已结算条数
     */
    private Integer settledCount;

    /**
     * 已结算件数
     */
    private Integer settledNum;
}
