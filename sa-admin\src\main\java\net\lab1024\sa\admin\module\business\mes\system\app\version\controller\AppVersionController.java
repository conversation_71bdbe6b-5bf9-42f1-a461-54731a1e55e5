package net.lab1024.sa.admin.module.business.mes.system.app.version.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.system.app.version.constant.VersionStatusEnum;
import net.lab1024.sa.admin.module.business.mes.system.app.version.domain.form.*;
import net.lab1024.sa.admin.module.business.mes.system.app.version.domain.vo.AppVersionVO;
import net.lab1024.sa.admin.module.business.mes.system.app.version.service.AppVersionService;
import net.lab1024.sa.base.common.annoation.NoNeedLogin;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * app版本管理 Controller
 *
 * <AUTHOR>
 * @Date 2025-02-25 14:44:40
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class AppVersionController {

    @Resource
    private AppVersionService appVersionService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/appVersion/queryPage")
    public ResponseDTO<PageResult<AppVersionVO>> queryPage(@RequestBody @Valid AppVersionQueryForm queryForm) {
        return ResponseDTO.ok(appVersionService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/appVersion/add")
    public ResponseDTO<String> add(@RequestBody @Valid AppVersionAddForm addForm) {
        addForm.setStatus(VersionStatusEnum.UN_PUBLISH.getValue());
        return appVersionService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/appVersion/update")
    public ResponseDTO<String> update(@RequestBody @Valid AppVersionUpdateForm updateForm) {

        return appVersionService.update(updateForm);
    }

    /**
     * 批量删除
     * @param idList
     * @return
     */
    @Operation(summary = "批量删除 <AUTHOR>
    @PostMapping("/appVersion/batchDelete")
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> idList) {
        return appVersionService.batchDelete(idList);
    }

    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/appVersion/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return appVersionService.delete(id);
    }

    /**
     * 修改状态
     */
    @Operation(summary = "修改状态 <AUTHOR>
    @PostMapping("/appVersion/updateStatus")
    public ResponseDTO<String> updateStatus(@RequestBody @Valid AppVersionUpadteStatusForm updateForm) {
        return appVersionService.updateStatus(updateForm);
    }

    /**
     * 获取最新版本
     * @param query
     * @return
     */
    @NoNeedLogin
    @Operation(summary = "获取最新版本 <AUTHOR>
    @PostMapping("/appVersion/getLatestVersion")
    public ResponseDTO<AppVersionVO> getLatestVersion(@RequestBody @Valid AppVersionQuery query) {
        return appVersionService.getLatestVersion(query);
    }
}
