package net.lab1024.sa.admin.module.business.mes.ai.setting.domain.form;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AssistantConfigUpadteForm {

    @NotNull(message = "配置ID不能为空")
    private Long configId;

    /**
     * 智能体名称
     */
    @NotBlank(message = "智能体名称不能为空")
    private String agentName;

    /**
     * 智能体头像
     */

    private String agentAvatar;

    /**
     * 系统提示
     */
    @NotBlank(message = "系统提示不能为空")
    private String systemPrompt;


    /**
     * 用户推荐提示
     */

    @NotNull(message = "用户推荐提示不能为空")
    private Boolean userRecommendPromptFlag;

    /**
     * 用户推荐提示模型ID
     */
    private Long userRecommendPromptModelId;

    /**
     * 用户推荐系统提示词
     */
    private String userRecommendSystemPrompt;
}
