package net.lab1024.sa.admin.module.business.mes.salary.domain.form;

import net.lab1024.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 薪酬结算记录 分页查询表单
 *
 * <AUTHOR>
 * @Date 2024-11-20 20:48:59
 * @Copyright zscbdic
 */

@Data
public class SettlementRecordQueryForm extends PageParam{

    @Schema(description = "员工id")
    private Long employeeId;

    @Schema(description = "结算方式")
    private String settlementWay;

}
