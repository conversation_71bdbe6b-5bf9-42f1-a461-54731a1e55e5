package net.lab1024.sa.admin.module.business.mes.part.station.inventory.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateRange;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.common.vo.XyChartVO;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.constant.PartStationInventoryOptTypeEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.dao.PartStationOptLogDao;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.entity.PartStationOptLogEntity;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PartStationOptLogStatsService {
    @Resource
    private PartStationOptLogDao partStationOptLogDao;

    public ResponseDTO<Double> inOutRatio(Long warehouseId, Date beginTime, Date endTime) {
        DateTime beginOfDay = DateUtil.beginOfDay(beginTime);
        DateTime endOfDay = DateUtil.endOfDay(endTime);
        List<PartStationOptLogEntity> logs = partStationOptLogDao.selectList(new LambdaQueryWrapper<PartStationOptLogEntity>()
                .eq(warehouseId != null, PartStationOptLogEntity::getWarehouseId, warehouseId)
                .in(PartStationOptLogEntity::getOptType, PartStationInventoryOptTypeEnum.IN.getValue(), PartStationInventoryOptTypeEnum.OUT.getValue())
                .between(PartStationOptLogEntity::getOptTime, beginOfDay, endOfDay));
        if (CollUtil.isEmpty(logs)) {
            return ResponseDTO.ok(0.0);
        }

        long inNum = 0;
        long outNum = 0;
        for (PartStationOptLogEntity log : logs) {
            if (PartStationInventoryOptTypeEnum.IN.getValue().equals(log.getOptType())) {
                inNum += log.getNum();
            } else if (PartStationInventoryOptTypeEnum.OUT.getValue().equals(log.getOptType())) {
                outNum += log.getNum();
            }
        }

        //保留两位小数
        return ResponseDTO.ok(Double.parseDouble(String.format("%.2f", ((double) outNum / (double) inNum) * 100)));
    }


    /**
     * 出入库次数趋势
     *
     * @param warehouseId
     * @param beginTime
     * @param endTime
     * @return
     */
    public ResponseDTO<Map<String, List<XyChartVO<String, Long>>>> inOutCountTrend(Long warehouseId, Date beginTime, Date endTime) {
        DateTime beginOfDay = DateUtil.beginOfDay(beginTime);
        DateTime endOfDay = DateUtil.endOfDay(endTime);
        DateRange range = DateUtil.range(beginTime, endTime, DateField.DAY_OF_YEAR);
        List<PartStationOptLogEntity> logs = partStationOptLogDao.selectList(new LambdaQueryWrapper<PartStationOptLogEntity>()
                .eq(warehouseId != null, PartStationOptLogEntity::getWarehouseId, warehouseId)
                .in(PartStationOptLogEntity::getOptType, PartStationInventoryOptTypeEnum.IN.getValue(), PartStationInventoryOptTypeEnum.OUT.getValue())
                .between(PartStationOptLogEntity::getOptTime, beginOfDay, endOfDay));
        if (CollUtil.isEmpty(logs)) {
            List<XyChartVO<String, Long>> inResult = new ArrayList<>();
            List<XyChartVO<String, Long>> outResult = new ArrayList<>();

            range.forEach(date -> {
                String key = DateUtil.format(date, "yyyy-MM-dd");
                inResult.add(new XyChartVO<>(key, 0L));
                outResult.add(new XyChartVO<>(key, 0L));
            });

            HashMap<String, List<XyChartVO<String, Long>>> map = new HashMap<>();
            map.put("in", inResult);
            map.put("out", outResult);
            return ResponseDTO.ok(map);
        }

        Map<String, List<PartStationOptLogEntity>> logDayMap = logs.stream()
                .collect(Collectors.groupingBy(e -> DateUtil.format(e.getOptTime(), "yyyy-MM-dd")));

        List<XyChartVO<String, Long>> inResult = new ArrayList<>();
        List<XyChartVO<String, Long>> outResult = new ArrayList<>();
        range.forEach(date -> {
            String key = DateUtil.format(date, "yyyy-MM-dd");
            if (MapUtil.isNotEmpty(logDayMap) && logDayMap.containsKey(key)) {
                List<PartStationOptLogEntity> logList = logDayMap.get(key);
                long inCount = 0;
                long outCount = 0;
                for (PartStationOptLogEntity log : logList) {
                    if (PartStationInventoryOptTypeEnum.IN.getValue().equals(log.getOptType())) {
                        inCount++;
                    } else if (PartStationInventoryOptTypeEnum.OUT.getValue().equals(log.getOptType())) {
                        outCount++;
                    }
                }
                inResult.add(new XyChartVO<>(key, inCount));
                outResult.add(new XyChartVO<>(key, outCount));
            } else {
                inResult.add(new XyChartVO<>(key, 0L));
                outResult.add(new XyChartVO<>(key, 0L));
            }
        });
        HashMap<String, List<XyChartVO<String, Long>>> map = new HashMap<>();
        map.put("in", inResult);
        map.put("out", outResult);
        return ResponseDTO.ok(map);
    }

    public ResponseDTO<Map<String, List<XyChartVO<String, Long>>>> inOutNumTrend(Long warehouseId, Date beginTime, Date endTime) {
        DateTime beginOfDay = DateUtil.beginOfDay(beginTime);
        DateTime endOfDay = DateUtil.endOfDay(endTime);
        DateRange range = DateUtil.range(beginTime, endTime, DateField.DAY_OF_YEAR);
        List<PartStationOptLogEntity> logs = partStationOptLogDao.selectList(new LambdaQueryWrapper<PartStationOptLogEntity>()
                .eq(warehouseId != null, PartStationOptLogEntity::getWarehouseId, warehouseId)
                .in(PartStationOptLogEntity::getOptType, PartStationInventoryOptTypeEnum.IN.getValue(), PartStationInventoryOptTypeEnum.OUT.getValue())
                .between(PartStationOptLogEntity::getOptTime, beginOfDay, endOfDay));
        if (CollUtil.isEmpty(logs)) {
            List<XyChartVO<String, Long>> inResult = new ArrayList<>();
            List<XyChartVO<String, Long>> outResult = new ArrayList<>();

            range.forEach(date -> {
                String key = DateUtil.format(date, "yyyy-MM-dd");
                inResult.add(new XyChartVO<>(key, 0L));
                outResult.add(new XyChartVO<>(key, 0L));
            });

            HashMap<String, List<XyChartVO<String, Long>>> map = new HashMap<>();
            map.put("in", inResult);
            map.put("out", outResult);
            return ResponseDTO.ok(map);
        }

        Map<String, List<PartStationOptLogEntity>> logDayMap = logs.stream()
                .collect(Collectors.groupingBy(e -> DateUtil.format(e.getOptTime(), "yyyy-MM-dd")));

        List<XyChartVO<String, Long>> inResult = new ArrayList<>();
        List<XyChartVO<String, Long>> outResult = new ArrayList<>();
        range.forEach(date -> {
            String key = DateUtil.format(date, "yyyy-MM-dd");
            if (MapUtil.isNotEmpty(logDayMap) && logDayMap.containsKey(key)) {
                List<PartStationOptLogEntity> logList = logDayMap.get(key);
                long inCount = 0;
                long outCount = 0;
                for (PartStationOptLogEntity log : logList) {
                    if (PartStationInventoryOptTypeEnum.IN.getValue().equals(log.getOptType())) {
                        inCount += log.getNum();
                    } else if (PartStationInventoryOptTypeEnum.OUT.getValue().equals(log.getOptType())) {
                        outCount += log.getNum();
                    }
                }
                inResult.add(new XyChartVO<>(key, inCount));
                outResult.add(new XyChartVO<>(key, outCount));
            } else {
                inResult.add(new XyChartVO<>(key, 0L));
                outResult.add(new XyChartVO<>(key, 0L));
            }
        });
        HashMap<String, List<XyChartVO<String, Long>>> map = new HashMap<>();
        map.put("in", inResult);
        map.put("out", outResult);
        return ResponseDTO.ok(map);
    }

    /**
     * 根据操作类型获取入库(出库)的次数和件数 <AUTHOR>
     * @param warehouseId 选填则统计该仓库，不填则统计所有仓库
     * @param beginTime   开始时间 (yyyy-MM-dd)
     * @param endTime     结束时间 (yyyy-MM-dd)
     * @param optType 操作类型
     * @return 次数和件数
     */
    public ResponseDTO<Map<String,Integer>> getNumByOptType(Long warehouseId, Date beginTime, Date endTime,PartStationInventoryOptTypeEnum optType) {
        DateTime beginOfDay = DateUtil.beginOfDay(beginTime);
        DateTime endOfDay = DateUtil.endOfDay(endTime);

        List<PartStationOptLogEntity> incomeOptList = partStationOptLogDao.selectList(new LambdaQueryWrapper<PartStationOptLogEntity>()
                .eq(ObjectUtil.isNotEmpty(warehouseId), PartStationOptLogEntity::getWarehouseId, warehouseId)
                .eq(PartStationOptLogEntity::getOptType, optType.getValue())
                .between(PartStationOptLogEntity::getOptTime, beginOfDay, endOfDay));
        //件数
        int num = incomeOptList.stream().mapToInt(PartStationOptLogEntity::getNum).sum();
        //次数
        int count = incomeOptList.size();

        Map<String,Integer> result = new HashMap<>();
        result.put("num",num);
        result.put("count",count);
        return ResponseDTO.ok(result);
    }
}
