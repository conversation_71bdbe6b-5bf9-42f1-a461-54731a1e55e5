package net.lab1024.sa.admin.module.business.mes.produce.instruct.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderArrangeDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderArrangeEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderArrangeAddForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderArrangeUpdateForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderArrangeVO;
import net.lab1024.sa.admin.module.system.employee.dao.EmployeeDao;
import net.lab1024.sa.admin.module.system.employee.domain.vo.EmployeeVO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.util.SmartBeanUtil;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 指令单安排信息  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:26:34
 * @Copyright zscbdic
 */
@Service
public class ProduceInstructOrderArrangeManager extends ServiceImpl<ProduceInstructOrderArrangeDao, ProduceInstructOrderArrangeEntity> {

    @Resource
    private EmployeeDao employeeDao;


    public List<ProduceInstructOrderArrangeEntity> parseAddForm(List<ProduceInstructOrderArrangeAddForm> arrangeList) {
        if(CollUtil.isEmpty(arrangeList)){
            return Collections.emptyList();
        }


        List<ProduceInstructOrderArrangeEntity> arrangeEntities = SmartBeanUtil.copyList(arrangeList, ProduceInstructOrderArrangeEntity.class);

        // 排序
        arrangeEntities.sort(Comparator.comparing(ProduceInstructOrderArrangeEntity::getSerialNumber));
        for (int i = 0; i < arrangeEntities.size(); i++) {
            ProduceInstructOrderArrangeEntity arrange = arrangeEntities.get(i);
            arrange.setSerialNumber(i + 1);
            arrange.setEndFlag(false);

        }

        // 获取员工信息
        List<Long> headIds = arrangeEntities
                .stream()
                .map(ProduceInstructOrderArrangeEntity::getHeadId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, EmployeeVO> employeeVoMap = employeeDao.getEmployeeByIds(headIds)
                .stream()
                .collect(Collectors.toMap(EmployeeVO::getEmployeeId, employeeVO -> employeeVO));

        for (ProduceInstructOrderArrangeEntity entity : arrangeEntities) {
            entity.setFinishFlag(false);
            EmployeeVO employeeVO = employeeVoMap.get(entity.getHeadId());
            if (employeeVO != null) {
                entity.setHeadName(employeeVO.getActualName());
            }
        }

        arrangeEntities.get(arrangeList.size() - 1).setEndFlag(true);

        return arrangeEntities;
    }

    public List<ProduceInstructOrderArrangeEntity> parseUpdateForm(ValidateList<ProduceInstructOrderArrangeUpdateForm> arrangeList) {
        if(CollUtil.isEmpty(arrangeList)){
            return Collections.emptyList();
        }


        List<ProduceInstructOrderArrangeEntity> arrangeEntities = SmartBeanUtil.copyList(arrangeList, ProduceInstructOrderArrangeEntity.class);

        // 排序
        arrangeEntities.sort(Comparator.comparing(ProduceInstructOrderArrangeEntity::getSerialNumber));
        for (int i = 0; i < arrangeEntities.size(); i++) {
            ProduceInstructOrderArrangeEntity arrange = arrangeEntities.get(i);
            arrange.setSerialNumber(i + 1);
            arrange.setEndFlag(false);

        }

        // 获取员工信息
        List<Long> headIds = arrangeEntities
                .stream()
                .map(ProduceInstructOrderArrangeEntity::getHeadId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, EmployeeVO> employeeVoMap = employeeDao.getEmployeeByIds(headIds)
                .stream()
                .collect(Collectors.toMap(EmployeeVO::getEmployeeId, employeeVO -> employeeVO));

        for (ProduceInstructOrderArrangeEntity entity : arrangeEntities) {
            entity.setFinishFlag(false);
            // 获取员工信息
            EmployeeVO employeeVO = employeeVoMap.get(entity.getHeadId());
            if (employeeVO != null) {
                entity.setHeadName(employeeVO.getActualName());
            }
        }

        arrangeEntities.get(arrangeList.size() - 1).setEndFlag(true);

        return arrangeEntities;
    }

    /**
     * 获取指令单安排信息
     * @param orderId
     * @return
     */
    public List<ProduceInstructOrderArrangeVO> getArrangeInfo(Long orderId) {
        List<ProduceInstructOrderArrangeEntity> arrangeEntityList = this.lambdaQuery().eq(ProduceInstructOrderArrangeEntity::getOrderId, orderId).list();
        if(CollUtil.isEmpty(arrangeEntityList)){
            return Collections.emptyList();
        }

        // 获取员工id
        List<Long> userIds = new ArrayList<>();
        arrangeEntityList.forEach(arrangeEntity -> {
            if (arrangeEntity.getHeadId() != null) {
                userIds.add(arrangeEntity.getHeadId());
            }
            if (arrangeEntity.getFinishId() != null) {
                userIds.add(arrangeEntity.getFinishId());
            }
        });
        // 去重
        List<Long> distinctUserIds = userIds.stream().distinct().collect(Collectors.toList());
        // 获取员工信息
        List<EmployeeVO> employee = employeeDao.getEmployeeByIds(distinctUserIds);
        if (CollUtil.isEmpty(employee)) {
            //如果没有员工信息，直接返回
            return SmartBeanUtil.copyList(arrangeEntityList, ProduceInstructOrderArrangeVO.class);
        }
        // 员工信息map
        Map<Long, EmployeeVO> employeeVoMap = employee.stream().collect(Collectors.toMap(EmployeeVO::getEmployeeId, employeeVO -> employeeVO));
        return arrangeEntityList.stream().map(arrangeEntity -> {
            ProduceInstructOrderArrangeVO arrangeVO = SmartBeanUtil.copy(arrangeEntity, ProduceInstructOrderArrangeVO.class);
            if (arrangeEntity.getHeadId() != null && employeeVoMap.containsKey(arrangeEntity.getHeadId())) {
                EmployeeVO employeeVO = employeeVoMap.get(arrangeEntity.getHeadId());
                arrangeVO.setHeadName(employeeVO.getActualName());
            }
            if (arrangeEntity.getFinishId() != null && employeeVoMap.containsKey(arrangeEntity.getFinishId())) {
                EmployeeVO employeeVO = employeeVoMap.get(arrangeEntity.getFinishId());
                arrangeVO.setFinishName(employeeVO.getActualName());
            }
            return arrangeVO;
        }).collect(Collectors.toList());
    }
}
