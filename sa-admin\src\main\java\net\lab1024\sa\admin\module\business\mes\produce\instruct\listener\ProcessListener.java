package net.lab1024.sa.admin.module.business.mes.produce.instruct.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.event.process.ProcessDeleteCheckEvent;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderProcessDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderProcessEntity;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;

@Component("produceProcessListener")
public class ProcessListener {

    @Resource
    private ProduceInstructOrderProcessDao produceInstructOrderProcessDao;


    @EventListener(ProcessDeleteCheckEvent.class)
    public void processDeleteCheck(ProcessDeleteCheckEvent event) {
        Long id = event.getId();
        Long count = produceInstructOrderProcessDao.selectCount(new LambdaQueryWrapper<ProduceInstructOrderProcessEntity>()
                .eq(ProduceInstructOrderProcessEntity::getProcessId, id));
        if (count > 0) {
            throw new BusinessException("该工序正在被指令单使用中，无法删除");
        }
    }
}
