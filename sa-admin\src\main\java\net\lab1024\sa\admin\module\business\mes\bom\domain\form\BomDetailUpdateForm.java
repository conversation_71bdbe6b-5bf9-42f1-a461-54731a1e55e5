package net.lab1024.sa.admin.module.business.mes.bom.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * BOM详情 更新表单
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:25:50
 * @Copyright zscbdic
 */

@Data
public class BomDetailUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

    @Schema(description = "bomid", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "bomid 不能为空")
    private Long bomId;

    @Schema(description = "物料id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物料id 不能为空")
    private Long itemId;

    @Schema(description = "物料名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "物料名称 不能为空")
    private String itemName;

    @Schema(description = "物料编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "物料编号 不能为空")
    private String itemNumber;

    @Schema(description = "物料规格型号")
    private String itemModel;

    @Schema(description = "物料分类id")
    private Long itemTypeId;

    @Schema(description = "物料类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "物料类型 不能为空")
    private String itemCategory;

    @Schema(description = "物料单位id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物料单位id 不能为空")
    private Long itemUnitId;

    @Schema(description = "物料单位名称",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物料单位名称 不能为空")
    private String  itemUnitName;

    @Schema(description = "物料属性", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "物料属性 不能为空")
    private String itemAttribute;

    @Schema(description = "单位用量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "单位用量 不能为空")
    private Double dosage;

    /**
     * 单位损耗率
     */
    @Schema(description = "单位损耗率")
    @NotNull(message = "单位损耗率 不能为空")
    private Double loss;

}
