package net.lab1024.sa.admin.module.business.mes.base.manager;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.lab1024.sa.admin.module.business.mes.base.dao.SupplierDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.SupplierEntity;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * 供应商表  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-03 09:43:14
 * @Copyright zscbdic
 */
@Service
public class SupplierManager extends ServiceImpl<SupplierDao, SupplierEntity> {

    /**
     *
     * @return  以供应商id为键，名称为值的Map
     */
    public Map<Long, String> processMap() {
        return query()
                .list().stream()
                .collect(Collectors.toMap(SupplierEntity::getId, SupplierEntity::getName));
    }
}
