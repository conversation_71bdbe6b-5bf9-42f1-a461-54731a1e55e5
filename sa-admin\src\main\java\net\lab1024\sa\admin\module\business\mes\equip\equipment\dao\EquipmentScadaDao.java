package net.lab1024.sa.admin.module.business.mes.equip.equipment.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.entity.EquipmentScadaEntity;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentScadaQueryForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.vo.EquipmentScadaVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.vo.EquipmentVO;

/**
 * 设备scada信息 Dao
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:13:22
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface EquipmentScadaDao extends BaseMapper<EquipmentScadaEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<EquipmentVO> queryEquipScadaPage(Page page, @Param("queryForm") EquipmentScadaQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
