package net.lab1024.sa.admin.module.business.mes.base.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.base.dao.SizeDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.SizeEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SizeAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SizeQuery;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SizeQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SizeUpdateForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.Size<PERSON>;
import net.lab1024.sa.admin.module.business.mes.base.manager.SizeManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 尺码表 Service
 *
 * <AUTHOR>
 * @Date 2024-07-05 22:37:21
 * @Copyright zscbdic
 */

@Service
public class SizeService {

    @Resource
    private SizeDao sizeDao;

    @Resource
    private SizeManager sizeManager;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<SizeVO> queryPage(SizeQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<SizeVO> list = sizeDao.queryPage(page, queryForm);
        PageResult<SizeVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(SizeAddForm addForm) {
        sizeManager.addCheck(addForm);
        SizeEntity sizeEntity = SmartBeanUtil.copy(addForm, SizeEntity.class);
        sizeDao.insert(sizeEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(SizeUpdateForm updateForm) {
        sizeManager.updateCheck(updateForm);
        SizeEntity sizeEntity = SmartBeanUtil.copy(updateForm, SizeEntity.class);
        sizeDao.updateById(sizeEntity);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    public ResponseDTO<String> batchDelete(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return ResponseDTO.ok();
        }

        sizeDao.batchUpdateDeleted(idList, true);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }

        sizeDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }


    /**
     * 查询所有
     *
     * @param query
     * @return
     */
    public ResponseDTO<List<SizeVO>> queryAll(SizeQuery query) {
        List<SizeEntity> sizeEntities = sizeDao.selectList(new LambdaQueryWrapper<SizeEntity>()
                .like(StrUtil.isNotBlank(query.getSizeMessage()), SizeEntity::getSizeMessage, query.getSizeMessage()));
        return ResponseDTO.ok(SmartBeanUtil.copyList(sizeEntities, SizeVO.class));
    }
}
