package net.lab1024.sa.admin.module.business.mes.base.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 单位表 更新表单
 *
 * <AUTHOR>
 * @Date 2024-07-04 11:12:45
 * @Copyright zscbdic
 */

@Data
public class UnitUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "单位编码")
    private String unitCode;

    @Schema(description = "单位名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "单位名称 不能为空")
    private String name;

}
