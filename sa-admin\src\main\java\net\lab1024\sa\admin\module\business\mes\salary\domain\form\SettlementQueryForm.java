package net.lab1024.sa.admin.module.business.mes.salary.domain.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import net.lab1024.sa.admin.module.business.mes.salary.constant.SettlementWayEnum;
import net.lab1024.sa.base.common.domain.PageParam;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 结算查询表单
 */
@Data
public class SettlementQueryForm extends PageParam {

    /**
     * 查询员工id
     */
    @Schema(description = "查询员工id")
    private List<Long> queryEmployeeIds;

    /**
     * 开始时间 yyyy-MM-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 结束时间 yyyy-MM-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 结算方式 1日结 2月结
     */
    @NotNull(message = "结算方式 不能为空")
    @CheckEnum(value = SettlementWayEnum.class, message = "结算方式 不能为空")
    private String settlementWay;

    /**
     * 前端不传
     * 记录状态;0正常 1作废
     */
    @Null(message = "记录状态 得为空")
        private String recordStatus;

    /**
     * 前端不传
     * 审核状态;0未审核 1已审核 2审核不通过
     */
    @Null(message = "审核状态 得为空")
    private String auditFlag;

    /**
     * 员工ID
     */
    @Null(message = "员工ID 得为空")
    private List<Long> employeeIds;

    @Null(message = "结算标识 得为空")
    private Boolean settlementFlag;
}
