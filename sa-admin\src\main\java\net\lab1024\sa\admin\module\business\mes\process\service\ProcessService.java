package net.lab1024.sa.admin.module.business.mes.process.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;
import net.lab1024.sa.admin.event.process.ProcessDeleteCheckEvent;
import net.lab1024.sa.admin.module.business.mes.common.excel.service.ExcelBaseService;
import net.lab1024.sa.admin.module.business.mes.process.constant.ProcessControlEnum;
import net.lab1024.sa.admin.module.business.mes.process.dao.ProcessDao;
import net.lab1024.sa.admin.module.business.mes.process.domain.entity.ProcessEntity;
import net.lab1024.sa.admin.module.business.mes.process.domain.excel.ProcessExcel;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessAddForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessQueryForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessUpdateForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.vo.ProcessVO;
import net.lab1024.sa.admin.module.business.mes.process.manager.ProcessManager;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartBigDecimalUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import net.lab1024.sa.base.common.util.SmartVerificationUtil;

import jakarta.annotation.Resource;

/**
 * 工序信息 Service
 *
 * <AUTHOR>
 * @Date 2024-07-13 15:19:57
 * @Copyright zscbdic
 */

@Service
public class ProcessService {

    @Resource
    private ProcessDao processDao;

    @Resource
    private ProcessManager processManager;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private ExcelBaseService excelBaseService;



    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<ProcessVO> queryPage(ProcessQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ProcessVO> list = processDao.queryPage(page, queryForm);
        PageResult<ProcessVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(ProcessAddForm addForm) {
        processManager.addCheck(addForm);
        ProcessEntity processEntity = SmartBeanUtil.copy(addForm, ProcessEntity.class);
        processDao.insert(processEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(ProcessUpdateForm updateForm) {
        processManager.updateCheck(updateForm);


        ProcessEntity processEntity = SmartBeanUtil.copy(updateForm, ProcessEntity.class);
        processDao.updateById(processEntity);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }

        processManager.deleteCheck(id);
        eventPublisher.publishEvent(new ProcessDeleteCheckEvent(this, id));

        processDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }

    /**
     * 导入
     *
     * @param file
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> importExcel(MultipartFile file) {
        excelBaseService.importExcel(file, ProcessExcel.class, 1,
                e -> {
                    //数据校验与处理
                    String verify = SmartBeanUtil.verify(e);
                    if (verify != null) {
                        throw new BusinessException(verify);
                    }

                    String processControl = e.getProcessControl();
                    ProcessControlEnum controlEnum = ProcessControlEnum.getByDesc(processControl);
                    if (controlEnum == null) {
                        throw new BusinessException("工序控制值不正确");
                    }
                    ProcessEntity process = SmartBeanUtil.copy(e, ProcessEntity.class);
                    process.setProcessControl(controlEnum.getValue());
                    process.setUnitPrice1(e.getUnitPrice1() == null ? BigDecimal.ZERO : e.getUnitPrice1());
                    return process;
                }, list -> {
                    //业务校验与保存
                    List<String> numbers = list.stream().map(ProcessEntity::getProcessNumber).collect(Collectors.toList());
                    Long count = processDao.selectCount(new LambdaQueryWrapper<ProcessEntity>()
                            .in(ProcessEntity::getProcessNumber, numbers));
                    if (count > 0) {
                        throw new BusinessException("工序编号重复");
                    }


                    processManager.saveBatch(list, 1000);
                });
        return ResponseDTO.ok();
    }
}
