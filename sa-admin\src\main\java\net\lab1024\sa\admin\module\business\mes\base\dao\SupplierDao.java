package net.lab1024.sa.admin.module.business.mes.base.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.SupplierEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SupplierQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.SupplierVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 供应商表 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-03 09:43:14
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface SupplierDao extends BaseMapper<SupplierEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<SupplierVO> queryPage(Page page, @Param("queryForm") SupplierQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

}
