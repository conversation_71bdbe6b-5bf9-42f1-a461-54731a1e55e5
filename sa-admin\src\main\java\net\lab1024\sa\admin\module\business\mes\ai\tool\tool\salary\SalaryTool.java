package net.lab1024.sa.admin.module.business.mes.ai.tool.tool.salary;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonClassDescription;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.mes.ai.tool.tool.constant.ToolNameConstant;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.PayoffRecordEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.vo.WageFieldValueVO;
import net.lab1024.sa.admin.module.business.mes.salary.manager.PayoffRecordManager;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Slf4j
@Component(ToolNameConstant.SALARY_TOOL)
public class SalaryTool {

    @Resource
    private PayoffRecordManager payoffRecordManager;

    /**
     * 获取工资
     *
     * @return
     */
    @Tool(description = "获取当前用户工资数据")
    public List<Response> getSalary(ToolContext toolContext) {
        Object userId = toolContext.getContext().get(ToolNameConstant.TOOL_CONTENT_USER_ID);
        if(userId==null){
           return Collections.emptyList();
        }
        List<PayoffRecordEntity> list = payoffRecordManager.lambdaQuery().eq(PayoffRecordEntity::getEmployeeId, userId)
                .list();
        List<Response> vos = list.stream().map(e -> {
            Response copy = SmartBeanUtil.copy(e, Response.class);
            copy.setPayoffTime(e.getPayoffTime().toLocalDate());
            if (CharSequenceUtil.isNotBlank(e.getOtherAmountData())) {
                List<WageFieldValueVO> wageFieldValueVos = JSON.parseArray(e.getOtherAmountData(), WageFieldValueVO.class);
                copy.setOtherAmountDetail(wageFieldValueVos);
            }
            return copy;
        }).toList();
        log.info("获取工资数据成功：{}", vos);
        return vos;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonClassDescription(value = "工资记录")
    public static class Response {

        /**
         * 员工姓名
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "员工姓名")
        private String actualName;

        /**
         * 归属月份
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "归属月份")
        private Date belongMonth;

        /**
         * 总金额
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "总金额")
        private BigDecimal totalAmount;

        /**
         * 计件金额
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "计件金额")
        private BigDecimal pieceAmount;

        /**
         * 其他项金额
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "其他项金额")
        private BigDecimal otherAmount;

        /**
         * 其他项金额详情
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "其他项金额详情")
        private List<WageFieldValueVO> otherAmountDetail;

        /**
         * 发放时间
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "发放时间")
        private LocalDate payoffTime;


    }
}
