package net.lab1024.sa.admin.module.business.mes.salary.dao;

import java.util.ArrayList;
import java.util.List;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.WageFieldValueEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 工资字段值 Dao
 *
 * <AUTHOR>
 * @Date 2024-11-25 20:28:37
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface WageFieldValueDao extends BaseMapper<WageFieldValueEntity> {



    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 批量新增
     * @param list
     */
    void batchInsert(@Param("list") List<WageFieldValueEntity> list);
}
