package net.lab1024.sa.admin.module.business.mes.base.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 款式品类表 更新表单
 *
 * <AUTHOR>
 * @Date 2024-07-05 15:45:34
 * @Copyright zscbdic
 */

@Data
public class StyleUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

    @Schema(description = "父id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "父id 不能为空")
    private Long parentId;

    @Schema(description = "品类名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "品类名称 不能为空")
    private String styleName;

    @Schema(description = "品类代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "品类代码 不能为空")
    private String styleCode;

    @Schema(description = "默认尺码表", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "默认尺码表 不能为空")
    private Long defaultSizeId;

    @Schema(description = "备注")
//    @NotNull(message = "默认尺码表 不能为空")
    private String remark;

}
