package net.lab1024.sa.admin.module.business.mes.factory.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.ProduceTeamEntity;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.ProduceTeamQueryForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.vo.ProduceTeamVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 生产小组 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:32:46
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface ProduceTeamDao extends BaseMapper<ProduceTeamEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<ProduceTeamVO> queryPage(Page page, @Param("queryForm") ProduceTeamQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
