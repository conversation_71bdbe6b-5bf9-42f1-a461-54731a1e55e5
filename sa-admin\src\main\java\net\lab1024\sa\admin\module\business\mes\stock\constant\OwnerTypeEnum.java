package net.lab1024.sa.admin.module.business.mes.stock.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 货主类型 枚举
 */
@Getter
@AllArgsConstructor
public enum OwnerTypeEnum implements BaseEnum {

    /**
     * 车间
     */
    WORKSHOP("workshop", "车间"),

    /**
     * 部门
     */
    DEPARTMENT("department", "部门"),

    /**
     * 供应商
     */
    SUPPLIER("supplier", "供应商"),

    /**
     * 客户
     */
    CUSTOMER("customer", "客户"),

    /**
     * 未知
     */
    UNKNOWN("unknown", "未知"),

    ;


    private String value;

    private String desc;


    public static OwnerTypeEnum getByValue(String value) {
        for (OwnerTypeEnum item : OwnerTypeEnum.values()) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return UNKNOWN;
    }
}
