package net.lab1024.sa.admin.module.business.mes.bom.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import net.lab1024.sa.base.common.domain.ValidateList;

/**
 * 物料BOM表 更新表单
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:25:22
 * @Copyright zscbdic
 */

@Data
public class BomUpdateForm {

    /**
     * 主键
     */
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

// 当前版本默认编号不能更改
//    @Schema(description = "bom编号", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "bom编号 不能为空")
//    private String bomNumber;

    /**
     * bom名称
     */
    @Schema(description = "bom名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "bom名称 不能为空")
    private String bomName;

//    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "版本号 不能为空")
//    private Integer versionNumber;

    /**
     * 物料id
     */
    @Schema(description = "物料id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物料id 不能为空")
    private Long itemId;

    /**
     * 物料名称
     */
    @Schema(description = "物料名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "物料名称 不能为空")
    private String itemName;

    /**
     * 物料编号
     */
    @Schema(description = "物料编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "物料编号 不能为空")
    private String itemNumber;

    /**
     * 物料规格型号
     */
    @Schema(description = "物料规格型号")
    private String itemModel;

    /**
     * 物料分类id
     */
    @Schema(description = "物料分类id")
    private Long itemTypeId;

    /**
     * 物料类型
     */
    @Schema(description = "物料类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "物料类型 不能为空")
    private String itemCategory;

    /**
     * 物料单位id
     */
    @Schema(description = "物料单位id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物料单位id 不能为空")
    private Long itemUnitId;

    @Schema(description = "物料单位名称",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物料单位名称 不能为空")
    private String  itemUnitName;

    /**
     * 物料属性
     */
    @Schema(description = "物料属性", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "物料属性 不能为空")
    private String itemAttribute;

    /**
     * 停用标识
     */
    @Schema(description = "停用标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "停用标识 不能为空")
    private Boolean enableFlag;

    /**
     * 物料BOM明细列表
     */
    @Schema(description = "物料BOM表明 添加表单列表")
    private ValidateList<BomDetailAddForm> bomDetailList;

}
