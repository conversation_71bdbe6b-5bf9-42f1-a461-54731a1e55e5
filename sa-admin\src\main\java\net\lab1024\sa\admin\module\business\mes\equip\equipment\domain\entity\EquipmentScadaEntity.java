package net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设备scada信息 实体类
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:13:22
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_equip_equipment_scada")
public class EquipmentScadaEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 设备ID
     */
    private Long equipmentId;

    /**
     * 是否物联网连接;0否,1是
     */
    private Boolean iotNetworkFlag;

    /**
     * 物联网平台;yuanyi元一
     */
    private String iotNetworkPlatform;

    /**
     * 对接设备类型
     */
    private String iotEquipmentType;

    /**
     * 设备ID(SCADA);保留字段
     */
    private String scadaEquipmentId;

    /**
     * 产品编码(SCADA);SCADA产品编码
     */
    private String scadaProductCode;


    /**
     * 设备编码(SCADA);SCADA设备编码
     */
    private String scadaEquipmentCode;

}
