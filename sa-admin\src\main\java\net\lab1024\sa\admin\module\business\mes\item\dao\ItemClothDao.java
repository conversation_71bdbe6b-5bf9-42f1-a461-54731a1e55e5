package net.lab1024.sa.admin.module.business.mes.item.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemClothEntity;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemClothQueryForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.vo.ItemClothVO;

import java.util.List;

/**
 * 布料信息表 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-02 12:03:01
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface ItemClothDao extends BaseMapper<ItemClothEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
//    List<ItemClothVO> queryPage(Page page, @Param("queryForm") ItemClothQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id") Long id, @Param("deletedFlag") boolean deletedFlag);

    /**
     * 布料分页查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<ItemClothVO> queryClothPage(Page<?> page, @Param("queryForm") ItemClothQueryForm queryForm);
}
