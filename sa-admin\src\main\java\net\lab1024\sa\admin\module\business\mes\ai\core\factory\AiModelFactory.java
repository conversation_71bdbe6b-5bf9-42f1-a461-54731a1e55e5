package net.lab1024.sa.admin.module.business.mes.ai.core.factory;

import cn.hutool.core.lang.Singleton;
import cn.hutool.core.lang.func.Func0;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.extra.spring.SpringUtil;
import net.lab1024.sa.admin.module.business.mes.ai.core.constant.AiPlatformEnum;
import net.lab1024.sa.base.common.exception.BusinessException;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.model.tool.ToolCallingManager;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.openai.api.common.OpenAiApiConstants;
import org.springframework.stereotype.Component;

@Component
public class AiModelFactory {



    /**
     * 获取或创建ChatModel
     *
     * @param platform
     * @param apiKey
     * @param url
     * @return
     */
    public static ChatModel getOrCreateChatModel(AiPlatformEnum platform, String model, String apiKey, String url) {
        String cacheKey = buildClientCacheKey(ChatModel.class, platform, apiKey, url, model);
        return Singleton.get(cacheKey, (Func0<ChatModel>) () -> {
            if (AiPlatformEnum.OPENAI.equals(platform)) {
                return buildOpenAiChatModel(apiKey, url, model);
            }
            throw new BusinessException("不支持的AI平台");

        });
    }

    private static String buildClientCacheKey(Class<?> clazz, Object... params) {
        if (ArrayUtil.isEmpty(params)) {
            return clazz.getName();
        }
        return CharSequenceUtil.format("{}#{}", clazz.getName(), ArrayUtil.join(params, "_"));
    }

    private static OpenAiChatModel buildOpenAiChatModel(String openAiToken, String url, String model) {
        url = CharSequenceUtil.blankToDefault(url, OpenAiApiConstants.DEFAULT_BASE_URL);
        OpenAiApi openAiApi = OpenAiApi.builder().baseUrl(url).apiKey(openAiToken).build();
        return OpenAiChatModel.builder()
                .openAiApi(openAiApi)
//                .toolCallingManager(ToolCallingManager.builder().build())
                .defaultOptions(OpenAiChatOptions.builder().model(model).build())
                .build();
    }

    private static ToolCallingManager getToolCallingManager() {
        return SpringUtil.getBean(ToolCallingManager.class);
    }
}
