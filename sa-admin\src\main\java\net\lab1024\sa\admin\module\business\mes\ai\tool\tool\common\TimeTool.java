package net.lab1024.sa.admin.module.business.mes.ai.tool.tool.common;

import com.fasterxml.jackson.annotation.JsonClassDescription;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.mes.ai.tool.tool.constant.ToolNameConstant;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;

@Slf4j
@Component(ToolNameConstant.TIME_TOOL)
public class TimeTool  {


    @Tool(description = "获取当前时间")
    public Response getTime() {
        ZonedDateTime zonedDateTime = LocalDateTime.now().atZone(LocaleContextHolder.getTimeZone().toZoneId());
        String string = zonedDateTime.toString();
        DayOfWeek dayOfWeek = zonedDateTime.getDayOfWeek();
        return new TimeTool.Response(string, dayOfWeek.toString());
    }



    @Data
    @JsonClassDescription("查询时间相关信息")
    public static class Request {

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Response {

        @JsonPropertyDescription("当前时间")
        private String time;

        @JsonPropertyDescription("当前星期")
        private String week;

    }

}
