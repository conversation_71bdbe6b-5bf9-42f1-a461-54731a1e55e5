package net.lab1024.sa.admin.module.business.mes.process.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 工序库详情工序 更新表单
 *
 * <AUTHOR>
 * @Date 2024-07-13 15:19:57
 * @Copyright zscbdic
 */

@Data
public class ProcessLibraryDetailsUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "工序库id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "工序库id 不能为空")
    private Long processLibraryId;

    @Schema(description = "序号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "序号 不能为空")
    private Integer serialNumber;

    /**
     * 工序id
     */
    private Long processId;

    @Schema(description = "工序名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "工序名称 不能为空")
    private String name;

    @Schema(description = "部位")
    private String position;

    @Schema(description = "工序类型")
    private String processType;

    @Schema(description = "标准工时;单位")
    private Integer standardTime;

    @Schema(description = "工价一")
    private BigDecimal unitPrice1;

    @Schema(description = "工价二")
    private BigDecimal unitPrice2;

    @Schema(description = "工价三")
    private BigDecimal unitPrice3;

    @Schema(description = "工价四")
    private BigDecimal unitPrice4;

    @Schema(description = "工价五")
    private BigDecimal unitPrice5;

    @Schema(description = "工价六")
    private BigDecimal unitPrice6;

    @Schema(description = "车间id")
    private Long workshopId;

    @Schema(description = "末道工序;0否 1是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "末道工序;0否 1是 不能为空")
    private Boolean endFlag;

    @Schema(description = "是否审核;0不审核 1审核", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否审核;0不审核 1审核 不能为空")
    private Boolean auditFlag;

    @Schema(description = "工序控制;0自制 1委外 2不限", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "工序控制;0自制 1委外 2不限 不能为空")
    private String processControl;

    @Schema(description = "sopId;保留")
    private Long sopId;

}
