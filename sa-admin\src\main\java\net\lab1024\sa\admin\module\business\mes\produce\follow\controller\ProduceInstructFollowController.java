package net.lab1024.sa.admin.module.business.mes.produce.follow.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import net.lab1024.sa.admin.module.business.mes.produce.follow.domain.form.ProduceInstructFollowAddForm;
import net.lab1024.sa.admin.module.business.mes.produce.follow.domain.form.ProduceInstructFollowUpdateForm;
import net.lab1024.sa.admin.module.business.mes.produce.follow.domain.vo.ProduceInstructFollowVO;
import net.lab1024.sa.admin.module.business.mes.produce.follow.service.ProduceInstructFollowService;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderVO;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import net.lab1024.sa.base.common.util.SmartRequestUtil;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 生产跟单 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-26 21:36:36
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class ProduceInstructFollowController {

    @Resource
    private ProduceInstructFollowService produceInstructFollowService;

    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/produceInstructFollow/queryPage")
    public ResponseDTO<PageResult<ProduceInstructFollowVO>> queryPage(@RequestBody @Valid ProduceInstructOrderQueryForm queryForm) {
        return ResponseDTO.ok(produceInstructFollowService.queryPage(queryForm));
    }

    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/produceInstructFollow/add")
    public ResponseDTO<String> add(@RequestBody @Valid ProduceInstructFollowAddForm addForm) {
        return produceInstructFollowService.add(addForm);
    }

    @Operation(summary = "我的跟单信息 <AUTHOR>
    @GetMapping("/produceInstructFollow/info/{employeeId}")
    public ResponseDTO<List<ProduceInstructFollowVO>> queryInfo(@PathVariable Long employeeId) {
        return produceInstructFollowService.instructInfo(employeeId);
    }

//    @Operation(summary = "我的跟单动态信息 <AUTHOR>
//    @GetMapping("/produceInstructFollow/dynamicInfo/{instructId}")
//    public ResponseDTO<ProduceInstructOrderVO> queryDynamicInfo
//            (@PathVariable Long instructId) {
//        return produceInstructFollowService.instructDynamicInfo(instructId);
//    }
//----------------------------------------------------------------------------------------

    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/produceInstructFollow/update")
    public ResponseDTO<String> update(@RequestBody @Valid ProduceInstructFollowUpdateForm updateForm) {
        return produceInstructFollowService.update(updateForm);
    }

    @Operation(summary = "取消跟单 <AUTHOR>
    @PostMapping("/produceInstructFollow/delete")
    public ResponseDTO<String> batchDelete(@RequestBody @Valid ProduceInstructFollowAddForm addForm) {
        return produceInstructFollowService.delete(addForm);
    }
}
