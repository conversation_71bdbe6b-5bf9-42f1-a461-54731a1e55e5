package net.lab1024.sa.admin.module.business.mes.produce.instruct.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProcessCountVo;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.service.ProduceInstructOrderProcessStatsService;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 工序统计相关接口
 */
@RestController
@Tag(name = "")
public class ProduceInstructOrderProcessStatsController {

    @Resource
    private ProduceInstructOrderProcessStatsService produceInstructOrderProcessStatsService;


    /**
     * 工序统计
     * @param beginTime yyyy-MM-dd
     * @param endTime yyyy-MM-dd
     * @return
     */
    @GetMapping("/produceInstructOrderProcess/processCountStats")
    public ResponseDTO<List<ProcessCountVo>> processCountStats(@RequestParam(name = "beginTime") @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginTime,
                                                               @RequestParam(name = "endTime") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime) {
        return produceInstructOrderProcessStatsService.processCountStats(beginTime, endTime);
    }

}
