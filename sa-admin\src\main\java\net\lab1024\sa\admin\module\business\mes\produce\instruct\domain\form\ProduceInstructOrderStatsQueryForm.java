package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.time.LocalDate;

/**
 * 生产看板 查询表单
 *
 * <AUTHOR>
 * @Date 2024-09-19 10:22:24
 * @Copyright zscbdic
 */

@Data
public class ProduceInstructOrderStatsQueryForm {

    /**
     * 开始时间 yyyy-MM-dd
     */
    @Schema(description = "开始时间",requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate startTime;

    /**
     * 结束时间 yyyy-MM-dd
     */
    @Schema(description = "结束时间",requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate endTime;

}
