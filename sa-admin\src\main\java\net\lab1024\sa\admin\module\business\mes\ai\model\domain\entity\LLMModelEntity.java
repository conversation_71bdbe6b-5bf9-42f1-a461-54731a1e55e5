package net.lab1024.sa.admin.module.business.mes.ai.model.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 大模型表 实体类
 *
 * <AUTHOR>
 * @Date 2025-04-17 11:25:27
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_ai_llm_model")
public class LLMModelEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 模型类型
     */
    private String modelType;

    /**
     * 模型优先级;1
     */
    private Integer priority;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型别名
     */
    private String modelNickname;

    /**
     * 启用标识;1启用 0停用
     */
    private Boolean enableFlag;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 请求地址
     */
    private String baseUrl;

    /**
     * api_key
     */
    private String apiKey;

    /**
     * 默认标识;1默认 0非默认
     */
    private Boolean defaultFlag;


    /**
     * 工具调用能力;1行 0不行
     */
    private Boolean useToolFlag;

    /**
     * 知识库调用能力;1行 0不行
     */
    private Boolean knowledgeUseFlag;

    /**
     * 视觉调用能力;1行 0不行
     */
    private Boolean visionUseFlag;



}
