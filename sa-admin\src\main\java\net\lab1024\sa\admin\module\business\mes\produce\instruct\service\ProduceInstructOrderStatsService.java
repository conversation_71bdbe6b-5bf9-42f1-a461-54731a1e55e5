package net.lab1024.sa.admin.module.business.mes.produce.instruct.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.common.vo.XyChartVO;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.constant.ProduceInstructOrderPrioityEnum;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.constant.ProduceInstructOrderProduceStatusEnum;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderStatsQueryForm;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartRequestUtil;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 生产看板-绩效指标 Service
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:22:24
 * @Copyright zscbdic
 */

@Service
@Slf4j
public class ProduceInstructOrderStatsService {

    @Resource
    ProduceInstructOrderDao produceInstructOrderDao;

    /**
     *
     * @param queryForm 时间范围
     * @param status 指令单生产状态
     * @return 根据status返回数据
     */
    public ResponseDTO<Long> getNum(ProduceInstructOrderStatsQueryForm queryForm, ProduceInstructOrderProduceStatusEnum status){
        boolean timeFlag = queryForm.getStartTime()!=null && queryForm.getEndTime()!=null;
        LocalDateTime bTime =null;
        LocalDateTime eTime = null;
        if(timeFlag){
           bTime = queryForm.getStartTime().atStartOfDay();
           eTime = queryForm.getEndTime().atTime(LocalTime.MAX);
        }

        LambdaQueryWrapper<ProduceInstructOrderEntity> lq = new LambdaQueryWrapper<ProduceInstructOrderEntity>()
                .between(timeFlag, ProduceInstructOrderEntity::getCreateTime, bTime, eTime);
        if(status!=null){
            lq.eq(ProduceInstructOrderEntity::getProduceStatus, status.getValue());
        }

        Long count = produceInstructOrderDao.selectCount(lq);
        return ResponseDTO.ok(count);
    }


    /**
     *
     * @param queryForm 时间范围
     * @param status 指令单生产状态
     * @return 平均生产周期
     */
    public ResponseDTO<Double> getAverageDays(ProduceInstructOrderStatsQueryForm queryForm, ProduceInstructOrderProduceStatusEnum status){
        boolean timeFlag = queryForm.getStartTime()!=null && queryForm.getEndTime()!=null;
        LocalDateTime bTime =null;
        LocalDateTime eTime = null;
        if(timeFlag){
            bTime = queryForm.getStartTime().atStartOfDay();
            eTime = queryForm.getEndTime().atTime(LocalTime.MAX);
        }


        List<ProduceInstructOrderEntity> list = produceInstructOrderDao.selectList(new LambdaQueryWrapper<ProduceInstructOrderEntity>()
                .between(timeFlag,ProduceInstructOrderEntity::getCreateTime, bTime, eTime)
                .eq(ProduceInstructOrderEntity::getProduceStatus, status.getValue()));
        //对将要计算的两个时间数据进行判空
        List<ProduceInstructOrderEntity> collect = list.stream()
                .filter(e -> e.getIssuedTime() != null && e.getRealFinishTime() != null).collect(Collectors.toList());
        long millisecond = 0L;
        for (ProduceInstructOrderEntity e : collect) {
            long millis = LocalDateTimeUtil.between(e.getIssuedTime(), e.getRealFinishTime()).toMillis();
            millisecond += millis;
        }

        //毫秒转化为天数
        double flag = 86400000.0;
        Double days = millisecond / flag;
        return ResponseDTO.ok(Double.parseDouble(String.format("%.2f", days)));
    }

    /**
     *
     * @param queryForm 时间范围
     * @return 超时未完成指令单数
     */
    public ResponseDTO<Long> getTimeoutNum(ProduceInstructOrderStatsQueryForm queryForm) {
        boolean timeFlag = queryForm.getStartTime()!=null && queryForm.getEndTime()!=null;
        LocalDateTime bTime =null;
        LocalDateTime eTime = null;
        if(timeFlag){
            bTime = queryForm.getStartTime().atStartOfDay();
            eTime = queryForm.getEndTime().atTime(LocalTime.MAX);
        }
        //未完成
        Long count = produceInstructOrderDao.selectCount(new LambdaQueryWrapper<ProduceInstructOrderEntity>()
                 .between(timeFlag,ProduceInstructOrderEntity::getCreateTime, bTime, eTime)
                .in(ProduceInstructOrderEntity::getProduceStatus,ProduceInstructOrderProduceStatusEnum.ISSUED.getValue(),ProduceInstructOrderProduceStatusEnum.START.getValue())
                .lt(ProduceInstructOrderEntity::getPlanFinishTime, LocalDateTime.now()));

        return ResponseDTO.ok(count);
    }

    /**
     *
     * @param queryForm 时间范围
     * @return 进行中指令单数
     */
    public ResponseDTO<Long> getOnGoingNum(ProduceInstructOrderStatsQueryForm queryForm) {
        boolean timeFlag = queryForm.getStartTime()!=null && queryForm.getEndTime()!=null;
        LocalDateTime bTime =null;
        LocalDateTime eTime = null;
        if(timeFlag){
            bTime = queryForm.getStartTime().atStartOfDay();
            eTime = queryForm.getEndTime().atTime(LocalTime.MAX);
        }
        //进行中
        Long count = produceInstructOrderDao.selectCount(new LambdaQueryWrapper<ProduceInstructOrderEntity>()
                .between(timeFlag,ProduceInstructOrderEntity::getCreateTime, bTime, eTime)
                .in(ProduceInstructOrderEntity::getProduceStatus,ProduceInstructOrderProduceStatusEnum.ISSUED.getValue(),ProduceInstructOrderProduceStatusEnum.START.getValue()));

        return ResponseDTO.ok(count);
    }

    /**
     *
     * @param queryForm 时间范围
     * @return 指定时间范围内每天指令单数量
     */
    public ResponseDTO<List<XyChartVO<String,Integer>>> perDayOrdersNum(ProduceInstructOrderStatsQueryForm queryForm){
        boolean timeFlag = queryForm.getStartTime()!=null && queryForm.getEndTime()!=null;
        LocalDateTime bTime =null;
        LocalDateTime eTime = null;
        if(timeFlag){
            bTime = queryForm.getStartTime().atStartOfDay();
            eTime = queryForm.getEndTime().atTime(LocalTime.MAX);
        } else
            return ResponseDTO.userErrorParam();
        //以时间为键
        Map<LocalDate, List<ProduceInstructOrderEntity>> map = produceInstructOrderDao.selectList(new LambdaQueryWrapper<ProduceInstructOrderEntity>()
                        .between(timeFlag, ProduceInstructOrderEntity::getCreateTime, bTime, eTime))
                .stream().collect(Collectors.groupingBy(e -> e.getCreateTime().toLocalDate()));

        LocalDate startTime = queryForm.getStartTime();
        LocalDate endTime = queryForm.getEndTime();
        List<XyChartVO<String,Integer>> result = new ArrayList<>();

        while(startTime.until(endTime, ChronoUnit.DAYS)>=0){
            String start = startTime.toString();
            if(map.get(startTime)!=null) {
                //若某天有数据
                Integer num = map.get(startTime).size();
                result.add(new XyChartVO<>(start,num));
            }else {
                //若某天没数据则赋值0
                result.add(new XyChartVO<>(start,0));
            }
            startTime = startTime.plusDays(1);
        }
        return ResponseDTO.ok(result);
    }

    /**
     * @param queryForm 时间范围
     * @return 指定时间范围指令单各优先级数量
     */
    public ResponseDTO<List<XyChartVO<String, Integer>>> priorityNum(ProduceInstructOrderStatsQueryForm queryForm){
        boolean timeFlag = queryForm.getStartTime()!=null && queryForm.getEndTime()!=null;
        LocalDateTime bTime =null;
        LocalDateTime eTime = null;
        if(timeFlag){
            bTime = queryForm.getStartTime().atStartOfDay();
            eTime = queryForm.getEndTime().atTime(LocalTime.MAX);
        }

        Map<String, List<ProduceInstructOrderEntity>> map = produceInstructOrderDao.selectList(new LambdaQueryWrapper<ProduceInstructOrderEntity>()
                .between(timeFlag, ProduceInstructOrderEntity::getCreateTime, bTime, eTime))
                .stream()
                .collect(Collectors.groupingBy(ProduceInstructOrderEntity::getPriority));

        List<XyChartVO<String,Integer>> points = new ArrayList<>();
        for (ProduceInstructOrderPrioityEnum value : ProduceInstructOrderPrioityEnum.values()) {
            if (CollectionUtil.isNotEmpty(map) && map.containsKey(value.getValue())){
                int num = map.get(value.getValue()).size();
                points.add(new XyChartVO<>(value.getDesc(),num));
            }else {
                //查询不到赋值为0
                points.add(new XyChartVO<>(value.getDesc(), 0));
            }
        }
        return ResponseDTO.ok(points);
    }

    /**
     *
     * @param queryForm 时间范围
     * @return 个人下达指令单数量
     */
    public ResponseDTO<Long> getSelfInsNum(ProduceInstructOrderStatsQueryForm queryForm) {
        boolean timeFlag = queryForm.getStartTime()!=null && queryForm.getEndTime()!=null;
        LocalDateTime bTime =null;
        LocalDateTime eTime = null;
        if(timeFlag){
            bTime = queryForm.getStartTime().atStartOfDay();
            eTime = queryForm.getEndTime().atTime(LocalTime.MAX);
        }
        Long workerId = SmartRequestUtil.getRequestUserId();
        LambdaQueryWrapper<ProduceInstructOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(timeFlag,ProduceInstructOrderEntity::getCreateTime,bTime,eTime);
        wrapper.eq(ProduceInstructOrderEntity::getIssuerId,workerId);
        wrapper.ne(ProduceInstructOrderEntity::getProduceStatus,ProduceInstructOrderProduceStatusEnum.PLAN.getValue());
        Long result = produceInstructOrderDao.selectCount(wrapper);
        return ResponseDTO.ok(result);
    }

    /**
     *
     * @param queryForm 时间范围
     * @return 个人创建指令单数量
     */
    public ResponseDTO<Long> getSelfCreNum(ProduceInstructOrderStatsQueryForm queryForm) {
        boolean timeFlag = queryForm.getStartTime()!=null && queryForm.getEndTime()!=null;
        LocalDateTime bTime =null;
        LocalDateTime eTime = null;
        if(timeFlag){
            bTime = queryForm.getStartTime().atStartOfDay();
            eTime = queryForm.getEndTime().atTime(LocalTime.MAX);
        }
        String creator = SmartRequestUtil.getRequestUser().getUserName();
        LambdaQueryWrapper<ProduceInstructOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(timeFlag,ProduceInstructOrderEntity::getCreateTime,bTime,eTime);
        wrapper.eq(ProduceInstructOrderEntity::getCreateBy,creator);
        Long result = produceInstructOrderDao.selectCount(wrapper);
        return ResponseDTO.ok(result);
    }

    /**
     * 查询下达单数
     * @param queryForm 时间范围
     * @return 时间范围内下达单数
     */
    public ResponseDTO<Long> getIssuedNum(ProduceInstructOrderStatsQueryForm queryForm) {
        boolean timeFlag = queryForm.getStartTime()!=null && queryForm.getEndTime()!=null;
        LocalDateTime bTime =null;
        LocalDateTime eTime = null;
        if(timeFlag){
            bTime = queryForm.getStartTime().atStartOfDay();
            eTime = queryForm.getEndTime().atTime(LocalTime.MAX);
        }
        LambdaQueryWrapper<ProduceInstructOrderEntity> wrapper = new LambdaQueryWrapper<ProduceInstructOrderEntity>()
                .between(timeFlag,ProduceInstructOrderEntity::getIssuedTime,bTime,eTime);
        Long count = produceInstructOrderDao.selectCount(wrapper);
        return ResponseDTO.ok(count);
    }
}
