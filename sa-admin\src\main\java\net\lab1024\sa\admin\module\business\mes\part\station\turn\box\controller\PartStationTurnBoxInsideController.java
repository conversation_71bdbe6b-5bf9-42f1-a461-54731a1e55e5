package net.lab1024.sa.admin.module.business.mes.part.station.turn.box.controller;

import net.lab1024.sa.admin.module.business.mes.part.station.setting.annotation.PartStationStoreManageMode;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.constant.StorageManageModeConfigEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form.PartStationTurnBoxInsideForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form.PartStationTurnBoxInsideQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form.PartStationTurnBoxQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.vo.PartStationTurnBoxInsideVO;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.service.PartStationTurnBoxInsideService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 周转箱内容 Controller
 *
 * <AUTHOR>
 * @Date 2025-03-14 15:03:20
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "周转箱内容")
public class PartStationTurnBoxInsideController {

    @Resource
    private PartStationTurnBoxInsideService partStationTurnBoxInsideService;

    /**
     * 箱内菲票
     * @param form
     * @return
     */
    @Operation(summary = "箱内菲票")
    @PostMapping("/partStationTurnBoxInside/queryList")
    public ResponseDTO<List<PartStationTurnBoxInsideVO>> queryList(@RequestBody @Valid PartStationTurnBoxInsideForm form) {
        return partStationTurnBoxInsideService.queryList(form);
    }

    /**
     * 入箱
     * @param form
     * @return
     */
    @Operation(summary = "入箱 @cjm")
    @PostMapping("/partStationTurnBoxInside/inToBox")
    public ResponseDTO<String> inBox(@RequestBody @Valid PartStationTurnBoxInsideForm form) {
        return partStationTurnBoxInsideService.inBox(form);
    }

    /**
     * 出箱
     * @param form
     * @return
     */
    @Operation(summary = "出箱 @cjm")
    @PostMapping("/partStationTurnBoxInside/outToBox")
    public ResponseDTO<String> outBox(@RequestBody @Valid PartStationTurnBoxInsideForm form) {
        return partStationTurnBoxInsideService.outBox(form);
    }

    /**
     * 移箱
     * @param form
     * @return
     */
    @Operation(summary = "移箱 @cjm")
    @PostMapping("/partStationTurnBoxInside/moveToBox")
    public ResponseDTO<String> moveBox(@RequestBody @Valid PartStationTurnBoxInsideForm form) {
        return partStationTurnBoxInsideService.moveBox(form);
    }

}
