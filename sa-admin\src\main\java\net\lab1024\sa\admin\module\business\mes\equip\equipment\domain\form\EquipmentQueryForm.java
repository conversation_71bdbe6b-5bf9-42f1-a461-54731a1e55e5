package net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form;

import net.lab1024.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.annotation.Resource;

/**
 * 设备 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:12:13
 * @Copyright zscbdic
 */

@Data
public class EquipmentQueryForm extends PageParam{

    @Schema(description = "关键字查询")
    private String queryKey;

    @Schema(description = "车间")
    private Long workshopId;

    @Schema(description = "设备类别")
    private Long typeId;

    @Schema(description = "物联网平台")
    private String iotNetworkPlatform;

}
