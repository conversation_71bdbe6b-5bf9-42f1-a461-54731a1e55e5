package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 指令单成衣信息 新建表单
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:25:32
 * @Copyright zscbdic
 */

@Data
public class ProduceInstructOrderClothesAddForm {

    @NotNull(message = "物料ID 不能为空")
    @Schema(description = "物料ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long itemId;

    @Schema(description = "款式颜色", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "款式颜色 不能为空")
    private String styleColor;

    @Schema(description = "尺码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "尺码 不能为空")
    private String size;

    @Schema(description = "数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数量 不能为空")
    @Min(value = 1, message = "数量不能小于1")
    private Integer num;

}
