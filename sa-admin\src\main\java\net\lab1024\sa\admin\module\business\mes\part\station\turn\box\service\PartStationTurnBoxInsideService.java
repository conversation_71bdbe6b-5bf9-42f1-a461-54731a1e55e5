package net.lab1024.sa.admin.module.business.mes.part.station.turn.box.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.lab1024.sa.admin.event.tailor.FeTicketTraceLogEvent;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.manager.PartStationInventoryManager;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.constant.StorageManageModeConfigEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.manager.PartStationConfigManager;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.constant.TurnBoxOptTypeEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxInsideDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxInsideEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form.PartStationTurnBoxInsideForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form.PartStationTurnBoxInsideQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.vo.PartStationTurnBoxInsideVO;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.manager.PartStationTurnBoxInsideManager;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.manager.PartStationTurnBoxManager;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationBinDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationBinEntity;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.constant.FeTicketTraceLogTemplateEnum;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.dao.FeTicketDao;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.domain.entity.FeTicketEntity;
import net.lab1024.sa.base.common.code.ErrorCode;
import net.lab1024.sa.base.common.code.UserErrorCode;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 周转箱内容 Service
 *
 * <AUTHOR>
 * @Date 2025-03-14 15:03:20
 * @Copyright zscbdic
 */

@Service
public class PartStationTurnBoxInsideService {

    @Resource
    private PartStationTurnBoxInsideDao partStationTurnBoxInsideDao;

    @Resource
    private PartStationTurnBoxInsideManager partStationTurnBoxInsideManager;

    @Resource
    private FeTicketDao feTicketDao;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private PartStationTurnBoxManager partStationTurnBoxManager;

    @Resource
    private PartStationInventoryManager partStationInventoryManager;

    @Resource
    private PartStationBinDao partStationBinDao;

    @Resource
    private PartStationConfigManager partStationConfigManager;


    /**
     * 入箱
     *
     * @param form
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> inBox(PartStationTurnBoxInsideForm form) {
        Long turnBoxId = form.getTurnBoxId();
        Long feTicketId = form.getFeTicketId();
        partStationTurnBoxInsideManager.checkTurnBoxIdAndFeTicketId(form);

        PartStationTurnBoxInsideEntity inside = partStationTurnBoxInsideDao.selectOne(new LambdaQueryWrapper<PartStationTurnBoxInsideEntity>()
                .eq(PartStationTurnBoxInsideEntity::getFeTicketId, feTicketId));

        if (inside == null) {
            //放入周转箱内
            PartStationTurnBoxInsideEntity entity = new PartStationTurnBoxInsideEntity();
            entity.setTurnBoxId(turnBoxId);
            entity.setFeTicketId(feTicketId);
            entity.setInTime(LocalDateTime.now());
            partStationTurnBoxInsideDao.insert(entity);

            PartStationTurnBoxEntity box = partStationTurnBoxManager.getById(turnBoxId);
            String optDesc = String.format("裁片放入至 周转箱【%s】", box.getNumber());

            //如果周转箱为入库状态并且启用周转箱模式，则入库
            if (box.getInsideFlag() && StorageManageModeConfigEnum.TURN_BOX_MANAGE_MODE.getValue().equals(partStationConfigManager.getStorageManageModeConfig().getManageMode())) {
                PartStationBinEntity bin = partStationBinDao.selectById(box.getInsideLocationId());
                optDesc = String.format("裁片放入 周转箱【%s】 入库到 货位【%s】", box.getNumber(), bin.getBinCode());
                partStationInventoryManager.sockIn(feTicketId, bin.getId(), optDesc);
            }

            // 发布事件
            FeTicketTraceLogEvent.Payload payload = new FeTicketTraceLogEvent.Payload(feTicketId, FeTicketTraceLogTemplateEnum.PART_TURN_BOX_IN.getContent(), optDesc
                    , null, null, null);
            eventPublisher.publishEvent(new FeTicketTraceLogEvent(this, List.of(payload)));

            return ResponseDTO.ok("入箱成功");
        }

        if (inside.getTurnBoxId().equals(turnBoxId)) {
            return ResponseDTO.ok("已入箱成功，请勿重复扫描");
        }

        return new ResponseDTO<>(UserErrorCode.PARAM_ERROR, false, "菲票已入其他箱", TurnBoxOptTypeEnum.MOVE.getValue());

    }

    /**
     * 出箱
     *
     * @param form
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> outBox(PartStationTurnBoxInsideForm form) {
        Long turnBoxId = form.getTurnBoxId();
        Long feTicketId = form.getFeTicketId();
        partStationTurnBoxInsideManager.checkTurnBoxIdAndFeTicketId(form);

        PartStationTurnBoxInsideEntity entity = partStationTurnBoxInsideDao.selectOne(new LambdaQueryWrapper<PartStationTurnBoxInsideEntity>()
                .eq(PartStationTurnBoxInsideEntity::getFeTicketId, feTicketId));
        if (entity == null) {
            return new ResponseDTO<>(UserErrorCode.PARAM_ERROR, false, "菲票未入箱", null);
        }
        if (!entity.getTurnBoxId().equals(turnBoxId)) {
            return new ResponseDTO<>(UserErrorCode.PARAM_ERROR, false, "菲票未入本周转箱,请检查", TurnBoxOptTypeEnum.OUT.getValue());
        }
        //出箱
        PartStationTurnBoxEntity box = partStationTurnBoxManager.getById(turnBoxId);
        String optDesc = String.format("裁片已从 周转箱【%s】 取出", box.getNumber());
        partStationTurnBoxInsideDao.delete(new LambdaQueryWrapper<PartStationTurnBoxInsideEntity>()
                .eq(PartStationTurnBoxInsideEntity::getFeTicketId, feTicketId));

        //如果周转箱为入库状态并且启用周转箱模式，则出库
        if (box.getInsideFlag() && StorageManageModeConfigEnum.TURN_BOX_MANAGE_MODE.getValue().equals(partStationConfigManager.getStorageManageModeConfig().getManageMode())) {
            PartStationBinEntity bin = partStationBinDao.selectById(box.getInsideLocationId());
            optDesc = String.format("裁片已从 周转箱【%s】 取出并从 货位【%s】 出库 ", box.getNumber(), bin.getBinCode());
            partStationInventoryManager.stockOut(feTicketId, bin.getId(), optDesc);
        }

        // 发布事件
        FeTicketTraceLogEvent.Payload payload = new FeTicketTraceLogEvent.Payload(feTicketId, FeTicketTraceLogTemplateEnum.PART_TURN_BOX_IN.getContent(), optDesc
                , null, null, null);
        eventPublisher.publishEvent(new FeTicketTraceLogEvent(this, List.of(payload)));

        return ResponseDTO.ok("出箱成功");
    }

    /**
     * 移箱
     *
     * @param form
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> moveBox(PartStationTurnBoxInsideForm form) {
        Long turnBoxId = form.getTurnBoxId();
        Long feTicketId = form.getFeTicketId();
        partStationTurnBoxInsideManager.checkTurnBoxIdAndFeTicketId(form);

        PartStationTurnBoxInsideEntity entity = partStationTurnBoxInsideDao.selectOne(new LambdaQueryWrapper<PartStationTurnBoxInsideEntity>()
                .eq(PartStationTurnBoxInsideEntity::getFeTicketId, feTicketId));
        if (entity == null) {
            return new ResponseDTO<>(UserErrorCode.PARAM_ERROR, false, "菲票未入箱", TurnBoxOptTypeEnum.IN.getValue());
        }
        long oldTurnBoxId = entity.getTurnBoxId();
        long newTurnBoxId = turnBoxId;
        entity.setTurnBoxId(turnBoxId);
        entity.setInTime(LocalDateTime.now());
        partStationTurnBoxInsideDao.updateById(entity);

        PartStationTurnBoxEntity newBox = partStationTurnBoxManager.getById(turnBoxId);
        PartStationTurnBoxEntity oldBox = partStationTurnBoxManager.getById(oldTurnBoxId);
        String optDesc = String.format("裁片移动至 周转箱【%s】", newBox.getNumber());

        //如果启用周转箱模式
        if (StorageManageModeConfigEnum.TURN_BOX_MANAGE_MODE.getValue().equals(partStationConfigManager.getStorageManageModeConfig().getManageMode())) {
            if (!oldBox.getInsideFlag() && newBox.getInsideFlag()) {
                //无入库箱到有入库箱，进行入库操作
                PartStationBinEntity bin = partStationBinDao.selectById(newBox.getInsideLocationId());
                optDesc = String.format("裁片移动至 周转箱【%s】 入库到 货位【%s】", newBox.getNumber(), bin.getBinCode());
                partStationInventoryManager.sockIn(feTicketId, bin.getId(), optDesc);
            } else if (oldBox.getInsideFlag() && !newBox.getInsideFlag()) {
                //有入库箱到无入库箱，进行出库操作
                PartStationBinEntity bin = partStationBinDao.selectById(oldBox.getInsideLocationId());
                optDesc = String.format("裁片移动至 周转箱【%s】 从 货位【%s】 出库", oldBox.getNumber(), bin.getBinCode());
                partStationInventoryManager.stockOut(feTicketId, bin.getId(), optDesc);
            } else if (oldBox.getInsideFlag() && newBox.getInsideFlag()) {
                //有入库箱到有入库箱，进行移库操作
                PartStationBinEntity bin = partStationBinDao.selectById(newBox.getInsideLocationId());
                optDesc = String.format("裁片移动至 周转箱【%s】 移库到 货位【%s】", newBox.getNumber(), bin.getBinCode());
                partStationInventoryManager.stockMove(feTicketId, bin.getId(), optDesc);
            }
        }


        // 发布事件
        FeTicketTraceLogEvent.Payload payload = new FeTicketTraceLogEvent.Payload(feTicketId, FeTicketTraceLogTemplateEnum.PART_TURN_BOX_IN.getContent(), optDesc
                , null, null, null);
        eventPublisher.publishEvent(new FeTicketTraceLogEvent(this, List.of(payload)));

        return ResponseDTO.ok("移箱成功");
    }

    /**
     * 查询列表
     *
     * @param form
     * @return
     */
    public ResponseDTO<List<PartStationTurnBoxInsideVO>> queryList(PartStationTurnBoxInsideForm form) {
        Long turnBoxId = form.getTurnBoxId();
        List<PartStationTurnBoxInsideEntity> list = partStationTurnBoxInsideDao.selectList(new LambdaQueryWrapper<PartStationTurnBoxInsideEntity>()
                .eq(PartStationTurnBoxInsideEntity::getTurnBoxId, turnBoxId));
        if (CollUtil.isEmpty(list)) {
            return ResponseDTO.ok(Collections.emptyList());
        }
        List<Long> ticketIds = list.stream().map(PartStationTurnBoxInsideEntity::getFeTicketId).toList();
        List<FeTicketEntity> tickets = feTicketDao.selectBatchIds(ticketIds);
        if (CollUtil.isEmpty(tickets)) {
            return ResponseDTO.ok(Collections.emptyList());
        }

        Map<Long, LocalDateTime> inTimeMap = list.stream()
                .collect(Collectors.toMap(PartStationTurnBoxInsideEntity::getFeTicketId, PartStationTurnBoxInsideEntity::getInTime));
        List<PartStationTurnBoxInsideVO> vos = tickets.stream().map(t -> {
            PartStationTurnBoxInsideVO vo = SmartBeanUtil.copy(t, PartStationTurnBoxInsideVO.class);
            vo.setInTime(inTimeMap.get(t.getId()));
            return vo;
        }).collect(Collectors.toList());
        return ResponseDTO.ok(vos);
    }
}
