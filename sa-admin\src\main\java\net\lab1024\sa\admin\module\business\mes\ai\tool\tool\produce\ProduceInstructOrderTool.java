package net.lab1024.sa.admin.module.business.mes.ai.tool.tool.produce;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonClassDescription;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.mes.ai.tool.tool.constant.ToolNameConstant;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderVO;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.manager.ProduceInstructOrderManager;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.service.ProduceInstructOrderService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Slf4j
@Component(ToolNameConstant.PRODUCE_INSTRUCT_ORDER_TOOL)
public class ProduceInstructOrderTool {

    @Resource
    private ProduceInstructOrderManager produceInstructOrderManager;

    @Resource
    private ProduceInstructOrderService produceInstructOrderService;

    @Tool(description = "查询获取生产订单（生产指令单）数据，查询时间范围若没有提供则默认查询近20天")
    public List<OrderResponse> queryProduceInstructOrder(ToolContext toolContext) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(20);
        LocalDateTime endTime = LocalDateTime.now();
        List<ProduceInstructOrderEntity> orders = produceInstructOrderManager.lambdaQuery()
                .between(ProduceInstructOrderEntity::getCreateTime, startTime, endTime)
                .list();
        if (CollectionUtils.isEmpty(orders)) {
            return Collections.emptyList();
        }

        return SmartBeanUtil.copyList(orders, OrderResponse.class);
    }

    @Tool(description ="""
            查询获取生产订单（生产指令单）详情数据，包含生产产品信息，工序信息，生产安排信息，生产用料信息
            以下是回答要求
            分模块结构化呈现：基础信息（编号、数量、客户等）、原材料状态（需列明每种原材料的计划用量、当前实际到位量、是否齐套、短缺量）、生产步骤进度（按计划顺序列出各步骤，明确计划起止时间、当前完成进度（如 “已完成 80%”“未开始”）、是否滞后计划）、当前核心进度（整体生产进度百分比、已完成关键步骤及占比）；
            突出关键指标：用加粗或项目符号标注 “整体进度”“滞后步骤”“物料短缺风险” 等核心信息；
            补充执行细节：对 “查花片（中片2）” 说明该步骤的总工作量（如 “查花片需完成中片50，当前完成 2 片，完成 4%”），明确当前完成度；
            提示潜在问题：若存在步骤滞后、物料未齐套等情况，简要说明可能影响后续生产的风险（如 “面料未齐套，可能导致裁剪工序滞后 1 天”）。”
            """)
    public OrderResponse queryProduceInstructOrderDetail(ToolContext toolContext,
                                                         @ToolParam(description = "生产指令单编号") String instructNumber) {
        ProduceInstructOrderEntity order = produceInstructOrderManager
                .lambdaQuery()
                .eq(ProduceInstructOrderEntity::getInstructNumber, instructNumber)
                .last("LIMIT 1")
                .one();
        if (order == null) {
            return null;
        }
        ResponseDTO<ProduceInstructOrderVO> voResponseDTO = produceInstructOrderService.get(order.getId());
        OrderResponse orderResponse = SmartBeanUtil.copy(voResponseDTO.getData(), OrderResponse.class);

        List<OrderProcessResponse> processResponse = SmartBeanUtil.copyList(voResponseDTO.getData().getProcessList(), OrderProcessResponse.class);
        orderResponse.setOrderProcess(processResponse);

        List<OrderItemResponse> itemResponses = SmartBeanUtil.copyList(voResponseDTO.getData().getItemList(), OrderItemResponse.class);
        orderResponse.setOrderItems(itemResponses);

        List<OrderClothesResponse> clothesResponses = SmartBeanUtil.copyList(voResponseDTO.getData().getClothesList(), OrderClothesResponse.class);
        orderResponse.setOrderClothes(clothesResponses);

        List<OrderArrangeResponse> arrangeResponses = SmartBeanUtil.copyList(voResponseDTO.getData().getArrangeList(), OrderArrangeResponse.class);
        orderResponse.setOrderArrange(arrangeResponses);
        return orderResponse;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonClassDescription(value = "生产指令单（生产订单）记录")
    public static class OrderResponse {

        /**
         * 主键
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "主键ID")
        private Long id;

        /**
         * 创建时间
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "创建时间")
        private LocalDateTime createTime;

        /**
         * 创建人
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "创建人")
        private String createBy;

        /**
         * 更新时间
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "更新时间")
        private LocalDateTime updateTime;

        /**
         * 更新人
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "更新人")
        private String updateBy;

        /**
         * 备注
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "备注")
        private String remark;

        /**
         * 单据编号
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "单据编号")
        private String instructNumber;

        /**
         * 生产数量
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "计划生产数量")
        private Integer produceNum;

        /**
         * 完成数量
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "已完成数量")
        private Integer finishNum;

        /**
         * 客户id
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "客户id")
        private Long customerId;

        /**
         * 客户名称
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "客户名称")
        private String customerName;

        /**
         * 图片地址
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "图片地址")
        private String imgUrl;

        /**
         * 计划开工日期
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "计划开工日期")
        private LocalDate planStartTime;

        /**
         * 计划完工日期
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "计划完工日期")
        private LocalDate planFinishTime;

        /**
         * 实际开工日期
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "实际开工日期")
        private LocalDateTime realStartTime;

        /**
         * 实际完工日期
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "实际完工日期")
        private LocalDateTime realFinishTime;

        /**
         * 生产业务状态;0计划，1下达，2开工，3完工
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "生产业务状态;0代表计划状态，1代表下达状态，2代表开工状态，3代表完工状态")
        private String produceStatus;

        /**
         * 优先级;0一般,1紧急,2非常紧急
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "优先级;0代表一般,1代表紧急,2代表非常紧急")
        private String priority;

        /**
         * 交货日期
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "交货日期")
        private LocalDate deliverTime;

        /**
         * 下达日期
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "下达日期")
        private LocalDateTime issuedTime;

        /**
         * 物料id
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "物料id")
        private Long itemId;

        /**
         * 物料编号
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "物料编号")
        private String itemNumber;

        /**
         * 规格型号
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "规格型号")
        private String model;

        /**
         * 物料名称
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "物料名称")
        private String itemName;


        /**
         * 单位id
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "单位id")
        private Long unitId;

        /**
         * 单位名称
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "单位名称")
        private String unitName;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "生产指令单（生产订单）用料清单")
        private List<OrderItemResponse> orderItems;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "生产指令单（生产订单）生产产品清单")
        private List<OrderClothesResponse> orderClothes;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "生产指令单（生产订单）任务安排清单")
        private List<OrderArrangeResponse> orderArrange;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "生产指令单（生产订单）工序清单")
        private List<OrderProcessResponse> orderProcess;


    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonClassDescription(value = "生产指令单（生产订单）用料清单")
    public static class OrderItemResponse {

        /**
         * 用料物料id
         */
        private Long itemId;

        /**
         * 用料物料名称
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "用料物料名称")
        private String itemName;

        /**
         * 用料物料spu编号
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "用料物料spu编号")
        private String itemNumber;

        /**
         * 用料物料sku编号
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "用料物料sku编号")
        private String itemSkuNumber;

        /**
         * 用料物料规格型号
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "用料物料规格型号")
        private String itemModel;

        /**
         * 用料物料单位id
         */
        private Long itemUnitId;

        /**
         * 用料物料单位
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "用料物料单位")
        private String itemUnitName;

        /**
         * 用料数量
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "用料数量")
        private Double itemNum;

        /**
         * 单位用量
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "单位用量")
        private Double dosage;

        /**
         * 损耗率
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "损耗率")
        private Double loss;

        /**
         * 总用量
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "总用量")
        private Double totalDosage;

        /**
         * 发料数量
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "发料数量")
        private Double giveQty;

        /**
         * 退料数量
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "退料数量")
        private Double backQty;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonClassDescription(value = "生产指令单（生产订单）生产产品清单")
    public static class OrderClothesResponse {

        private Long itemId;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "物料名称")
        private String itemName;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "物料spu编号（款号）")
        private String itemNumber;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "物料sku编号")
        private String itemSkuNumber;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "颜色")
        private String styleColor;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "尺码")
        private String size;

        private Long unitId;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "单位")
        private String unitName;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "数量")
        private Integer num;

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonClassDescription(value = "生产指令单（生产订单）任务安排清单")
    public static class OrderArrangeResponse {

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "序号")
        private Integer serialNumber;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "节点名称")
        private String nodeName;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "负责人名称")
        private String headName;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "计划开始时间")
        private LocalDate planBeginTime;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "计划结束时间")
        private LocalDate planEndTime;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "实际开始时间")
        private LocalDateTime realBeginTime;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "实际结束时间")
        private LocalDateTime realEndTime;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "完成人名称")
        private String finishName;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "完成标识;false未完成，true完成")
        private Boolean finishFlag;

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonClassDescription(value = "生产指令单（生产订单）工序清单")
    public static class OrderProcessResponse {

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "序号")
        private Integer serialNumber;

        private Long processId;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "工序名称")
        private String name;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "部位")
        private String position;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "单价")
        private BigDecimal unitPrice1;

        private Long workshopId;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "车间名称")
        private String workshopName;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "末道工序;false否 true是")
        private Boolean endFlag;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "是否审核;false否 true是")
        private Boolean auditFlag;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "是否超数生产;false否 true是")
        private Boolean overflowWorkFlag;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "工序控制;0自制 1委外 2不限")
        private String processControl;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "应生产数量")
        private Integer shouldNum;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonPropertyDescription(value = "完成数量")
        private Integer finishNum;

    }

}
