package net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

import jakarta.validation.Valid;

/**
 * 生产安排信息 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-22 18:44:38
 * @Copyright zscbdic
 */

@Data
public class ProduceArrangeVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "详细生产安排")
    private List<ProduceArrangeDetailVO> produceArrangeDetailVOS;

}
