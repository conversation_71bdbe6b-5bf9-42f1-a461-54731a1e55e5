package net.lab1024.sa.admin.module.business.mes.stock.inventory.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.dao.StkInventoryDao;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.entity.StkInventoryEntity;
import net.lab1024.sa.base.common.util.SmartBeanUtil;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 即时库存  Manager
 *
 * <AUTHOR>
 * @Date 2025-01-14 15:20:23
 * @Copyright zscbdic
 */
@Service
public class StkInventoryManager extends ServiceImpl<StkInventoryDao, StkInventoryEntity> {

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private StkInventoryDao stkInventoryDao;

    /**
     * 获取库存信息
     * @param stkInventory
     * @param initInvFlag 是否初始化库存
     */
    public StkInventoryEntity getInventoryData(StkInventoryEntity stkInventory,boolean initInvFlag) {
        Long warehouseId = stkInventory.getWarehouseId();
        Long locationId = stkInventory.getLocationId();
        Long materielId = stkInventory.getMaterielId();
        Long lotId = stkInventory.getLotId();
        Long snId = stkInventory.getSnId();
        String ownerType = stkInventory.getOwnerType();
        Long ownerId = stkInventory.getOwnerId();
        Long unitId = stkInventory.getUnitId();
        if (StrUtil.isEmpty(ownerType)) {
            if (ownerId != null) {
                throw new RuntimeException("当 ownerType 为空时，ownerId 也必须为空");
            }
        } else {
            if (ownerId == null) {
                throw new RuntimeException("当 ownerType 不为空时，ownerId 也必须不为空");
            }
        }
        StkInventoryEntity inventory = this.lambdaQuery()
                .eq(StkInventoryEntity::getWarehouseId, warehouseId)
                .eq(locationId != null, StkInventoryEntity::getLocationId, locationId)
                .eq(StkInventoryEntity::getMaterielId, materielId)
                .eq(lotId != null, StkInventoryEntity::getLotId, lotId)
                .eq(snId != null, StkInventoryEntity::getSnId, snId)
                .eq(StrUtil.isNotEmpty(ownerType), StkInventoryEntity::getOwnerType, ownerType)
                .eq(ownerId != null, StkInventoryEntity::getOwnerId, ownerId)
                .eq(StkInventoryEntity::getUnitId, unitId)
                .last("limit 1")
                .one();
        if (inventory != null) {
            //说明已经存在库存记录了
            return inventory;
        }

        StkInventoryEntity entity = SmartBeanUtil.copy(stkInventory, StkInventoryEntity.class);
        entity.setId(null);
        entity.setQty(BigDecimal.ZERO);
        entity.setAvbQty(BigDecimal.ZERO);
        entity.setLockQty(BigDecimal.ZERO);
        entity.setPredictQty(BigDecimal.ZERO);
        if(initInvFlag){
            this.save(entity);
        }
        return entity;
    }


    public String generateInventoryKey(StkInventoryEntity inv) {
        // 返回生成的 key
        return inv.getWarehouseId() + "|" +
                (inv.getLocationId() != null ? inv.getLocationId() : "NULL") + "|" +
                inv.getMaterielId() + "|" +
                (inv.getLotId() != null ? inv.getLotId() : "NULL") + "|" +
                (inv.getSnId() != null ? inv.getSnId() : "NULL") + "|" +
                (StrUtil.isNotEmpty(inv.getOwnerType()) ? inv.getOwnerType() : "NULL") + "|" +
                (inv.getOwnerId() != null ? inv.getOwnerId() : "NULL") + "|" +
                inv.getUnitId();
    }

    /**
     * 批量更新库存
     *
     * @param updateDataList
     * @param addOrSub       true:增加，false:减少
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateInventory(List<StkInventoryEntity> updateDataList, boolean addOrSub) {

        //获取库存信息
        Map<String, StkInventoryEntity> invMap = updateDataList.stream()
                .map(data -> getInventoryData(data, true))
                .collect(Collectors.toMap(this::generateInventoryKey, e -> e,(e, r)->e));

        List<StkInventoryEntity> inventoryList = updateDataList.stream().map(data -> {

            StkInventoryEntity inventory = invMap.get(generateInventoryKey(data));
//            StkInventoryEntity inventory = getInventoryData(data,true);

            boolean add = true;
            //库存数
            if (addOrSub == add) {
                inventory.setQty(inventory.getQty().add(data.getQty()));
            } else {
                inventory.setQty(inventory.getQty().subtract(data.getQty()));
            }

            //可用库存
            if (data.getAvbQty() != null) {
                if (addOrSub == add) {
                    inventory.setAvbQty(inventory.getAvbQty().add(data.getAvbQty()));
                } else {
                    inventory.setAvbQty(inventory.getAvbQty().subtract(data.getAvbQty()));
                }
            }

            //锁定库存
            if (data.getLockQty() != null) {
                if (addOrSub == add) {
                    inventory.setLockQty(inventory.getLockQty().add(data.getLockQty()));
                } else {
                    inventory.setLockQty(inventory.getLockQty().subtract(data.getLockQty()));
                }
            }
            //预测库存
            if (data.getPredictQty() != null) {
                if (addOrSub == add) {
                    inventory.setPredictQty(inventory.getPredictQty().add(data.getPredictQty()));
                } else {
                    inventory.setPredictQty(inventory.getPredictQty().subtract(data.getPredictQty()));
                }
            }
            return inventory;
        }).collect(Collectors.toList());

        // 删除库存为0的数据
        List<StkInventoryEntity> zeroQtyList = inventoryList.stream()
                .filter(e -> e.getQty().compareTo(BigDecimal.ZERO) == 0)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(zeroQtyList)) {
            this.removeBatchByIds(zeroQtyList.stream().map(StkInventoryEntity::getId).collect(Collectors.toList()));
        }
        //并更新库存不为0的数据
        List<StkInventoryEntity> nonZeroQtyList = inventoryList.stream()
                .filter(e -> e.getQty().compareTo(BigDecimal.ZERO) != 0)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(nonZeroQtyList)) {
            this.updateBatchById(nonZeroQtyList);
        }

    }

    /**
     * 合并仓位库存
     *
     * @param warehouseId
     */
    @Transactional(rollbackFor = Exception.class)
    public void mergeLocationInventory(Long warehouseId) {
        List<StkInventoryEntity> mergeList = stkInventoryDao.queryMergeLocationInventory(warehouseId);
        if (CollUtil.isEmpty(mergeList)) {
            return;
        }
        //清空仓库库存
        this.lambdaUpdate().eq(StkInventoryEntity::getWarehouseId, warehouseId).remove();
        //批量保存
        this.saveBatch(mergeList);
    }


    /**
     * 合并物料批次库存
     *
     * @param materielId
     */
    @Transactional(rollbackFor = Exception.class)
    public void mergeLotInventory(Long materielId) {
        List<StkInventoryEntity> mergeList = stkInventoryDao.queryMergeLotInventory(materielId);
        if (CollUtil.isEmpty(mergeList)) {
            return;
        }
        //清空仓库库存
        this.lambdaUpdate().eq(StkInventoryEntity::getMaterielId, materielId).remove();
        //批量保存
        this.saveBatch(mergeList);
    }



}
