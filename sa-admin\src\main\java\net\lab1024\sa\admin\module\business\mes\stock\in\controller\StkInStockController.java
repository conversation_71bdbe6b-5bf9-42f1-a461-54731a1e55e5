package net.lab1024.sa.admin.module.business.mes.stock.in.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.vo.StkPrintCodeVO;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;
import net.lab1024.sa.admin.module.business.mes.stock.in.service.StkInStockService;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * 入库单 Controller
 *
 * <AUTHOR>
 * @Date 2025-02-03 15:54:07
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class StkInStockController {

    @Resource
    private StkInStockService stkInStockService;



//    @Operation(summary = "批量删除 <AUTHOR>
//    @PostMapping("/stkInStock/batchDelete")
//    public ResponseDTO<String> delete(@RequestBody ValidateList<Long> idList) {
//        return stkInStockService.batchDelete(idList);
//    }

    /**
     * 获取标签打印数据
     * @param id 入库单id
     * @return
     */
    @Operation(summary = "标签打印数据 <AUTHOR>
    @GetMapping("/stkInStock/getPrintCode/{id}")
    public ResponseDTO<List<StkPrintCodeVO>> getPrintCode(@PathVariable Long id) {
        return ResponseDTO.ok(stkInStockService.getPrintCode(id));
    }


}
