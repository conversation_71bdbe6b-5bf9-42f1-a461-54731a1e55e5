package net.lab1024.sa.admin.module.business.mes.ai.setting.controller;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.ai.setting.domain.form.ChatRouteConfigUpdateForm;
import net.lab1024.sa.admin.module.business.mes.ai.setting.domain.vo.ChatRouteConfigVO;
import net.lab1024.sa.admin.module.business.mes.ai.setting.service.ChatRouteConfigService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 大模型路由配置
 */
@RestController
public class ChatRouteConfigController {

    @Resource
    private ChatRouteConfigService chatRouteConfigService;


    /**
     * 大模型路由配置-获取
     * @return
     */
    @Operation(summary = "大模型路由配置-获取")
    @GetMapping("/ai/llm/chatRouteConfig/get")
    public ResponseDTO<ChatRouteConfigVO> getConfig() {
        return ResponseDTO.ok(chatRouteConfigService.getConfig());
    }

    /**
     * 大模型路由配置-更新
     * @param form
     * @return
     */
    @Operation(summary = "大模型路由配置-获取")
    @PostMapping("/ai/llm/chatRouteConfig/update")
    public ResponseDTO<String> updateConfig(@RequestBody @Valid ChatRouteConfigUpdateForm form) {
        chatRouteConfigService.updateConfig(form);
        return ResponseDTO.ok();
    }

}
