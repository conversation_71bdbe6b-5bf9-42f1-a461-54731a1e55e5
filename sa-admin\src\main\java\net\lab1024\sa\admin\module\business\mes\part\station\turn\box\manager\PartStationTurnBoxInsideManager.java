package net.lab1024.sa.admin.module.business.mes.part.station.turn.box.manager;

import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxInsideEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxInsideDao;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form.PartStationTurnBoxInsideForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationBinDao;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.dao.FeTicketDao;
import net.lab1024.sa.base.common.exception.BusinessException;
import org.springframework.stereotype.Service;

/**
 * 周转箱内容  Manager
 *
 * <AUTHOR>
 * @Date 2025-03-14 15:03:20
 * @Copyright zscbdic
 */
@Service
public class PartStationTurnBoxInsideManager extends ServiceImpl<PartStationTurnBoxInsideDao, PartStationTurnBoxInsideEntity> {

    @Resource
    private FeTicketDao feTicketDao;

    @Resource
    private PartStationBinDao partStationBinDao;

    @Resource
    private PartStationTurnBoxDao partStationTurnBoxDao;

    public void checkTurnBoxIdAndFeTicketId(PartStationTurnBoxInsideForm form) {
        Long turnBoxId = form.getTurnBoxId();
        Long feTicketId = form.getFeTicketId();

        if (turnBoxId == null) {
            throw new BusinessException("周转箱ID不能为空");
        } else {
            if (partStationTurnBoxDao.selectById(turnBoxId) == null) {
                throw new BusinessException("周转箱不存在");
            }
        }

        if (feTicketId == null) {
            throw new BusinessException("菲票ID不能为空");
        } else {
            if (feTicketDao.selectById(feTicketId) == null) {
                throw new BusinessException("菲票不存在");
            }
        }
    }
}
