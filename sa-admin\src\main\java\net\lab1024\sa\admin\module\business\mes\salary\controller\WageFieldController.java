package net.lab1024.sa.admin.module.business.mes.salary.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.WageFieldAddForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.WageFieldQueryForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.WageFieldUpdateForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.vo.WageFieldVO;
import net.lab1024.sa.admin.module.business.mes.salary.service.WageFieldService;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 工资字段 Controller
 *
 * <AUTHOR>
 * @Date 2024-11-25 20:20:26
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class WageFieldController {

    @Resource
    private WageFieldService wageFieldService;

    /**
     * 查询列表
     * @return
     */
    @PostMapping("/wageField/queryList")
    public ResponseDTO<List<WageFieldVO>> queryList() {
        return wageFieldService.queryList();
    }


    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/wageField/add")
    public ResponseDTO<String> add(@RequestBody @Valid WageFieldAddForm addForm) {
        return wageFieldService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/wageField/update")
    public ResponseDTO<String> update(@RequestBody @Valid WageFieldUpdateForm updateForm) {
        return wageFieldService.update(updateForm);
    }

    /**
     * 单个删除
      * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/wageField/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return wageFieldService.delete(id);
    }
}
