package net.lab1024.sa.admin.module.business.mes.process.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.process.domain.entity.ProcessLibraryEntity;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessLibraryQueryForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.vo.ProcessLibraryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 工序库 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-13 15:19:57
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface ProcessLibraryDao extends BaseMapper<ProcessLibraryEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<ProcessLibraryVO> queryPage(Page page, @Param("queryForm") ProcessLibraryQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */

}
