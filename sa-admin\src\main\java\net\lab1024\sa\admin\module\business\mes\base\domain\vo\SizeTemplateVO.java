package net.lab1024.sa.admin.module.business.mes.base.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 尺寸模板表 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-05 15:45:35
 * @Copyright zscbdic
 */

@Data
public class SizeTemplateVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识；0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "尺码编号（如：001上衣、002衬衫等）")
    private String sizeCode;

    @Schema(description = "尺码模板名称")
    private String templateName;

    @Schema(description = "尺码标准（如：国际标准GB/T2664-2001等）")
    private String standard;


}
