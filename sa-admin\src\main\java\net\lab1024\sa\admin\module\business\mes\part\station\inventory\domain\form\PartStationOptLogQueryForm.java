package net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

/**
 * 裁片驿站操作日志 分页查询表单
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:47:06
 * @Copyright zscbdic
 */

@Data
public class PartStationOptLogQueryForm extends PageParam{

    @Schema(description = "指令单编号")
    private String instructOrderNumber;

    @Schema(description = "物料编号")
    private String itemNumber;

    @Schema(description = "操作类型")
        private String optType;

    @Schema(description = "操作人id")
    private Long operatorId;


}
