package net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

@Getter
@AllArgsConstructor
public enum TurnTaskTypeEnum implements BaseEnum {

    /**
     * 运送至车间
     */
    TO_WORKSHOP("TO_WORKSHOP", "运送至车间"),

    /**
     * 运送至驿站
     */
    TO_PART_STATION("TO_PART_STATION", "运送至驿站"),
    ;


    /**
     * 根据value获取枚举
     *
     * @param value
     * @return
     */
    public static TurnTaskTypeEnum getEnum(String value) {
        for (TurnTaskTypeEnum turnTaskTypeEnum : TurnTaskTypeEnum.values()) {
            if (turnTaskTypeEnum.getValue().equals(value)) {
                return turnTaskTypeEnum;
            }
        }
        return null;
    }

    private String value;

    private String desc;


}
