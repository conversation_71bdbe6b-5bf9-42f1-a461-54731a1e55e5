package net.lab1024.sa.admin.module.business.mes.base.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.PartAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.PartQuery;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.PartQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.PartUpdateForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.PartVO;
import net.lab1024.sa.admin.module.business.mes.base.service.PartService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 部位表 Controller
 *
 * <AUTHOR>
 * @Date 2025-06-23 19:56:55
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "部位表")
public class PartController {

    @Resource
    private PartService partService;

    /**
     * 列表查询
     * @param query
     * @return
     */
    @Operation(summary = "列表查询")
    @PostMapping("/part/queryList")
    public ResponseDTO<List<PartVO>> queryList(@RequestBody @Valid PartQuery query){
        return ResponseDTO.ok(partService.queryList(query));
    }


    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/part/queryPage")

    //    @SaCheckPermission("part:query")
    public ResponseDTO<PageResult<PartVO>> queryPage(@RequestBody @Valid PartQueryForm queryForm) {
        return ResponseDTO.ok(partService.queryPage(queryForm));
    }

    /**
     * 添加
     *
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/part/add")
//    @SaCheckPermission("part:add")
    public ResponseDTO<String> add(@RequestBody @Valid PartAddForm addForm) {
        return partService.add(addForm);
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/part/update")
//    @SaCheckPermission("part:update")
    public ResponseDTO<String> update(@RequestBody @Valid PartUpdateForm updateForm) {
        return partService.update(updateForm);
    }

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    @Operation(summary = "批量删除 <AUTHOR>
    @PostMapping("/part/batchDelete")
//    @SaCheckPermission("part:delete")
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> idList) {
        return partService.batchDelete(idList);
    }

    /**
     * 单个删除
     *
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/part/delete/{id}")
//    @SaCheckPermission("part:delete")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return partService.delete(id);
    }


}
