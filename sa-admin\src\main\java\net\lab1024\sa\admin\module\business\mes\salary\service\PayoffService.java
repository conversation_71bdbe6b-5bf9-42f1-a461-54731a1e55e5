package net.lab1024.sa.admin.module.business.mes.salary.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.salary.constant.ComputeTypeEnum;
import net.lab1024.sa.admin.module.business.mes.salary.dao.PayoffDao;
import net.lab1024.sa.admin.module.business.mes.salary.dao.SettlementRecordDao;
import net.lab1024.sa.admin.module.business.mes.salary.dao.SettlementRecordDetailDao;
import net.lab1024.sa.admin.module.business.mes.salary.dao.WageFieldDao;
import net.lab1024.sa.admin.module.business.mes.salary.domain.bo.PayoffBo;
import net.lab1024.sa.admin.module.business.mes.salary.domain.dto.PayoffDto;
import net.lab1024.sa.admin.module.business.mes.salary.domain.dto.WageFieldValueDto;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.PayoffRecordDetailEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.PayoffRecordEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.SettlementRecordDetailEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.SettlementRecordEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.PayoffForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.PayoffQueryForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.vo.PayoffVO;
import net.lab1024.sa.admin.module.business.mes.salary.manager.PayOffManager;
import net.lab1024.sa.admin.module.business.mes.work.dao.WorkRecordDao;
import net.lab1024.sa.admin.module.business.mes.work.domain.entity.WorkRecordEntity;
import net.lab1024.sa.admin.module.business.mes.work.domain.vo.WorkRecordVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PayoffService {

    @Resource
    private PayoffDao payoffDao;

    @Resource
    private WageFieldDao wageFieldDao;

    @Resource
    private PayOffManager payOffManager;

    @Resource
    private SettlementRecordDao settlementRecordDao;

    @Resource
    private SettlementRecordDetailDao settlementRecordDetailDao;

    @Resource
    private WorkRecordDao workRecordDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<PayoffVO> queryPage(PayoffQueryForm queryForm) {
        // 查询计件分页
        queryForm.setPayoffFlag(false);
        Long pageNum = queryForm.getPageNum();
        Long pageSize = queryForm.getPageSize();
        Long offset = (pageNum - 1) * pageSize;
        Long total = payoffDao.queryPageCount(queryForm);
        List<PayoffDto> list = payoffDao.queryPage(queryForm, offset, pageSize);
        if (CollUtil.isEmpty(list)) {
            SmartPageUtil.emptyPage(queryForm.getPageNum(), queryForm.getPageSize());
        }

        List<Long> employeeIds = list.stream()
                .map(PayoffDto::getEmployeeId)
                .collect(Collectors.toList());

        // 检查并更新工资项值
        payOffManager.checkAndUpdateWageFieldValue(employeeIds);

        // 查询自定义工资项值
        List<WageFieldValueDto> fieldValues = wageFieldDao.queryWageFieldValue(employeeIds);
        if (CollUtil.isEmpty(fieldValues)) {
            // 没有自定义工资项
            PageResult<PayoffVO> result = new PageResult<>();
            List<PayoffVO> vos = SmartBeanUtil.copyList(list, PayoffVO.class);
            vos.forEach(vo -> {
                vo.setTotalSalary(vo.getCountSalary());
                vo.setBelongMonth(queryForm.getBelongMonth());
            });
            result.setList(vos);
            result.setPageNum(pageNum);
            result.setPageSize(pageSize);
            result.setTotal(total);
            return result;
        }
        //有自定义工资项
        Map<Long, List<WageFieldValueDto>> fieldValueMap = fieldValues.stream()
                .collect(Collectors.groupingBy(WageFieldValueDto::getEmployeeId));
        List<PayoffVO> vos = SmartBeanUtil.copyList(list, PayoffVO.class);
        vos.forEach(vo -> {
            List<WageFieldValueDto> fieldValueList = fieldValueMap.get(vo.getEmployeeId());
            vo.setWageFieldValues(fieldValueList);
            // 计算总工资
            BigDecimal totalSalary = vo.getCountSalary();
            for (WageFieldValueDto fv : fieldValueList) {
                if (ComputeTypeEnum.ADD.getValue().equals(fv.getType())) {
                    totalSalary = totalSalary.add(fv.getFieldValue());
                } else if (ComputeTypeEnum.SUBTRACT.getValue().equals(fv.getType())) {
                    totalSalary = totalSalary.subtract(fv.getFieldValue());
                }
            }
            vo.setBelongMonth(queryForm.getBelongMonth());
            vo.setTotalSalary(totalSalary);
        });

        PageResult<PayoffVO> result = new PageResult<>();
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);
        result.setTotal(total);
        result.setList(vos);
        return result;
    }

    public ResponseDTO<String> payoff(PayoffForm payoffForm) {
        List<SettlementRecordEntity> settlementRecords = settlementRecordDao.selectList(new LambdaQueryWrapper<SettlementRecordEntity>()
                .in(SettlementRecordEntity::getEmployeeId, payoffForm.getEmployeeIds())
                .eq(SettlementRecordEntity::getBelongMonth, payoffForm.getBelongMonth())
                .eq(SettlementRecordEntity::getPayoffFlag, false));
        if (CollUtil.isEmpty(settlementRecords)) {
            return ResponseDTO.userErrorParam("暂无可发放结算记录");
        }
        // 按员工分组
        Map<Long, List<SettlementRecordEntity>> settlementRecordMap = settlementRecords.stream()
                .collect(Collectors.groupingBy(SettlementRecordEntity::getEmployeeId));
        List<Long> settRecordIds = settlementRecords.stream()
                .map(SettlementRecordEntity::getId).collect(Collectors.toList());


        // 检查并更新工资项值
        payOffManager.checkAndUpdateWageFieldValue(payoffForm.getEmployeeIds());

        // 查询自定义工资项值
        List<WageFieldValueDto> fieldValues = wageFieldDao.queryWageFieldValue(payoffForm.getEmployeeIds());
        Map<Long, List<WageFieldValueDto>> fieldValueMap;
        if (CollUtil.isNotEmpty(fieldValues)) {
            fieldValueMap = fieldValues.stream()
                    .collect(Collectors.groupingBy(WageFieldValueDto::getEmployeeId));
        } else {
            fieldValueMap = null;
        }

        PayoffBo payoffBo = new PayoffBo();
        ArrayList<PayoffBo.RecordBo> recordBos = new ArrayList<>();
        for (Long employeeId : payoffForm.getEmployeeIds()) {
            List<SettlementRecordEntity> settRecord = settlementRecordMap.getOrDefault(employeeId, CollUtil.newArrayList());
            // 发放记录明细
            List<PayoffRecordDetailEntity> details = settRecord.stream().map(s -> {
                PayoffRecordDetailEntity detail = new PayoffRecordDetailEntity();
                detail.setSettlementRecordId(s.getId());
                return detail;
            }).collect(Collectors.toList());
            // 发放记录
            PayoffRecordEntity record = new PayoffRecordEntity();
            record.setEmployeeId(employeeId);
            record.setActualName(settRecord.get(0).getActualName());
            record.setBelongMonth(payoffForm.getBelongMonth());
            record.setPayoffTime(LocalDateTime.now());
            record.setRemark(payoffForm.getRemark());
            // 计件总金额
            BigDecimal pieceAmount = settRecord.stream().map(SettlementRecordEntity::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            record.setPieceAmount(pieceAmount);

            if (CollUtil.isNotEmpty(fieldValueMap) && fieldValueMap.containsKey(employeeId)) {
                //如果有自定义工资项
                List<WageFieldValueDto> fvs = fieldValueMap.get(employeeId);
                BigDecimal otherAmount = BigDecimal.ZERO;
                for (WageFieldValueDto fv : fvs) {
                    if (ComputeTypeEnum.ADD.getValue().equals(fv.getType())) {
                        otherAmount = otherAmount.add(fv.getFieldValue());
                    } else if (ComputeTypeEnum.SUBTRACT.getValue().equals(fv.getType())) {
                        otherAmount = otherAmount.subtract(fv.getFieldValue());
                    }
                }
                record.setOtherAmount(otherAmount);
                record.setTotalAmount(pieceAmount.add(otherAmount));
                record.setOtherAmountData(JSONObject.toJSONString(fvs));
            } else {
                // 没有自定义工资项
                record.setOtherAmount(BigDecimal.ZERO);
                record.setOtherAmountData(JSONObject.toJSONString(Collections.EMPTY_LIST));
                record.setTotalAmount(pieceAmount);
            }

            recordBos.add(new PayoffBo.RecordBo(record, details));
        }

        payoffBo.setPayoffRecords(recordBos);
        payoffBo.setSettlementRecordIds(settRecordIds);

        payOffManager.savePayoff(payoffBo);
        return ResponseDTO.ok();
    }

    /**
     * 查询工资发放明细
     *
     * @param queryForm
     * @param employeeId
     * @return
     */
    public PageResult<WorkRecordVO> queryDetails(PayoffQueryForm queryForm, Long employeeId) {
        Date belongMonth = queryForm.getBelongMonth();
        //查询符合条件的结算记录
        List<SettlementRecordEntity> settlementRecord = settlementRecordDao.selectList(new LambdaQueryWrapper<SettlementRecordEntity>()
                .eq(SettlementRecordEntity::getEmployeeId, employeeId)
                .eq(SettlementRecordEntity::getBelongMonth, belongMonth)
                .eq(SettlementRecordEntity::getPayoffFlag, false));
        if (CollUtil.isEmpty(settlementRecord)) {
            return SmartPageUtil.emptyPage(queryForm.getPageNum(), queryForm.getPageSize());
        }
        List<Long> settlementRecordIds = settlementRecord.stream()
                .map(SettlementRecordEntity::getId)
                .collect(Collectors.toList());

        //查询结算记录明细
        List<SettlementRecordDetailEntity> recordDetails = settlementRecordDetailDao.selectList(new LambdaQueryWrapper<SettlementRecordDetailEntity>()
                .in(SettlementRecordDetailEntity::getMainId, settlementRecordIds));
        if (CollUtil.isEmpty(recordDetails)) {
            return SmartPageUtil.emptyPage(queryForm.getPageNum(), queryForm.getPageSize());
        }

        //查询报工记录
        List<Long> workRecordIds = recordDetails.stream()
                .map(SettlementRecordDetailEntity::getWorkRecordId)
                .collect(Collectors.toList());
        List<WorkRecordEntity> workRecords = workRecordDao.selectBatchIds(workRecordIds);
        List<WorkRecordVO> workRecordVOS = SmartBeanUtil.copyList(workRecords, WorkRecordVO.class);

        return SmartPageUtil.subListPage(
                queryForm.getPageNum().intValue(),
                queryForm.getPageSize().intValue(),
                workRecordVOS);
    }
}
