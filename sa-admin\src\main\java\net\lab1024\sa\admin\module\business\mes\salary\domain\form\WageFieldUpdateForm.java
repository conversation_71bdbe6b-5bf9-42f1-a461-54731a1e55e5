package net.lab1024.sa.admin.module.business.mes.salary.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.salary.constant.ComputeTypeEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;

/**
 * 工资字段 更新表单
 *
 * <AUTHOR>
 * @Date 2024-11-25 20:20:26
 * @Copyright zscbdic
 */

@Data
public class WageFieldUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

    @Schema(description = "字段名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "字段名称 不能为空")
    private String fieldName;

    @CheckEnum(value = ComputeTypeEnum.class, message = "运算类型;0 加项,1减项 不能为空")
    @Schema(description = "运算类型;0 加项,1减项", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "运算类型;0 加项,1减项 不能为空")
    private String type;

}
