package net.lab1024.sa.admin.module.business.mes.stock.lot.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/* *
 * 批次追踪方向
 */
@Getter
@AllArgsConstructor
public enum StockDirectEnum implements BaseEnum {

    /**
     * 来源
     */
    COME("come", "来源"),

    /**
     * 去向
     */
    TO("to", "去向");


    private final String value;

    private final String desc;
}
