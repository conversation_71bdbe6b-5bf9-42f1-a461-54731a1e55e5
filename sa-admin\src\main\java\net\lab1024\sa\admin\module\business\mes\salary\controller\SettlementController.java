package net.lab1024.sa.admin.module.business.mes.salary.controller;

import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.SettlementForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.SettlementQueryForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.vo.SettlementVO;
import net.lab1024.sa.admin.module.business.mes.salary.service.SettlementService;
import net.lab1024.sa.admin.module.business.mes.work.domain.vo.WorkRecordVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 计件结算 Controller
 */
@RestController
@Tag(name = "结算接口")
public class SettlementController {

    @Resource
    private SettlementService settlementService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "结算分页 <AUTHOR>
    @PostMapping("/settlement/queryPage")
    public ResponseDTO<PageResult<SettlementVO>> queryPage(@Valid @RequestBody SettlementQueryForm queryForm){
        return settlementService.queryPage(queryForm);
    }

    /**
     * 明细
     * @param queryForm
     * @return
     */
    @Operation(summary = "结算明细 <AUTHOR>
    @PostMapping("/settlement/querySettlementDetails/{employeeId}")
    public ResponseDTO<PageResult<WorkRecordVO>> querySettlementDetails(@Valid @RequestBody SettlementQueryForm queryForm, @PathVariable("employeeId") Long employeeId){
        return settlementService.querySettlementDetails(queryForm,employeeId);
    }

    /**
     * 明细列表 pageNum pageSize 无作用
     * @param queryForm
     * @param employeeId
     * @return
     */
    @Operation(summary = "结算明细列表 <AUTHOR>
    @PostMapping("/settlement/querySettlementDetailList/{employeeId}")
    public ResponseDTO<List<WorkRecordVO>> querySettlementDetailList(@Valid @RequestBody SettlementQueryForm queryForm, @PathVariable("employeeId") Long employeeId){
    	return settlementService.querySettlementDetailList(queryForm,employeeId);
    }


    /**
     * 结算
     */
    @Operation(summary = "结算 <AUTHOR>
    @PostMapping("/settlement/settlement")
    public ResponseDTO<String> settlement(@Valid @RequestBody SettlementForm form){
        return settlementService.settlement(form);
    }

}
