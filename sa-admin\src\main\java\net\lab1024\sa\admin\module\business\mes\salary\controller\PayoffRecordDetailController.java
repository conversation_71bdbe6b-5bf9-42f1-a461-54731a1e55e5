package net.lab1024.sa.admin.module.business.mes.salary.controller;

import net.lab1024.sa.admin.module.business.mes.salary.service.PayoffRecordDetailService;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;

import jakarta.annotation.Resource;

/**
 * 薪酬发放记录详情 Controller
 *
 * <AUTHOR>
 * @Date 2024-12-03 21:37:54
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class PayoffRecordDetailController {

    @Resource
    private PayoffRecordDetailService payoffRecordDetailService;


}
