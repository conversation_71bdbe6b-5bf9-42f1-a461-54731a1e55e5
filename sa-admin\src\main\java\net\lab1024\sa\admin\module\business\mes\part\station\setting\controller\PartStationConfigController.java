package net.lab1024.sa.admin.module.business.mes.part.station.setting.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.domain.form.PartStationConfigForm;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.domain.vo.PartStationConfigVO;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.service.PartStationConfigService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 裁片驿站配置接口
 */
@RestController
@Tag(name = "")
public class PartStationConfigController {

    @Resource
    private PartStationConfigService partStationConfigService;

    /**
     * 查询配置
     * @return
     */
    @GetMapping("/partStationConfig/query")
    @Operation(summary = "查询配置 ")
    public ResponseDTO<PartStationConfigVO> query() {
        return partStationConfigService.query();
    }

    /**
     * 更新配置
     * @param form
     * @return
     */
    @PostMapping("/partStationConfig/update")
    @Operation(summary = "更新配置 ")
    public ResponseDTO<String> update(@RequestBody @Valid PartStationConfigForm form) {
        return partStationConfigService.update(form);
    }
}
