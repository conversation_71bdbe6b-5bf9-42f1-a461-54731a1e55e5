package net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 货架 新建表单
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:13:43
 * @Copyright zscbdic
 */

@Data
public class StkRackAddForm {

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "仓库id 不能为空")
    private Long warehouseId;

    /**
     * 货架编号
     */
    @Schema(description = "货架编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "货架编号 不能为空")
    private String number;

    /**
     * 位置描述
     */
    @Schema(description = "位置描述")
    private String location;

}
