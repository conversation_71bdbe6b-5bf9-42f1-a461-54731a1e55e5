package net.lab1024.sa.admin.module.business.mes.part.station.inventory.service;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.dao.PartStationOptLogDao;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.form.PartStationOptLogQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.vo.PartStationOptLogVO;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 裁片驿站操作日志 Service
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:47:06
 * @Copyright zscbdic
 */

@Service
public class PartStationOptLogService {

    @Resource
    private PartStationOptLogDao partStationOptLogDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<PartStationOptLogVO> queryPage(PartStationOptLogQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<PartStationOptLogVO> list = partStationOptLogDao.queryPage(page, queryForm);
        PageResult<PartStationOptLogVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
//    public ResponseDTO<String> add(PartStationOptLogAddForm addForm) {
//        PartStationOptLogEntity partStationOptLogEntity = SmartBeanUtil.copy(addForm, PartStationOptLogEntity.class);
//        partStationOptLogDao.insert(partStationOptLogEntity);
//        return ResponseDTO.ok();
//    }
//
//    /**
//     * 更新
//     *
//     * @param updateForm
//     * @return
//     */
//    public ResponseDTO<String> update(PartStationOptLogUpdateForm updateForm) {
//        PartStationOptLogEntity partStationOptLogEntity = SmartBeanUtil.copy(updateForm, PartStationOptLogEntity.class);
//        partStationOptLogDao.updateById(partStationOptLogEntity);
//        return ResponseDTO.ok();
//    }
//
//    /**
//     * 批量删除
//     *
//     * @param idList
//     * @return
//     */
//    public ResponseDTO<String> batchDelete(List<Long> idList) {
//        if (CollectionUtils.isEmpty(idList)){
//            return ResponseDTO.ok();
//        }
//
//        partStationOptLogDao.batchUpdateDeleted(idList, true);
//        return ResponseDTO.ok();
//    }
//
//    /**
//     * 单个删除
//     */
//    public ResponseDTO<String> delete(Long id) {
//        if (null == id){
//            return ResponseDTO.ok();
//        }
//
//        partStationOptLogDao.updateDeleted(id,true);
//        return ResponseDTO.ok();
//    }
}
