package net.lab1024.sa.admin.module.business.mes.item.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.item.constant.ItemAttributeEnum;
import net.lab1024.sa.admin.module.business.mes.item.dao.ItemClothesDao;
import net.lab1024.sa.admin.module.business.mes.item.dao.ItemDao;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemClothesEntity;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemEntity;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 成衣信息表  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-09 11:23:49
 * @Copyright zscbdic
 */
@Service
public class ItemClothesManager extends ServiceImpl<ItemClothesDao, ItemClothesEntity> {

    @Resource
    private ItemDao itemDao;

    @Resource
    private ItemClothesDao itemClothesDao;

    /**
     * 校验spu编号是否在辅料或布料中存在
     * @param spuNumber
     * @param excludeItemId
     */
    public void spuNumberCheck(String spuNumber) {
        Long count = itemDao.selectCount(new LambdaQueryWrapper<ItemEntity>()
                .eq(ItemEntity::getNumber, spuNumber)
                .in(ItemEntity::getAttribute, ItemAttributeEnum.CLOTH.getValue(), ItemAttributeEnum.OTHER.getValue()));
        if (count > 0) {
            throw new BusinessException("spu编号 " + spuNumber + " 已在辅料或布料中存在");
        }
    }


    /**
     * 删除成衣信息
     *
     * @param id
     * @param clothesId
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteClothes(Long id, Long clothesId) {
        itemDao.deleteById(id);
        itemClothesDao.deleteById(clothesId);
    }


    public void colorSizeCheck(String spuNumber, String styleColor, String size, Long excludeItemId) {
        List<ItemEntity> items = itemDao.selectList(new LambdaQueryWrapper<ItemEntity>()
                .eq(ItemEntity::getNumber, spuNumber)
                .select(ItemEntity::getId));
        if (CollUtil.isEmpty(items)) {
            return;
        }
        List<Long> ids = items.stream().map(ItemEntity::getId).collect(Collectors.toList());
        Long count = itemClothesDao.selectCount(new LambdaQueryWrapper<ItemClothesEntity>()
                .in(ItemClothesEntity::getItemId, ids)
                .eq(ItemClothesEntity::getStyleColor, styleColor)
                .eq(ItemClothesEntity::getSize, size)
                .ne(excludeItemId != null, ItemClothesEntity::getItemId, excludeItemId));
        if (count > 0) {
            throw new BusinessException(styleColor + size + "组合已存在");
        }
    }
}
