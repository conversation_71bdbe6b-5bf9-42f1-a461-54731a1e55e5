package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.item.constant.ItemAttributeEnum;
import net.lab1024.sa.admin.module.business.mes.item.constant.ItemCategoryEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 指令单用料信息 新建表单
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:24:54
 * @Copyright zscbdic
 */

@Data
public class ProduceInstructOrderItemAddForm {


    @Schema(description = "物料id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物料id 不能为空")
    private Long itemId;

    @Schema(description = "物料名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "物料名称 不能为空")
    private String itemName;

    @Schema(description = "物料编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "物料编号 不能为空")
    private String itemNumber;

    @Schema(description = "物料规格型号")
    private String itemModel;

    @Schema(description = "物料分类id")
    private Long itemTypeId;

    @Schema(description = "物料类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "物料类型 不能为空")
    @CheckEnum(value = ItemCategoryEnum.class, message = "类型;0半成品 1成品 值不合法", required = true)
    private String itemCategory;

    @Schema(description = "物料单位id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物料单位id 不能为空")
    private Long itemUnitId;

    /**
     * 物料单位
     */
    @Schema(description = "物料单位名称")
    @NotBlank(message = "物料单位名称 不能为空")
    private String itemUnitName;

    @Schema(description = "物料属性", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "物料属性 不能为空")
    @CheckEnum(value = ItemAttributeEnum.class, message = "属性;0面料，1其他，2成衣 值不合法", required = true)
    private String itemAttribute;


    /**
     * 用料数量
     */
    @NotNull(message = "用料数量 不能为空")
    @Min(value = 0, message = "用料数量不能小于0")
    @Schema(description = "用料数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Double itemNum;

    @Schema(description = "单位用量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "单位用量 不能为空")
    @Min(value = 0, message = "单位用量不能小于0")
    private Double dosage;

    /**
     * 损耗率
     */
    @Schema(description = "单位损耗率")
    @Min(value = 0, message = "单位损耗率不能小于0")
    @NotNull(message = "单位损耗率不能为空")
    private Double loss;

    /**
     * 总用量
     */
    @Schema(description = "总用量")
    @Min(value = 0, message = "总用量不能小于0")
    @NotNull(message = "总用量不能为空")
    private Double totalDosage;

}
