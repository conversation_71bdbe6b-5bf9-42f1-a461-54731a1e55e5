package net.lab1024.sa.admin.module.business.mes.ai.tool.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 大模型工具角色权限表 更新表单
 *
 * <AUTHOR>
 * @Date 2025-04-06 15:15:25
 * @Copyright zscbdic
 */

@Data
public class LLMToolRoleUpdateForm {

    /**
     * 角色id
     */
    @Schema(description = "角色id")
    @NotNull(message = "角色id不能为空")
    private Long roleId;

    /**
     * 菜单ID 集合
     */
    @Schema(description = "工具ID集合")
    @NotNull(message = "工具ID不能为空")
    private List<Long> tools;

}
