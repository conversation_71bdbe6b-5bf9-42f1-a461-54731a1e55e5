package net.lab1024.sa.admin.module.business.mes.base.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 尺码表 实体类
 *
 * <AUTHOR>
 * @Date 2024-07-05 22:37:21
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_size")
public class SizeEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 删除标识；0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 关联t_sizing_template
     */
    private Long templateId;

    /**
     * 尺码信息（如：S码、M码、L码等）
     */
    private String sizeMessage;

    /**
     * 备注
     */
    private String remark;

}
