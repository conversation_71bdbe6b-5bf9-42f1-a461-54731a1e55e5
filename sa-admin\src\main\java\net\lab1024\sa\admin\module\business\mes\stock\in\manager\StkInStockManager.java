package net.lab1024.sa.admin.module.business.mes.stock.in.manager;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockBillStatusEnum;
import net.lab1024.sa.admin.module.business.mes.stock.in.dao.StkInStockDao;
import net.lab1024.sa.admin.module.business.mes.stock.in.dao.StkInStockDetailDao;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.bo.StkInStockBO;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.entity.StkInStockDetailEntity;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.entity.StkInStockEntity;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 入库单  Manager
 *
 * <AUTHOR>
 * @Date 2025-02-03 15:54:07
 * @Copyright zscbdic
 */
@Service
public class StkInStockManager extends ServiceImpl<StkInStockDao, StkInStockEntity> {

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private StkInStockDao stkInStockDao;

    @Resource
    private StkInStockDetailDao stkInStockDetailDao;

    @Resource
    private StkInStockDetailManager stkInStockDetailManager;

    /**
     * 更新单据状态检查
     *
     * @param status
     */
    public void updateBillStatusCheck(String status) {
        if (StockBillStatusEnum.AUDIT.getValue().equals(status)) {
            throw new BusinessException("单据已审核");
        }
    }

    /**
     * 保存入库单
     *
     * @param bo
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBill(StkInStockBO bo) {
        StkInStockEntity stkInStock = bo.getStkInStock();
        List<StkInStockDetailEntity> details = bo.getDetails();

        stkInStockDao.insert(stkInStock);
        Long id = stkInStock.getId();
        details.forEach(detail -> {
            detail.setInStockId(id);
        });
        stkInStockDetailManager.saveBatch(details);
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateBill(StkInStockBO bo) {
        List<StkInStockDetailEntity> details = bo.getDetails();
        StkInStockEntity stkInStock = bo.getStkInStock();

        stkInStockDao.updateById(stkInStock);
        stkInStockDetailManager.lambdaUpdate()
                .eq(StkInStockDetailEntity::getInStockId, stkInStock.getId())
                .remove();
        details.forEach(detail -> {
            detail.setInStockId(stkInStock.getId());
        });
        stkInStockDetailManager.saveBatch(details);
    }


    /**
     * 删除入库单
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBill(Long id) {
        stkInStockDao.deleteById(id);
        stkInStockDetailManager.lambdaUpdate()
                .eq(StkInStockDetailEntity::getInStockId, id)
                .remove();
    }

    /**
     * 入库单编号检查
     *
     * @param number     入库单编号
     * @param excludedId 排除的id
     */
    public void checkNumber(String number, Long excludedId) {
        Long count = this.lambdaQuery().eq(StkInStockEntity::getNumber, number)
                .ne(excludedId != null, StkInStockEntity::getId, excludedId)
                .count();
        if (count > 0) {
            throw new BusinessException("入库单编号已存在");
        }
    }
}
