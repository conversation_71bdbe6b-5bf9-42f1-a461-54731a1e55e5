package net.lab1024.sa.admin.module.business.mes.salary.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.salary.domain.dto.WageFieldValueDto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class PayoffVO {

    /**
     * 员工id
     */
    private Long employeeId;

    /**
     * 员工姓名
     */
    private String actualName;

    /**
     * 工资归属月份
     */
    @JsonFormat(pattern = "yyyy-MM")
    private Date belongMonth;

    /**
     * 计件工资
     */
    private BigDecimal countSalary;

    /**
     * 总工资
     */
    private BigDecimal totalSalary;

    /**
     * 其他项
     */
    private List<WageFieldValueDto> wageFieldValues;
}
