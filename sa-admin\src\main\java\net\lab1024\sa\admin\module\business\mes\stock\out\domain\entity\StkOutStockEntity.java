package net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 出库单 实体类
 *
 * <AUTHOR>
 * @Date 2025-02-09 19:58:18
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_stk_out_stock")
public class StkOutStockEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 单据来源类型
     */
    private String originType;

    /**
     * 单据来源ID
     */
    private Long originId;

    /**
     * 单据来源编号
     */
    private String originNumber;

    /**
     * 单据类型
     */
    private String type;

    /**
     * 单据方式
     */
    private String way;

    /**
     * 单据状态;
     */
    private String status;

    /**
     * 单据编号
     */
    private String number;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 出库时间
     */
    private LocalDateTime outStockTime;

    /**
     * 仓管员ID
     */
    private Long stockerId;

    /**
     * 仓管员名称
     */
    private String stockerName;

    /**
     * 货主类型
     */
    private String ownerType;

    /**
     * 货主id
     */
    private Long ownerId;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 申请人ID
     */
    private Long applicantId;

    /**
     * 申请人名称
     */
    private String applicantName;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 审核人ID
     */
    private Long auditorId;

    /**
     * 审核人名称
     */
    private String auditorName;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

}
