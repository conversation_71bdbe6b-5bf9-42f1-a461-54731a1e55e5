package net.lab1024.sa.admin.module.business.mes.produce.instruct.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderClothesDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.dto.ProdiceInstructOrderSkuCheckDTO;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderClothesEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderClothesAddForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderClothesUpdateForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderClothesVO;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.common.util.SmartBeanUtil;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static net.lab1024.sa.admin.module.business.mes.base.constant.SizeAndColorConstant.SPLIT_CHAR;

/**
 * 指令单成衣信息  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:25:32
 * @Copyright zscbdic
 */
@Service
public class ProduceInstructOrderClothesManager extends ServiceImpl<ProduceInstructOrderClothesDao, ProduceInstructOrderClothesEntity> {

    @Resource
    private ProduceInstructOrderClothesDao produceInstructOrderClothesDao;

    /**
     * 颜色尺寸校验
     *
     * @param list
     * @param orderId
     * @return
     */
    public List<ProdiceInstructOrderSkuCheckDTO> colorSizeCheck(Long orderId, List<ProdiceInstructOrderSkuCheckDTO> list) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        //间隔符号
        String separator = "&";

        // 颜色尺寸去重
        List<ProdiceInstructOrderSkuCheckDTO> uniqueList = new ArrayList<>(list.stream()
                .collect(Collectors.toMap(
                        dto -> dto.getColor() + SPLIT_CHAR + dto.getSize(),
                        dto -> dto,
                        (existing, replacement) -> existing
                ))
                .values());

        // 查询数据库
        LambdaQueryWrapper<ProduceInstructOrderClothesEntity> lq = new LambdaQueryWrapper<>();
        lq.eq(ProduceInstructOrderClothesEntity::getOrderId, orderId).and(q -> {
            for (ProdiceInstructOrderSkuCheckDTO dto : uniqueList) {
                q.or(w -> {
                    w.eq(ProduceInstructOrderClothesEntity::getStyleColor, dto.getColor())
                            .eq(ProduceInstructOrderClothesEntity::getSize, dto.getSize());
                });
            }
        });
        List<ProduceInstructOrderClothesEntity> clothes = produceInstructOrderClothesDao.selectList(lq);
        if (CollUtil.isEmpty(clothes)) {
            throw new BusinessException("请检查成衣颜色尺码信息");
        }
        //判断是否有不存在的颜色尺码组合
        Set<String> existingCombinations = clothes.stream()
                .map(entity -> entity.getStyleColor() + SPLIT_CHAR + entity.getSize())
                .collect(Collectors.toSet());

        for (ProdiceInstructOrderSkuCheckDTO dto : uniqueList) {
            if (!existingCombinations.contains(dto.getColor() + SPLIT_CHAR + dto.getSize())) {
                throw new BusinessException(dto.getColor() + " - " + dto.getSize() + "组合不存在");
            }
        }

        // 返回结果
        return clothes.stream()
                .map(e -> new ProdiceInstructOrderSkuCheckDTO(e.getItemId(), e.getStyleColor(), e.getSize()))
                .collect(Collectors.toList());
    }


    /**
     * 解析成衣信息
     *
     * @param clothesList
     * @return
     */
    public List<ProduceInstructOrderClothesEntity> parseAddForm(List<ProduceInstructOrderClothesAddForm> clothesList) {
        if (CollUtil.isEmpty(clothesList)) {
            return Collections.emptyList();
        }

        HashMap<String, Integer> map = new HashMap<>();

        for (ProduceInstructOrderClothesAddForm e : clothesList) {
            //判断数量
            if (e.getNum() == null || e.getNum() <= 0) {
                continue;
            }

            String size = e.getSize();
            String styleColor = e.getStyleColor();
            Long itemId = e.getItemId();
            String temp = size + SPLIT_CHAR + styleColor + SPLIT_CHAR + itemId;


            if (map.containsKey(temp)) {
                int num = map.get(temp);
                map.put(temp, num + e.getNum());
            } else {
                map.put(temp, e.getNum());
            }
        }

        List<ProduceInstructOrderClothesEntity> entities = map.entrySet().stream().map(e -> {
            String key = e.getKey();
            //分割字符串
            String[] split = key.split(SPLIT_CHAR);
            ProduceInstructOrderClothesEntity entity = new ProduceInstructOrderClothesEntity();
            entity.setSize(split[0]);
            entity.setStyleColor(split[1]);
            entity.setItemId(Long.valueOf(split[2]));
            entity.setNum(e.getValue());
            return entity;
        }).collect(Collectors.toList());

        return entities;
    }

    public List<ProduceInstructOrderClothesEntity> parseUpdateForm(List<ProduceInstructOrderClothesUpdateForm> clothesList) {
        if (CollUtil.isEmpty(clothesList)) {
            return Collections.emptyList();
        }


        HashMap<String, Integer> map = new HashMap<>();

        for (ProduceInstructOrderClothesUpdateForm e : clothesList) {
            if (e.getNum() == null || e.getNum() <= 0) {
                continue;
            }

            String size = e.getSize();
            String styleColor = e.getStyleColor();
            Long itemId = e.getItemId();
            String temp = size + SPLIT_CHAR + styleColor + SPLIT_CHAR + itemId;

            if (map.containsKey(temp)) {
                int num = map.get(temp);
                map.put(temp, num + e.getNum());
            } else {
                map.put(temp, e.getNum());
            }
        }

        List<ProduceInstructOrderClothesEntity> entities = map.entrySet().stream().map(e -> {
            String key = e.getKey();
            //分割字符串
            String[] split = key.split(SPLIT_CHAR);
            ProduceInstructOrderClothesEntity entity = new ProduceInstructOrderClothesEntity();
            entity.setSize(split[0]);
            entity.setStyleColor(split[1]);
            entity.setItemId(Long.valueOf(split[2]));
            entity.setNum(e.getValue());
            return entity;
        }).collect(Collectors.toList());

        return entities;
    }

    /**
     * 根据指令单id获取成衣信息
     *
     * @param orderId
     * @return
     */
    public List<ProduceInstructOrderClothesVO> getClothesInfo(Long orderId) {
        List<ProduceInstructOrderClothesEntity> clothesEntityList = this.lambdaQuery().eq(ProduceInstructOrderClothesEntity::getOrderId, orderId).list();
        if (CollUtil.isEmpty(clothesEntityList)) {
            return Collections.emptyList();
        }
        return SmartBeanUtil.copyList(clothesEntityList, ProduceInstructOrderClothesVO.class);
    }
}
