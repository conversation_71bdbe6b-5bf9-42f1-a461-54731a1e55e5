package net.lab1024.sa.admin.module.business.mes.part.match.controller;

import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.part.match.domain.entity.PartMatchEntity;
import net.lab1024.sa.admin.module.business.mes.part.match.domain.form.PartMatchForm;
import net.lab1024.sa.admin.module.business.mes.part.match.domain.form.PartMatchQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.match.domain.vo.PartMatchStatusListVo;
import net.lab1024.sa.admin.module.business.mes.part.match.domain.vo.PartMatchStatusVo;
import net.lab1024.sa.admin.module.business.mes.part.match.service.PartMatchService;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.form.ProduceArrangeQueryForm;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 裁片配扎情况 Controller
 *
 * <AUTHOR>
 * @Date 2024-11-07 15:17:40
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class PartMatchController {

    @Resource
    private PartMatchService partMatchService;


    /**
     * 配扎
     * @param partMatchForm
     * @return
     */
    @Operation(summary = "配扎")
    @PostMapping("/partMatch/match")
    public ResponseDTO<String> match(@RequestBody @Valid PartMatchForm partMatchForm) {
        return partMatchService.match(partMatchForm);
    }

    /**
     * 查询配扎情况
     * @param id
     * @return
     */
    @Operation(summary = "通过TicketId查询配扎情况")
    @GetMapping("/partMatch/queryStatus/byTicketId/{id}")
    public ResponseDTO<PartMatchStatusVo> queryStatusByTicketId(@PathVariable Long id) {
        return partMatchService.queryStatusByTicketId(id);
    }


    @Operation(summary = "查询配扎情况")
    @PostMapping("/partMatch/queryPage")
    public ResponseDTO<PageResult<PartMatchStatusListVo>> queryStatus(@RequestBody @Valid PartMatchQueryForm queryForm) {
        return ResponseDTO.ok(partMatchService.queryStatus(queryForm));
    }


}
