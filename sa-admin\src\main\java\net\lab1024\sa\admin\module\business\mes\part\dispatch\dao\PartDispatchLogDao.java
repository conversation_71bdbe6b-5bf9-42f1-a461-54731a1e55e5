package net.lab1024.sa.admin.module.business.mes.part.dispatch.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.entity.PartDispatchLogEntity;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.form.PartDispatchQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.vo.PartDispatchLogVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.vo.PartDispatchSummaryVO;

/**
 * 裁片收发日志 Dao
 *
 * <AUTHOR>
 * @Date 2024-11-06 15:25:13
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface PartDispatchLogDao extends BaseMapper<PartDispatchLogEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<PartDispatchLogVO> queryPage(Page page, @Param("queryForm") PartDispatchQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Integer id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Integer> idList,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 收发汇总分页
     * @param queryForm
     * @return
     */
    List<PartDispatchSummaryVO> querySummaryPage(@Param("queryForm") PartDispatchQueryForm queryForm, @Param("offset") Long offset, @Param("limit") Long limit);

    /**
     * 收发汇总总数
     * @param queryForm
     * @return
     */
    Long querySummaryTotal(@Param("queryForm") PartDispatchQueryForm queryForm);

    /**
     * 收发汇总详情
     * @param produceInstructOrderId
     * @return
     */
    List<PartDispatchSummaryVO.DetailVO> querySummaryDetail(@Param("produceInstructOrderId") Long produceInstructOrderId);
}
