package net.lab1024.sa.admin.module.business.mes.factory.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

/**
 * 生产小组 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:32:46
 * @Copyright zscbdic
 */

@Data
public class ProduceTeamVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "生产小组名称")
    private String teamName;

    @Schema(description = "负责人id")
    private Long leaderId;

    @Schema(description = "负责人名称")
    private String leader;

    @Schema(description = "车间id")
    private Long workshopId;

    @Schema(description ="车间名称")
    private String workshopName;

    @Schema(description = "小组成员信息")
    private List<ProduceTeamMemberVO> produceTeamMemberVOList;

}
