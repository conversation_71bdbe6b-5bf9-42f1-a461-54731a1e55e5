package net.lab1024.sa.admin.module.business.mes.produce.instruct.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.event.tailor.CutBedSheetAddEvent;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.constant.ProduceInstructOrderProduceStatusEnum;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.manager.ProduceInstructOrderManager;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;

@Slf4j
@Component("produceCutBedSheetListener")
public class CutBedSheetListener {

    @Resource
    private ProduceInstructOrderManager orderManager;

//    @Async(AsyncConfig.ASYNC_EXECUTOR_THREAD_NAME)
    @EventListener(CutBedSheetAddEvent.class)
    public void cutBedSheetAdd(CutBedSheetAddEvent cutBedSheetAddEvent) {
        Long produceInstructOrderId = cutBedSheetAddEvent.getProduceInstructOrderId();
        orderManager.lambdaUpdate()
                .eq(ProduceInstructOrderEntity::getId, produceInstructOrderId)
                .eq(ProduceInstructOrderEntity::getProduceStatus, ProduceInstructOrderProduceStatusEnum.ISSUED.getValue())
                .set(ProduceInstructOrderEntity::getProduceStatus, ProduceInstructOrderProduceStatusEnum.START.getValue())
                .set(ProduceInstructOrderEntity::getRealStartTime, LocalDateTime.now())
                .update();


    }
}
