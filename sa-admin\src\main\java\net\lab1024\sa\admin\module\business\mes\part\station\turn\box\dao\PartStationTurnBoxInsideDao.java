package net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxInsideEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form.PartStationTurnBoxInsideQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.vo.PartStationTurnBoxInsideVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 周转箱内容 Dao
 *
 * <AUTHOR>
 * @Date 2025-03-14 15:03:20
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface PartStationTurnBoxInsideDao extends BaseMapper<PartStationTurnBoxInsideEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<PartStationTurnBoxInsideVO> queryPage(Page page, @Param("queryForm") PartStationTurnBoxInsideQueryForm queryForm);

}
