package net.lab1024.sa.admin.event.produce;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 生产指令单安排完成事件
 */
@Getter
public class ProduceInstructOrderArrangeFinishEvent extends ApplicationEvent {

    /**
     * 安排id
     */
    private Long id;

    public ProduceInstructOrderArrangeFinishEvent(final Object source , Long id) {
        super(source);
        this.id = id;
    }


}
