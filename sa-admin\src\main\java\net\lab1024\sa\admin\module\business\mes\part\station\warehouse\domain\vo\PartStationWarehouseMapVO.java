package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 裁片仓库地图 列表VO
 *
 * <AUTHOR>
 * @Date 2024-11-05 16:55:45
 * @Copyright zscbdic
 */

@Data
public class PartStationWarehouseMapVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "仓库id")
    private Long warehouseId;

    @Schema(description = "地图数据字符串")
    private String mapDataStr;

    @Schema(description = "是否可见")
    private Boolean visibleFlag;
}
