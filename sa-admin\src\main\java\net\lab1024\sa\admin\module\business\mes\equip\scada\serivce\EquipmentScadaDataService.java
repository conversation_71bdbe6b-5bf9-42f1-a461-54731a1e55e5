package net.lab1024.sa.admin.module.business.mes.equip.scada.serivce;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.dao.EquipmentDao;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.dao.EquipmentScadaDao;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.entity.EquipmentScadaEntity;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentQueryForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.vo.EquipmentVO;
import net.lab1024.sa.admin.module.business.mes.equip.scada.constant.EquipmentScadaDataRedisConstant;
import net.lab1024.sa.admin.module.business.mes.equip.scada.domain.vo.EquipmentScadaDataVO;
import net.lab1024.sa.admin.module.business.mes.equip.scada.domain.vo.EquipmentScadaPropertyVO;
import net.lab1024.sa.admin.module.business.mes.equip.scada.manager.EquipmentScadaDataManager;
import net.lab1024.sa.admin.module.business.mes.equip.scada.strategy.ScadaDataStrategy;
import net.lab1024.sa.admin.module.business.mes.equip.scada.strategy.ScadaDataStrategyFactory;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class EquipmentScadaDataService {

    @Resource
    private EquipmentDao equipmentDao;

    @Resource
    private EquipmentScadaDao equipmentScadaDao;

    @Resource
    private ScadaDataStrategyFactory scadaDataStrategyFactory;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private EquipmentScadaDataManager equipmentScadaDataManager;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public ResponseDTO<PageResult<EquipmentScadaDataVO>> queryScadaDataPage(EquipmentQueryForm queryForm) {
        //查询设备列表
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<EquipmentVO> list = equipmentDao.queryOpenIotEquipIdsAndPlatform(page, queryForm);
        if (list == null) {
            return ResponseDTO.ok(SmartPageUtil.emptyPage(queryForm.getPageNum(), queryForm.getPageSize()));
        }

        List<EquipmentScadaDataVO> resultVOS = new ArrayList<>(list.size());

        // 按物联网平台分组
        Map<String, List<EquipmentVO>> equipPlatformMap = list.stream().
                collect(Collectors.groupingBy(EquipmentVO::getIotNetworkPlatform));

        // 遍历设备
        for (Map.Entry<String, List<EquipmentVO>> entry : equipPlatformMap.entrySet()) {
            String platform = entry.getKey();
            List<EquipmentVO> equips = entry.getValue();
            if (StrUtil.isEmpty(platform) || CollUtil.isEmpty(equips)) {
                // 跳过空平台或空设备
                continue;
            }
            // 获取物联网平台对应的策略
            ScadaDataStrategy strategy = scadaDataStrategyFactory.getStrategy(platform);
            if (strategy == null) {
                // 跳过不支持的物联网平台
                continue;
            }

            List<Long> equipsIds = equips.stream().map(EquipmentVO::getId).collect(Collectors.toList());
            List<EquipmentScadaDataVO> scadaData = strategy.queryScadaData(equipsIds);
            if (CollUtil.isEmpty(scadaData)) {
                // 跳过没有SCADA数据的设备
                continue;
            }
            resultVOS.addAll(scadaData);
        }

        if (CollUtil.isEmpty(resultVOS)) {
            return ResponseDTO.ok(SmartPageUtil.emptyPage(queryForm.getPageNum(), queryForm.getPageSize()));
        }

        PageResult<EquipmentScadaDataVO> pageResult = SmartPageUtil.convert2PageResult(page, resultVOS);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 查询SCADA属性
     *
     * @param equipId
     * @return
     */
    public ResponseDTO<EquipmentScadaPropertyVO> queryScadaProperties(Long equipId) {
        ScadaDataStrategy strategy = equipmentScadaDataManager.getScadaStrategyByEquipId(equipId);
        if (strategy == null) {
            return ResponseDTO.ok(null);
        }

        EquipmentScadaPropertyVO equipmentScadaPropertyVO = strategy.queryScadaProperties(equipId);
        return ResponseDTO.ok(equipmentScadaPropertyVO);
    }


    /**
     * 查询SCADA属性（对象格式）
     *
     * @param equipId
     * @return
     */
    public ResponseDTO<EquipmentScadaPropertyVO> queryScadaPropertiesObj(Long equipId) {
        ScadaDataStrategy strategy = equipmentScadaDataManager.getScadaStrategyByEquipId(equipId);
        if (strategy == null) {
            return ResponseDTO.ok(null);
        }

        EquipmentScadaPropertyVO vo = strategy.queryScadaPropertyObj(equipId);
        return ResponseDTO.ok(vo);
    }

    /**
     * 刷新SCADA数据
     *
     * @param equipId
     * @return
     */
    public ResponseDTO<EquipmentScadaDataVO> refreshScadaData(Long equipId) {
        // 删除缓存
        stringRedisTemplate.delete(EquipmentScadaDataRedisConstant.EQUIPMENT_SCADA_DATA_KEY + equipId);

        return this.queryScadaData(equipId);
    }


    /**
     * 查询SCADA数据
     *
     * @param equipId
     * @return
     */
    public ResponseDTO<EquipmentScadaDataVO> queryScadaData(Long equipId) {

        ScadaDataStrategy strategy = equipmentScadaDataManager.getScadaStrategyByEquipId(equipId);
        if (strategy == null) {
            return ResponseDTO.ok(null);
        }

        List<EquipmentScadaDataVO> vos = strategy.queryScadaData(CollUtil.newArrayList(equipId));
        if (CollUtil.isEmpty(vos)) {
            return ResponseDTO.ok(null);
        }
        return ResponseDTO.ok(vos.get(0));
    }


}
