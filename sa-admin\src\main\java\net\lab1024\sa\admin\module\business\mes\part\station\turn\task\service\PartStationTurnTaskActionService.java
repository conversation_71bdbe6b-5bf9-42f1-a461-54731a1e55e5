package net.lab1024.sa.admin.module.business.mes.part.station.turn.task.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.event.part.station.PartStationTurnTaskFinishEvent;
import net.lab1024.sa.admin.event.part.station.PartStationTurnTaskSubmitEvent;
import net.lab1024.sa.admin.module.business.mes.factory.dao.ProduceTeamDao;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.ProduceTeamEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxInsideDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxInsideEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskExecutorTypeEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskStatusEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskTypeEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.dao.PartStationTurnTaskDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.entity.PartStationTurnTaskEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.form.PartStationTurnTaskBeginForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.form.PartStationTurnTaskFinishForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.form.PartStationTurnTaskSumbitForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.manager.PartStationTurnTaskManager;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationBinDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationBinEntity;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.dao.FeTicketDao;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.domain.entity.FeTicketEntity;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
public class PartStationTurnTaskActionService {

    @Resource
    private PartStationTurnTaskDao partStationTurnTaskDao;

    @Resource
    private PartStationTurnTaskManager partStationTurnTaskManager;

    @Resource
    private PartStationBinDao partStationBinDao;

    @Resource
    private PartStationTurnBoxDao partStationTurnBoxDao;

    @Resource
    private PartStationTurnBoxInsideDao partStationTurnBoxInsideDao;

    @Resource
    private FeTicketDao feTicketDao;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private ProduceTeamDao produceTeamDao;

    /**
     * 提交任务
     *
     * @param form
     * @return
     */
    public ResponseDTO<Long> submitTask(PartStationTurnTaskSumbitForm form) {
        Long startLocationId = form.getStartLocationId();
        PartStationBinEntity startBin = partStationBinDao.selectById(startLocationId);
        if (startBin == null) {
            return ResponseDTO.userErrorParam("货位不存在");
        }
        Long turnoverBoxId = form.getTurnoverBoxId();
        PartStationTurnBoxEntity turnBox = partStationTurnBoxDao.selectById(turnoverBoxId);
        if (turnBox == null) {
            return ResponseDTO.userErrorParam("周转箱不存在");
        }
        // 运送至车间，校验生产小组
        if (TurnTaskTypeEnum.TO_WORKSHOP.getValue().equals(form.getType()) && form.getEndProduceTeamId() != null) {
            Long count = produceTeamDao.selectCount(new LambdaQueryWrapper<ProduceTeamEntity>().eq(ProduceTeamEntity::getId, form.getEndProduceTeamId()));
            if (count == 0) {
                return ResponseDTO.userErrorParam("生产小组不存在");
            }
        } else {
            form.setEndProduceTeamId(null);
        }

        // 校验任务编号唯一性
        partStationTurnTaskManager.checkNumber(form.getNumber());
        // 校验周转箱可用性
        partStationTurnTaskManager.checkBoxAvailable(turnoverBoxId);


        PartStationTurnTaskEntity task = SmartBeanUtil.copy(form, PartStationTurnTaskEntity.class);

        // 获取周转箱里面的 菲票ID
        task.setTicketQty(0);
        task.setPartQty(0);
        List<PartStationTurnBoxInsideEntity> boxInsides = partStationTurnBoxInsideDao.selectList(new LambdaQueryWrapper<PartStationTurnBoxInsideEntity>()
                .select(PartStationTurnBoxInsideEntity::getFeTicketId)
                .eq(PartStationTurnBoxInsideEntity::getTurnBoxId, turnoverBoxId));
        if (CollUtil.isNotEmpty(boxInsides)) {
            //记录 菲票 数量和裁片数
            List<Long> ticketIds = boxInsides.stream().map(PartStationTurnBoxInsideEntity::getFeTicketId).toList();
            task.setTicketIds(JSON.toJSONString(ticketIds));
            task.setTicketQty(boxInsides.size());
            List<FeTicketEntity> tickets = feTicketDao.selectList(new LambdaQueryWrapper<FeTicketEntity>()
                    .select(FeTicketEntity::getNum)
                    .in(FeTicketEntity::getId, ticketIds));
            if (CollUtil.isNotEmpty(tickets)) {
                task.setPartQty(tickets.stream().mapToInt(FeTicketEntity::getNum).sum());
            }
        } else {
            task.setTicketIds(JSON.toJSONString(Collections.emptyList()));
        }

        if (StrUtil.isBlank(task.getName())) {
            TurnTaskTypeEnum typeEnum = TurnTaskTypeEnum.getEnum(task.getType());
            if (typeEnum == null) {
                return ResponseDTO.userErrorParam("任务类型错误");
            }
            String name = String.format("周转箱 %s %s", turnBox.getNumber(), typeEnum.getDesc());
            task.setName(name);
        }

        partStationTurnTaskManager.save(task);

        eventPublisher.publishEvent(new PartStationTurnTaskSubmitEvent(this, task.getId()));

        return ResponseDTO.ok(task.getId());
    }

    /**
     * 取消任务
     *
     * @param idList
     * @return
     */
    public ResponseDTO<String> cancelTask(ValidateList<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return ResponseDTO.userErrorParam("参数错误");
        }
        partStationTurnTaskDao.update(new LambdaUpdateWrapper<PartStationTurnTaskEntity>()
                .ne(PartStationTurnTaskEntity::getStatus, TurnTaskStatusEnum.FINISH.getValue())
                .in(PartStationTurnTaskEntity::getId, idList)
                .set(PartStationTurnTaskEntity::getStatus, TurnTaskStatusEnum.CANCEL.getValue()));
        return ResponseDTO.ok("取消任务成功");
    }

    /**
     * 开始任务
     *
     * @param form
     * @return
     */
    public ResponseDTO<String> beginTask(PartStationTurnTaskBeginForm form) {
//        Long taskId = form.getTaskId();
        LocalDateTime now = LocalDateTime.now();
        RequestUser user = SmartRequestUtil.getRequestUser();

        //查询待领取或待开始任务
        PartStationTurnTaskEntity task = null;
        if (form.getTaskId() != null) {
            task = partStationTurnTaskDao.selectById(form.getTaskId());
            if (task == null) {
                return ResponseDTO.userErrorParam("任务不存在");
            }
            if (!(TurnTaskStatusEnum.WAIT_START.getValue().equals(task.getStatus()) || TurnTaskStatusEnum.WAIT_GET.getValue().equals(task.getStatus()))) {
                return ResponseDTO.userErrorParam("任务状态错误");
            }
            if (!Objects.equals(task.getTurnoverBoxId(), form.getTurnoverBoxId())) {
                return ResponseDTO.userErrorParam("非指定周转箱");
            }
        } else {
            task = partStationTurnTaskDao.selectOne(new LambdaQueryWrapper<PartStationTurnTaskEntity>()
                    .eq(PartStationTurnTaskEntity::getTurnoverBoxId, form.getTurnoverBoxId())
                    .in(PartStationTurnTaskEntity::getStatus, TurnTaskStatusEnum.WAIT_GET.getValue(), TurnTaskStatusEnum.WAIT_START.getValue())
                    .last("limit 1"));
        }

        if (task == null) {
            return ResponseDTO.userErrorParam("任务不存在");
        }
        //无人领取任务直接开始
        if (TurnTaskStatusEnum.WAIT_GET.getValue().equals(task.getStatus())) {
            partStationTurnTaskDao.update(new LambdaUpdateWrapper<PartStationTurnTaskEntity>()
                    .eq(PartStationTurnTaskEntity::getId, task.getId())
                    .set(PartStationTurnTaskEntity::getStatus, TurnTaskStatusEnum.PROCESSING.getValue())
                    .set(PartStationTurnTaskEntity::getBeginTime, now)
                    .set(PartStationTurnTaskEntity::getGetTime, now)
                    .set(PartStationTurnTaskEntity::getExecutorType, TurnTaskExecutorTypeEnum.HUMAN.getValue())
                    .set(PartStationTurnTaskEntity::getExecutorId, String.valueOf(user.getUserId()))
                    .set(PartStationTurnTaskEntity::getExecutorName, user.getUserName()));
            return ResponseDTO.ok("领取并开始任务成功");
        }
        //已领取任务开始
        if (TurnTaskStatusEnum.WAIT_START.getValue().equals(task.getStatus())) {
            if (!task.getExecutorId().equals(String.valueOf(user.getUserId()))) {
                return ResponseDTO.userErrorParam("任务已被领取");
            }
            partStationTurnTaskDao.update(new LambdaUpdateWrapper<PartStationTurnTaskEntity>()
                    .eq(PartStationTurnTaskEntity::getId, task.getId())
                    .set(PartStationTurnTaskEntity::getStatus, TurnTaskStatusEnum.PROCESSING.getValue())
                    .set(PartStationTurnTaskEntity::getBeginTime, now));
            return ResponseDTO.ok("开始任务成功");
        }


        return ResponseDTO.ok("任务状态错误");

    }

    /**
     * 领取任务
     *
     * @param id
     * @param turnTaskExecutorTypeEnum
     * @param s
     * @param userName
     * @return
     */
    public ResponseDTO<String> getTask(Long id, TurnTaskExecutorTypeEnum turnTaskExecutorTypeEnum, String userId, String userName) {
        PartStationTurnTaskEntity task = partStationTurnTaskDao.selectById(id);
        if (task == null) {
            return ResponseDTO.userErrorParam("任务不存在");
        }
        if (!TurnTaskStatusEnum.WAIT_GET.getValue().equals(task.getStatus())) {
            return ResponseDTO.userErrorParam("任务非待领取状态");
        }
        LocalDateTime now = LocalDateTime.now();
        partStationTurnTaskDao.update(new LambdaUpdateWrapper<PartStationTurnTaskEntity>()
                .eq(PartStationTurnTaskEntity::getId, id)
                .set(PartStationTurnTaskEntity::getStatus, TurnTaskStatusEnum.WAIT_START.getValue())
                .set(PartStationTurnTaskEntity::getGetTime, now)
                .set(PartStationTurnTaskEntity::getExecutorType, turnTaskExecutorTypeEnum.getValue())
                .set(PartStationTurnTaskEntity::getExecutorId, userId)
                .set(PartStationTurnTaskEntity::getExecutorName, userName));
        return ResponseDTO.ok("领取成功");
    }

    /**
     * 放弃任务
     *
     * @param id
     * @param turnTaskExecutorTypeEnum
     * @param userId
     * @param userName
     * @return
     */
    public ResponseDTO<String> giveUpTask(Long id, TurnTaskExecutorTypeEnum turnTaskExecutorTypeEnum, String userId, String userName) {
        PartStationTurnTaskEntity task = partStationTurnTaskDao.selectById(id);
        if (task == null) {
            return ResponseDTO.userErrorParam("任务不存在");
        }
        if (!task.getExecutorId().equals(userId)) {
            return ResponseDTO.userErrorParam("任务执行人错误");
        }
        if (TurnTaskStatusEnum.FINISH.getValue().equals(task.getStatus())) {
            return ResponseDTO.userErrorParam("任务已完成");
        }
        partStationTurnTaskDao.update(new LambdaUpdateWrapper<PartStationTurnTaskEntity>()
                .eq(PartStationTurnTaskEntity::getId, id)
                .set(PartStationTurnTaskEntity::getStatus, TurnTaskStatusEnum.WAIT_GET.getValue())
                .set(PartStationTurnTaskEntity::getGetTime, null)
                .set(PartStationTurnTaskEntity::getExecutorType, null)
                .set(PartStationTurnTaskEntity::getExecutorId, null)
                .set(PartStationTurnTaskEntity::getExecutorName, null));
        return ResponseDTO.ok("取消成功");
    }

    public ResponseDTO<String> finishTask(PartStationTurnTaskFinishForm form) {
        PartStationTurnTaskEntity task = partStationTurnTaskDao.selectOne(new LambdaQueryWrapper<PartStationTurnTaskEntity>()
                .eq(PartStationTurnTaskEntity::getTurnoverBoxId, form.getTurnoverBoxId())
                .eq(PartStationTurnTaskEntity::getStatus, TurnTaskStatusEnum.PROCESSING.getValue())
                .last("limit 1"));
        if (task == null) {
            return ResponseDTO.userErrorParam("任务不存在");
        }
        RequestUser user = SmartRequestUtil.getRequestUser();
        if (!task.getExecutorId().equals(String.valueOf(user.getUserId()))) {
            return ResponseDTO.userErrorParam("任务执行人错误");
        }
        PartStationBinEntity bin = partStationBinDao.selectById(form.getEndLocationId());
        if (bin == null) {
            return ResponseDTO.userErrorParam("货位不存在");
        }

        partStationTurnTaskDao.update(new LambdaUpdateWrapper<PartStationTurnTaskEntity>()
                .eq(PartStationTurnTaskEntity::getId, task.getId())
                .set(PartStationTurnTaskEntity::getStatus, TurnTaskStatusEnum.FINISH.getValue())
                .set(PartStationTurnTaskEntity::getEndTime, LocalDateTime.now())
                .set(PartStationTurnTaskEntity::getEndLocationId, form.getEndLocationId()));

        //同步裁片收发记录
        List<PartStationTurnBoxInsideEntity> insides = partStationTurnBoxInsideDao.selectList(new LambdaQueryWrapper<PartStationTurnBoxInsideEntity>()
                .eq(PartStationTurnBoxInsideEntity::getTurnBoxId, task.getTurnoverBoxId()));
        if (CollUtil.isEmpty(insides)) {
            return ResponseDTO.ok("完成成功");
        }
        List<Long> ticketIds = null;

        if (TurnTaskTypeEnum.TO_WORKSHOP.getValue().equals(task.getType()) && task.getSyncPartDispatchFlag()) {
            //转移到车间，并且需要同步裁片收发记录
            ticketIds = insides.stream().map(PartStationTurnBoxInsideEntity::getFeTicketId).toList();
        } else if (TurnTaskTypeEnum.TO_PART_STATION.getValue().equals(task.getType())) {
            //转到裁片驿站，则直接更新裁片收发记录
            ticketIds = insides.stream().map(PartStationTurnBoxInsideEntity::getFeTicketId).toList();
        }
        if (CollUtil.isNotEmpty(ticketIds)) {
            PartStationTurnTaskFinishEvent.Payload payload = new PartStationTurnTaskFinishEvent.Payload();
            payload.setTaskId(task.getId());
            payload.setTicketIds(ticketIds);
            PartStationTurnTaskFinishEvent event = new PartStationTurnTaskFinishEvent(this, payload);
            eventPublisher.publishEvent(event);
            return ResponseDTO.ok("完成成功");
        }


        return ResponseDTO.ok("完成成功");

    }
}
