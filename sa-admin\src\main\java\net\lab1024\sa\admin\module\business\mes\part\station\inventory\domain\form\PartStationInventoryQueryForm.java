package net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.form;

import net.lab1024.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 裁片驿站库存表 分页查询表单
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:44:34
 * @Copyright zscbdic
 */

@Data
public class PartStationInventoryQueryForm extends PageParam{


    /**
     * 物料编号
     */
    @Schema(description = "物料编号")
    private String itemNumber;

    /**
     * 生产指令单号
     */
    @Schema(description = "生产指令单号")
    private String produceInstructOrderNumber;

    @Schema(description = "颜色")
    private List<String> colors;

    @Schema(description = "尺码")
    private List<String> sizes;

    @Schema(description = "部位")
    private List<String> positions;

    @Schema(description = "菲票id")
    private Long feTicketId;

    @Schema(description = "货位id")
    private Long binId;

}
