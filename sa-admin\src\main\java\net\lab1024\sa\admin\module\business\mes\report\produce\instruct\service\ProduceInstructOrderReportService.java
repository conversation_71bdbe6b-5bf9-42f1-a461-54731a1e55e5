package net.lab1024.sa.admin.module.business.mes.report.produce.instruct.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderClothesDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderProcessDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderClothesEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderSummaryVO;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderVO;
import net.lab1024.sa.admin.module.business.mes.tailor.bed.dao.CutBedSheetDao;
import net.lab1024.sa.admin.module.business.mes.tailor.bed.dao.CutBedSheetDetailDao;
import net.lab1024.sa.admin.module.business.mes.tailor.bed.domain.entity.CutBedSheetEntity;
import net.lab1024.sa.admin.module.business.mes.tailor.bed.domain.vo.CutBedSheetDetailVO;
import net.lab1024.sa.admin.module.business.mes.work.dao.WorkRecordDao;
import net.lab1024.sa.admin.module.business.mes.work.domain.vo.WorkRecordVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ProduceInstructOrderReportService {

    @Resource
    private ProduceInstructOrderDao produceInstructOrderDao;

    @Resource
    private CutBedSheetDao cutBedSheetDao;

    @Resource
    private CutBedSheetDetailDao cutBedSheetDetailDao;

    @Resource
    private WorkRecordDao workRecordDao;

    @Resource
    private ProduceInstructOrderClothesDao orderClothesDao;

    @Resource
    private ProduceInstructOrderProcessDao orderProcessDao;


    /**
     * 执行汇总报表
     *
     * @param queryForm
     */
    public ResponseDTO<PageResult<ProduceInstructOrderSummaryVO>> executeSummary(ProduceInstructOrderQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ProduceInstructOrderVO> list = produceInstructOrderDao.queryPage(page, queryForm);
        if (CollUtil.isEmpty(list)) {
            List<ProduceInstructOrderSummaryVO> vos = SmartBeanUtil.copyList(list, ProduceInstructOrderSummaryVO.class);
            SmartPageUtil.convert2PageResult(page, vos);
            return ResponseDTO.ok();
        }

        List<Long> orderIds = list.stream().map(ProduceInstructOrderVO::getId).collect(Collectors.toList());
        List<CutBedSheetEntity> cutBedSheet = cutBedSheetDao.selectList(new LambdaQueryWrapper<CutBedSheetEntity>()
                .select(CutBedSheetEntity::getInstructOrderId, CutBedSheetEntity::getQuantity)
                .in(CutBedSheetEntity::getInstructOrderId, orderIds));
        if (CollUtil.isEmpty(cutBedSheet)) {
            List<ProduceInstructOrderSummaryVO> vos = SmartBeanUtil.copyList(list, ProduceInstructOrderSummaryVO.class);
            SmartPageUtil.convert2PageResult(page, vos);
            return ResponseDTO.ok();
        }

        Map<Long, List<CutBedSheetEntity>> cutBedSheetMap = cutBedSheet.stream().collect(Collectors.groupingBy(CutBedSheetEntity::getInstructOrderId));

        List<ProduceInstructOrderSummaryVO> vos = list.stream().map(e -> {
            ProduceInstructOrderSummaryVO vo = SmartBeanUtil.copy(e, ProduceInstructOrderSummaryVO.class);
            if (cutBedSheetMap.containsKey(e.getId())) {
                vo.setCutNum(cutBedSheetMap.get(e.getId()).stream().mapToInt(CutBedSheetEntity::getQuantity).sum());
            }
            return vo;
        }).collect(Collectors.toList());
        PageResult<ProduceInstructOrderSummaryVO> pageResult = SmartPageUtil.convert2PageResult(page, vos);
        return ResponseDTO.ok(pageResult);
    }

    public ResponseDTO<List<ProduceInstructOrderSummaryVO.DetailVO>> executeDetail(Long produceInstructOrderId) {
        List<ProduceInstructOrderClothesEntity> clothes = orderClothesDao.selectList(new LambdaQueryWrapper<ProduceInstructOrderClothesEntity>()
                .eq(ProduceInstructOrderClothesEntity::getOrderId, produceInstructOrderId));
        if (CollUtil.isEmpty(clothes)) {
            return ResponseDTO.ok(Collections.emptyList());
        }
        List<ProduceInstructOrderSummaryVO.DetailVO> clothesDetail = clothes.stream().map(e ->
                SmartBeanUtil.copy(e, ProduceInstructOrderSummaryVO.DetailVO.class)
        ).collect(Collectors.toList());


        List<CutBedSheetDetailVO> cutBedSheetDetail = cutBedSheetDetailDao.queryCutSummary(produceInstructOrderId);
        if (CollUtil.isNotEmpty(cutBedSheetDetail)) {
            Map<String, CutBedSheetDetailVO> de = cutBedSheetDetail.stream()
                    .collect(Collectors.toMap(e -> e.getStyleColor() + "@" + e.getSize(), e -> e));
            clothesDetail.forEach(e -> {
                String key  = e.getStyleColor() + "@" + e.getSize();
                if (de.containsKey(key)) {
                    e.setCutNum(de.get(key).getNum());
                }
            });
        }

        List<WorkRecordVO> workRecords = workRecordDao.queryClothesFinishSummary(produceInstructOrderId);
        if (CollUtil.isNotEmpty(workRecords)) {
            Map<String, WorkRecordVO> de = workRecords.stream()
                    .collect(Collectors.toMap(e -> e.getStyleColor() + "@" + e.getSize(), e -> e));
            clothesDetail.forEach(e -> {
                String key  = e.getStyleColor() + "@" + e.getSize();
                if (de.containsKey(key)) {
                    e.setFinishNum(de.get(key).getWorkQuantity());
                }
            });
        }

        return ResponseDTO.ok(clothesDetail);

    }
}
