package net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 裁片周转箱 列表VO
 *
 * <AUTHOR>
 * @Date 2025-03-14 11:36:51
 * @Copyright zscbdic
 */

@Data
public class PartStationTurnBoxVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

//    @Schema(description = "删除标识;0未删除，1删除")
//    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "周转箱编号")
    private String number;

    /**
     * 周转箱名称
     */
    @Schema(description = "周转箱名称")
    private String name;

    @Schema(description = "容量")
    private Integer capacity;

    @Schema(description = "库内标识")
    private Boolean insideFlag;

}
