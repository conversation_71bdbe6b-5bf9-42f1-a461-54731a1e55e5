package net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

/**
 * 货架 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:13:43
 * @Copyright zscbdic
 */

@Data
public class StkRackQueryForm extends PageParam{

    @Schema(description = "关键字查询")
    private String queryKey;

    @Schema(description = "仓库id")
    private Long warehouseId;

}
