package net.lab1024.sa.admin.module.business.mes.stock.out.domain.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 生产领料单
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StkProdPickMtrlVO extends StkOutStockVO {

    private List<StkProdPickMtrlVO.DetailVO> details;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DetailVO extends StkOutStockDetailVO {

    }
}
