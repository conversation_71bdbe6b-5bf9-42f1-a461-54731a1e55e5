package net.lab1024.sa.admin.module.business.mes.equip.equipment.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.dao.EquipmentScadaDao;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentScadaQuery;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentScadaQueryForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.vo.EquipmentScadaVO;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.vo.EquipmentVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 设备scada信息 Service
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:13:22
 * @Copyright zscbdic
 */

@Service
public class EquipmentScadaService {

    @Resource
    private EquipmentScadaDao equipmentScadaDao;


    /**
     * 设备scada信息 分页查询
     * @param queryForm
     * @return
     */
    public ResponseDTO<PageResult<EquipmentVO>> queryScadaPage(EquipmentScadaQueryForm queryForm) {
        queryForm.setIotNetworkFlag(true);
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<EquipmentVO> vos = equipmentScadaDao.queryEquipScadaPage(page, queryForm);
        PageResult<EquipmentVO> pageResult = SmartPageUtil.convert2PageResult(page, vos);
        return ResponseDTO.ok(pageResult);
    }



    public ResponseDTO<List<EquipmentVO>> queryScadaList(EquipmentScadaQuery query) {
        EquipmentScadaQueryForm queryForm = new EquipmentScadaQueryForm();
        queryForm.setQueryKey(query.getQueryKey());
        queryForm.setIotNetworkFlag(true);
        queryForm.setIotNetworkPlatform(query.getIotNetworkPlatform());
        queryForm.setIotEquipmentType(query.getIotEquipmentType());

        List<EquipmentVO> vos = equipmentScadaDao.queryEquipScadaPage(null, queryForm);
        return ResponseDTO.ok(vos);
    }
}
