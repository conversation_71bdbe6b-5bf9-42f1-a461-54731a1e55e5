package net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 即时库存更新日志 列表VO
 *
 * <AUTHOR>
 * @Date 2025-01-15 11:20:19
 * @Copyright zscbdic
 */

@Data
public class StkInventoryLogVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "仓库id")
    private Long warehouseId;

    @Schema(description = "仓库名称")
    private String warehouseName;

    @Schema(description = "仓库编号")
    private String warehouseNumber;

    @Schema(description = "仓位id")
    private Long locationId;

    @Schema(description = "仓位编码")
    private String locationNumber;

    @Schema(description = "物料id")
    private Long materielId;

    @Schema(description = "物料名称")
    private String materielName;

    @Schema(description = "物料规格型号")
    private String materielModel;

    @Schema(description = "物料spu编号")
    private String materielSpuNumber;

    @Schema(description = "物料sku编号")
    private String materielSkuNumber;

    /**
     * 单位id
     */
    @Schema(description = "单位id")
    private Long unitId;

    /**
     * 单位名称
     */
    @Schema(description = "单位名称")
    private String unitName;

    @Schema(description = "货主类型")
    private String ownerType;

    @Schema(description = "货主id")
    private Long ownerId;

    @Schema(description = "货主名称")
    private String ownerName;

    @Schema(description = "批次id")
    private Long lotId;

    @Schema(description = "批次编号")
    private String lotNumber;

    @Schema(description = "SN ID")
    private Long snId;

    @Schema(description = "SN编号")
    private String snNumber;

    @Schema(description = "操作类型")
    private String optType;

    @Schema(description = "操作数量;影响数量")
    private BigDecimal optQty;

    @Schema(description = "剩余数量;影响结果后数量")
    private BigDecimal optAfterQty;

    @Schema(description = "操作前数量")
    private BigDecimal optBeforeQty;

    @Schema(description = "操作人id")
    private Long optUserId;

    @Schema(description = "操作人名称")
    private String optUserName;

    @Schema(description = "操作时间")
    private LocalDateTime optTime;

    @Schema(description = "业务单据类型")
    private String bizFormType;

    @Schema(description = "业务单据id")
    private Long bizFormId;

    @Schema(description = "业务单据编号")
    private String bizFormNumber;

    @Schema(description = "业务单据详情ID")
    private Long bizFormDetailId;

    @Schema(description = "作业单据类型")
    private String workFormType;

    @Schema(description = "作业单据ID")
    private Long workFormId;

    @Schema(description = "作业单据编号")
    private String workFormNumber;

    @Schema(description = "作业单据详情ID")
    private Long workFormDetailId;

}
