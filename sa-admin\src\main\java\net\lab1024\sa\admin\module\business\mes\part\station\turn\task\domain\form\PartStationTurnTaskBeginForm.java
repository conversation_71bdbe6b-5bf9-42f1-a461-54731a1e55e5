package net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.form;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 驿站任务开始
 */
@Data
public class PartStationTurnTaskBeginForm {

//    @NotNull(message = "任务ID 不能为空")
    private Long taskId;

    @NotNull(message = "周转箱ID 不能为空")
    private Long turnoverBoxId;

//    private String executorId;
//
//    private String executorName;
//
//    private String executorType;
//
//    private LocalDateTime beginTime;
//
//    private LocalDateTime getTime;
}
