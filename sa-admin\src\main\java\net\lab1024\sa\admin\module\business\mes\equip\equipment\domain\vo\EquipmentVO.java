package net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设备 列表VO
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:12:13
 * @Copyright zscbdic
 */

@Data
public class EquipmentVO {

    @Schema(description = "主键")
    private Long id;


    @Schema(description = "备注")
    private String remark;

    @Schema(description = "设备编号")
    private String number;

    @Schema(description = "设备名称")
    private String name;

    @Schema(description = "设备类别id")
    private Long typeId;

    @Schema(description = "规格")
    private String specification;

    @Schema(description = "型号")
    private String model;

    @Schema(description = "设备状态")
    private String status;

    @Schema(description = "图片 保留")
    private String imgs;

    @Schema(description = "车间id")
    private Long workshopId;

    //----------------------------------


    /**
     * 是否物联网连接;0否,1是
     */
    private Boolean iotNetworkFlag;

    /**
     * 物联网平台;yuanyi_iot元一
     */
    private String iotNetworkPlatform;

    /**
     * 对接设备类型
     */
    private String iotEquipmentType;

    /**
     * 设备ID(SCADA);保留字段
     */
    private String scadaEquipmentId;

    /**
     * 产品编码(SCADA);SCADA产品编码
     */
    private String scadaProductCode;

    /**
     * 设备编码(SCADA);SCADA设备编码
     */
    private String scadaEquipmentCode;

}
