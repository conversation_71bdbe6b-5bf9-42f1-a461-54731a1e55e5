package net.lab1024.sa.admin.module.business.mes.stock.out.manager;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockBillStatusEnum;
import net.lab1024.sa.admin.module.business.mes.stock.out.dao.StkOutStockDao;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.bo.StkOutStockBO;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity.StkOutStockDetailEntity;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity.StkOutStockEntity;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.vo.StkOtherOutStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.vo.StkOutStockVO;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.common.util.SmartBeanUtil;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 出库单  Manager
 *
 * <AUTHOR>
 * @Date 2025-02-09 19:58:18
 * @Copyright zscbdic
 */
@Service
public class StkOutStockManager extends ServiceImpl<StkOutStockDao, StkOutStockEntity> {

    @Resource
    private StkOutStockDetailManager stkOutStockDetailManager;

    @Resource
    private StkOutStockDao stkOutStockDao;


    public void updateBillStatusCheck(String status) {
        if (StockBillStatusEnum.AUDIT.getValue().equals(status)) {
            throw new BusinessException("单据已审核");
        }
    }

    public StkOutStockVO queryById(Long id) {
        StkOutStockEntity stockEntity = this.getById(id);
        if (stockEntity == null) {
            throw new BusinessException("单据不存在");
        }
        StkOtherOutStockVO vo = SmartBeanUtil.copy(stockEntity, StkOtherOutStockVO.class);
        List<StkOutStockDetailEntity> detailEntities = stkOutStockDetailManager.lambdaQuery()
                .eq(StkOutStockDetailEntity::getOutStockId, id)
                .list();
        List<StkOtherOutStockVO.DetailVO> detailVOS = SmartBeanUtil.copyList(detailEntities, StkOtherOutStockVO.DetailVO.class);
        vo.setDetails(detailVOS);

        return vo;
    }

    /**
     * 保存入库单
     *
     * @param bo
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBill(StkOutStockBO bo) {
        StkOutStockEntity stkOutStock = bo.getStkOutStock();
        List<StkOutStockDetailEntity> details = bo.getDetails();

        stkOutStockDao.insert(stkOutStock);
        Long id = stkOutStock.getId();
        details.forEach(detail -> {
            detail.setOutStockId(id);
        });
        stkOutStockDetailManager.saveBatch(details);
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateBill(StkOutStockBO bo) {
        List<StkOutStockDetailEntity> details = bo.getDetails();
        StkOutStockEntity stkOutStock = bo.getStkOutStock();

        stkOutStockDao.updateById(stkOutStock);
        stkOutStockDetailManager.lambdaUpdate()
                .eq(StkOutStockDetailEntity::getOutStockId, stkOutStock.getId())
                .remove();
        details.forEach(detail -> {
            detail.setOutStockId(stkOutStock.getId());
        });
        stkOutStockDetailManager.saveBatch(details);
    }


    /**
     * 删除出库单
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBill(Long id) {
        stkOutStockDao.deleteById(id);
        stkOutStockDetailManager.lambdaUpdate()
                .eq(StkOutStockDetailEntity::getOutStockId, id)
                .remove();
    }

    /**
     * 校验单号
     *
     * @param number
     * @param excludedId
     */
    public void checkNumber(String number, Long excludedId) {
        Long count = this.lambdaQuery().eq(StkOutStockEntity::getNumber, number)
                .ne(excludedId != null, StkOutStockEntity::getId, excludedId)
                .count();
        if (count > 0) {
            throw new BusinessException("入库单编号已存在");
        }
    }
}
