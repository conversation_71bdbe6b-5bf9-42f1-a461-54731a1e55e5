package net.lab1024.sa.admin.event.tailor;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class CutBedSheetAddEvent extends ApplicationEvent {

    private Long produceInstructOrderId;

    private Long cutBedSheetId;

    public CutBedSheetAddEvent(Object source, Long produceInstructOrderId) {
        super(source);
        this.produceInstructOrderId = produceInstructOrderId;
    }

}
