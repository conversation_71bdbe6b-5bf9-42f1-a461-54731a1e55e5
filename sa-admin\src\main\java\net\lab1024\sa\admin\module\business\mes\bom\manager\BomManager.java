package net.lab1024.sa.admin.module.business.mes.bom.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.bom.dao.BomDao;
import net.lab1024.sa.admin.module.business.mes.bom.dao.BomDetailDao;
import net.lab1024.sa.admin.module.business.mes.bom.domain.entity.BomDetailEntity;
import net.lab1024.sa.admin.module.business.mes.bom.domain.entity.BomEntity;
import net.lab1024.sa.admin.module.business.mes.bom.domain.form.BomAddForm;
import net.lab1024.sa.admin.module.business.mes.bom.domain.form.BomDetailAddForm;
import net.lab1024.sa.admin.module.business.mes.bom.domain.form.BomUpdateForm;
import net.lab1024.sa.admin.module.business.mes.bom.domain.vo.BomDetailVO;
import net.lab1024.sa.admin.module.business.mes.bom.domain.vo.BomVO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物料BOM表  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:25:22
 * @Copyright zscbdic
 */
@Service
public class BomManager extends ServiceImpl<BomDao, BomEntity> {


    @Resource
    private BomDao bomDao;

    @Resource
    private BomDetailDao bomDetailDao;

    /**
     * 根据id和版本号查询
     *
     * @param id
     * @param versionNumber
     * @return
     */
    public BomVO queryByIdAndVersionNumber(Integer id, Integer versionNumber) {
        List<BomEntity> bomEntityList = bomDao.selectList(new LambdaQueryWrapper<BomEntity>()
                .eq(BomEntity::getId, id)
                .eq(BomEntity::getVersionNumber, versionNumber));
        if(CollectionUtils.isEmpty(bomEntityList)){
            return null;
        }
        BomEntity bomEntity = bomEntityList.get(0);

        List<BomDetailEntity> bomDetailEntities = bomDetailDao.selectList(new LambdaQueryWrapper<BomDetailEntity>()
                .eq(BomDetailEntity::getBomId, id));
        BomVO bomVO = SmartBeanUtil.copy(bomEntity, BomVO.class);

        if (CollectionUtils.isNotEmpty(bomDetailEntities)) {
            List<BomDetailVO> bomDetailVOS = SmartBeanUtil.copyList(bomDetailEntities, BomDetailVO.class);
            bomVO.setBomDetailList(bomDetailVOS);
        }
        return bomVO;
    }

    /**
     * 新建版本bom
     *
     * @param bomEntity
     * @param bomDetailList
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBom(BomEntity bomEntity, ValidateList<BomDetailAddForm> bomDetailList) {

        bomDao.insert(bomEntity);
        Long bomId = bomEntity.getId();

        if (CollectionUtils.isNotEmpty(bomDetailList)) {
            // 添加物料BOM明细
            List<BomDetailEntity> bomDetailEntities = SmartBeanUtil.copyList(bomDetailList, BomDetailEntity.class);
            bomDetailEntities.forEach(bomDetailEntity -> {
                bomDetailEntity.setBomId(bomId);
                bomDetailDao.insert(bomDetailEntity);
            });
        }
    }


    /**
     * 更新bom
     *
     * @param bomEntity
     * @param bomDetailAddFormList
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBom(BomEntity bomEntity, ValidateList<BomDetailAddForm> bomDetailAddFormList) {

        Long bomId = bomEntity.getId();

        //删除原有详细信息
        List<BomDetailEntity> oldBomDetailEntities = bomDetailDao.selectList(new LambdaQueryWrapper<BomDetailEntity>().
                eq(BomDetailEntity::getBomId, bomId));
        if (CollectionUtils.isNotEmpty(oldBomDetailEntities)) {
            oldBomDetailEntities.forEach(e -> {
                bomDetailDao.updateDeleted(e.getId(), true);
            });
        }

        //加入新详细信息
        if (CollectionUtils.isNotEmpty(bomDetailAddFormList)) {
            bomDetailAddFormList.forEach(e -> {
                BomDetailEntity bomDetailEntity = SmartBeanUtil.copy(e, BomDetailEntity.class);
                bomDetailEntity.setBomId(bomId);
                bomDetailDao.insert(bomDetailEntity);
            });
        }
        bomDao.updateById(bomEntity);
    }

    /**
     * 删除所有版本的bom
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteAllBom(Long id) {
        //获取编号
        BomEntity bomEntity = bomDao.selectById(id);
        String bomNumber = bomEntity.getBomNumber();

        //编号的所有相同bom以及bomid
        List<BomEntity> bomEntityList = bomDao.selectList(new LambdaQueryWrapper<BomEntity>().eq(BomEntity::getBomNumber, bomNumber));
        Map<Long, BomEntity> idAndBom = bomEntityList.stream().collect(Collectors.toMap(BomEntity::getId, Function.identity()));

        idAndBom.forEach((k, v) -> {
            List<BomDetailEntity> bomDetailEntities = bomDetailDao.selectList(new LambdaQueryWrapper<BomDetailEntity>().eq(BomDetailEntity::getBomId, k));
            if (CollectionUtils.isNotEmpty(bomDetailEntities)) {
                bomDetailEntities.forEach(e -> {
                    bomDetailDao.updateDeleted(e.getId(), true);
                });
            }
            bomDao.updateDeleted(v.getId(), true);
        });
    }

    public List<Integer> allVersionNumber(String bomNumber) {
        List<BomEntity> bomEntityList = bomDao.selectList(new LambdaQueryWrapper<BomEntity>().eq(BomEntity::getBomNumber, bomNumber));
        List<Integer> versionList = bomEntityList.stream().map(BomEntity::getVersionNumber).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(versionList)){
            return null;
        }
        return versionList;
    }

    /**
     * 新增校验
     * @param addForm
     */
    public void addCheck(BomAddForm addForm) {
        String bomNumber = addForm.getBomNumber();
        long count = this.count(new LambdaQueryWrapper<BomEntity>()
                .eq(BomEntity::getBomNumber, bomNumber));
        if (count > 0) {
            throw new BusinessException("BOM编号已存在");
        }
        Long itemId = addForm.getItemId();
        count = this.count(new LambdaQueryWrapper<BomEntity>()
                .eq(BomEntity::getItemId, itemId));
        if (count > 0) {
            throw new BusinessException("物料已存在BOM");
        }
    }

    /**
     * 更新校验
     * @param updateForm
     */
    public void updateCheck(BomUpdateForm updateForm) {
        Long itemId = updateForm.getItemId();
        long count = this.count(new LambdaQueryWrapper<BomEntity>()
                .eq(BomEntity::getItemId, itemId)
                .ne(BomEntity::getId, updateForm.getId()));
        if (count > 0) {
            throw new BusinessException("物料已存在BOM");
        }
    }
}
