package net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 裁片收发汇总VO
 */
@Data
public class PartDispatchSummaryVO {

    @Schema(description = "生产指令单编号")
    private String instructOrderNumber;

    @Schema(description = "生产指令单id")
    private Long instructOrderId;

    @Schema(description = "物料id")
    private Long itemId;

    @Schema(description = "物料编号")
    private String itemNumber;

    @Schema(description = "规格型号")
    private String model;

    @Schema(description = "物料名称")
    private String itemName;

    @Schema(description = "物料单位id")
    private Long unitId;

    @Schema(description = "物料单位名称")
    private String unitName;

    /**
     * 下发数
     */
    @Schema(description = "下发数")
    private Integer sendNum;

    /**
     * 回收数
     */
    @Schema(description = "回收数")
    private Integer receiveNum;

    /**
     * 收发对象数量
     */
    @Schema(description = "收发对象数量")
    private Integer objectNum;

    /**
     * 下发总扎数
     */
    @Schema(description = "下发总扎数")
    private Integer sendTieCount;

    /**
     * 回收总扎数
     */
    @Schema(description = "回收总扎数")
    private Integer receiveTieCount;
    /**
     * 详情
     */
    @Data
    public static class DetailVO {

        @Schema(description = "款式颜色")
        private String styleColor;

        @Schema(description = "尺码")
        private String size;

        @Schema(description = "位置")
        private String positions;

        @Schema(description = "下发数")
        private Integer sendNum;

        @Schema(description = "回收数")
        private Integer receiveNum;

        @Schema(description = "下发总扎数")
        private Integer sendTieCount;

        @Schema(description = "回收总扎数")
        private Integer receiveTieCount;

        @Schema(description = "收发对象名称")
        private String objectName;



    }
}
