package net.lab1024.sa.admin.module.business.mes.bom.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 物料BOM表 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:25:22
 * @Copyright zscbdic
 */

@Data
public class BomVO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /*
     * 删除标识;0未删除，1删除
     */
    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    /**
     * bom编号
     */
    @Schema(description = "bom编号")
    private String bomNumber;

    /**
     * bom名称
     */
    @Schema(description = "bom名称")
    private String bomName;

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    private Integer versionNumber;

    /**
     * 物料id
     */
    @Schema(description = "物料id")
    private Long itemId;

    /**
     * 物料名称
     */
    @Schema(description = "物料名称")
    private String itemName;

    /**
     * 物料编号
     */
    @Schema(description = "物料编号")
    private String itemNumber;

    /**
     * 物料规格型号
     */
    @Schema(description = "物料规格型号")
    private String itemModel;

    /**
     * 物料分类id
     */
    @Schema(description = "物料分类id")
    private Long itemTypeId;

    /**
     * 物料分类名称
     */
    @Schema(description = "物料分类名称")
    private String itemTypeName;

    /**
     * 物料类型
     */
    @Schema(description = "物料类型")
    private String itemCategory;

    /**
     * 物料单位id
     */
    @Schema(description = "物料单位id")
    private Long itemUnitId;

    /**
     * 物料单位名称
     */
    @Schema(description = "物料单位名称")
    private String itemUnitName;

    /**
     * 物料属性
     */
    @Schema(description = "物料属性")
    private String itemAttribute;

    /**
     * 停用标识
     */
    @Schema(description = "停用标识")
    private Boolean enableFlag;

    /**
     * Bom详情
     */
    @Schema(description = "Bom详情")
    @Valid
    private List<BomDetailVO> bomDetailList;

}
