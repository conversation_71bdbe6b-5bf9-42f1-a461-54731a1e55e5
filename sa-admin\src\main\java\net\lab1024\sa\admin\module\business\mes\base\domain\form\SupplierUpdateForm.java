package net.lab1024.sa.admin.module.business.mes.base.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 供应商表 更新表单
 *
 * <AUTHOR>
 * @Date 2024-07-03 09:43:14
 * @Copyright zscbdic
 */

@Data
public class SupplierUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "供应商名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "供应商名称 不能为空")
    private String name;

    @Schema(description = "供应商简称")
    private String fastName;

    @Schema(description = "联系人")
    private String contact;

    @Schema(description = "电话")
    private String telephone;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "供应商类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "供应商类型 不能为空")
    private String type;

    @Schema(description = "结算方式")
    private String way;

    @Schema(description = "等级;5星最高，无半星", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "等级;5星最高，无半星 不能为空")
    private Integer level;

    @Schema(description = "停用标识;0启用，1停用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "停用标识;0启用，1停用 不能为空")
    private Boolean enableFlag;

}
