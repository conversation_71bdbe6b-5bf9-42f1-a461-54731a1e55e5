package net.lab1024.sa.admin.module.business.mes.stock.lot.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.entity.StkLotMaterialTraceEntity;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.form.StkLotMaterialTraceQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.vo.StkLotMaterialTraceVO;

import java.util.List;

/**
 * 批号跟踪信息 Dao
 *
 * <AUTHOR>
 * @Date 2025-02-08 19:57:37
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface StkLotMaterialTraceDao extends BaseMapper<StkLotMaterialTraceEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<StkLotMaterialTraceVO> queryPage(Page page, @Param("queryForm") StkLotMaterialTraceQueryForm queryForm);

    /**
     * 分页 查询 关联表查询
     * @param page
     * @param queryForm
     * @return
     */
    List<StkLotMaterialTraceVO> queryPageWithExtra(Page<?> page,@Param("queryForm")  StkLotMaterialTraceQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);


}
