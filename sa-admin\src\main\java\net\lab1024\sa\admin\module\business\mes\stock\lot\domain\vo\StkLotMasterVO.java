package net.lab1024.sa.admin.module.business.mes.stock.lot.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 批号主档 列表VO
 *
 * <AUTHOR>
 * @Date 2025-02-03 19:28:09
 * @Copyright zscbdic
 */

@Data
public class StkLotMasterVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "批次编号")
    private String number;

    @Schema(description = "物料ID")
    private Long materielId;

    @Schema(description = "物料名称")
    private String materielName;

    @Schema(description = "物料型号")
    private String materielModel;

    @Schema(description = "物料SPU编号")
    private String materielSpuNumber;

    @Schema(description = "物料SKU编号")
    private String materielSkuNumber;


    @Schema(description = "批号状态;0 未生效 1 生效 2 断号（保留）")
    private String lotStatus;

    @Schema(description = "业务类型;保留")
    private String bizType;

    @Schema(description = "货主类型;保留")
    private String ownerType;

    @Schema(description = "货主id;保留")
    private Long ownerId;

    @Schema(description = "货主名称;保留")
    private String ownerName;

}
