package net.lab1024.sa.admin.module.business.mes.base.manager;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.lab1024.sa.admin.module.business.mes.base.dao.PartDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.PartEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.PartAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.PartUpdateForm;
import net.lab1024.sa.base.common.exception.BusinessException;
import org.springframework.stereotype.Service;

/**
 * 部位表  Manager
 *
 * <AUTHOR>
 * @Date 2025-06-23 19:56:55
 * @Copyright zscbdic
 */
@Service
public class PartManager extends ServiceImpl<PartDao, PartEntity> {


    public void addCheck(PartAddForm addForm) {
        Long count = this.lambdaQuery()
                .eq(PartEntity::getName, addForm.getName())
                .count();
        if (count > 0) {
            throw new BusinessException("名称已存在");
        }
    }

    public void updateCheck(PartUpdateForm updateForm) {
        Long count = this.lambdaQuery()
                .eq(PartEntity::getName, updateForm.getName())
                .ne(PartEntity::getId, updateForm.getId())
                .count();
        if (count > 0) {
            throw new BusinessException("名称已存在");
        }
    }

}
