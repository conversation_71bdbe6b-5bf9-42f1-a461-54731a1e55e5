package net.lab1024.sa.admin.module.business.mes.stock.lot.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 批号跟踪信息 列表VO
 *
 * <AUTHOR>
 * @Date 2025-02-08 19:57:37
 * @Copyright zscbdic
 */

@Data
public class StkLotMaterialTraceVO {


    @Schema(description = "主键")
    private Long id;

//    @Schema(description = "创建时间")
//    private LocalDateTime createTime;
//
//    @Schema(description = "创建人")
//    private String createBy;
//
//    @Schema(description = "更新时间")
//    private LocalDateTime updateTime;
//
//    @Schema(description = "更新人")
//    private String updateBy;

//    @Schema(description = "删除标识;0未删除，1删除")
//    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "批号ID")
    private Long lotMasterId;

    @Schema(description = "批号")
    private String lotMasterNumber;

    @Schema(description = "物料ID")
    private Long materielId;

    @Schema(description = "物料spu编码")
    private String materielSpuNumber;

    @Schema(description = "物料sku编码")
    private String materielSkuNumber;

    @Schema(description = "物料名称")
    private String materielName;

    @Schema(description = "物料规格型号")
    private String materielModel;

    @Schema(description = "跟踪方向;  come来源 to去向")
    private String stockDirect;

    @Schema(description = "单据类型")
    private String billType;

    @Schema(description = "单据ID")
    private Long billId;

    @Schema(description = "单据编号")
    private String billNumber;

    @Schema(description = "单据时间")
    private LocalDateTime billTime;

    @Schema(description = "单据详情行号")
    private Integer billDetailSeq;

    @Schema(description = "单据详情ID")
    private Long billDetailId;

    @Schema(description = "数量")
    private BigDecimal qty;

    @Schema(description = "单位ID")
    private Long unitId;

    @Schema(description = "单位名称")
    private String unitName;

    @Schema(description = "订单类型")
    private String orderType;

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "订单编号")
    private String orderNumber;

    @Schema(description = "货主类型;保留")
    private String ownerType;

    @Schema(description = "货主id;保留")
    private Long ownerId;

    @Schema(description = "货主名称;保留")
    private String ownerName;


    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库编号
     */
    private String warehouseNumber;

    /**
     * 库位ID
     */
    private Long locationId;

    /**
     * 库位编号
     */
    private String locationNumber;

}
