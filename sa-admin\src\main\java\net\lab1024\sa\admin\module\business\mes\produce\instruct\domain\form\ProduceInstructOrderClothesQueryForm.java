package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

/**
 * 指令单成衣信息 分页查询表单
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:25:32
 * @Copyright zscbdic
 */

@Data
public class ProduceInstructOrderClothesQueryForm extends PageParam {

    @Schema(description = "关键字信息")
    private String queryKey;

    @Schema(description = "指令单id")
    private Long orderId;

    @Schema(description = "指令单编号")
    private String orderNumber;

    @Schema(description = "物料spu编号")
    private String itemSpuNumber;

    @Schema(description = "物料sku编号")
    private String itemSkuNumber;

    @Schema(description = "物料名称")
    private String itemName;

}
