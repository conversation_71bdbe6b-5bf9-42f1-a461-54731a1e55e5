package net.lab1024.sa.admin.module.business.mes.base.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 款式树表VO
 */
@Data
public class StyleTreeVO {
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "主键")
    private Long value;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "父id")
    private Long parentId;

    @Schema(description = "品类名称")
    private String styleName;

    @Schema(description = "品类名称")
    private String label;

    @Schema(description = "层级名称")
    private String fullName;

    @Schema(description = "品类代码")
    private String styleCode;

    @Schema(description = "默认模版表")
    private Long defaultSizeId;

    @Schema(description = "模版尺码信息")
    private String templateSizeMessage;

    @Schema(description = "子款式")
    private List<StyleTreeVO> children;
}
