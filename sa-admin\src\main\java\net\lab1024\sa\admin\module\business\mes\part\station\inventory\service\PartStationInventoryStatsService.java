package net.lab1024.sa.admin.module.business.mes.part.station.inventory.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationBinDao;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.dao.PartStationInventoryDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationRackDao;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.entity.PartStationInventoryEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.manager.PartStationBinManager;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.dao.FeTicketDao;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.domain.entity.FeTicketEntity;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PartStationInventoryStatsService {

    @Resource
    private PartStationInventoryDao partStationInventoryDao;

    @Resource
    private PartStationRackDao rackDao;

    @Resource
    private PartStationBinDao binDao;

    @Resource
    private FeTicketDao feTicketDao;

    @Resource
    private PartStationBinManager binManager;

    /**
     * 总扎数
     *
     * @param warehouseId
     * @return
     */
    public ResponseDTO<Long> totalTieNum(Long warehouseId) {
        if (warehouseId == null) {
            return ResponseDTO.ok(partStationInventoryDao.selectCount(null));
        }

        List<Long> binIds = binManager.getBinIdsByWarehouseId(warehouseId);
        if(CollUtil.isEmpty(binIds)){
            //没有库位数据，则默认为0
            return ResponseDTO.ok(0L);
        }

        Long count = partStationInventoryDao.selectCount(new LambdaQueryWrapper<PartStationInventoryEntity>()
                .in(CollUtil.isNotEmpty(binIds), PartStationInventoryEntity::getBinId, binIds));
        return ResponseDTO.ok(count);


    }

    /**
     * 总库存数
     *
     * @param warehouseId
     * @return
     */
    public ResponseDTO<Long> totalInventoryNum(Long warehouseId) {
        List<Long> binIds = null;
        if (warehouseId != null) {
            binIds = binManager.getBinIdsByWarehouseId(warehouseId);
            if(CollUtil.isEmpty(binIds)){
                //没有库位数据，则默认为0
                return ResponseDTO.ok(0L);
            }
        }
        List<PartStationInventoryEntity> inventorys = partStationInventoryDao.selectList(new LambdaQueryWrapper<PartStationInventoryEntity>()
                .in(CollUtil.isNotEmpty(binIds), PartStationInventoryEntity::getBinId, binIds));
        if (CollUtil.isEmpty(inventorys)) {
            return ResponseDTO.ok(0L);
        }
        List<Long> ticketIds = inventorys.stream()
                .map(PartStationInventoryEntity::getFeTicketId)
                .collect(Collectors.toList());

        // 查询菲票列表
        List<FeTicketEntity> tickets = feTicketDao.selectList(new LambdaQueryWrapper<FeTicketEntity>()
                .select(FeTicketEntity::getNum)
                .in(FeTicketEntity::getId, ticketIds));
        if (CollUtil.isEmpty(tickets)) {
            return ResponseDTO.ok(0L);
        }
        return ResponseDTO.ok(tickets.stream().mapToLong(FeTicketEntity::getNum).sum());

    }

    public ResponseDTO<Double> averageStoredDays(Long warehouseId) {
        List<Long> binIds = null;
        if (warehouseId != null) {
            binIds = binManager.getBinIdsByWarehouseId(warehouseId);
            if(CollUtil.isEmpty(binIds)){
                //没有库位数据，则默认为0
                return ResponseDTO.ok(0D);
            }
        }

        List<PartStationInventoryEntity> inventory = partStationInventoryDao.selectList(new LambdaQueryWrapper<PartStationInventoryEntity>()
                .in(CollUtil.isNotEmpty(binIds), PartStationInventoryEntity::getBinId, binIds));
        if (CollUtil.isEmpty(inventory)) {
            return ResponseDTO.ok(0D);
        }

        LocalDateTime now = LocalDateTime.now();
        OptionalDouble average = inventory.stream()
                .mapToDouble(e -> now.getDayOfYear() - e.getStockInTime().getDayOfYear()).average();
        if (average.isPresent()) {
            //保留两位小数
            Double value = Double.valueOf(String.format("%.2f", average.getAsDouble()));
            return ResponseDTO.ok(value);
        }
        return ResponseDTO.ok(0D);
    }

    /**
     * 库位使用率
     *
     * @param warehouseId
     * @return
     */
    public ResponseDTO<Double> binUsageRate(Long warehouseId) {
        List<Long> binIds = null;
        if (warehouseId != null) {
            binIds = binManager.getBinIdsByWarehouseId(warehouseId);
            if(CollUtil.isEmpty(binIds)){
                //没有库位数据，则默认为0
                return ResponseDTO.ok(0D);
            }
        }

        List<PartStationInventoryEntity> inventory = partStationInventoryDao.selectList(new LambdaQueryWrapper<PartStationInventoryEntity>()
                .select(PartStationInventoryEntity::getBinId)
                .in(CollUtil.isNotEmpty(binIds), PartStationInventoryEntity::getBinId, binIds));
        if (CollUtil.isEmpty(inventory)) {
            //没有库存数据，则默认为0
            return ResponseDTO.ok(0D);
        }
        //去重，获取使用的库位id
        List<Long> useBinIds = inventory.stream()
                .map(PartStationInventoryEntity::getBinId)
                .distinct()
                .collect(Collectors.toList());

        long allCount = CollUtil.isEmpty(binIds) ? binDao.selectCount(null) : binIds.size();
        Double usageRate = Double.valueOf(String.format("%.2f", useBinIds.size() / (double) allCount));
        return ResponseDTO.ok(usageRate);
    }


}
