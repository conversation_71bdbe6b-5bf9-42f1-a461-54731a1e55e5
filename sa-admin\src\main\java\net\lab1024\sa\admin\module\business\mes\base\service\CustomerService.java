package net.lab1024.sa.admin.module.business.mes.base.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.base.dao.CustomerDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.CustomerEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.CustomerAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.CustomerQuery;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.CustomerQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.CustomerUpdateForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.CustomerVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户表 Service
 *
 * <AUTHOR>
 * @Date 2024-07-03 09:43:14
 * @Copyright zscbdic
 */

@Service
public class CustomerService {

    @Resource
    private CustomerDao customerDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<CustomerVO> queryPage(CustomerQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<CustomerVO> list = customerDao.queryPage(page, queryForm);
        PageResult<CustomerVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(CustomerAddForm addForm) {
        CustomerEntity customerEntity = SmartBeanUtil.copy(addForm, CustomerEntity.class);

        customerDao.insert(customerEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(CustomerUpdateForm updateForm) {
        CustomerEntity customerEntity = SmartBeanUtil.copy(updateForm, CustomerEntity.class);
        customerDao.updateById(customerEntity);
        return ResponseDTO.ok();
    }

//    /**
//     * 批量删除
//     *
//     * @param idList
//     * @return
//     */
//    public ResponseDTO<String> batchDelete(List<Long> idList) {
//        if (CollectionUtils.isEmpty(idList)){
//            return ResponseDTO.ok();
//        }
//
//        customerDao.batchUpdateDeleted(idList, true);
//        return ResponseDTO.ok();
//    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }

        customerDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }

    /**
     * @return 所有客户信息
     */
    public ResponseDTO<List<CustomerVO>> queryList(CustomerQuery query) {
        List<CustomerEntity> customerEntities = customerDao.selectList(new LambdaQueryWrapper<CustomerEntity>()
                .and(StrUtil.isNotBlank(query.getQueryKey()), q -> {
                    q.like(CustomerEntity::getName, query.getQueryKey())
                            .or()
                            .like(CustomerEntity::getNumber, query.getQueryKey()).or()
                            .like(CustomerEntity::getCompany, query.getQueryKey());

                }));
        if (CollectionUtil.isNotEmpty(customerEntities)) {
            List<CustomerVO> customers = BeanUtil.copyToList(customerEntities, CustomerVO.class);
            return ResponseDTO.ok(customers);
        }
        return ResponseDTO.ok();
    }
}
