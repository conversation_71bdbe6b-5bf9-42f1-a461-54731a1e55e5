package net.lab1024.sa.admin.module.business.mes.ai.tool.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 大模型工具角色权限表 实体类
 *
 * <AUTHOR>
 * @Date 2025-04-06 15:15:25
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_ai_llm_tool_role")
public class LLMToolRoleEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 工具ID
     */
    private Long llmToolId;

    /**
     * 角色ID
     */
    private Long roleId;

}
