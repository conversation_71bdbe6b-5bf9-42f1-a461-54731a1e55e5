package net.lab1024.sa.admin.module.business.mes.stock.lot.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.entity.StkLotMasterEntity;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.form.StkLotMasterQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.vo.StkLotMasterVO;

import java.util.List;

/**
 * 批号主档 Dao
 *
 * <AUTHOR>
 * @Date 2025-02-03 19:28:09
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface StkLotMasterDao extends BaseMapper<StkLotMasterEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<StkLotMasterVO> queryPage(Page page, @Param("queryForm") StkLotMasterQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
