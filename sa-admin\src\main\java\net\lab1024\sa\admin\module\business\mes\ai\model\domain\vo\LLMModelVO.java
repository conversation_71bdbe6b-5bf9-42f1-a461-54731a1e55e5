package net.lab1024.sa.admin.module.business.mes.ai.model.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 大模型表 列表VO
 *
 * <AUTHOR>
 * @Date 2025-04-17 11:25:27
 * @Copyright zscbdic
 */

@Data
public class LLMModelVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    /**
     * 模型类型
     */
    @Schema(description = "模型类型")
    private String modelType;

    /**
     * 优先级
     */
    @Schema(description = "模型优先级;1")
    private Integer priority;

    /**
     * 模型名称
     */
    @Schema(description = "模型名称")
    private String modelName;

    /**
     * 模型别名
     */
    @Schema(description = "模型别名")
    private String modelNickname;

    /**
     * 启用标识
     */
    @Schema(description = "启用标识;1启用 0停用")
    private Boolean enableFlag;

    /**
     * 头像
     */
    @Schema(description = "头像")
    private String avatar;

    /**
     * 请求地址
     */
    @Schema(description = "请求地址")
    private String baseUrl;

    /**
     * api_key
     */
    @Schema(description = "api_key")
    private String apiKey;

    @Schema(description = "默认标识;1默认 0非默认")
    private Boolean defaultFlag;

    /**
     * 工具调用能力;1行 0不行
     */
    @Schema(description = "工具调用能力;1行 0不行")
    private Boolean useToolFlag;

    /**
     * 知识库调用能力;1行 0
     */
    @Schema(description = "知识库调用能力;1行 0不行")
    private Boolean knowledgeUseFlag;

    /**
     * 视觉调用能力;1行 0不行
     */
    @Schema(description = "视觉调用能力;1行 0不行")
    private Boolean visionUseFlag;

}
