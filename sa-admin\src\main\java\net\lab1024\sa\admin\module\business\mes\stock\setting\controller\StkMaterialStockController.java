package net.lab1024.sa.admin.module.business.mes.stock.setting.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import net.lab1024.sa.admin.module.business.mes.stock.setting.constant.StockScopeEnum;
import net.lab1024.sa.admin.module.business.mes.stock.setting.domain.form.StkMaterialStockAddForm;
import net.lab1024.sa.admin.module.business.mes.stock.setting.domain.form.StkMaterialStockQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.setting.domain.form.StkMaterialStockUpdateForm;
import net.lab1024.sa.admin.module.business.mes.stock.setting.domain.vo.StkMaterialStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.setting.service.StkMaterialStockService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 物料库存属性 Controller
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:18:19
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class StkMaterialStockController {

    @Resource
    private StkMaterialStockService stkMaterialStockService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/stkMaterialStock/queryPage")
    public ResponseDTO<PageResult<StkMaterialStockVO>> queryPage(@RequestBody @Valid StkMaterialStockQueryForm queryForm) {
        return ResponseDTO.ok(stkMaterialStockService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/stkMaterialStock/add")
    public ResponseDTO<String> add(@RequestBody @Valid StkMaterialStockAddForm addForm) {
        addForm.setScope(StockScopeEnum.ALL.getValue());
        return stkMaterialStockService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/stkMaterialStock/update")
    public ResponseDTO<String> update(@RequestBody @Valid StkMaterialStockUpdateForm updateForm) {
        return stkMaterialStockService.update(updateForm);
    }
}
