package net.lab1024.sa.admin.module.business.mes.part.station.setting.domain.vo;

import lombok.Data;

import java.util.List;

/**
 * 裁片驿站配置
 */
@Data
public class PartStationConfigVO {

    /**
     * 存储时间配置
     */
    private StorageTimeConfigVO storageTimeConfig;

    private StoragePressureConfigVO storagePressureConfig;

    private StorageManageModeConfigVO storageManageModeConfig;

    @Data
    public static class StorageTimeConfigVO {

        /**
         * 主键
         */
        private Long configId;


        /**
         * 是否启用
         */
        private Boolean enableFlag;

        /**
         * 最小存放时间
         */
        private Integer minDay;

        /**
         * 最大存放时间
         */
        private Integer maxDay;

        /**
         * 通知员工id
         */
        private List<Long> employeeIds;
    }

    @Data
    public static class StoragePressureConfigVO{
        /**
         * 主键
         */
        private Long configId;

        /**
         * 是否启用
         */
        private Boolean enableFlag;

        /**
         * 最大使用率 0-100
         */
        private Integer maxUsageRate;

        /**
         * 通知员工id
         */
        private List<Long> employeeIds;
    }

    @Data
    public static class StorageManageModeConfigVO {

        private Long configId;

        private String manageMode;
    }
}
