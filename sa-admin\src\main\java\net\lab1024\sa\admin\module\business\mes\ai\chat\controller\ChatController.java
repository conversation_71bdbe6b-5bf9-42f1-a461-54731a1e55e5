package net.lab1024.sa.admin.module.business.mes.ai.chat.controller;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.ai.chat.domain.form.AiChatMessageForm;
import net.lab1024.sa.admin.module.business.mes.ai.chat.domain.vo.AiChatMessageVO;
import net.lab1024.sa.admin.module.business.mes.ai.chat.domain.vo.AiChatVO;
import net.lab1024.sa.admin.module.business.mes.ai.chat.service.ChatService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * 大模型 聊天 接口
 */
@RestController
public class ChatController {


    @Resource
    private ChatService chatService;

    /**
     * 聊天 流S
     *
     * @param form
     * @return
     */
    @Operation(summary = "聊天")
    @PostMapping(value = "/ai/chatStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ResponseDTO<AiChatVO>> chatStream(@RequestBody @Valid AiChatMessageForm form) {
        return chatService.chatStream(form);
    }

    /**
     * 聊天
     *
     * @param form
     * @return
     */
    @Operation(summary = "聊天")
    @PostMapping(value = "/ai/chat")
    public ResponseDTO<AiChatVO> chat(@RequestBody @Valid AiChatMessageForm form) {
        return chatService.chat(form);
    }


    /**
     * 推荐提示语
     * @return
     */
    @Operation(summary = "推荐提示语")
    @GetMapping("/ai/chat/recommendPrompt")
    public ResponseDTO<List<String>> recommendPrompt() {
        return chatService.recommendPrompt();
    }


    /**
     * 清除当前聊天数据
     *
     * @return
     */
    @Operation(summary = "清除当前聊天数据")
    @GetMapping("/ai/chat/clear")
    public ResponseDTO<String> clear() {
        chatService.clearChat();
        return ResponseDTO.ok();
    }


    /**
     * 获取历史聊天记录
     *
     * @return
     */
    @Operation(summary = "获取历史聊天记录")
    @GetMapping("/ai/chat/history")
    public ResponseDTO<List<AiChatMessageVO>> getHistory() {
        return ResponseDTO.ok(chatService.getHistory());
    }


}
