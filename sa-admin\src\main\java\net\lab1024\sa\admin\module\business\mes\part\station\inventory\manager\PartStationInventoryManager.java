package net.lab1024.sa.admin.module.business.mes.part.station.inventory.manager;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jdk.dynalink.linker.LinkerServices;
import net.lab1024.sa.admin.event.tailor.FeTicketTraceLogEvent;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxInsideDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxInsideEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationBinDao;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.constant.FeTicketTraceLogTemplateEnum;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.constant.PartStationInventoryOptTypeEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.dao.PartStationInventoryDao;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.entity.PartStationInventoryEntity;
import net.lab1024.sa.admin.event.part.station.PartStationInventoryOptEvent;

import jakarta.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 裁片驿站库存表  Manager
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:44:34
 * @Copyright zscbdic
 */
@Service
public class PartStationInventoryManager extends ServiceImpl<PartStationInventoryDao, PartStationInventoryEntity> {

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private PartStationTurnBoxInsideDao partStationTurnBoxInsideDao;

    @Resource
    private PartStationTurnBoxDao partStationTurnBoxDao;


    /**
     * 入库，并发送库存变更事件
     *
     * @param feTicketId
     * @param binId
     * @param optDesc
     */
    @Transactional(rollbackFor = Exception.class)
    public void sockIn(Long feTicketId, Long binId, String optDesc) {
        PartStationInventoryEntity inventory = new PartStationInventoryEntity(feTicketId, binId);
        this.save(inventory);
        eventPublisher.publishEvent(new PartStationInventoryOptEvent(this, PartStationInventoryOptTypeEnum.IN, feTicketId, binId, optDesc));

        // 跟踪事件
        FeTicketTraceLogEvent.Payload payload = new FeTicketTraceLogEvent.Payload(feTicketId, FeTicketTraceLogTemplateEnum.PART_STATION_STOCK_IN.getContent(),
                optDesc, null, null, null);
        FeTicketTraceLogEvent event = new FeTicketTraceLogEvent(this, List.of(payload));
        eventPublisher.publishEvent(event);
    }

    /**
     * 出库，并发送库存变更事件
     *
     * @param feTicketId
     * @param optDesc
     */
    @Transactional(rollbackFor = Exception.class)
    public void stockOut(Long feTicketId, Long binId, String optDesc) {
        LambdaQueryWrapper<PartStationInventoryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PartStationInventoryEntity::getFeTicketId, feTicketId);
        this.remove(queryWrapper);
        eventPublisher.publishEvent(new PartStationInventoryOptEvent(this, PartStationInventoryOptTypeEnum.OUT, feTicketId, binId, optDesc));

        // 发布跟踪事件
        FeTicketTraceLogEvent.Payload payload = new FeTicketTraceLogEvent.Payload(feTicketId, FeTicketTraceLogTemplateEnum.PART_STATION_STOCK_OUT.getContent(),
                optDesc, null, null, null);
        FeTicketTraceLogEvent event = new FeTicketTraceLogEvent(this, List.of(payload));
        eventPublisher.publishEvent(event);
    }

    /**
     * 库位移动，并发送库存变更事件
     *
     * @param feTicketId
     * @param binId
     * @param optDesc
     */
    @Transactional(rollbackFor = Exception.class)
    public void stockMove(Long feTicketId, Long binId, String optDesc) {
        this.lambdaUpdate().eq(PartStationInventoryEntity::getFeTicketId, feTicketId)
                .set(PartStationInventoryEntity::getBinId, binId)
                .update();
        eventPublisher.publishEvent(new PartStationInventoryOptEvent(this, PartStationInventoryOptTypeEnum.MOVE, feTicketId, binId, optDesc));

        // 发布跟踪事件
        FeTicketTraceLogEvent.Payload payload = new FeTicketTraceLogEvent.Payload(feTicketId, FeTicketTraceLogTemplateEnum.PART_STATION_STOCK_MOVE.getContent(),
                optDesc, null, null, null);
        FeTicketTraceLogEvent event = new FeTicketTraceLogEvent(this, List.of(payload));
        eventPublisher.publishEvent(event);
    }

    /**
     * 库存盘点
     *
     * @param feTicketId
     * @param binId
     * @param optDesc
     */
    @Transactional(rollbackFor = Exception.class)
    public void stockTake(Long feTicketId, Long binId, String optDesc) {
        this.lambdaUpdate()
                .eq(PartStationInventoryEntity::getFeTicketId, feTicketId)
                .eq(PartStationInventoryEntity::getBinId, binId)
                .set(PartStationInventoryEntity::getLastCheckTime, LocalDateTime.now())
                .update();
        eventPublisher.publishEvent(new PartStationInventoryOptEvent(this, PartStationInventoryOptTypeEnum.TAKE, feTicketId, binId, optDesc));
    }

    /**
     * 周转箱批量入库，并发送库存变更事件
     *
     * @param boxId   周转箱id
     * @param binId   货位id
     * @param optDesc 操作描述
     */
    @Transactional(rollbackFor = Exception.class)
    public void stockInByBox(List<Long> feTicketIds, Long boxId, Long binId, String optDesc) {
        partStationTurnBoxDao.update(new LambdaUpdateWrapper<PartStationTurnBoxEntity>()
                .eq(PartStationTurnBoxEntity::getId, boxId)
                .set(PartStationTurnBoxEntity::getInsideFlag, true)
                .set(PartStationTurnBoxEntity::getInsideLocationId, binId));

        if (CollUtil.isEmpty(feTicketIds)) {
            return;
        }

        List<PartStationInventoryEntity> invs = feTicketIds.stream().map(feTicketId -> {
            return new PartStationInventoryEntity(feTicketId, binId);
        }).toList();
        this.saveBatch(invs);

        eventPublisher.publishEvent(new PartStationInventoryOptEvent(this, PartStationInventoryOptTypeEnum.IN, feTicketIds, binId, optDesc));

        // 跟踪事件
        List<FeTicketTraceLogEvent.Payload> payloads = feTicketIds.stream().map(feTicketId -> {
            return new FeTicketTraceLogEvent.Payload(feTicketId, FeTicketTraceLogTemplateEnum.PART_STATION_STOCK_IN.getContent(),
                    optDesc, null, null, null);
        }).toList();
        FeTicketTraceLogEvent event = new FeTicketTraceLogEvent(this, payloads);
        eventPublisher.publishEvent(event);
    }

    /**
     * 周转箱批量出库，并发送库存变更事件
     *
     * @param feTicketIds
     * @param boxId
     * @param binId
     * @param optDesc
     */
    @Transactional(rollbackFor = Exception.class)
    public void stockOutByBox(List<Long> feTicketIds, Long boxId, Long binId, String optDesc) {
        // 更新周转箱状态
        partStationTurnBoxDao.update(new LambdaUpdateWrapper<PartStationTurnBoxEntity>()
                .eq(PartStationTurnBoxEntity::getId, boxId)
                .set(PartStationTurnBoxEntity::getInsideFlag, false)
                .set(PartStationTurnBoxEntity::getInsideLocationId, null));

        if (CollUtil.isEmpty(feTicketIds)) {
            return;
        }
        // 删除库存记录
        LambdaQueryWrapper<PartStationInventoryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PartStationInventoryEntity::getFeTicketId, feTicketIds);
        this.remove(queryWrapper);

        eventPublisher.publishEvent(new PartStationInventoryOptEvent(this, PartStationInventoryOptTypeEnum.OUT, feTicketIds, binId, optDesc));
        // 跟踪事件
        List<FeTicketTraceLogEvent.Payload> payloads = feTicketIds.stream().map(feTicketId -> {
            return new FeTicketTraceLogEvent.Payload(feTicketId, FeTicketTraceLogTemplateEnum.PART_STATION_STOCK_OUT.getContent(),
                    optDesc, null, null, null);
        }).toList();
        FeTicketTraceLogEvent event = new FeTicketTraceLogEvent(this, payloads);
        eventPublisher.publishEvent(event);
    }

    /**
     * 周转箱移动，并发送库存变更事件
     *
     * @param feTicketIds
     * @param turnBoxId
     * @param binId
     * @param optDesc
     */
    @Transactional(rollbackFor = Exception.class)
    public void stockMoveByBox(List<Long> feTicketIds, Long boxId, Long binId, String optDesc) {
        // 更新周转箱状态
        partStationTurnBoxDao.update(new LambdaUpdateWrapper<PartStationTurnBoxEntity>()
                .eq(PartStationTurnBoxEntity::getId, boxId)
                .set(PartStationTurnBoxEntity::getInsideFlag, true)
                .set(PartStationTurnBoxEntity::getInsideLocationId, binId));

        if (CollUtil.isEmpty(feTicketIds)) {
            return;
        }
        // 更新库存记录
        this.lambdaUpdate().in(PartStationInventoryEntity::getFeTicketId, feTicketIds)
                .set(PartStationInventoryEntity::getBinId, binId)
                .update();

        eventPublisher.publishEvent(new PartStationInventoryOptEvent(this, PartStationInventoryOptTypeEnum.MOVE, feTicketIds, binId, optDesc));
        // 发布跟踪事件
        List<FeTicketTraceLogEvent.Payload> payloads = feTicketIds.stream().map(feTicketId -> {
            return new FeTicketTraceLogEvent.Payload(feTicketId, FeTicketTraceLogTemplateEnum.PART_STATION_STOCK_MOVE.getContent(),
                    optDesc, null, null, null);
        }).toList();
        FeTicketTraceLogEvent event = new FeTicketTraceLogEvent(this, payloads);
        eventPublisher.publishEvent(event);
    }
}
