package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationWarehouseEntity;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.List;

@Data
public class PartStationWarehouseQuickAddForm {

    @Schema(description = "仓库名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "仓库名称 不能为空")
    private String name;

    @Schema(description = "仓库编码")
    @NotNull(message = "仓库编码 不能为空")
    private String warehouseCode;

    @Schema(description = "仓库地址")
    private String address;

    @Schema(description = "备注")
    private String remark;

    /**
     * 货架数量
     */
    @NotNull(message = "货架数量不能为空")
    @Min(value = 1, message = "货架数量不能小于1")
    @Max(value = 30, message = "货架数量不能大于30")
    private Integer rackNum;

    /**
     * 层数
     */
    @NotNull(message = "层数不能为空")
    @Min(value = 1, message = "层数不能小于1")
    @Max(value = 5, message = "层数不能大于5")
    private Integer layerNum;

    /**
     * 每层货位数
     */
    @NotNull(message = "每层货位数不能为空")
    @Min(value = 1, message = "每层货位数不能小于1")
    @Max(value = 8, message = "每层货位数不能大于8")
    private Integer binNum;

    /**
     * 容量
     */
    @NotNull(message = "容量不能为空")
    @Min(value = 1, message = "容量不能小于1")
    private Integer capacity;

    @Schema(description = "仓库任务员")
    private List<Long> taskerIds;

    public PartStationWarehouseEntity toWarehouseEntity() {
        PartStationWarehouseEntity entity = new PartStationWarehouseEntity();
        entity.setName(name);
        entity.setAddress(address);
        entity.setRemark(remark);
        if(CollUtil.isEmpty(taskerIds)){
            entity.setTaskerIds("[]");
        }else {
            entity.setTaskerIds(JSON.toJSONString(taskerIds));
        }
        entity.setWarehouseCode(warehouseCode);
        return entity;
    }



}
