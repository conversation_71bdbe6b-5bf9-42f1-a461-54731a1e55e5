package net.lab1024.sa.admin.module.business.mes.stock.warehouse.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.dao.StkInventoryDao;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.entity.StkInventoryEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.dao.StkLocationDao;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkLocationEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkLocationAddForm;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkLocationUpdateForm;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 货位  Manager
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:14:20
 * @Copyright zscbdic
 */
@Service
public class StkLocationManager extends ServiceImpl<StkLocationDao, StkLocationEntity> {

    @Resource
    private StkInventoryDao stkInventoryDao;

    /**
     * 添加校验
     *
     * @param addForm
     */
    public void addCheck(StkLocationAddForm addForm) {
        String number = addForm.getNumber();
        long count = this.count(new LambdaQueryWrapper<StkLocationEntity>()
                .eq(StkLocationEntity::getNumber, number));
        if (count > 0) {
            throw new BusinessException("货位编号已存在");
        }

    }

    /**
     * 更新校验
     *
     * @param updateForm
     */
    public void updateCheck(StkLocationUpdateForm updateForm) {
        Long id = updateForm.getId();
        String number = updateForm.getNumber();
        long count = this.count(new LambdaQueryWrapper<StkLocationEntity>()
                .eq(StkLocationEntity::getNumber, number)
                .ne(StkLocationEntity::getId, id));
        if (count > 0) {
            throw new BusinessException("货位编号已存在");
        }
    }

    /**
     * 删除校验
     *
     * @param locationIds
     */
    public void deleteCheck(List<Long> locationIds) {
        Long count = stkInventoryDao.selectCount(new LambdaQueryWrapper<StkInventoryEntity>()
                .in(StkInventoryEntity::getLocationId, locationIds));
        if (count > 0) {
            throw new BusinessException("该货位存在库存，无法删除");
        }
    }

    public void deleteCheck(Long locationId) {
        Long count = stkInventoryDao.selectCount(new LambdaQueryWrapper<StkInventoryEntity>()
                .eq(StkInventoryEntity::getLocationId, locationId));
        if (count > 0) {
            throw new BusinessException("该货位存在库存，无法删除");
        }
    }
}
