package net.lab1024.sa.admin.module.business.mes.stock.in.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 入库单 新建表单
 *
 * <AUTHOR>
 * @Date 2025-02-03 15:54:07
 * @Copyright zscbdic
 */

@Data
public class StkInStockAddForm {

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

//    /**
//     * 单据来源类型
//     */
//    @Schema(description = "单据来源类型")
//    private String originType;
//
//    /**
//     * 单据来源ID
//     */
//    @Schema(description = "单据来源ID")
//    private Long originId;
//
//    /**
//     * 单据来源编号
//     */
//    @Schema(description = "单据来源编号")
//    private String originNumber;
//
//    /**
//     * 单据类型
//     */
//    @Schema(description = "单据类型", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "单据类型 不能为空")
//    @CheckEnum(value = BillType.class,required = true,message = "单据类型错误")
//    private String type;
//
//    /**
//     * 单据方式
//     */
//    @Schema(description = "单据方式")
//    private String way;
//
//    /**
//     * 单据状态
//     */
//    @Schema(description = "单据状态", requiredMode = Schema.RequiredMode.REQUIRED)
////    @NotBlank(message = "单据状态; 不能为空")
//    private String status;

    /**
     * 单据编号
     */
    @Schema(description = "单据编号", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "单据编号 不能为空")
    private String number;

    /**
     * 仓库ID
     */
    @Schema(description = "仓库ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "仓库ID 不能为空")
    private Long warehouseId;

    /**
     * 入库时间
     */
    @Schema(description = "入库时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "入库时间 不能为空")
    private LocalDateTime inStockTime;

    /**
     * 仓管员ID
     */
    @Schema(description = "仓管员ID")
    private Long stockerId;

    /**
     * 仓管员名称
     */
    @Schema(description = "仓管员名称")
    private String stockerName;

    /**
     * 货主类型
     */
    @Schema(description = "货主类型")
    private String ownerType;

    /**
     * 货主id
     */
    @Schema(description = "货主id")
    private Long ownerId;

    /**
     * 货主名称
     */
    @Schema(description = "货主名称")
    private String ownerName;

    /**
     * 申请人ID
     */
    @Schema(description = "申请人ID")
    private Long applicantId;

    /**
     * 申请人名称
     */
    @Schema(description = "申请人名称")
    private String applicantName;

    /**
     * 申请时间
     */
    @Schema(description = "申请时间")
    private LocalDateTime applyTime;

    /**
     * 审核人ID
     */
    @Schema(description = "审核人ID")
    private Long auditorId;

    /**
     * 审核人名称
     */
    @Schema(description = "审核人名称")
    private String auditorName;

    /**
     * 审核时间
     */
    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

//    /**
//     * 入库单详情
//     */
//    @Valid
//    @NotEmpty(message = "入库单详情 不能为空")
//    @Schema(description = "入库单详情")
//    private ValidateList<StkInStockDetailAddForm> details;

}
