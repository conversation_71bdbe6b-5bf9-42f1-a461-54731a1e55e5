package net.lab1024.sa.admin.module.business.mes.salary.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 结算方式枚举
 */
@AllArgsConstructor
@Getter
public enum SettlementWayEnum implements BaseEnum {

    BY_DAY("1", "日结"),


    BY_MONTH("2", "月结");


    private final String value;

    private final String desc;

    public static SettlementWayEnum getByValue(String value) {
        for (SettlementWayEnum item : SettlementWayEnum.values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }
}
