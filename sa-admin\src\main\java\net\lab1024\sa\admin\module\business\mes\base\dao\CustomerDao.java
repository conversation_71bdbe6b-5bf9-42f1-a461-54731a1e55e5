package net.lab1024.sa.admin.module.business.mes.base.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.CustomerEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.CustomerQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.CustomerVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 客户表 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-03 09:43:14
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface CustomerDao extends BaseMapper<CustomerEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<CustomerVO> queryPage(Page page, @Param("queryForm") CustomerQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
//    /**
//     * 批量更新删除状态
//     */
//    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
