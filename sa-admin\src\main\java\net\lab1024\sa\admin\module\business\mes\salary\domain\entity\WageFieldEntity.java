package net.lab1024.sa.admin.module.business.mes.salary.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 工资字段 实体类
 *
 * <AUTHOR>
 * @Date 2024-11-25 20:20:26
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_salary_wage_field")
public class WageFieldEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 运算类型;0 加项,1减项
     */
    private String type;

    /**
     * 配置值（保留）
     */
    private BigDecimal value;

}
