package net.lab1024.sa.admin.module.business.mes.stock.in.domain.vo;

import lombok.Data;

import java.time.LocalDate;

@Data
public class StkPrintCodeVO {

    /**
     * 二维码值
     */
    private String code;

    /**
     * 货主类型
     */
    private String ownerType;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 物料spu
     */
    private String materialSpuNumber;

    /**
     * 物料sku
     */
    private String materialSkuNumber;

    /**
     * 物料名词
     */
    private String materialName;

    /**
     * 物料型号
     */
    private String materialModel;

    /**
     * 批次号
     */
    private String lotNumber;

    /**
     * 入库时间
     */
    private LocalDate inStockDate;

    /**
     * 数量
     */
    private Integer qty;
}
