package net.lab1024.sa.admin.module.business.mes.stock.inventory.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.base.dao.CustomerDao;
import net.lab1024.sa.admin.module.business.mes.base.dao.SupplierDao;
import net.lab1024.sa.admin.module.business.mes.base.dao.UnitDao;
import net.lab1024.sa.admin.module.business.mes.factory.dao.WorkshopDao;
import net.lab1024.sa.admin.module.business.mes.item.dao.ItemDao;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemEntity;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockOptTypeEnum;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.bo.StkInStockBO;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.entity.StkInStockDetailEntity;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.entity.StkInStockEntity;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.dao.StkInventoryDao;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.dao.StkInventoryLogDao;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.entity.StkInventoryEntity;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.entity.StkInventoryLogEntity;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.bo.StkOutStockBO;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity.StkOutStockDetailEntity;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity.StkOutStockEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.dao.StkLocationDao;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.dao.StkWarehouseDao;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkWarehouseEntity;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.common.util.SmartRequestUtil;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 即时库存更新日志  Manager
 *
 * <AUTHOR>
 * @Date 2025-01-15 11:20:19
 * @Copyright zscbdic
 */
@Service
public class StkInventoryLogManager extends ServiceImpl<StkInventoryLogDao, StkInventoryLogEntity> {
    @Resource
    private ItemDao itemDao;

    @Resource
    private UnitDao unitDao;

    @Resource
    private SupplierDao supplierDao;

    @Resource
    private WorkshopDao workshopDao;

    @Resource
    private CustomerDao customerDao;

    @Resource
    private StkWarehouseDao stkWarehouseDao;

    @Resource
    private StkLocationDao stkLocationDao;

    @Resource
    private StkInventoryDao stkInventoryDao;

    /**
     * 解析日志
     *
     * @param bo
     * @param in
     * @return
     */
    public List<StkInventoryLogEntity> parseInLogs(StkInStockBO bo, StockOptTypeEnum optTypeEnum) {
        StkInStockEntity stkInStock = bo.getStkInStock();
        List<StkInStockDetailEntity> details = bo.getDetails();
        //物料
        List<Long> materielIds = details.stream()
                .map(StkInStockDetailEntity::getMaterielId)
                .distinct()
                .collect(Collectors.toList());
        List<ItemEntity> materiels = itemDao.selectBatchIds(materielIds);
        if (CollUtil.isEmpty(materiels) || materiels.size() != materielIds.size()) {
            throw new BusinessException("物料不存在");
        }
        Map<Long, ItemEntity> mMap = materiels.stream()
                .collect(Collectors.toMap(ItemEntity::getId, item -> item));
        //仓库
        Long warehouseId = stkInStock.getWarehouseId();
        StkWarehouseEntity warehouse = stkWarehouseDao.selectById(warehouseId);
        if (warehouse == null) {
            throw new BusinessException("仓库不存在");
        }
        //仓位
//        List<Long> locationIds = details.stream()
//                .map(StkInStockDetailEntity::getLocationId)
//                .filter(Objects::nonNull)
//                .collect(Collectors.toList());
//        List<StkLocationEntity> locations = stkLocationDao.selectBatchIds(locationIds);
//        if (CollUtil.isEmpty(locations)) {
//            throw new BusinessException("仓位不存在");
//        }
        //库存数据
        List<StkInventoryEntity> queryInv = details.stream().map(d -> {
            StkInventoryEntity stkInventoryEntity = new StkInventoryEntity();
            stkInventoryEntity.setWarehouseId(warehouseId);
            stkInventoryEntity.setLocationId(d.getLocationId());
            stkInventoryEntity.setMaterielId(d.getMaterielId());
            stkInventoryEntity.setLotId(d.getLotId());
//            stkInventoryEntity.setOwnerType(stkInStock.getOwnerType());
//            stkInventoryEntity.setOwnerId(stkInStock.getOwnerId());
            return stkInventoryEntity;
        }).collect(Collectors.toList());
        List<StkInventoryEntity> invData = stkInventoryDao.queryList(queryInv);
        Map<String, BigDecimal> invMap;
        String invMapKeyFormat = "%s_%s_%s_%s";
        if (CollUtil.isNotEmpty(invData)) {
            invMap = invData.stream().collect(Collectors.toMap(
                    k -> String.format(invMapKeyFormat,
                            warehouse.getId(),
                            k.getLocationId() == null ? "null" : k.getLocationId(),
                            k.getMaterielId(),
                            k.getLotId() == null ? "null" : k.getLotId()
                    ),
                    StkInventoryEntity::getQty
            ));
        } else {
            invMap = new HashMap<>();
        }
        List<StkInventoryLogEntity> logs = details.stream().map(d -> {
            StkInventoryLogEntity log = new StkInventoryLogEntity();
            //仓库
            log.setWarehouseId(warehouseId);
            log.setWarehouseName(warehouse.getName());
            log.setWarehouseNumber(warehouse.getNumber());
            log.setLocationId(d.getLocationId());
            log.setLocationNumber(d.getLocationNumber());
            //物料
            log.setMaterielId(d.getMaterielId());
            log.setMaterielName(mMap.get(d.getMaterielId()).getName());
            log.setMaterielModel(mMap.get(d.getMaterielId()).getModel());
            log.setMaterielSpuNumber(mMap.get(d.getMaterielId()).getNumber());
            log.setMaterielSkuNumber(mMap.get(d.getMaterielId()).getSkuNumber());
            //单位
            log.setUnitId(d.getUnitId());
            log.setUnitName(d.getUnitName());
            //货主
            log.setOwnerType(stkInStock.getOwnerType());
            log.setOwnerId(stkInStock.getOwnerId());
            log.setOwnerName(stkInStock.getOwnerName());
            //批次
            log.setLotId(d.getLotId());
            log.setLotNumber(d.getLotNumber());
//            log.setSnId(d.getSnId());
//            log.setSnNumber(d.getSnNumber());
            //操作数据
            log.setOptType(optTypeEnum.getValue());
            log.setOptQty(d.getQty());
            String invKey = String.format(invMapKeyFormat,
                    warehouse.getId(),
                    d.getLocationId() == null ? "null" : d.getLocationId(),
                    d.getMaterielId(),
                    d.getLotId() == null ? "null" : d.getLotId()
            );
            BigDecimal beforeQty = invMap.get(invKey) == null ? BigDecimal.ZERO : invMap.get(invKey);
            log.setOptBeforeQty(beforeQty);
            BigDecimal afterQty = beforeQty.add(d.getQty());
            invMap.put(invKey, afterQty);
            log.setOptAfterQty(afterQty);

            RequestUser user = SmartRequestUtil.getRequestUser();
            log.setOptUserId(user.getUserId());
            log.setOptUserName(user.getUserName());
            log.setOptTime(LocalDateTime.now());

            log.setBizFormType(stkInStock.getType());
            log.setBizFormId(stkInStock.getId());
            log.setBizFormNumber(stkInStock.getNumber());
            log.setBizFormDetailId(d.getId());

            log.setWorkFormType(stkInStock.getType());
            log.setWorkFormId(stkInStock.getId());
            log.setWorkFormNumber(stkInStock.getNumber());
            log.setWorkFormDetailId(d.getId());
            return log;
        }).collect(Collectors.toList());

        return logs;
    }


    /**
     * 解析日志
     *
     * @param bo
     * @param optTypeEnum
     * @return
     */
    public List<StkInventoryLogEntity> parseOutLogs(StkOutStockBO bo, StockOptTypeEnum optTypeEnum) {
        StkOutStockEntity stkOutStock = bo.getStkOutStock();
        List<StkOutStockDetailEntity> details = bo.getDetails();
        //物料
        List<Long> materielIds = details.stream()
                .map(StkOutStockDetailEntity::getMaterielId)
                .distinct()
                .collect(Collectors.toList());
        List<ItemEntity> materiels = itemDao.selectBatchIds(materielIds);
        if (CollUtil.isEmpty(materiels) || materiels.size() != materielIds.size()) {
            throw new BusinessException("物料不存在");
        }
        Map<Long, ItemEntity> mMap = materiels.stream()
                .collect(Collectors.toMap(ItemEntity::getId, item -> item));
        //仓库
        Long warehouseId = stkOutStock.getWarehouseId();
        StkWarehouseEntity warehouse = stkWarehouseDao.selectById(warehouseId);
        if (warehouse == null) {
            throw new BusinessException("仓库不存在");
        }
        //仓位
//        List<Long> locationIds = details.stream()
//                .map(StkInStockDetailEntity::getLocationId)
//                .filter(Objects::nonNull)
//                .collect(Collectors.toList());
//        List<StkLocationEntity> locations = stkLocationDao.selectBatchIds(locationIds);
//        if (CollUtil.isEmpty(locations)) {
//            throw new BusinessException("仓位不存在");
//        }
        //库存数据
        List<StkInventoryEntity> queryInv = details.stream().map(d -> {
            StkInventoryEntity stkInventoryEntity = new StkInventoryEntity();
            stkInventoryEntity.setWarehouseId(warehouseId);
            stkInventoryEntity.setLocationId(d.getLocationId());
            stkInventoryEntity.setMaterielId(d.getMaterielId());
            stkInventoryEntity.setLotId(d.getLotId());
//            stkInventoryEntity.setOwnerType(stkInStock.getOwnerType());
//            stkInventoryEntity.setOwnerId(stkInStock.getOwnerId());
            return stkInventoryEntity;
        }).collect(Collectors.toList());
        List<StkInventoryEntity> invData = stkInventoryDao.queryList(queryInv);
        Map<String, BigDecimal> invMap;
        String invMapKeyFormat = "%s_%s_%s_%s";
        if (CollUtil.isNotEmpty(invData)) {
            invMap = invData.stream().collect(Collectors.toMap(
                    k -> String.format(invMapKeyFormat,
                            warehouse.getId(),
                            k.getLocationId() == null ? "null" : k.getLocationId(),
                            k.getMaterielId(),
                            k.getLotId() == null ? "null" : k.getLotId()
                    ),
                    StkInventoryEntity::getQty
            ));
        } else {
            invMap = new HashMap<>();
        }
        List<StkInventoryLogEntity> logs = details.stream().map(d -> {
            StkInventoryLogEntity log = new StkInventoryLogEntity();
            //仓库
            log.setWarehouseId(warehouseId);
            log.setWarehouseName(warehouse.getName());
            log.setWarehouseNumber(warehouse.getNumber());
            log.setLocationId(d.getLocationId());
            log.setLocationNumber(d.getLocationNumber());
            //物料
            log.setMaterielId(d.getMaterielId());
            log.setMaterielName(mMap.get(d.getMaterielId()).getName());
            log.setMaterielModel(mMap.get(d.getMaterielId()).getModel());
            log.setMaterielSpuNumber(mMap.get(d.getMaterielId()).getNumber());
            log.setMaterielSkuNumber(mMap.get(d.getMaterielId()).getSkuNumber());
            //单位
            log.setUnitId(d.getUnitId());
            log.setUnitName(d.getUnitName());
            //货主
            log.setOwnerType(stkOutStock.getOwnerType());
            log.setOwnerId(stkOutStock.getOwnerId());
            log.setOwnerName(stkOutStock.getOwnerName());
            //批次
            log.setLotId(d.getLotId());
            log.setLotNumber(d.getLotNumber());
//            log.setSnId(d.getSnId());
//            log.setSnNumber(d.getSnNumber());
            //操作数据
            log.setOptType(optTypeEnum.getValue());
            log.setOptQty(d.getQty());
            String invKey = String.format(invMapKeyFormat,
                    warehouse.getId(),
                    d.getLocationId() == null ? "null" : d.getLocationId(),
                    d.getMaterielId(),
                    d.getLotId() == null ? "null" : d.getLotId()
            );
            BigDecimal beforeQty = invMap.get(invKey) == null ? BigDecimal.ZERO : invMap.get(invKey);
            log.setOptBeforeQty(beforeQty);
            BigDecimal afterQty = beforeQty.subtract(d.getQty());
            invMap.put(invKey, afterQty);
            log.setOptAfterQty(afterQty);

            RequestUser user = SmartRequestUtil.getRequestUser();
            log.setOptUserId(user.getUserId());
            log.setOptUserName(user.getUserName());
            log.setOptTime(LocalDateTime.now());

            log.setBizFormType(stkOutStock.getType());
            log.setBizFormId(stkOutStock.getId());
            log.setBizFormNumber(stkOutStock.getNumber());
            log.setBizFormDetailId(d.getId());

            log.setWorkFormType(stkOutStock.getType());
            log.setWorkFormId(stkOutStock.getId());
            log.setWorkFormNumber(stkOutStock.getNumber());
            log.setWorkFormDetailId(d.getId());
            return log;
        }).collect(Collectors.toList());

        return logs;
    }


}
