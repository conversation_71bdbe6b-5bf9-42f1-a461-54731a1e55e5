package net.lab1024.sa.admin.module.business.mes.stock.out.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 出库单详情 列表VO
 *
 * <AUTHOR>
 * @Date 2025-02-09 19:59:35
 * @Copyright zscbdic
 */

@Data
public class StkOutStockDetailVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

//    @Schema(description = "删除标识;0未删除，1删除")
//    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "出库单ID")
    private Long outStockId;

    /**
     * 来源单类型
     */
    @Schema(description = "来源订单类型")
    private String originOrderBillType;

    /**
     * 来源单ID
     */
    @Schema(description = "来源订单ID")
    private Long originOrderBillId;

    /**
     * 来源单号
     */
    @Schema(description = "来源订单编号")
    private String originOrderBillNumber;

    @Schema(description = "单据来源详情类型")
    private String originDetailType;

    @Schema(description = "单据来源详情ID")
    private Long originDetailId;

    @Schema(description = "单据来源详情行号")
    private Integer originDetailSeq;

    @Schema(description = "行号")
    private Integer seq;

    @Schema(description = "物料ID")
    private Long materielId;

    @Schema(description = "批次ID")
    private Long lotId;

    @Schema(description = "批次编号")
    private String lotNumber;

    @Schema(description = "SN集合")
    private String sns;

    @Schema(description = "仓位id")
    private Long locationId;

    @Schema(description = "仓位编号")
    private String locationNumber;

    @Schema(description = "单位id")
    private Long unitId;

    @Schema(description = "单位名称")
    private String unitName;

    @Schema(description = "实收数量")
    private BigDecimal qty;

    @Schema(description = "关联数量")
    private BigDecimal joinQty;

    @Schema(description = "单价")
    private BigDecimal price;

    @Schema(description = "金额")
    private BigDecimal amount;

    /**
     * 出库类型
     */
    @Schema(description = "出库类型")
    private String outType;

    /**
     * 出库原因
     */
    @Schema(description = "出库原因")
    private String outReason;


}
