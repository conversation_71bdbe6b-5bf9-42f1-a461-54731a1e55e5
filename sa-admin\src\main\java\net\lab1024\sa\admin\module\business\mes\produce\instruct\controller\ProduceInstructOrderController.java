package net.lab1024.sa.admin.module.business.mes.produce.instruct.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.constant.ProduceInstructOrderProduceTypeEnum;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.*;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderVO;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.service.ProduceInstructOrderService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 生产指令单 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:22:24
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "生产指令单接口")
public class ProduceInstructOrderController {

    @Resource
    private ProduceInstructOrderService produceInstructOrderService;

    @Resource
    private SerialNumberService serialNumberService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/produceInstructOrder/queryPage")
    public ResponseDTO<PageResult<ProduceInstructOrderVO>> queryPage(@RequestBody @Valid ProduceInstructOrderQueryForm queryForm) {
        return ResponseDTO.ok(produceInstructOrderService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/produceInstructOrder/add")
    public ResponseDTO<String> add(@RequestBody @Valid ProduceInstructOrderAddForm addForm) {
        if(StrUtil.isBlank(addForm.getInstructNumber())){
            addForm.setInstructNumber(serialNumberService.generate(SerialNumberIdEnum.PRODUCE));
        }

        addForm.setProduceType(ProduceInstructOrderProduceTypeEnum.SELF_PRODUCE.getValue());
        List<ProduceInstructOrderArrangeAddForm> arrangeList = addForm.getArrangeList();
        List<ProduceInstructOrderItemAddForm> itemList = addForm.getItemList();
        List<ProduceInstructOrderClothesAddForm> clothesList = addForm.getClothesList();
        List<ProduceInstructOrderProcessAddForm> processList = addForm.getProcessList();

        if (CollUtil.isNotEmpty(arrangeList)) {
            String verify = SmartBeanUtil.verify(arrangeList);
            if (verify != null) {
                return ResponseDTO.userErrorParam(verify);
            }
        }
        if (CollUtil.isNotEmpty(itemList)) {
            String verify = SmartBeanUtil.verify(itemList);
            if (verify != null) {
                return ResponseDTO.userErrorParam(verify);
            }
        }
        if (CollUtil.isNotEmpty(clothesList)) {
            String verify = SmartBeanUtil.verify(clothesList);
            if (verify != null) {
                return ResponseDTO.userErrorParam(verify);
            }
        }
        if (CollUtil.isNotEmpty(processList)) {
            String verify = SmartBeanUtil.verify(processList);
            if (verify != null) {
                return ResponseDTO.userErrorParam(verify);
            }
        }

        return produceInstructOrderService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/produceInstructOrder/update")
    public ResponseDTO<String> update(@RequestBody @Valid ProduceInstructOrderUpdateForm updateForm) {
        List<ProduceInstructOrderArrangeUpdateForm> arrangeList = updateForm.getArrangeList();
        if (CollUtil.isNotEmpty(arrangeList)) {
            String verify = SmartBeanUtil.verify(arrangeList);
            if (verify != null) {
                return ResponseDTO.userErrorParam(verify);
            }
        }
        List<ProduceInstructOrderItemUpdateForm> itemList = updateForm.getItemList();
        if (CollUtil.isNotEmpty(itemList)) {
            String verify = SmartBeanUtil.verify(itemList);
            if (verify != null) {
                return ResponseDTO.userErrorParam(verify);
            }
        }
        List<ProduceInstructOrderClothesUpdateForm> clothesList = updateForm.getClothesList();
        if (CollUtil.isNotEmpty(clothesList)) {
            String verify = SmartBeanUtil.verify(clothesList);
            if (verify != null) {
                return ResponseDTO.userErrorParam(verify);
            }
        }
        List<ProduceInstructOrderProcessUpdateForm> processList = updateForm.getProcessList();
        if (CollUtil.isNotEmpty(processList)) {
            String verify = SmartBeanUtil.verify(processList);
            if (verify != null) {
                return ResponseDTO.userErrorParam(verify);
            }
        }
        return produceInstructOrderService.update(updateForm);
    }


    /**
     * 删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/produceInstructOrder/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return produceInstructOrderService.delete(id);
    }

    /**
     * 获取详情
     * @param id
     * @return
     */
    @Operation(summary = "获取详情 <AUTHOR>
    @GetMapping("/produceInstructOrder/get/{id}")
    public ResponseDTO<ProduceInstructOrderVO> get(@PathVariable Long id) {
        return produceInstructOrderService.get(id);
    }


    /**
     * 当个下达指令单
     */
    @Operation(summary = "单个下达指令单 <AUTHOR>
    @GetMapping("/produceInstructOrder/issued/{id}")
    public ResponseDTO<String> issued(@PathVariable Long id) {
        return produceInstructOrderService.issued(id);
    }

    /**
     * 单个反下达指令单
     * @param id
     * @return
     */
    @Operation(summary = "单个反下达指令单 <AUTHOR>
    @GetMapping("/produceInstructOrder/reIssued/{id}")
    public ResponseDTO<String> reIssued(@PathVariable Long id) {
        return produceInstructOrderService.reIssued(id);
    }

    /**
     * 获取指令单基本信息
     * @param id
     * @return
     */
    @Operation(summary = "获取指令单基本信息 <AUTHOR>
    @GetMapping("/produceInstructOrder/getBaseInfo/{id}")
    public ResponseDTO<ProduceInstructOrderVO> getBaseInfo(@PathVariable Long id) {
        return produceInstructOrderService.getBaseInfo(id);
    }

    /**
     * 修改指令单优先级
     * @param changePriorityForm
     * @return
     */
    @Operation(description = "修改指令单优先级 <AUTHOR>
    @PostMapping("/produceInstructOrder/changePriority")
    public ResponseDTO<String>  changePriority(@RequestBody ChangePriorityForm changePriorityForm){

        return produceInstructOrderService.changePriority(changePriorityForm);
    }


    /**
     * 自动生成生产指令编号
     * @return
     */
    @Operation(summary = "自动生成生产指令编号 <AUTHOR>
    @GetMapping("/produceInstructOrder/getProduceInstructOrderNo")
    public ResponseDTO<String> getProduceInstructOrderNo() {
        return ResponseDTO.ok(serialNumberService.generate(SerialNumberIdEnum.PRODUCE));
    }


    /**
     * 强制完工 <AUTHOR>
     * @param ids 指令单id
     * @return
     */
    @Operation(summary = "强制完工 <AUTHOR>
    @PostMapping("/produceInstructOrder/forceComplete")
    public ResponseDTO<String> forceComplete(@RequestBody @NotEmpty ValidateList<Long> ids) {
        return produceInstructOrderService.forceComplete(ids);
    }

    @Operation(summary = "获取指令单编号或物料编号 <AUTHOR>
    @PostMapping("/produceInstructOrder/getInsOrItemNumber")
    public ResponseDTO<List<ProduceInstructOrderVO>> getInsAndItemNumber() {
        return produceInstructOrderService.getInsOrItemNumber();
    }
}
