package net.lab1024.sa.admin.module.business.mes.item.domain.form;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import lombok.Data;
import net.lab1024.sa.base.common.json.deserializer.FileKeyVoDeserializer;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * 主物料表 新建表单
 *
 * <AUTHOR>
 * @Date 2024-07-02 08:33:07
 * @Copyright zscbdic
 */

@Data
public class ItemAddForm {

    /**
     * 物料分类id;关联t_item_type
     */
    @Schema(description = "物料分类id;关联t_item_type", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "物料分类id;关联t_item_type 不能为空")
    private Long typeId;

    /**
     * 物料名称;
     */
    @Schema(description = "物料名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "物料名称 不能为空")
    private String name;

    /**
     * 供应商id;关联t_item_supplier
     */
    @Schema(description = "供应商id;关联t_item_supplier")
    private Long supplierId;

    /**
     * 规格型号;
     */
    @Schema(description = "规格型号")
    private String model;

    /**
     * sku编号 无自动生成;
     */
    @Schema(description = "sku编号", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "spu编号 不能为空")
    private String skuNumber;


    @Schema(description = "物料编号")
    private String number;

    /**
     * 价格
     */
    @Schema(description = "价格")
    @Min(value = 0, message = "价格不能小于0")
    private BigDecimal price = BigDecimal.ZERO;

    /**
     * 单位id;关联t_unit
     */
    @Schema(description = "单位id;关联t_unit", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "单位ID 不能为空")
    private Long unitId;

    /**
     * 停用标识;0启用，1停用
     */
    @Schema(description = "停用标识;0启用，1停用")
    @NotNull(message = "停用标识;0启用，1停用 不能为空")
    private Boolean enableFlag;

    /**
     * 类型;0半成品 1成品
     */
//    @Schema(description = "类型;0半成品 1成品", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "类型;0半成品 1成品 不能为空")
//    @CheckEnum(value = ItemCategoryEnum.class, message = "类型;0半成品 1成品 值不合法")
    private String category;

    /**
     * 属性;0面料，1其他，2成衣
     */
    @Schema(description = "属性;0面料，1其他，2成衣", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "属性;0面料，1其他，2成衣 不能为空")
//    @CheckEnum(value = ItemAttributeEnum.class, message = "属性;0面料，1其他，2成衣 值不合法")
    private String attribute;

    /**
     * 图片url
     */
    @Schema(description = "图片url")
    @JsonDeserialize(using = FileKeyVoDeserializer.class)
    private String imgUrl;


}
