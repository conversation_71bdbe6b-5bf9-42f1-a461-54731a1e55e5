package net.lab1024.sa.admin.module.business.mes.produce.instruct.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import net.lab1024.sa.admin.module.business.mes.common.vo.XyChartVO;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.constant.ProduceInstructOrderProduceStatusEnum;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderStatsQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.service.ProduceInstructOrderStatsService;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import java.util.List;


/**
 * 生产指令单统计 Controller
 *
 * <AUTHOR>
 * @Date 2024-09-19 20:32:28
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "生产指令单统计")
@Slf4j
public class ProduceInstructOrderStatsController {

    @Resource
    ProduceInstructOrderStatsService produceInstructOrderStatsService;


    /**
     * 查询计划数
     * @param queryForm
     * @return
     */
    @Operation(summary = "查询计划数 <AUTHOR>
    @PostMapping("/produceInstructOrderStats/planNum")
    public ResponseDTO<Long> getPlanNum(@RequestBody ProduceInstructOrderStatsQueryForm queryForm){
        return produceInstructOrderStatsService.getNum(queryForm, ProduceInstructOrderProduceStatusEnum.PLAN);
    }

    /**
     * 查询完成数
     * @param queryForm
     * @return
     */
    @Operation(summary = "查询完成数 <AUTHOR>
    @PostMapping("/produceInstructOrderStats/finishedNum")
    public ResponseDTO<Long> getFinishedNum(@RequestBody ProduceInstructOrderStatsQueryForm queryForm){
        return produceInstructOrderStatsService.getNum(queryForm,ProduceInstructOrderProduceStatusEnum.FINISH);
    }

    /**
     * 查询工单数
     * @param queryForm
     * @return
     */
    @Operation(summary = "查询工单数 <AUTHOR>
    @PostMapping("/produceInstructOrderStats/instructNum")
    public ResponseDTO<Long> getInstructNum(@RequestBody ProduceInstructOrderStatsQueryForm queryForm){
        return produceInstructOrderStatsService.getNum(queryForm,null);
    }

    /**
     * 查询平均生产周期
     * @param queryForm
     * @return
     */
    @Operation(summary = "查询平均生产周期 <AUTHOR>
    @PostMapping("/produceInstructOrderStats/averageDays")
    public ResponseDTO<Double> getAverageDays(@RequestBody ProduceInstructOrderStatsQueryForm queryForm){
        return produceInstructOrderStatsService.getAverageDays(queryForm, ProduceInstructOrderProduceStatusEnum.FINISH);
    }

    /**
     * 查询超时未完成数量
     * @param queryForm
     * @return
     */
    @Operation(summary = "超时未完成数量 <AUTHOR>
    @PostMapping("/produceInstructOrderStats/timeout")
    public ResponseDTO<Long> getTimeoutNum(@RequestBody ProduceInstructOrderStatsQueryForm queryForm){
        return produceInstructOrderStatsService.getTimeoutNum(queryForm);
    }

    /**
     * 查询下达指令单数
     * @param queryForm
     * @return
     */
    @Operation(summary = "查询完成数 <AUTHOR>
    @PostMapping("/produceInstructOrderStats/issuedNum")
    public ResponseDTO<Long> getIssuedNum(@RequestBody ProduceInstructOrderStatsQueryForm queryForm){
        return produceInstructOrderStatsService.getIssuedNum(queryForm);
    }

    /**
     * 查询进行中指令单数量
     * @param queryForm
     * @return
     */
    @Operation(summary = "查询完成数 <AUTHOR>
    @PostMapping("/produceInstructOrderStats/onGoingNum")
    public ResponseDTO<Long> getOnGoingNum(@RequestBody ProduceInstructOrderStatsQueryForm queryForm){
        return produceInstructOrderStatsService.getOnGoingNum(queryForm);
    }

    /**
     * 查询时间范围内每日指令单数
     *
     * @param queryForm
     * @return
     */
    @Operation(summary = "查询时间范围内每日指令单数 <AUTHOR>
    @PostMapping("/produceInstructOrderStats/perDayOrdersNum")
    public ResponseDTO<List<XyChartVO<String, Integer>>> getPerDayOrdersNum(@RequestBody ProduceInstructOrderStatsQueryForm queryForm){
        return produceInstructOrderStatsService.perDayOrdersNum(queryForm);
    }

    /**
     * 根据时间范围获取指令单各优先级数量
     *
     * @param queryForm
     * @return
     */
    @Operation(summary = "查询时间范围内各优先级指令单数 <AUTHOR>
    @PostMapping("/produceInstructOrderStats/priorityNum")
    public ResponseDTO<List<XyChartVO<String, Integer>>> getPriorityNum(@RequestBody ProduceInstructOrderStatsQueryForm queryForm){
        return produceInstructOrderStatsService.priorityNum(queryForm);
    }

    /**
     * 根据时间范围查询个人下达指令单数量
     *
     * @param queryForm
     * @return
     */
    @Operation(summary = "个人下达指令单数量 <AUTHOR>
    @PostMapping("/produceInstructOrderStats/selfInsNum")
    public ResponseDTO<Long> getSelfInsNum(@RequestBody ProduceInstructOrderStatsQueryForm queryForm){
        return produceInstructOrderStatsService.getSelfInsNum(queryForm);
    }

    /**
     * 根据时间范围查询个人创建指令单数量
     *
     * @param queryForm
     * @return
     */
    @Operation(summary = "个人创建指令单数量 <AUTHOR>
    @PostMapping("/produceInstructOrderStats/selfCreNum")
    public ResponseDTO<Long> getSelfCreNum(@RequestBody ProduceInstructOrderStatsQueryForm queryForm){
        return produceInstructOrderStatsService.getSelfCreNum(queryForm);
    }

}
