package net.lab1024.sa.admin.module.business.mes.item.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import net.lab1024.sa.admin.constant.AdminSwaggerTagConst;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemTypeAddForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemTypeQueryForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemTypeQueryTreeForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemTypeUpdateForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.vo.ItemTypeTreeVO;
import net.lab1024.sa.admin.module.business.mes.item.domain.vo.ItemTypeVO;
import net.lab1024.sa.admin.module.business.mes.item.service.ItemTypeService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 物料分类表 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-02 09:54:23
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class ItemTypeController {

    @Resource
    private ItemTypeService itemTypeService;

    /**
     * 查询层级树
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询层级树 <AUTHOR>
    @PostMapping("/itemType/queryTree")
    public ResponseDTO<List<ItemTypeTreeVO>> queryTree(@RequestBody @Valid ItemTypeQueryTreeForm queryForm) {
        return itemTypeService.queryTree(queryForm);
    }


    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/itemType/add")
    public ResponseDTO<String> add(@RequestBody @Valid ItemTypeAddForm addForm) {
        return itemTypeService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/itemType/update")
    public ResponseDTO<String> update(@RequestBody @Valid ItemTypeUpdateForm updateForm) {
        return itemTypeService.update(updateForm);
    }


    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/itemType/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return itemTypeService.delete(id);
    }
}
