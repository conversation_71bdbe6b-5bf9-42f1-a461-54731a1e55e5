package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.controller;

import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationRackAddForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationRackQuery;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationRackQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationRackUpdateForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.vo.PartStationRackVO;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.service.PartStationRackService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 裁片货架 Controller
 *
 * <AUTHOR>
 * @Date 2024-10-06 16:49:27
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class PartStationRackController {

    @Resource
    private PartStationRackService partStationRackService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/partStationRack/queryPage")
    public ResponseDTO<PageResult<PartStationRackVO>> queryPage(@RequestBody @Valid PartStationRackQueryForm queryForm) {
        return ResponseDTO.ok(partStationRackService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/partStationRack/add")
    public ResponseDTO<String> add(@RequestBody @Valid PartStationRackAddForm addForm) {
        return partStationRackService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/partStationRack/update")
    public ResponseDTO<String> update(@RequestBody @Valid PartStationRackUpdateForm updateForm) {
        return partStationRackService.update(updateForm);
    }


    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/partStationRack/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return partStationRackService.delete(id);
    }


    /**
     * 获取所有
     * @return
     */
    @Operation(summary = "获取所有 <AUTHOR>
    @PostMapping("/partStationRack/getAll")
    public ResponseDTO<List<PartStationRackVO>> getAll(@RequestBody PartStationRackQuery queryForm) {
        return partStationRackService.getAll(queryForm);
    }


    /**
     * 获取所属仓库下货架数量
     * @param warehouseId 仓库Id(可选)
     * @return 货架数
     */
    @Operation(summary = "获取所属仓库下货架数量 <AUTHOR>
    @GetMapping("/partStationRack/getRackNum")
    public ResponseDTO<Long> getRackNum(@RequestParam(name = "warehouseId",required = false) Long warehouseId) {
        return partStationRackService.getRackNum(warehouseId);
    }
}
