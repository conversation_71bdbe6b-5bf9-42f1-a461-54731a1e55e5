package net.lab1024.sa.admin.module.business.mes.stock.in.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

import java.time.LocalDate;

/**
 * 入库单 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-02-03 15:54:07
 * @Copyright zscbdic
 */

@Data
public class StkInStockQueryForm extends PageParam{

    @Schema(description = "关键字查询")
    private String queryKey;

    @Schema(description = "单据类型")
    private String type;

    @Schema(description = "单据状态")
    private String status;

    @Schema(description = "入库时间")
    private LocalDate inStockTimeBegin;

    @Schema(description = "入库时间")
    private LocalDate inStockTimeEnd;

}
