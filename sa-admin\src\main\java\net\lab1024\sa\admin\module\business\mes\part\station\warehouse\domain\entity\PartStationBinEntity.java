package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 裁片货位 实体类
 *
 * <AUTHOR>
 * @Date 2024-10-06 20:17:14
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_part_station_bin")
public class PartStationBinEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 所属货架id
     */
    private Long rackId;

    /**
     * 库位编码
     */
    private String binCode;

    /**
     * 库位描述
     */
    private String binDesc;

    /**
     * 容量
     */
    private Integer capacity;

}
