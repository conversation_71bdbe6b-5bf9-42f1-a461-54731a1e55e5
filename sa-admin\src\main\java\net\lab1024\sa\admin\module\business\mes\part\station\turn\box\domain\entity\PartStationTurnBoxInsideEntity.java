package net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 周转箱内容 实体类
 *
 * <AUTHOR>
 * @Date 2025-03-14 15:03:20
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_part_station_turn_box_inside")
public class PartStationTurnBoxInsideEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 周转箱ID
     */
    private Long turnBoxId;

    /**
     * 菲票ID
     */
    private Long feTicketId;

    /**
     * 入箱时间
     */
    private LocalDateTime inTime;

}
