package net.lab1024.sa.admin.module.business.mes.produce.instruct.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderClothesQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderClothesVO;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.service.ProduceInstructOrderClothesService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 指令单成衣信息 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:25:32
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class ProduceInstructOrderClothesController {

    @Resource
    private ProduceInstructOrderClothesService produceInstructOrderClothesService;

    /**
     * 获取指令单下所有成衣颜色
     * @param id 指令单id
     * @return
     */
    @GetMapping("/produceInstructOrderClothes/styleColorList/{id}")
    @Operation(summary = "获取指令单下所有成衣颜色 <AUTHOR>
    public ResponseDTO<List<String>> getStyleColorList(@PathVariable Long id){
        return ResponseDTO.ok(produceInstructOrderClothesService.getStyleColorList(id));
    }

    /**
     * 获取指令单下所有成衣尺码
     * @param id 指令单id
     * @return
     */
    @Operation(summary = "获取指令单下所有成衣尺码 <AUTHOR>
    @GetMapping("/produceInstructOrderClothes/sizeList/{id}")
    public ResponseDTO<List<String>> getSizeList(@PathVariable Long id){
        return ResponseDTO.ok(produceInstructOrderClothesService.getSizeList(id));
    }

    /**
     * 获取指令单下所有成衣生产信息
     * @param id
     * @return
     */
    @Operation(summary = "获取指令单下所有成衣生产信息 <AUTHOR>
    @GetMapping("/produceInstructOrderClothes/list/{id}")
    public ResponseDTO<List<ProduceInstructOrderClothesVO>> list(@PathVariable Long id){
        return ResponseDTO.ok(produceInstructOrderClothesService.list(id));
    }


    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/produceInstructOrderClothes/queryPage")
    public ResponseDTO<PageResult<ProduceInstructOrderClothesVO>> queryPage(@RequestBody @Valid ProduceInstructOrderClothesQueryForm queryForm) {
        return ResponseDTO.ok(produceInstructOrderClothesService.queryPage(queryForm));
    }
//
//    @Operation(summary = "添加 <AUTHOR>
//    @PostMapping("/produceInstructOrderClothes/add")
//    public ResponseDTO<String> add(@RequestBody @Valid ProduceInstructOrderClothesAddForm addForm) {
//        return produceInstructOrderClothesService.add(addForm);
//    }
//
//    @Operation(summary = "更新 <AUTHOR>
//    @PostMapping("/produceInstructOrderClothes/update")
//    public ResponseDTO<String> update(@RequestBody @Valid ProduceInstructOrderClothesUpdateForm updateForm) {
//        return produceInstructOrderClothesService.update(updateForm);
//    }
//
//    @Operation(summary = "批量删除 <AUTHOR>
//    @PostMapping("/produceInstructOrderClothes/batchDelete")
//    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Integer> idList) {
//        return produceInstructOrderClothesService.batchDelete(idList);
//    }
//
//    @Operation(summary = "单个删除 <AUTHOR>
//    @GetMapping("/produceInstructOrderClothes/delete/{id}")
//    public ResponseDTO<String> batchDelete(@PathVariable Integer id) {
//        return produceInstructOrderClothesService.delete(id);
//    }
}
