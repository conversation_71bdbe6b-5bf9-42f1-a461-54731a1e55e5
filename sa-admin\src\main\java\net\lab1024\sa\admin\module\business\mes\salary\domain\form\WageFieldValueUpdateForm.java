package net.lab1024.sa.admin.module.business.mes.salary.domain.form;

import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class WageFieldValueUpdateForm {

    /**
     * 员工id
     */
    @NotNull(message = "员工id 不能为空")
    @NotEmpty(message = "员工id 不能为空")
    private List<Long> employeeIds;

    /**
     * 待更新的员工工资项值
     */
    @Valid
    @NotNull(message = "待更新的员工工资项值 不能为空")
    @NotEmpty(message = "待更新的员工工资项值 不能为空")
    private List<WageFieldValueUpdateItem> items;

    @Data
    public static class WageFieldValueUpdateItem {

        @NotNull(message = "工资项主键 不能为空")
        private Long fieldId;

        @NotNull(message = "工资项值 不能为空")
        @Min(value = 0, message = "工资项值 不能低0")
        private BigDecimal value;
    }
}
