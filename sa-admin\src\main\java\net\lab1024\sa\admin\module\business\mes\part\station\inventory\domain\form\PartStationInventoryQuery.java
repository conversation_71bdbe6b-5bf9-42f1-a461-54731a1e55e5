package net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class PartStationInventoryQuery {

    /**
     * 物料编号
     */
    @Schema(description = "物料编号")
    private String itemNumber;

    /**
     * 生产指令单号
     */
    @Schema(description = "生产指令单号")
    private String produceInstructOrderNumber;

    @Schema(description = "颜色")
    private List<String> colors;

    @Schema(description = "尺码")
    private List<String> sizes;
}
