package net.lab1024.sa.admin.module.business.mes.produce.instruct.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderProcessEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderProcessQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProcessCountVo;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderProcessVO;

import java.util.Date;
import java.util.List;

/**
 * 生产指令工序信息 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:24:08
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface ProduceInstructOrderProcessDao extends BaseMapper<ProduceInstructOrderProcessEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<ProduceInstructOrderProcessVO> queryPage(Page page, @Param("queryForm") ProduceInstructOrderProcessQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Integer id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Integer> idList,@Param("deletedFlag")boolean deletedFlag);


    /**
     * 更新 工序完成数量
     * @param id 表主键id
     * @param num 数量
     */
    void addFinishNum(@Param("id")Long id, @Param("num")Integer num);

    /**
     * 工序完成数量统计
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    List<ProcessCountVo> processCountStats(@Param("startTime") Date startTime,@Param("endTime") Date endTime);
}
