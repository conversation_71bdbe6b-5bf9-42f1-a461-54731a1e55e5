package net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.form;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 库存查询
 */
@Data
public class StkInventoryQuery {

    /**
     * 物料id
     */
    @NotNull(message = "物料id不能为空")
    private Long materialId;


    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 库位id
     */
    private Long locationId;

    /**
     * 批次号
     */
    private Long lotId;




}
