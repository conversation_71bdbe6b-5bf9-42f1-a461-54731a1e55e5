package net.lab1024.sa.admin.module.business.mes.stock.lot.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.form.StkLotMasterQuery;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.form.StkLotMasterQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.vo.StkLotMasterVO;
import net.lab1024.sa.admin.module.business.mes.stock.lot.service.StkLotMasterService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 批号主档 Controller
 *
 * <AUTHOR>
 * @Date 2025-02-03 19:28:09
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class StkLotMasterController {

    @Resource
    private StkLotMasterService stkLotMasterService;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/stkLotMaster/queryPage")
    public ResponseDTO<PageResult<StkLotMasterVO>> queryPage(@RequestBody @Valid StkLotMasterQueryForm queryForm) {
        return ResponseDTO.ok(stkLotMasterService.queryPage(queryForm));
    }

    /**
     * 查询列表
     *
     * @param queryForm
     * @return
     */
    @Operation(summary = "查询列表")
    @PostMapping("/stkLotMaster/queryList")
    public ResponseDTO<List<StkLotMasterVO>> queryList(@RequestBody @Valid StkLotMasterQuery queryForm) {
        return ResponseDTO.ok(stkLotMasterService.queryList(queryForm));
    }

//    @Operation(summary = "添加 <AUTHOR>
//    @PostMapping("/stkLotMaster/add")
//    public ResponseDTO<String> add(@RequestBody @Valid StkLotMasterAddForm addForm) {
//        return stkLotMasterService.add(addForm);
//    }
//
//    @Operation(summary = "更新 <AUTHOR>
//    @PostMapping("/stkLotMaster/update")
//    public ResponseDTO<String> update(@RequestBody @Valid StkLotMasterUpdateForm updateForm) {
//        return stkLotMasterService.update(updateForm);
//    }
//
//    @Operation(summary = "批量删除 <AUTHOR>
//    @PostMapping("/stkLotMaster/batchDelete")
//    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> idList) {
//        return stkLotMasterService.batchDelete(idList);
//    }
//
//    @Operation(summary = "单个删除 <AUTHOR>
//    @GetMapping("/stkLotMaster/delete/{id}")
//    public ResponseDTO<String> batchDelete(@PathVariable Long id) {
//        return stkLotMasterService.delete(id);
//    }
}
