package net.lab1024.sa.admin.module.business.mes.factory.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 生产小组成员 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:33:31
 * @Copyright zscbdic
 */

@Data
public class ProduceTeamMemberVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "小组id")
    private Long teamId;

    @Schema(description = "成员id")
    private Long memberId;

    @Schema(description = "成员名称")
    private String member;

}
