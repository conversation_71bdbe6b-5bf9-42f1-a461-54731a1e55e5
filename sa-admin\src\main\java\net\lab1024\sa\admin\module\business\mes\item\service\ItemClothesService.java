package net.lab1024.sa.admin.module.business.mes.item.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.event.item.ItemDeleteCheckEvent;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.UnitEntity;
import net.lab1024.sa.admin.module.business.mes.base.manager.UnitManager;
import net.lab1024.sa.admin.module.business.mes.common.excel.service.ExcelBaseService;
import net.lab1024.sa.admin.module.business.mes.item.constant.ItemAttributeEnum;
import net.lab1024.sa.admin.module.business.mes.item.constant.ItemCategoryEnum;
import net.lab1024.sa.admin.module.business.mes.item.dao.ItemClothesDao;
import net.lab1024.sa.admin.module.business.mes.item.dao.ItemDao;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemClothesEntity;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemEntity;
import net.lab1024.sa.admin.module.business.mes.item.domain.excel.ItemClothesExcel;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemClothesAddForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemClothesQueryForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemClothesUpdateForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.vo.ItemClothesVO;
import net.lab1024.sa.admin.module.business.mes.item.manager.ItemClothesManager;
import net.lab1024.sa.admin.module.business.mes.item.manager.ItemManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class ItemClothesService {

    @Resource
    private ItemClothesManager itemClothesManager;

    @Resource
    private ItemManager itemManager;

    @Resource
    private SerialNumberService serialNumberService;

    @Resource
    private ItemClothesDao itemClothesDao;

    @Resource
    private ItemDao itemDao;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private ExcelBaseService excelBaseService;

    @Resource
    private UnitManager unitManager;


    /**
     * 根据物料编号查询sku列表
     *
     * @param spuNumber
     * @return
     */
    public ResponseDTO<List<ItemClothesVO>> querySkuListBySpuNumber(String spuNumber) {

        List<ItemEntity> items = itemManager.lambdaQuery()
                .eq(ItemEntity::getNumber, spuNumber)
                .list();
        if (CollUtil.isEmpty(items)) {
            return ResponseDTO.ok(Collections.emptyList());
        }

        List<Long> ids = items.stream().map(ItemEntity::getId).collect(Collectors.toList());
        List<ItemClothesEntity> clothes = itemClothesManager.lambdaQuery()
                .in(ItemClothesEntity::getItemId, ids)
                .list();

        Map<Long, ItemClothesEntity> clothesMap = clothes.stream()
                .collect(Collectors.toMap(ItemClothesEntity::getItemId, e -> e));

        List<ItemClothesVO> vos = SmartBeanUtil.copyList(items, ItemClothesVO.class);
        for (ItemClothesVO vo : vos) {
            if (clothesMap.containsKey(vo.getId())) {
                vo.setSize(clothesMap.get(vo.getId()).getSize());
                vo.setStyleColor(clothesMap.get(vo.getId()).getStyleColor());
                vo.setSeasonId(clothesMap.get(vo.getId()).getSeasonId());
                vo.setStyleId(clothesMap.get(vo.getId()).getStyleId());
            }
        }

        return ResponseDTO.ok(vos);

    }

    /**
     * 添加成衣
     *
     * @param addForm
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(ItemClothesAddForm addForm) {
        itemClothesManager.spuNumberCheck(addForm.getNumber());
        int count = 0;

        Set<String> colorSizeSet = new HashSet<>(addForm.getSkuAttributes().size());
        for (ItemClothesAddForm.ItemClothesSkuAddBo skuAttribute : addForm.getSkuAttributes()) {
            ItemEntity item = SmartBeanUtil.copy(addForm, ItemEntity.class);
            if (StrUtil.isBlank(skuAttribute.getSkuNumber())) {
                item.setSkuNumber(addForm.getNumber() + "." + (++count));
            } else {
                item.setSkuNumber(skuAttribute.getSkuNumber());
            }
            itemManager.skuNumberCheck(skuAttribute.getSkuNumber(), null);

            item.setPrice(skuAttribute.getPrice());
            item.setImgUrl(skuAttribute.getImgUrl());
            // 设置型号附加颜色尺寸
            item.setModel(StrUtil.isBlank(item.getModel()) ?
                    skuAttribute.getStyleColor() + "/" + skuAttribute.getSize() :
                    item.getModel() + "|" + skuAttribute.getStyleColor() + "/" + skuAttribute.getSize());

            ItemClothesEntity itemCloth = SmartBeanUtil.copy(addForm, ItemClothesEntity.class);
            itemCloth.setSize(skuAttribute.getSize());
            itemCloth.setStyleColor(skuAttribute.getStyleColor());
            // 部位
            if (CollUtil.isNotEmpty(addForm.getPartList())) {
                itemCloth.setParts(JSON.toJSONString(addForm.getPartList()));
            }


            if (colorSizeSet.contains(skuAttribute.getSize() + skuAttribute.getStyleColor())) {
                throw new BusinessException(skuAttribute.getSize() + "和" + skuAttribute.getStyleColor() + "组合重复");
            } else {
                colorSizeSet.add(skuAttribute.getSize() + skuAttribute.getStyleColor());
            }

            itemDao.insert(item);
            itemCloth.setItemId(item.getId());
            itemClothesDao.insert(itemCloth);

        }
        return ResponseDTO.ok();
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    public ResponseDTO<ItemClothesVO> getById(Long id) {
        ItemEntity itemEntity = itemManager.getById(id);
        if (null == itemEntity) {
            return ResponseDTO.userErrorParam("物料不存在");
        }
        ItemClothesEntity itemClothesEntity = itemClothesDao.selectOne(new LambdaQueryWrapper<ItemClothesEntity>().eq(ItemClothesEntity::getItemId, id)
                .last("limit 1"));
        ItemClothesVO vo = SmartBeanUtil.copy(itemClothesEntity, ItemClothesVO.class);
        SmartBeanUtil.copyProperties(itemEntity, vo);
        vo.setId(id);

        if (CharSequenceUtil.isNotBlank(itemClothesEntity.getParts())) {
            vo.setPartList(JSON.parseArray(itemClothesEntity.getParts(), String.class));
        }

        return ResponseDTO.ok(vo);
    }

    /**
     * 修改
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(ItemClothesUpdateForm updateForm) {
        // 校验物料spu
        itemClothesManager.spuNumberCheck(updateForm.getNumber());
        ItemEntity itemEntity = itemDao.selectById(updateForm.getId());
        if (null == itemEntity) {
            return ResponseDTO.userErrorParam("物料不存在");
        }

        // 校验物料编号sku
        itemManager.skuNumberCheck(updateForm.getSkuNumber(), Lists.newArrayList(updateForm.getId()));
        //校验颜色和尺寸组合
        itemClothesManager.colorSizeCheck(updateForm.getNumber(), updateForm.getStyleColor(), updateForm.getSize(), updateForm.getId());
        //设置型号 附带颜色尺寸
        if (StrUtil.isBlank(updateForm.getModel())) {
            updateForm.setModel(updateForm.getStyleColor() + "/" + updateForm.getSize());
        }

        ItemEntity item = SmartBeanUtil.copy(updateForm, ItemEntity.class);
        ItemClothesEntity itemClothes = SmartBeanUtil.copy(updateForm, ItemClothesEntity.class);
        itemClothes.setItemId(updateForm.getId());
        itemClothes.setId(null);
        // 部位
        if (updateForm.getPartList()!= null) {
            itemClothes.setParts(JSON.toJSONString(updateForm.getPartList()));
        }

        // 更新
        transactionTemplate.execute(s -> {
            itemDao.updateById(item);
            itemClothesDao.update(itemClothes, new LambdaQueryWrapper<ItemClothesEntity>().eq(ItemClothesEntity::getItemId, updateForm.getId()));

            return null;
        });

        return ResponseDTO.ok();
    }

    /**
     * 分页
     *
     * @param queryForm
     * @return
     */
    public ResponseDTO<PageResult<ItemClothesVO>> queryPage(ItemClothesQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ItemClothesVO> list = itemClothesDao.queryClothesPage(page, queryForm);
        if (CollUtil.isEmpty(list)) {
            return ResponseDTO.ok(SmartPageUtil.emptyPage(page.getCurrent(), page.getSize()));
        }

        // 查询单位
        List<Long> unitIds = list.stream().map(ItemClothesVO::getUnitId).collect(Collectors.toList());
        Map<Long, String> unitMap = unitManager.listByIds(unitIds).stream().collect(Collectors.toMap(UnitEntity::getId, UnitEntity::getName));
        list.forEach(e -> e.setUnitName(unitMap.get(e.getUnitId())));

        PageResult<ItemClothesVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return ResponseDTO.ok(pageResult);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long id) {

        eventPublisher.publishEvent(new ItemDeleteCheckEvent(this, id));

        itemDao.deleteById(id);
        itemClothesDao.delete(new LambdaQueryWrapper<ItemClothesEntity>().eq(ItemClothesEntity::getItemId, id));
        return ResponseDTO.ok();
    }

    /**
     * 导入
     *
     * @param file
     * @return
     */
    public ResponseDTO<String> importExcel(MultipartFile file) {
        AtomicInteger count = new AtomicInteger(0);
        Set<String> colorSizeSet = new HashSet<>();
        excelBaseService.importExcel(file, ItemClothesExcel.class, 1,
                e -> {
                    //数据校验与处理
                    String verify = SmartBeanUtil.verify(e);
                    if (verify != null) {
                        throw new BusinessException(verify);
                    }

                    if (StrUtil.isBlank(e.getSkuNumber())) {
                        e.setSkuNumber(e.getNumber() + "." + count.incrementAndGet());
                    }
                    itemManager.skuNumberCheck(e.getSkuNumber(), null);
                    itemClothesManager.spuNumberCheck(e.getNumber());

                    if (e.getPrice() == null) {
                        e.setPrice(BigDecimal.ZERO);
                    }
                    e.setAttribute(ItemAttributeEnum.FINISHED_CLOTHES.getValue());
                    e.setEnableFlag(false);
                    e.setCategory(ItemCategoryEnum.FINISHED_PRODUCT.getValue());
                    //设置型号 附带颜色尺寸
                    e.setModel(StrUtil.isBlank(e.getModel()) ?
                            e.getStyleColor() + "/" + e.getSize() :
                            e.getModel() + "|" + e.getStyleColor() + "/" + e.getSize());

                    if (colorSizeSet.contains(e.getNumber() + e.getStyleColor() + e.getSize())) {
                        throw new BusinessException(e.getNumber() + "下" + e.getSize() + "和" + e.getStyleColor() + "组合重复");
                    } else {
                        colorSizeSet.add(e.getNumber() + e.getStyleColor() + e.getSize());
                    }

                    if (StrUtil.isNotBlank(e.getParts())) {
                        List<String> parts = StrUtil.split(e.getParts(), ",");
                        if (CollUtil.isNotEmpty(parts)) {
                            e.setParts(JSON.toJSONString(parts));
                        }
                    }

                    return e;
                }, list -> {


                    //处理单位
                    List<String> unitNames = list.stream().map(ItemClothesExcel::getUnitName).distinct().collect(Collectors.toList());
                    List<UnitEntity> units = unitManager.lambdaQuery().in(UnitEntity::getName, unitNames).list();
                    if (CollUtil.isEmpty(units) || units.size() != unitNames.size()) {
                        throw new BusinessException("单位名称不存在");
                    }
                    Map<String, Long> unitMap = units.stream().collect(Collectors.toMap(UnitEntity::getName, UnitEntity::getId));
                    list.forEach(e -> e.setUnitId(unitMap.get(e.getUnitName())));


                    List<ItemEntity> itemEntities = SmartBeanUtil.copyList(list, ItemEntity.class);
                    List<ItemClothesEntity> clothEntities = SmartBeanUtil.copyList(list, ItemClothesEntity.class);

                    transactionTemplate.execute(s -> {
                        for (int i = 0; i < itemEntities.size(); i++) {
                            itemManager.save(itemEntities.get(i));
                            clothEntities.get(i).setItemId(itemEntities.get(i).getId());
                            itemClothesDao.insert(clothEntities.get(i));
                        }
                        return null;
                    });

                });
        return ResponseDTO.ok();
    }
}
