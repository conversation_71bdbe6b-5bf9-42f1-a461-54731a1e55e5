package net.lab1024.sa.admin.module.business.mes.part.station.turn.box.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxInsideDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxEntity;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxInsideEntity;
import net.lab1024.sa.base.common.exception.BusinessException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 裁片周转箱  Manager
 *
 * <AUTHOR>
 * @Date 2025-03-14 11:36:51
 * @Copyright zscbdic
 */
@Service
public class PartStationTurnBoxManager extends ServiceImpl<PartStationTurnBoxDao, PartStationTurnBoxEntity> {

    @Resource
    private PartStationTurnBoxInsideDao partStationTurnBoxInsideDao;

    /**
     * 校验编号是否重复
     * @param number
     */
    public void checkNumber(String number, Long excludeId) {
        Long count = lambdaQuery().eq(PartStationTurnBoxEntity::getNumber, number)
                .ne(excludeId != null, PartStationTurnBoxEntity::getId, excludeId)
                .count();
        if (count > 0) {
            throw new BusinessException(number+" 编号已存在");
        }
    }


    /**
     * 删除校验
     * @param idList
     */
    public void deleteCheck(List<Long> idList) {
        Long count = partStationTurnBoxInsideDao.selectCount(new LambdaQueryWrapper<PartStationTurnBoxInsideEntity>()
                .in(PartStationTurnBoxInsideEntity::getTurnBoxId, idList));
        if (count > 0) {
            throw new BusinessException("周转箱正在被使用中，无法删除");
        }
    }
}
