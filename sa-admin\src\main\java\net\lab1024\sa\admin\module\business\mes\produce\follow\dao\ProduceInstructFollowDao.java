package net.lab1024.sa.admin.module.business.mes.produce.follow.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.produce.follow.domain.entity.ProduceInstructFollowEntity;
import net.lab1024.sa.admin.module.business.mes.produce.follow.domain.form.ProduceInstructFollowQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.follow.domain.vo.ProduceInstructFollowVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 生产跟单 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-26 21:36:36
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface ProduceInstructFollowDao extends BaseMapper<ProduceInstructFollowEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<ProduceInstructFollowVO> queryPage(Page page, @Param("queryForm") ProduceInstructFollowQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 根据指令单id删除所有信息
     * @param instructOrderId 指令单id
     * @param deletedFlag 修改标识符
     * @return
     */
    long updateByInstructId(@Param("instructOrderId") Long instructOrderId,@Param("deletedFlag")boolean deletedFlag);

    void cancelFollow(@Param("instructOrderId") Long instructOrderId,@Param("employeeId") Long employeeId,@Param("deletedFlag")boolean deletedFlag);
}
