package net.lab1024.sa.admin.module.business.mes.stock.warehouse.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkWarehouseEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkWarehouseQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.vo.StkWarehouseVO;

import java.util.List;

/**
 * 仓库 Dao
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:13:18
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface StkWarehouseDao extends BaseMapper<StkWarehouseEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<StkWarehouseVO> queryPage(Page page, @Param("queryForm") StkWarehouseQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);





}
