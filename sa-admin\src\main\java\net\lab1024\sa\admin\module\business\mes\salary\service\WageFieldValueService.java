package net.lab1024.sa.admin.module.business.mes.salary.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.salary.dao.WageFieldValueDao;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.WageFieldValueEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.WageFieldValueUpdateForm;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 工资字段值 Service
 *
 * <AUTHOR>
 * @Date 2024-11-25 20:28:37
 * @Copyright zscbdic
 */

@Service
public class WageFieldValueService {

    @Resource
    private WageFieldValueDao wageFieldValueDao;

    /**
     * 修改员工自定义工资项值
     *
     * @param fieldValueId
     * @return
     */
    public ResponseDTO<String> updateValue(Long fieldValueId, BigDecimal value) {
        wageFieldValueDao.update(null, new LambdaUpdateWrapper<WageFieldValueEntity>()
                .set(WageFieldValueEntity::getValue, value)
                .eq(WageFieldValueEntity::getId, fieldValueId));
        return ResponseDTO.ok();
    }


    /**
     * 批量修改员工工资项值
     *
     * @param updateForm
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> batchUpdateValue(WageFieldValueUpdateForm updateForm) {
        List<Long> ids = updateForm.getEmployeeIds();

        List<WageFieldValueUpdateForm.WageFieldValueUpdateItem> items = updateForm.getItems();
        for (WageFieldValueUpdateForm.WageFieldValueUpdateItem item : items) {
            Long fieldValueId = item.getFieldId();
            BigDecimal value = item.getValue();
            wageFieldValueDao.update(null, new LambdaUpdateWrapper<WageFieldValueEntity>()
                    .set(WageFieldValueEntity::getValue, value)
                    .eq(WageFieldValueEntity::getFieldId, fieldValueId)
                    .in(WageFieldValueEntity::getEmployeeId, ids));
        }

        return ResponseDTO.ok();
    }
}
