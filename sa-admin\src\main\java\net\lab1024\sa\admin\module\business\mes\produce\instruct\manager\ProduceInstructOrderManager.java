package net.lab1024.sa.admin.module.business.mes.produce.instruct.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.base.dao.CustomerDao;
import net.lab1024.sa.admin.module.business.mes.base.dao.UnitDao;
import net.lab1024.sa.admin.module.business.mes.item.dao.ItemDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.constant.ProduceInstructOrderPrioityEnum;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.constant.ProduceInstructOrderProduceStatusEnum;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.*;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.bo.ProduceInstructOrderBo;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.*;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ChangePriorityForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderAddForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderUpdateForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderVO;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.common.util.SmartBeanUtil;

import jakarta.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 生产指令单  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:22:24
 * @Copyright zscbdic
 */
@Service
public class ProduceInstructOrderManager extends ServiceImpl<ProduceInstructOrderDao, ProduceInstructOrderEntity> {

    @Resource
    private ProduceInstructOrderProcessDao orderProcessDao;

    @Resource
    private ProduceInstructOrderItemDao orderItemDao;

    @Resource
    private ProduceInstructOrderArrangeDao orderArrangeDao;

    @Resource
    private ProduceInstructOrderClothesDao orderClothesDao;

    @Resource
    private UnitDao unitDao;

    @Resource
    private CustomerDao customerDao;

    @Resource
    private ItemDao itemDao;

    @Resource
    private ProduceInstructOrderDao orderDao;

    /**
     * 添加生产指令单
     *
     * @param addBO
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveInstructOrderInfo(ProduceInstructOrderBo addBO) {
        ProduceInstructOrderEntity order = addBO.getOrder();
        this.save(order);

        Long orderId = order.getId();

        addBO.getArrangeList().forEach(e -> {
            e.setOrderId(orderId);
            orderArrangeDao.insert(e);
        });
        addBO.getItemList().forEach(e -> {
            e.setOrderId(orderId);
            orderItemDao.insert(e);
        });
        addBO.getProcessList().forEach(e -> {
            e.setOrderId(orderId);
            orderProcessDao.insert(e);
        });
        addBO.getClothesList().forEach(e -> {
            e.setOrderId(orderId);
            orderClothesDao.insert(e);
        });
    }

    /**
     * 修改生产指令单
     *
     * @param updateBo
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateInstructOrderInfo(ProduceInstructOrderBo updateBo) {
        ProduceInstructOrderEntity order = updateBo.getOrder();
        this.updateById(order);
        Long orderId = order.getId();

        orderArrangeDao.delete(new LambdaQueryWrapper<ProduceInstructOrderArrangeEntity>()
                .eq(ProduceInstructOrderArrangeEntity::getOrderId, orderId));
        orderItemDao.delete(new LambdaQueryWrapper<ProduceInstructOrderItemEntity>()
                .eq(ProduceInstructOrderItemEntity::getOrderId, orderId));
        orderProcessDao.delete(new LambdaQueryWrapper<ProduceInstructOrderProcessEntity>()
                .eq(ProduceInstructOrderProcessEntity::getOrderId, orderId));
        orderClothesDao.delete(new LambdaQueryWrapper<ProduceInstructOrderClothesEntity>()
                .eq(ProduceInstructOrderClothesEntity::getOrderId, orderId));

        updateBo.getArrangeList().forEach(e -> {
            e.setOrderId(orderId);
            orderArrangeDao.insert(e);
        });
        updateBo.getItemList().forEach(e -> {
            e.setOrderId(orderId);
            orderItemDao.insert(e);
        });
        updateBo.getProcessList().forEach(e -> {
            e.setOrderId(orderId);
            orderProcessDao.insert(e);
        });
        updateBo.getClothesList().forEach(e -> {
            e.setOrderId(orderId);
            orderClothesDao.insert(e);
        });
    }

    /**
     * 删除生产指令单检查
     *
     * @param order
     */
    public void deleteCheck(ProduceInstructOrderEntity order) {
        String produceStatus = order.getProduceStatus();
        if (ProduceInstructOrderProduceStatusEnum.PLAN.getValue().equals(produceStatus)) {
            return;
        } else if (ProduceInstructOrderProduceStatusEnum.FINISH.getValue().equals(produceStatus)) {
            return;
        } else if (ProduceInstructOrderProduceStatusEnum.ISSUED.getValue().equals(produceStatus)) {
            return;
        }
        throw new BusinessException("生产指令单状态非法，无法删除");
    }

    /**
     * 删除生产指令单
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deletedInstructOrderInfo(Long id) {
        this.removeById(id);
        orderArrangeDao.delete(new LambdaQueryWrapper<ProduceInstructOrderArrangeEntity>()
                .eq(ProduceInstructOrderArrangeEntity::getOrderId, id));
        orderItemDao.delete(new LambdaQueryWrapper<ProduceInstructOrderItemEntity>()
                .eq(ProduceInstructOrderItemEntity::getOrderId, id));
        orderProcessDao.delete(new LambdaQueryWrapper<ProduceInstructOrderProcessEntity>()
                .eq(ProduceInstructOrderProcessEntity::getOrderId, id));
        orderClothesDao.delete(new LambdaQueryWrapper<ProduceInstructOrderClothesEntity>()
                .eq(ProduceInstructOrderClothesEntity::getOrderId, id));
    }

    /**
     * 获取生产指令单主信息
     *
     * @param id
     * @return
     */
    public ProduceInstructOrderVO getOrderMainInfo(Long id) {
        ProduceInstructOrderEntity orderEntity = this.getById(id);
        if (orderEntity == null) {
            throw new BusinessException("生产指令单不存在");
        }
        // 生产指令单主信息
        ProduceInstructOrderVO orderVO = SmartBeanUtil.copy(orderEntity, ProduceInstructOrderVO.class);
        return orderVO;
    }

    /**
     * 修改生产指令单检查
     *
     * @param updateForm
     */
    public void updateCheck(ProduceInstructOrderUpdateForm updateForm) {
        ProduceInstructOrderEntity order = this.getById(updateForm.getId());
        ProduceInstructOrderProduceStatusEnum statusEnum = ProduceInstructOrderProduceStatusEnum.getByValue(order.getProduceStatus());

        if (statusEnum == null || !ProduceInstructOrderProduceStatusEnum.PLAN.equals(statusEnum)) {
            throw new BusinessException("生产状态不等于计划阶段，不可修改");
        }
//        String instructNumber = updateForm.getInstructNumber();
//        Long count = this.lambdaQuery()
//                .eq(ProduceInstructOrderEntity::getInstructNumber, instructNumber)
//                .ne(ProduceInstructOrderEntity::getId, updateForm.getId())
//                .count();
//        if (count > 0) {
//            throw new BusinessException("指令单号已存在");
//        }
    }

    /**
     * 新增生产指令单检查
     *
     * @param addForm
     */
    public void addCheck(ProduceInstructOrderAddForm addForm) {
        String instructNumber = addForm.getInstructNumber();
        Long count = this.lambdaQuery()
                .eq(ProduceInstructOrderEntity::getInstructNumber, instructNumber)
                .count();
        if (count > 0) {
            throw new BusinessException("指令单号已存在");
        }
    }


    /**
     * 指令单下达检查
     *
     * @param id
     */
    public void issuedCheck(Long id) {
        Long itemCount = orderItemDao.selectCount(new LambdaQueryWrapper<ProduceInstructOrderItemEntity>()
                .eq(ProduceInstructOrderItemEntity::getOrderId, id));
        if (itemCount == 0) {
            throw new BusinessException("指令单用料信息未完善");
        }
        Long clothesCount = orderClothesDao.selectCount(new LambdaQueryWrapper<ProduceInstructOrderClothesEntity>()
                .eq(ProduceInstructOrderClothesEntity::getOrderId, id));
        if (clothesCount == 0) {
            throw new BusinessException("指令单成衣信息未完善");
        }
        Long processCount = orderProcessDao.selectCount(new LambdaQueryWrapper<ProduceInstructOrderProcessEntity>()
                .eq(ProduceInstructOrderProcessEntity::getOrderId, id));
        if (processCount == 0) {
            throw new BusinessException("指令单工序信息未完善");
        }
//        Long arrangeCount = orderArrangeDao.selectCount(new LambdaQueryWrapper<ProduceInstructOrderArrangeEntity>()
//                .eq(ProduceInstructOrderArrangeEntity::getOrderId, id));
//        if (arrangeCount == 0) {
//            throw new BusinessException("指令单安排信息未完善");
//        }

    }

    /**
     * 修改指令单优先级
     *
     * @param changePriorityForm
     */
    public void changePriority(ChangePriorityForm changePriorityForm) {
        String priority = changePriorityForm.getValue();
        List<Long> id = changePriorityForm.getId();
        id.forEach(e -> {
            ProduceInstructOrderEntity produceInstructOrderEntity = new ProduceInstructOrderEntity();
            produceInstructOrderEntity.setId(e);
            produceInstructOrderEntity.setPriority(priority);
            orderDao.updateById(produceInstructOrderEntity);
        });
    }

    /**
     * 修改指令单完成数量
     *
     * @param id  指令单id
     * @param num 增加或减少数量
     */
    public void updateFinishNum(Long id, Integer num) {
        this.baseMapper.updateFinishNum(id, num);
    }

    /**
     * 修改指令单完成状态
     *
     * @param id 指令单id
     */
    public void updateFinish(Long id) {
        this.baseMapper.updateFinish(id);
    }
}
