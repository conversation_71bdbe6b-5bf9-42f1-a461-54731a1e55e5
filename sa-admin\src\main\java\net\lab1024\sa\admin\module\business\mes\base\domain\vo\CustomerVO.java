package net.lab1024.sa.admin.module.business.mes.base.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户表 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-03 09:43:14
 * @Copyright zscbdic
 */

@Data
public class CustomerVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "停用标识;0启用，1停用")
    private Integer enableFlag;

    @Schema(description = "客户编号")
    private String number;

    @Schema(description = "客户名称")
    private String name;

    @Schema(description = "公司名称")
    private String company;

    @Schema(description = "联系人")
    private String contact;

    @Schema(description = "电话")
    private String telephone;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "等级;5星最高，无半星")
    private String level;

}
