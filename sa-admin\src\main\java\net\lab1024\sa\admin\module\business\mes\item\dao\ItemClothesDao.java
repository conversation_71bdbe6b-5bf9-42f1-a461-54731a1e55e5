package net.lab1024.sa.admin.module.business.mes.item.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemClothesEntity;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemClothesQueryForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.vo.ItemClothesVO;

import java.util.List;

/**
 * 成衣信息表 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-09 11:23:49
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface ItemClothesDao extends BaseMapper<ItemClothesEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
//    List<ItemClothesVO> queryPage(Page page, @Param("queryForm") ItemClothesQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 分页查询成衣信息
     * @param page
     * @param queryForm
     * @return
     */
    List<ItemClothesVO> queryClothesPage(Page<?> page,@Param("queryForm") ItemClothesQueryForm queryForm);
}
