package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationWarehouseMapDao;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationWarehouseMapEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationWarehouseMapUpdateForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.vo.PartStationWarehouseMapVO;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;

import jakarta.annotation.Resource;

/**
 * 裁片仓库地图 Service
 *
 * <AUTHOR>
 * @Date 2024-11-05 16:55:45
 * @Copyright zscbdic
 */

@Service
public class PartStationWarehouseMapService {

    @Resource
    private PartStationWarehouseMapDao partStationWarehouseMapDao;


    /**
     * 更新
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(PartStationWarehouseMapUpdateForm updateForm) {
        Long warehouseId = updateForm.getWarehouseId();
        PartStationWarehouseMapEntity mapEntity = partStationWarehouseMapDao.selectOne(
                new LambdaQueryWrapper<PartStationWarehouseMapEntity>()
                        .eq(PartStationWarehouseMapEntity::getWarehouseId, warehouseId)
                        .last("LIMIT 1")
        );
        PartStationWarehouseMapEntity map = SmartBeanUtil.copy(updateForm, PartStationWarehouseMapEntity.class);
        if(mapEntity == null){
            partStationWarehouseMapDao.insert(map);
        }else {
           partStationWarehouseMapDao.update(map,new LambdaQueryWrapper<PartStationWarehouseMapEntity>()
                   .eq(PartStationWarehouseMapEntity::getWarehouseId,warehouseId));
        }
        return ResponseDTO.ok();
    }

    /**
     * 根据仓库id获取地图数据
     * @param warehouseId
     * @return
     */
    public ResponseDTO<PartStationWarehouseMapVO> getMapDataByWarehouseId(Long warehouseId) {
        PartStationWarehouseMapEntity mapEntity = partStationWarehouseMapDao.selectOne(new LambdaQueryWrapper<PartStationWarehouseMapEntity>()
                .eq(PartStationWarehouseMapEntity::getWarehouseId, warehouseId)
                .last("LIMIT 1"));
        if(mapEntity == null){
            return ResponseDTO.userErrorParam("该仓库未配置地图数据");
        }
        return ResponseDTO.ok(SmartBeanUtil.copy(mapEntity,PartStationWarehouseMapVO.class));

    }
}
