package net.lab1024.sa.admin.module.business.mes.equip.scada.domain.dto;

import lombok.Data;

import java.util.Date;

/**
 * 各平台设备基础数据
 */
@Data
public class EquipScadaDataDTO {

    /**
     * scada设备id
     */
    private String scadaEquipmentId;

    /**
     * scada产品编号
     */
    private String scadaProductCode;

    /**
     * scada设备编号
     */
    private String scadaEquipmentCode;


    /**
     * 最后scada数据更新时间
     */
    private Date lastScadaDataUpdateTime;

    /**
     * 最后scada设备上线时间
     */
    private Date lastScadaEquipmentUpTime;

    /**
     * 最后scada设备下线时间
     */
    private Date lastScadaEquipmentDownTime;


    /**
     * 设备运行状态
     */
    private String equipmentRunStatus;

    /**
     * 设备在线状态
     */
    private String equipmentOnlineStatus;

    /**
     * 设备scada运行状态
     */
    private String equipmentScadaRunStatus;

    /**
     * 设备scada在线状态
     */
    private String equipmentScadaOnlineStatus;






}
