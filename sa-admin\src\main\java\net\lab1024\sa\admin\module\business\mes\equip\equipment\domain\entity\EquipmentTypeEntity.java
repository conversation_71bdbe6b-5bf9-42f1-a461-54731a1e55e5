package net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 设备类别 实体类
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:12:33
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_equip_equipment_type")
public class EquipmentTypeEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 设备类别编号
     */
    private String number;

    /**
     * 设备类别名称
     */
    private String name;

}
