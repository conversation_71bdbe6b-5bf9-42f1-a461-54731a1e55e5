package net.lab1024.sa.admin.module.business.mes.stock.warehouse.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkRackAddForm;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkRackQuery;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkRackQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkRackUpdateForm;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.vo.StkRackVO;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.service.StkRackService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 货架 Controller
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:13:43
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class StkRackController {

    @Resource
    private StkRackService stkRackService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/stkRack/queryPage")
    public ResponseDTO<PageResult<StkRackVO>> queryPage(@RequestBody @Valid StkRackQueryForm queryForm) {
        return ResponseDTO.ok(stkRackService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/stkRack/add")
    public ResponseDTO<String> add(@RequestBody @Valid StkRackAddForm addForm) {
        return stkRackService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/stkRack/update")
    public ResponseDTO<String> update(@RequestBody @Valid StkRackUpdateForm updateForm) {
        return stkRackService.update(updateForm);
    }


    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/stkRack/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return stkRackService.delete(id);
    }


    /**
     * 查询列表
     * @param query
     * @return
     */
    @Operation(summary = "查询列表 <AUTHOR>
    @PostMapping("/stkRack/queryList")
    public ResponseDTO<List<StkRackVO>> queryList(@RequestBody @Valid StkRackQuery query) {
        return stkRackService.queryList(query);
    }
}
