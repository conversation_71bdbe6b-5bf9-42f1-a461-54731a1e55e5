package net.lab1024.sa.admin.module.business.mes.item.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 布料信息表 实体类
 *
 * <AUTHOR>
 * @Date 2024-07-02 12:03:01
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_item_cloth")
public class ItemClothEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */

    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 物料主信息id
     */
    private Long itemId;

    /**
     * 克重;克每平方米
     */
    private Integer gramWeight;

    /**
     * 幅宽;厘米
     */
    private Integer width;

    /**
     * 布重;千克
     */
    private Double weight;

    /**
     * 布长;米
     */
    private Double length;

    /**
     * 色号
     */
    private String colorNum;

    /**
     * 颜色名称
     */
    private String colorName;

    /**
     * 参考色
     */
    private String referColor;

    /**
     * 成分
     */
    private String ingredient;

    /**
     * 净价
     */
    private BigDecimal netPrice;

    /**
     * 米净价
     */
    private BigDecimal metreNetPrice;

    /**
     * 公斤净价
     */
    private BigDecimal kgNetPrice;





}
