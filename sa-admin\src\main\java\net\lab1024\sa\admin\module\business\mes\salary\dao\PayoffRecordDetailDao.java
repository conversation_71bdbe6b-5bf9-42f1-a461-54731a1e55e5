package net.lab1024.sa.admin.module.business.mes.salary.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.PayoffRecordDetailEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.PayoffRecordDetailQueryForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.vo.PayoffRecordDetailVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 薪酬发放记录详情 Dao
 *
 * <AUTHOR>
 * @Date 2024-12-03 21:37:54
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface PayoffRecordDetailDao extends BaseMapper<PayoffRecordDetailEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<PayoffRecordDetailVO> queryPage(Page page, @Param("queryForm") PayoffRecordDetailQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 批量插入
     */
    void batchInsert(@Param("list") List<PayoffRecordDetailEntity> details);
}
