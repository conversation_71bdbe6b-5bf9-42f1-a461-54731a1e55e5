package net.lab1024.sa.admin.module.business.mes.stock.lot.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.item.dao.ItemDao;
import net.lab1024.sa.admin.module.business.mes.stock.lot.dao.StkLotMasterDao;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.entity.StkLotMasterEntity;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.form.StkLotMasterQuery;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.form.StkLotMasterQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.vo.StkLotMasterVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 批号主档 Service
 *
 * <AUTHOR>
 * @Date 2025-02-03 19:28:09
 * @Copyright zscbdic
 */

@Service
public class StkLotMasterService {

    @Resource
    private StkLotMasterDao stkLotMasterDao;

    @Resource
    private ItemDao itemDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<StkLotMasterVO> queryPage(StkLotMasterQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<StkLotMasterVO> list = stkLotMasterDao.queryPage(page, queryForm);


        PageResult<StkLotMasterVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }


    /**
     * 查询列表
     * @param query
     * @return
     */
    public List<StkLotMasterVO> queryList(StkLotMasterQuery query) {
        List<StkLotMasterEntity> list = stkLotMasterDao.selectList(new LambdaQueryWrapper<StkLotMasterEntity>()
                .like(StrUtil.isNotBlank(query.getLotNumber()), StkLotMasterEntity::getNumber, query.getLotNumber())
                .eq(query.getMaterielId()!=null, StkLotMasterEntity::getMaterielId, query.getMaterielId()));
        return SmartBeanUtil.copyList(list, StkLotMasterVO.class);
    }
}
