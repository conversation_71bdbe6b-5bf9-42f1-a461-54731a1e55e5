package net.lab1024.sa.admin.module.business.mes.base.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import net.lab1024.sa.admin.module.business.mes.base.dao.StyleColorDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.StyleColorEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.StyleColorAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.StyleColorQueryAllForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.StyleColorQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.StyleColorUpdateForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.StyleColorVO;
import net.lab1024.sa.admin.module.business.mes.base.manager.StyleColorManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 款式颜色表 Service
 *
 * <AUTHOR>
 * @Date 2024-11-03 20:49:04
 * @Copyright zscbdic
 */

@Service
public class StyleColorService {

    @Resource
    private StyleColorDao styleColorDao;

    @Resource
    private StyleColorManager styleColorManager;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<StyleColorVO> queryPage(StyleColorQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<StyleColorVO> list = styleColorDao.queryPage(page, queryForm);
        PageResult<StyleColorVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(StyleColorAddForm addForm) {
        styleColorManager.addCheck(addForm);

        StyleColorEntity styleColorEntity = SmartBeanUtil.copy(addForm, StyleColorEntity.class);
        styleColorDao.insert(styleColorEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(StyleColorUpdateForm updateForm) {
        styleColorManager.updateCheck(updateForm);
        StyleColorEntity styleColorEntity = SmartBeanUtil.copy(updateForm, StyleColorEntity.class);
        styleColorDao.updateById(styleColorEntity);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    public ResponseDTO<String> batchDelete(List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return ResponseDTO.ok();
        }

        styleColorDao.batchUpdateDeleted(idList, true);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Integer id) {
        if (null == id){
            return ResponseDTO.ok();
        }

        styleColorDao.updateDeleted(id,true);
        return ResponseDTO.ok();
    }

    /**
     * 款式颜色全查询
     * @param queryAllForm 颜色名称（可选）
     * @return
     */
    public List<StyleColorVO> queryAll(StyleColorQueryAllForm queryAllForm) {
        LambdaQueryWrapper<StyleColorEntity> wrapper = new LambdaQueryWrapper<StyleColorEntity>()
                .select(StyleColorEntity::getId, StyleColorEntity::getStyleColor,StyleColorEntity::getRemark)
                .like(StringUtil.isNotEmpty(queryAllForm.getQueryKey()),StyleColorEntity::getStyleColor,queryAllForm.getQueryKey())
                .orderByDesc(StyleColorEntity::getCreateTime);
        List<StyleColorEntity> styleColorList = styleColorDao.selectList(wrapper);

        return SmartBeanUtil.copyList(styleColorList, StyleColorVO.class);
    }
}
