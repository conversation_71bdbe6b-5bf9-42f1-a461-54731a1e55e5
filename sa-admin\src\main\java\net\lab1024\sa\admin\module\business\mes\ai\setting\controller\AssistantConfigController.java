package net.lab1024.sa.admin.module.business.mes.ai.setting.controller;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.ai.setting.domain.form.AssistantConfigUpadteForm;
import net.lab1024.sa.admin.module.business.mes.ai.setting.domain.vo.AssistantConfigVO;
import net.lab1024.sa.admin.module.business.mes.ai.setting.service.AssistantConfigService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 智能体配置
 */
@RestController
public class AssistantConfigController {

    @Resource
    private AssistantConfigService assistantConfigService;


    /**
     * 智能体配置-获取
     * @return
     */
    @Operation(summary = "智能体配置-获取")
    @GetMapping("/ai/llm/assistantConfig/get")
    public ResponseDTO<AssistantConfigVO> getConfig() {
        return ResponseDTO.ok(assistantConfigService.getConfig());
    }

    /**
     * 智能体配置-更新
     * @param form
     * @return
     */
    @Operation(summary = "智能体配置-获取")
    @PostMapping("/ai/llm/assistantConfig/update")
    public ResponseDTO<String> updateConfig(@RequestBody @Valid AssistantConfigUpadteForm form) {
        assistantConfigService.updateConfig(form);
        return ResponseDTO.ok();
    }
}
