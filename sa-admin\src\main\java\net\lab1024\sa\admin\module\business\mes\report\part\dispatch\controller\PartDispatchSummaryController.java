package net.lab1024.sa.admin.module.business.mes.report.part.dispatch.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.form.PartDispatchQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.vo.PartDispatchSummaryVO;
import net.lab1024.sa.admin.module.business.mes.report.part.dispatch.service.PartDispatchSummaryService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 裁片收发汇总
 */
@RestController
@Tag(name = "")
public class PartDispatchSummaryController {

    @Resource
    private PartDispatchSummaryService partDispatchSummaryService;

    /**
     * 收发汇总分页
     * @param queryForm
     * @return
     */
    @Operation(summary = "收发汇总分页")
    @PostMapping("/partDispatchSummary/summaryPage")
    public ResponseDTO<PageResult<PartDispatchSummaryVO>> summaryPage(@RequestBody @Valid PartDispatchQueryForm queryForm) {
        return partDispatchSummaryService.summaryPage(queryForm);
    }

    /**
     * 收发汇总详情
     * @param produceInstructOrderId
     * @return
     */
    @Operation(summary = "收发汇总详情")
    @GetMapping("/partDispatchSummary/summaryDetail/{produceInstructOrderId}")
    public ResponseDTO<List<PartDispatchSummaryVO.DetailVO>> summaryDetail(@PathVariable Long produceInstructOrderId) {
        return partDispatchSummaryService.summaryDetail(produceInstructOrderId);
    }

}
