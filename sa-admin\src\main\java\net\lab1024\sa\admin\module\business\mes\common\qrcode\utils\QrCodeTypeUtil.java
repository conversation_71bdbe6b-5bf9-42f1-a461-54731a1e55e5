package net.lab1024.sa.admin.module.business.mes.common.qrcode.utils;

import cn.hutool.core.text.CharSequenceUtil;
import net.lab1024.sa.admin.module.business.mes.common.qrcode.QrCodeTypeEnum;
import net.lab1024.sa.base.common.exception.BusinessException;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class QrCodeTypeUtil {

    /**
     * 间隔符
     */
    public static final String SEPARATOR = "-";

    /**
     * 将枚举和值转换为二维码字符串
     *
     * @param typeEnum
     * @param value
     * @return
     */
    public static String toQrCodeString(QrCodeTypeEnum typeEnum, Object value) {
        return typeEnum.getPrefix() + SEPARATOR + value;
    }

    /**
     * 将枚举和值转换为二维码字符串
     *
     * @param typeEnum
     * @param value
     * @param extra
     * @return
     */
    public static String toQrCodeString(QrCodeTypeEnum typeEnum, Object value, String... extra) {
        return typeEnum.getPrefix() + SEPARATOR + value + SEPARATOR + String.join(SEPARATOR, extra);
    }

    /**
     * 获取二维码值
     *
     * @param qrCode
     * @return
     */
    public static Long getValue(String qrCode) {
        checkQrCode(qrCode);
        String[] split = qrCode.split(SEPARATOR);
        return Long.valueOf(split[1]);
    }

    /**
     * 获取二维码额外信息
     *
     * @param qrCode
     * @return
     */
    public static List<String> getExtra(String qrCode) {
        checkQrCode(qrCode);
        String[] split = qrCode.split(SEPARATOR);
        return split.length > 2 ? List.of(split[2].split(SEPARATOR)) : Collections.emptyList();
    }

    /**
     * 校验二维码
     * @param qrCode
     */
    public static void checkQrCode(String qrCode) {
        if (CharSequenceUtil.isBlank(qrCode)) {
            throw new BusinessException("二维码不能为空");
        }

        String[] split = qrCode.split(SEPARATOR);
        if (split.length < 2) {
            throw new BusinessException("二维码格式错误");
        }

        List<String> list = Arrays.stream(QrCodeTypeEnum.values()).map(QrCodeTypeEnum::getPrefix).toList();
        if (!list.contains(split[0])) {
            throw new BusinessException("二维码类型错误");
        }
    }

}
