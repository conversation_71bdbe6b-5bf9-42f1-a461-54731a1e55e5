package net.lab1024.sa.admin.module.business.mes.salary.domain.form;

import net.lab1024.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import lombok.Data;

/**
 * 薪酬发放记录表 分页查询表单
 *
 * <AUTHOR>
 * @Date 2024-12-03 21:37:41
 * @Copyright zscbdic
 */

@Data
public class PayoffRecordQueryForm extends PageParam{

    @Schema(description = "发放时间")
    private LocalDate payoffTimeBegin;

    @Schema(description = "发放时间")
    private LocalDate payoffTimeEnd;

    @Schema(description = "员工id")
    private Long employeeId;

}
