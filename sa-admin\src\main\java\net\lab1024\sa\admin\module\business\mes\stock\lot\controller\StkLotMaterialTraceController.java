package net.lab1024.sa.admin.module.business.mes.stock.lot.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.form.StkLotMaterialTraceQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.vo.StkLotMaterialTraceVO;
import net.lab1024.sa.admin.module.business.mes.stock.lot.service.StkLotMaterialTraceService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 批号跟踪信息 Controller
 *
 * <AUTHOR>
 * @Date 2025-02-08 19:57:37
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class StkLotMaterialTraceController {

    @Resource
    private StkLotMaterialTraceService stkLotMaterialTraceService;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/stkLotMaterialTrace/queryPage")
    public ResponseDTO<PageResult<StkLotMaterialTraceVO>> queryPage(@RequestBody @Valid StkLotMaterialTraceQueryForm queryForm) {
        return ResponseDTO.ok(stkLotMaterialTraceService.queryPage(queryForm));
    }

    /**
     * 分页查询(额外信息)
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询(额外信息) <AUTHOR>
    @PostMapping("/stkLotMaterialTrace/queryPageWithExtra")
    public ResponseDTO<PageResult<StkLotMaterialTraceVO>> queryPageWithExtra(@RequestBody @Valid StkLotMaterialTraceQueryForm queryForm) {
        return ResponseDTO.ok(stkLotMaterialTraceService.queryPageWithExtra(queryForm));
    }

}
