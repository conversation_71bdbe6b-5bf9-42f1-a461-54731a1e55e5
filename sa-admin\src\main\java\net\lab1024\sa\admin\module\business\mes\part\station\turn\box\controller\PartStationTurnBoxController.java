package net.lab1024.sa.admin.module.business.mes.part.station.turn.box.controller;

import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form.PartStationTurnBoxAddForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form.PartStationTurnBoxBatchAddForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form.PartStationTurnBoxQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form.PartStationTurnBoxUpdateForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.vo.PartStationTurnBoxVO;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.service.PartStationTurnBoxService;
import net.lab1024.sa.base.common.domain.ValidateList;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 裁片周转箱 Controller
 *
 * <AUTHOR>
 * @Date 2025-03-14 11:36:51
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "裁片周转箱")
public class PartStationTurnBoxController {

    @Resource
    private PartStationTurnBoxService partStationTurnBoxService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/partStationTurnBox/queryPage")
    @SaCheckPermission("partStationTurnBox:query")
    public ResponseDTO<PageResult<PartStationTurnBoxVO>> queryPage(@RequestBody @Valid PartStationTurnBoxQueryForm queryForm) {
        return ResponseDTO.ok(partStationTurnBoxService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/partStationTurnBox/add")
    @SaCheckPermission("partStationTurnBox:add")
    public ResponseDTO<String> add(@RequestBody @Valid PartStationTurnBoxAddForm addForm) {
        return partStationTurnBoxService.add(addForm);
    }

    /**
     * 批量添加
     * @param form
     * @return
     */
    @Operation(summary = "批量添加 <AUTHOR>
    @PostMapping("/partStationTurnBox/batchAdd")
    @SaCheckPermission("partStationTurnBox:add")
    public ResponseDTO<String> batchAdd(@RequestBody @Valid PartStationTurnBoxBatchAddForm form) {
        return partStationTurnBoxService.batchAdd(form);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/partStationTurnBox/update")
    @SaCheckPermission("partStationTurnBox:update")
    public ResponseDTO<String> update(@RequestBody @Valid PartStationTurnBoxUpdateForm updateForm) {
        return partStationTurnBoxService.update(updateForm);
    }

    /**
     * 批量删除
     * @param idList
     * @return
     */
    @Operation(summary = "批量删除 <AUTHOR>
    @PostMapping("/partStationTurnBox/batchDelete")
    @SaCheckPermission("partStationTurnBox:delete")
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> idList) {
        return partStationTurnBoxService.batchDelete(idList);
    }

    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/partStationTurnBox/delete/{id}")
    @SaCheckPermission("partStationTurnBox:delete")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return partStationTurnBoxService.delete(id);
    }

    /**
     * 获取周转箱二维码base64
     * @param id
     * @return
     */
    @Operation(summary = "获取二维码base64 <AUTHOR>
    @GetMapping("/partStationTurnBox/getQrCode/{id}")
    public ResponseDTO<String> getQrCode(@PathVariable("id") Long id) {
        return partStationTurnBoxService.getQrCode(id);
    }


    /**
     * 通过id查询
     * @param id
     * @return
     */
    @Operation(summary = "通过id查询 <AUTHOR>
    @GetMapping("/partStationTurnBox/getById/{id}")
    public ResponseDTO<PartStationTurnBoxVO> getById(@PathVariable("id") Long id) {
        return partStationTurnBoxService.getById(id);
    }
}
