package net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 货架 列表VO
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:13:43
 * @Copyright zscbdic
 */

@Data
public class StkRackVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "仓库id")
    private Long warehouseId;

    @Schema(description = "货架编号")
    private String number;

    @Schema(description = "位置描述")
    private String location;

}
