package net.lab1024.sa.admin.module.business.mes.part.station.inventory.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.entity.PartStationOptLogEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.form.PartStationOptLogQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.vo.PartStationOptLogVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 裁片驿站操作日志 Dao
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:47:06
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface PartStationOptLogDao extends BaseMapper<PartStationOptLogEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<PartStationOptLogVO> queryPage(Page page, @Param("queryForm") PartStationOptLogQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
