package net.lab1024.sa.admin.module.business.mes.base.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.base.dao.StyleDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.StyleEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.StyleAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.StyleQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.StyleTreeQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.StyleUpdateForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.StyleTreeVO;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.StyleVO;
import net.lab1024.sa.admin.module.business.mes.base.manager.StyleManager;
import net.lab1024.sa.base.common.code.UserErrorCode;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 款式品类表 Service
 *
 * <AUTHOR>
 * @Date 2024-07-05 15:45:34
 * @Copyright zscbdic
 */

@Service
public class StyleService {

    @Resource
    private StyleDao styleDao;

    @Resource
    private StyleManager styleManager;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<StyleVO> queryPage(StyleQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<StyleVO> list = styleDao.queryPage(page, queryForm);
        PageResult<StyleVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(StyleAddForm addForm) {
        StyleEntity styleEntity = SmartBeanUtil.copy(addForm, StyleEntity.class);
        ResponseDTO<String> res = styleManager.checkStyleType(styleEntity, false);
        if (!res.getOk()) {
            return res;
        }
        Long parentId = addForm.getParentId() == null ? NumberUtils.LONG_ZERO : addForm.getParentId();
        styleEntity.setParentId(parentId);
        styleDao.insert(styleEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(StyleUpdateForm updateForm) {

        Long id = updateForm.getId();
        StyleEntity originEntity = styleManager.getById(id);

        if (originEntity == null) {
            ResponseDTO.error(UserErrorCode.DATA_NOT_EXIST);
        }
        StyleEntity style = SmartBeanUtil.copy(updateForm, StyleEntity.class);

        style.setParentId(updateForm.getParentId());

        ResponseDTO<String> responseDTO = styleManager.checkStyleType(style, true);

        if (!responseDTO.getOk()) {
            return responseDTO;
        }
        styleDao.updateById(style);
        return ResponseDTO.ok();
    }


    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        StyleEntity styleEntity = styleDao.selectById(id);
        if (styleEntity == null) {
            return ResponseDTO.error(UserErrorCode.DATA_NOT_EXIST);
        }
        List<Long> styleSubId = styleManager.queryStyleSubId(Lists.newArrayList(id));
        if (CollectionUtils.isNotEmpty(styleSubId)) {
            return ResponseDTO.userErrorParam("请先删除子集类目");
        }
        styleDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }

    /**
     * 树表查询
     *
     * @param styleTreeQueryForm
     * @return
     */
    public ResponseDTO<List<StyleTreeVO>> queryTree(StyleTreeQueryForm styleTreeQueryForm) {
        if (styleTreeQueryForm.getParentId() == null) {
            styleTreeQueryForm.setParentId(NumberUtils.LONG_ZERO);
        }
        List<StyleTreeVO> list = styleManager.queryStyleTree(styleTreeQueryForm.getParentId(), styleTreeQueryForm.getQueryKey());
        return ResponseDTO.ok(list);
    }
}
