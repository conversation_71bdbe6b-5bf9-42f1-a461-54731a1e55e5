package net.lab1024.sa.admin.module.business.mes.stock.lot.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.event.item.ItemDeleteCheckEvent;
import net.lab1024.sa.admin.module.business.mes.stock.lot.dao.StkLotMasterDao;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.entity.StkLotMasterEntity;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;

/**
 * 物料删除事件监听器
 */
@Component("stkLotMaterielListener")
public class MaterielListener {

    @Resource
    private StkLotMasterDao stkLotMasterDao;

    @EventListener(ItemDeleteCheckEvent.class)
    public void materielDeleteCheck(ItemDeleteCheckEvent event) {
        Long id = event.getId();

        Long count = stkLotMasterDao.selectCount(new LambdaQueryWrapper<StkLotMasterEntity>()
                .eq(StkLotMasterEntity::getMaterielId, id));
        if (count > 0) {
            throw new BusinessException("该物料正在被存在批号，无法删除");
        }
    }

}
