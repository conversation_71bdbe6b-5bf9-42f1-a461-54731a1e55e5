package net.lab1024.sa.admin.module.business.mes.part.station.inventory.controller;

import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.form.PartStationInventoryQuery;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.form.PartStationInventoryQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.form.PartStationInventoryUsageSituationQuery;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.vo.PartStationInventoryUsageSituationVO;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.vo.PartStationInventoryVO;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.service.PartStationInventoryService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 裁片驿站库存表 Controller
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:44:34
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class PartStationInventoryController {

    @Resource
    private PartStationInventoryService partStationInventoryService;


    /**
     * 库存分页查询 <AUTHOR>
     * @param queryForm
     * @return
     */
    @Operation(summary = "库存分页查询 <AUTHOR>
    @PostMapping("/partStationInventory/queryPage")
    public ResponseDTO<PageResult<PartStationInventoryVO>> queryPage(@RequestBody @Valid PartStationInventoryQueryForm queryForm) {
        return ResponseDTO.ok(partStationInventoryService.queryPage(queryForm));
    }

    /**
     * 库存查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "库存查询")
    @PostMapping("/partStationInventory/query")
    public ResponseDTO<List<PartStationInventoryVO>> query(@RequestBody @Valid PartStationInventoryQuery queryForm) {
        return ResponseDTO.ok(partStationInventoryService.query(queryForm));
    }

    /**
     * 库存使用情况
     * @param queryForm
     * @return
     */
    @Operation(summary = "库存使用情况")
    @PostMapping("/partStationInventory/usageSituation")
    public ResponseDTO<List<PartStationInventoryUsageSituationVO>> usageSituation(@RequestBody @Valid PartStationInventoryUsageSituationQuery queryForm) {
        return ResponseDTO.ok(partStationInventoryService.usageSituation(queryForm));
    }

    /**
     * 库存汇总分页 <AUTHOR>
     * @param queryForm
     * @return
     */
    @Operation(summary = "库存汇总分页 <AUTHOR>
    @PostMapping("/partStationInventory/summaryPage")
    public ResponseDTO<PageResult<PartStationInventoryVO>> summaryPage(@RequestBody @Valid PartStationInventoryQueryForm queryForm) {
        return ResponseDTO.ok(partStationInventoryService.summaryPage(queryForm));
    }
//
//    @Operation(summary = "添加 <AUTHOR>
//    @PostMapping("/partStationInventory/add")
//    public ResponseDTO<String> add(@RequestBody @Valid PartStationInventoryAddForm addForm) {
//        return partStationInventoryService.add(addForm);
//    }
//
//    @Operation(summary = "更新 <AUTHOR>
//    @PostMapping("/partStationInventory/update")
//    public ResponseDTO<String> update(@RequestBody @Valid PartStationInventoryUpdateForm updateForm) {
//        return partStationInventoryService.update(updateForm);
//    }
//
//    @Operation(summary = "批量删除 <AUTHOR>
//    @PostMapping("/partStationInventory/batchDelete")
//    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> idList) {
//        return partStationInventoryService.batchDelete(idList);
//    }
//
//    @Operation(summary = "单个删除 <AUTHOR>
//    @GetMapping("/partStationInventory/delete/{id}")
//    public ResponseDTO<String> batchDelete(@PathVariable Long id) {
//        return partStationInventoryService.delete(id);
//    }
}
