package net.lab1024.sa.admin.module.business.mes.ai.core.assistant;

import cn.hutool.core.util.ArrayUtil;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.mes.ai.core.config.AssistantConfig;
import net.lab1024.sa.admin.module.business.mes.ai.core.route.ChatModelRoute;
import net.lab1024.sa.admin.module.business.mes.ai.tool.service.LLMToolRoleService;
import net.lab1024.sa.admin.module.business.mes.ai.tool.tool.constant.ToolNameConstant;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;


@Slf4j
@Service
public class ChatAssistant {


    @Getter
    @Resource
    private ChatMemory chatMemory;


    @Resource
    private LLMToolRoleService llmToolRoleService;

    @Resource
    private ChatModelRoute chatModelRoute;

    @Resource
    private AssistantConfig assistantConfig;

    @Getter
    private ChatClient chatClient;


    public static final int MAX_CHAT_HISTORY_SIZE = 5;


    /**
     * 构建客户端
     *
     * @param routeSelectDTO
     */
    public void buildClient(ChatModelRoute.RouteSelectDTO routeSelectDTO) {
        ChatModel model = chatModelRoute.selectModel(routeSelectDTO);

        AssistantConfig.Config assistantConfigData = assistantConfig.getConfig();
        String systemPrompt = assistantConfigData.getSystemPrompt();

        this.chatClient = ChatClient.builder(model)
                .defaultSystem(systemPrompt)
                .defaultAdvisors(
                        new SimpleLoggerAdvisor(),
                        MessageChatMemoryAdvisor.builder(chatMemory).build())
                .build();

    }

    public void buildClient(UserMessage userMsg) {
        buildClient(new ChatModelRoute.RouteSelectDTO(null, userMsg));

    }

    public void buildClient(String userMsg) {
        UserMessage userMessage = new UserMessage(userMsg);
        buildClient(new ChatModelRoute.RouteSelectDTO(null, userMessage));
    }


    /**
     * 聊天
     *
     * @param chatId
     * @param userMessageContent
     * @return
     */
    public Flux<String> chatStream(String chatId, String userMessageContent) {
        buildClient(userMessageContent);
        Long userId = SmartRequestUtil.getRequestUserId();

        ChatClient.ChatClientRequestSpec spec = this.chatClient.prompt()
                .user(userMessageContent)
                .toolContext(Map.of(ToolNameConstant.TOOL_CONTENT_USER_ID, userId))
                .advisors(a -> a.param(ChatMemory.CONVERSATION_ID, chatId));
        // 添加工具
        Object[] toolList = llmToolRoleService.getToolList(userId);
        if (ArrayUtil.isNotEmpty(toolList)) {
            spec.tools(toolList);
        }
        return spec.stream().content();

    }

    /**
     * 聊天
     *
     * @param chatId
     * @param userMessageContent
     * @return
     */
    public String chat(String chatId, String userMessageContent) {
        buildClient(userMessageContent);

        Long userId = SmartRequestUtil.getRequestUserId();


        ChatClient.ChatClientRequestSpec spec = this.chatClient.prompt()
                .user(userMessageContent)
                .advisors(a -> a
                        .param(ChatMemory.CONVERSATION_ID, chatId));
        // 添加工具
        Object[] toolList = llmToolRoleService.getToolList(userId);
        if (ArrayUtil.isNotEmpty(toolList)) {
            spec.tools(toolList);
        }
        return spec.call().content();
    }


    /**
     * 转换实体
     *
     * @param transformEntityDTO
     * @param clazz
     * @param <T>
     * @return
     */
    public <T> T transformEntity(TransformEntityDTO transformEntityDTO, Class<T> clazz) {
        UserMessage userMsg = transformEntityDTO.getUserMessage();
        SystemMessage sysMsg = transformEntityDTO.getSystemMessage();
        Long modelId = transformEntityDTO.getModelId();

        buildClient(new ChatModelRoute.RouteSelectDTO(modelId, userMsg));
        ChatClient.ChatClientRequestSpec spec = this.chatClient
                .prompt(new Prompt(userMsg))
                .system(sysMsg.getText());

        if (Boolean.TRUE.equals(transformEntityDTO.getHistoryFlag())) {
            spec.advisors(a -> a
                    .param(ChatMemory.CONVERSATION_ID, transformEntityDTO.getChatId()));
        }

        return spec.call().entity(clazz);
    }


    public <T> T transformEntity(TransformEntityDTO transformEntityDTO, ParameterizedTypeReference<T> p) {
        UserMessage userMsg = transformEntityDTO.getUserMessage();
        SystemMessage sysMsg = transformEntityDTO.getSystemMessage();
        Long modelId = transformEntityDTO.getModelId();

        buildClient(new ChatModelRoute.RouteSelectDTO(modelId, userMsg));
        ChatClient.ChatClientRequestSpec spec = this.chatClient
                .prompt(new Prompt(userMsg))
                .system(sysMsg.getText());

        if (Boolean.TRUE.equals(transformEntityDTO.getHistoryFlag())) {
            spec.advisors(a -> a
                    .param(ChatMemory.CONVERSATION_ID, transformEntityDTO.getChatId()));
        }
        return spec.call().entity(p);
    }

    /**
     * 清除会话
     *
     * @param chatId
     */
    public void clearChat(String chatId) {
        chatMemory.clear(chatId);
    }

    public List<Message> getChatHistory(String chatId) {
        return chatMemory.get(chatId);
    }


}
