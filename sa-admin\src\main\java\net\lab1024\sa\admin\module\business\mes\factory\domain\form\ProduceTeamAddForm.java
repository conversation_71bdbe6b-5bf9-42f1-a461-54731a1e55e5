package net.lab1024.sa.admin.module.business.mes.factory.domain.form;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import lombok.Data;

import java.util.List;

/**
 * 生产小组 新建表单
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:32:46
 * @Copyright zscbdic
 */

@Data
public class ProduceTeamAddForm {

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "生产小组名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "生产小组名称 不能为空")
    private String teamName;

    @Schema(description = "负责人id")
    @NotNull(message = "负责人id 不能为空")
    private Long leaderId;

    @Schema(description = "车间id")
    private Long workshopId;

    @Valid
    @Schema(description = "小组成员信息")
    @JsonProperty("memberIds")
    private List<ProduceTeamMemberAddForm> produceTeamMemberAddFormList;

}
