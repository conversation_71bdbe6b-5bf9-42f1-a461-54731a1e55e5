package net.lab1024.sa.admin.module.business.mes.base.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 季度表 新建表单
 *
 * <AUTHOR>
 * @Date 2024-07-05 15:45:34
 * @Copyright zscbdic
 */

@Data
public class SeasonAddForm {

    @Schema(description = "季度名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "季度名称 不能为空")
    private String seasonName;

    @Schema(description = "父级id")
//    @NotNull(message = "父级id 不能为空")
    private Long parentId;

}
