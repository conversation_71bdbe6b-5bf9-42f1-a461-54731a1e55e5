package net.lab1024.sa.admin.module.business.mes.part.station.inventory.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;

import cn.hutool.core.lang.mutable.MutablePair;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationBinDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.dto.PartStationBinDTO;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.domain.dto.StorageTimeConfigDTO;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.entity.PartStationInventoryEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.manager.PartStationBinManager;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.manager.PartStationConfigManager;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.manager.PartStationInventoryManager;
import net.lab1024.sa.base.common.enumeration.UserTypeEnum;
import net.lab1024.sa.base.module.support.job.core.SmartJob;
import net.lab1024.sa.base.module.support.message.constant.MessageTypeEnum;
import net.lab1024.sa.base.module.support.message.domain.MessageSendForm;
import net.lab1024.sa.base.module.support.message.service.MessageService;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 存储时间任务
 */
@Slf4j
@Component
public class StorageTimeJob implements SmartJob {

    @Resource
    private PartStationConfigManager partStationConfigManager;

    @Resource
    private PartStationInventoryManager partStationInventoryManager;

    @Resource
    private PartStationBinManager partStationBinManager;

    @Resource
    private PartStationBinDao partStationBinDao;

    @Resource
    private MessageService messageService;


    @Override
    public String run(String param) {
        checkStorageTime();
        log.info("检查存放时间完成");
        return "检查存放时间完成";
    }


    /**
     * 检查存储时间
     */
    private void checkStorageTime() {
        StorageTimeConfigDTO timeConfig = partStationConfigManager.getStorageTimeConfig();
        if (timeConfig == null || !timeConfig.getEnableFlag()) {
            // 未启用
            return;
        }
        if (CollUtil.isEmpty(timeConfig.getEmployeeIds())) {
            // 未配置人员
            return;
        }

        //查询
        LocalDateTime now = LocalDateTime.now();
        // 获取建议存放时间和最大存放时间的临界点
        Integer minDay = timeConfig.getMinDay();
        Integer maxDay = timeConfig.getMaxDay();
        LocalDateTime minTime = LocalDateTimeUtil.endOfDay(now.minusDays(minDay));
        // 获取过期的数据
        List<PartStationInventoryEntity> list = partStationInventoryManager
                .lambdaQuery()
                .select(PartStationInventoryEntity::getId, PartStationInventoryEntity::getStockInTime, PartStationInventoryEntity::getBinId)
                .lt(PartStationInventoryEntity::getStockInTime, minTime)
                .list();
        if (CollUtil.isEmpty(list)) {
            // 没有过期的数据
            return;
        }
        // 获取过期的数据的库位id
        List<Long> binIds = list.stream().map(PartStationInventoryEntity::getBinId).distinct().toList();
        List<PartStationBinDTO> partStationBinDTOS = partStationBinDao.queryBinByIds(binIds);
        if (CollUtil.isEmpty(partStationBinDTOS)) {
            // 没有库位数据
            return;
        }
        HashMap<Long, PartStationBinDTO> binMap = partStationBinDTOS.stream()
                .collect(HashMap::new, (m, v) -> m.put(v.getBinId(), v), HashMap::putAll);


        // 统计过期数据
        Map<Long, MutablePair<Integer, Integer>> countMap = new HashMap<>();
        for (PartStationInventoryEntity e : list) {
            Long binId = e.getBinId();
            PartStationBinDTO partStationBinDTO = binMap.get(binId);
            if (partStationBinDTO == null) {
                continue;
            }

            long days = LocalDateTimeUtil.between(e.getStockInTime(), now, ChronoUnit.DAYS);
            MutablePair<Integer, Integer> pair = countMap.getOrDefault(partStationBinDTO.getWarehouseId(), new MutablePair<>(0, 0));
            if (days <= minDay) {
                // 建议存放时间
                pair.setKey(pair.getKey() + 1);
            } else {
                // 最大存放时间
                pair.setValue(pair.getValue() + 1);
            }
            countMap.put(partStationBinDTO.getWarehouseId(), pair);
        }

        //拼装
        Map<Long, PartStationBinDTO> warehouseMap = partStationBinDTOS.stream()
                .collect(Collectors.toMap(PartStationBinDTO::getWarehouseId, v -> v));

        String msgFormat = """
                智能巡检发现以下裁片仓库有裁片超期存放，请及时处理：
                                
                """;
        StringBuilder completeMessage = new StringBuilder(msgFormat);
        for (Map.Entry<Long, MutablePair<Integer, Integer>> entry : countMap.entrySet()) {
            Long warehouseId = entry.getKey();
            MutablePair<Integer, Integer> pair = entry.getValue();
            PartStationBinDTO partStationBinDTO = warehouseMap.get(warehouseId);
            if (partStationBinDTO == null) {
                continue;
            }

            String format = """
                    驿站仓库编号：%s
                    驿站仓库名称：%s
                    超过建议存放天数： %s 扎
                    超过最大存放天数： %s 扎
                    """;
            String message = String.format(format,
                    partStationBinDTO.getWarehouseCode(),
                    partStationBinDTO.getWarehouseName(),
                    pair.getKey(),
                    pair.getValue());

            completeMessage.append(message).append("\n");


        }
        completeMessage.append("建议存放天数 ")
                .append(minDay)
                .append(" 天，最大存放天数 ")
                .append(maxDay)
                .append(" 天。").append("\n");
        completeMessage.append("系统提示：本消息由裁片驿站助手自动发送，无需回复。如需调整超期提醒规则，请在驿站设置中进行配置。");

        //发送
        List<MessageSendForm> msgs = timeConfig.getEmployeeIds().stream().map(id -> {
            MessageSendForm messageSendForm = new MessageSendForm();
            messageSendForm.setMessageType(MessageTypeEnum.MAIL.getValue());
            messageSendForm.setReceiverUserType(UserTypeEnum.ADMIN_EMPLOYEE.getValue());
            messageSendForm.setReceiverUserId(id);
            messageSendForm.setTitle("裁片驿站助手 | 智能巡检");
            messageSendForm.setContent(completeMessage.toString());
            return messageSendForm;
        }).toList();
        messageService.sendMessage(msgs);


    }
}
