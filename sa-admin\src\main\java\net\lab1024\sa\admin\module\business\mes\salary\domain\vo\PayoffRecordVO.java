package net.lab1024.sa.admin.module.business.mes.salary.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;
import net.bytebuddy.agent.builder.AgentBuilder;
import net.lab1024.sa.admin.module.business.mes.salary.domain.dto.WageFieldValueDto;

import jakarta.annotation.Resource;

/**
 * 薪酬发放记录表 列表VO
 *
 * <AUTHOR>
 * @Date 2024-12-03 21:37:41
 * @Copyright zscbdic
 */

@Data
public class PayoffRecordVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    @Schema(description = "员工id")
    private Long employeeId;

    /**
     * 员工姓名
     */
    @Schema(description = "员工姓名")
    private String actualName;

    /**
     * 归属月份
     */
    @Schema(description = "归属月份")
    private LocalDateTime belongMonth;

    /**
     * 总金额
     */
    @Schema(description = "总金额")
    private BigDecimal totalAmount;

    /**
     * 计件金额
     */
    @Schema(description = "计件金额")
    private BigDecimal pieceAmount;

    /**
     * 其他项金额
     */
    @Schema(description = "其他项金额")
    private BigDecimal otherAmount;

    /**
     * 发放时间
     */
    @Schema(description = "发放时间")
    private LocalDateTime payoffTime;

    /**
     * 其他项json字符串
     */
    @Schema(description = "其他项金额数据")
    private String otherAmountData;

    /**
     * 其他项
     */
    private List<WageFieldValueVO> otherAmounts;

}
