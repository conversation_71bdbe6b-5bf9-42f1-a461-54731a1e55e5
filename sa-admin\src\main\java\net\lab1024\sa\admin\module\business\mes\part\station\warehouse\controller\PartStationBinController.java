package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationBinAddForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationBinQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationBinUpdateForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.vo.PartStationBinVO;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.vo.PartStationInventoryVO;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.service.PartStationBinService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 裁片货位 Controller
 *
 * <AUTHOR>
 * @Date 2024-10-06 20:17:14
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class PartStationBinController {

    @Resource
    private PartStationBinService partStationBinService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/partStationBin/queryPage")
    public ResponseDTO<PageResult<PartStationBinVO>> queryPage(@RequestBody @Valid PartStationBinQueryForm queryForm) {
        return ResponseDTO.ok(partStationBinService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/partStationBin/add")
    public ResponseDTO<String> add(@RequestBody @Valid PartStationBinAddForm addForm) {
        return partStationBinService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/partStationBin/update")
    public ResponseDTO<String> update(@RequestBody @Valid PartStationBinUpdateForm updateForm) {
        return partStationBinService.update(updateForm);
    }


    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/partStationBin/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return partStationBinService.delete(id);
    }

    /**
     * 获取货位二维码base64
     * @param id
     * @return
     */
    @Operation(summary = "获取货位二维码base64 <AUTHOR>
    @GetMapping("/partStationBin/getQrCode/{id}")
    public ResponseDTO<String> getQrCode(@PathVariable("id") Long id) {
        return partStationBinService.getQrCode(id);
    }

    /**
     * 根据id查询 <AUTHOR>
     * @param id
     * @return
     */
    @Operation(summary = "根据id查询 <AUTHOR>
    @GetMapping("/partStationBin/getById/{id}")
    public ResponseDTO<PartStationBinVO> getById(@PathVariable("id") Long id) {
        return partStationBinService.getById(id);
    }

    /**
     * 获取货位下菲票 <AUTHOR>
     * @param id
     * @return
     */
    @Operation(summary = "获取货位下菲票 <AUTHOR>
    @GetMapping("/partStationBin/getFeTicketList/{id}")
    public ResponseDTO<List<PartStationInventoryVO>> getFeTicketList(@PathVariable("id") Long id) {
        return partStationBinService.getFeTicketList(id);
    }

    /**
     * 获取所属仓库下货位数量
     * @param warehouseId 仓库Id(可选)
     * @return 货位数
     */
    @Operation(summary = "获取所属仓库下货位数量 <AUTHOR>
    @GetMapping("/partStationBin/getBinNum")
    public ResponseDTO<Long> getBinNum(@RequestParam(name = "warehouseId",required = false) Long warehouseId) {
        return partStationBinService.getBinNum(warehouseId);
    }


    /**
     * 获取货位二维码内容
     * @param ids
     * @return
     */
    @Operation(summary = "货位二维码内容 <AUTHOR>
    @PostMapping("/partStationBin/getQrCodeContent")
    public ResponseDTO<List<String>> getQrCodeContent(@RequestBody ValidateList<Long> ids) {
        return partStationBinService.getQrCodeContent(ids);
    }
}
