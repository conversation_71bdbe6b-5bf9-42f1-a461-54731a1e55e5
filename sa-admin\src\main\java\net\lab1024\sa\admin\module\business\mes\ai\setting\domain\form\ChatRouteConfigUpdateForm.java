package net.lab1024.sa.admin.module.business.mes.ai.setting.domain.form;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class ChatRouteConfigUpdateForm {

    private Long configId;

    /**
     * 提示
     */
    private String prompt;

    /**
     * 决策模型id
     */
    private Long modelId;

    /**
     * 是否启用
     */
    @NotNull(message = "启用不能为空")
    private Boolean enableFlag;

    /**
     * 配置
     */
    @Valid
    private List<Item> items;

    @Data
    public static class Item {

        /**
         * 值
         */
        @NotBlank(message = "value不能为空")
        private String value;

        /**
         * 选择的模型id
         */
        @NotNull(message = "modelId不能为空")
        private Long modelId;
    }
}
