package net.lab1024.sa.admin.module.business.mes.part.match.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.part.match.domain.entity.PartMatchEntity;
import net.lab1024.sa.admin.module.business.mes.part.match.dao.PartMatchDao;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 裁片配扎情况  Manager
 *
 * <AUTHOR>
 * @Date 2024-11-07 15:17:40
 * @Copyright zscbdic
 */
@Service
public class PartMatchManager extends ServiceImpl<PartMatchDao, PartMatchEntity> {

    @Resource
    private PartMatchDao partMatchDao;

    /**
     * 批量删除并插入数据
     * @param sameTieTicketIds
     * @param updateList
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchData(List<Long> sameTieTicketIds,List<PartMatchEntity> updateList) {
        partMatchDao.delete(new LambdaQueryWrapper<PartMatchEntity>()
                .in(PartMatchEntity::getTicketId,sameTieTicketIds));
        partMatchDao.saveBatch(updateList);
    }

    /**
     * 根据菲票id去判断是否已经配包
     * @param id
     * @return
     */
    public boolean isMatch(Long id) {
        PartMatchEntity p =  partMatchDao.selectOne(new LambdaQueryWrapper<PartMatchEntity>()
                .eq(PartMatchEntity::getTicketId,id));
        return p != null;
    }

}
