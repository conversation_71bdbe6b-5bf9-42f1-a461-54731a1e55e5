package net.lab1024.sa.admin.module.business.mes.part.dispatch.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

@Getter
@AllArgsConstructor
public enum DispatchRangeEnum implements BaseEnum {

    /**
     * 厂内
     */
    INSIDE("1","厂内"),

    /**
     * 厂外
     */
    OUTSIDE("2","厂外");


    private String value;

    private String desc;
}
