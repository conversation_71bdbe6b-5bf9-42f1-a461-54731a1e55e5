package net.lab1024.sa.admin.event.produce;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 生产指令单完成事件
 */
@Getter
public class ProduceInstructOrderFinishEvent extends ApplicationEvent {

    /**
     * 生产指令单id
     */
    private Long id;

    public ProduceInstructOrderFinishEvent(final Object source , Long id) {
        super(source);
        this.id = id;
    }
}
