package net.lab1024.sa.admin.module.business.mes.stock.out.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.lab1024.sa.admin.module.business.mes.common.bill.constant.BillType;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockBillStatusEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 生产退库单
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StkProdOutStockAddForm extends StkOutStockAddForm {

    /**
     * 单据来源类型
     */
    @Schema(description = "单据来源类型")
    private String originType;

    /**
     * 单据来源ID
     */
    @Schema(description = "单据来源ID")
    private Long originId;

    /**
     * 单据来源编号
     */
    @Schema(description = "单据来源编号")
    private String originNumber;

    /**
     * 单据类型
     */
    @Schema(description = "单据类型", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "单据类型 不能为空")
//    @CheckEnum(value = BillType.class,required = true,message = "单据类型错误")
    private String type;

    /**
     * 单据方式
     */
    @Schema(description = "单据方式")
    private String way;

    /**
     * 单据状态
     */
    @Schema(description = "单据状态", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "单据状态; 不能为空")
    @CheckEnum(value = StockBillStatusEnum.class, required = true, message = "单据状态错误")
    private String status;

    @Valid
    @NotEmpty(message = "入库单详情 不能为空")
    private List<DetailAddForm> details;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DetailAddForm extends StkOutStockDetailAddForm {

        @Schema(description = "单据来源详情类型")
        @NotBlank(message = "单据来源详情类型 不能为空")
        @CheckEnum(value = BillType.class, required = true, message = "单据来源详情类型错误")
        private String originDetailType;

        @Schema(description = "单据来源详情ID")
        @NotNull(message = "单据来源详情ID 不能为空")
        private Long originDetailId;

        @Schema(description = "单据来源详情行号")
        private Integer originDetailSeq;


        /**
         * 来源单类型
         */
        @NotBlank(message = "来源订单类型 不能为空")
        @Schema(description = "来源订单类型")
        @CheckEnum(value = BillType.class, message = "来源订单类型 值非法",required = true)
        private String originOrderBillType;

        /**
         * 来源单ID
         */
        @NotNull(message = "来源订单ID 不能为空")
        @Schema(description = "来源订单ID")
        private Long originOrderBillId;

        /**
         * 来源单号
         */
        @NotBlank(message = "来源订单编号 不能为空")
        @Schema(description = "来源订单编号")
        private String originOrderBillNumber;


    }
}
