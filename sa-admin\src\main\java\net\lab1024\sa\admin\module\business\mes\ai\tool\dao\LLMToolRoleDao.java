package net.lab1024.sa.admin.module.business.mes.ai.tool.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.entity.LLMToolEntity;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.entity.LLMToolRoleEntity;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.form.LLMToolRoleQueryForm;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.vo.LLMToolRoleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 大模型工具角色权限表 Dao
 *
 * <AUTHOR>
 * @Date 2025-04-06 15:15:25
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface LLMToolRoleDao extends BaseMapper<LLMToolRoleEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<LLMToolRoleVO> queryPage(Page page, @Param("queryForm") LLMToolRoleQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);



    /**
     * 根据角色ID列表查询工具列表
     * @param roleIdList
     * @return
     */
    List<LLMToolEntity> selectToolListByRoleIdList(@Param("enableFlag") boolean enableFlag,@Param("roleIdList") List<Long> roleIdList,@Param("deletedFlag") boolean deletedFlag);
}
