package net.lab1024.sa.admin.module.business.mes.process.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.lab1024.sa.admin.module.business.mes.process.domain.entity.ProcessLibraryEntity;
import net.lab1024.sa.admin.module.business.mes.process.dao.ProcessLibraryDao;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessLibraryAddForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessLibraryUpdateForm;
import net.lab1024.sa.base.common.exception.BusinessException;

/**
 * 工序库  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-13 15:19:57
 * @Copyright zscbdic
 */
@Service
public class ProcessLibraryManager extends ServiceImpl<ProcessLibraryDao, ProcessLibraryEntity> {


    /**
     * 新增校验
     * @param addForm
     */
    public void addCheck(ProcessLibraryAddForm addForm) {
        String processLibraryNumber = addForm.getProcessLibraryNumber();

        long count = this.count(new LambdaQueryWrapper<ProcessLibraryEntity>()
                .eq(ProcessLibraryEntity::getProcessLibraryNumber, processLibraryNumber));
        if (count > 0) {
            throw new BusinessException("工艺库编号已存在");
        }
    }

    /**
     * 更新校验
     * @param updateForm
     */
    public void updateCheck(ProcessLibraryUpdateForm updateForm) {
        String processLibraryNumber = updateForm.getProcessLibraryNumber();

        long count = this.count(new LambdaQueryWrapper<ProcessLibraryEntity>()
                .eq(ProcessLibraryEntity::getProcessLibraryNumber, processLibraryNumber)
                .ne(ProcessLibraryEntity::getId, updateForm.getId()));
        if (count > 0) {
            throw new BusinessException("工艺库编号已存在");
        }
    }
}
