package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.bo;

import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.*;

import java.util.List;

@Data
public class ProduceInstructOrderBo {

    /**
     * 指令单信息
     */
    private ProduceInstructOrderEntity order;

    /**
     * 指令单物料信息
     */
    private List<ProduceInstructOrderItemEntity> itemList;

    /**
     * 指令单工序信息
     */
    private List<ProduceInstructOrderProcessEntity> processList;

    /**
     * 指令单成衣信息
     */
    private List<ProduceInstructOrderClothesEntity> clothesList;

    /**
     * 指令单安排信息
     */
    private List<ProduceInstructOrderArrangeEntity> arrangeList;
}
