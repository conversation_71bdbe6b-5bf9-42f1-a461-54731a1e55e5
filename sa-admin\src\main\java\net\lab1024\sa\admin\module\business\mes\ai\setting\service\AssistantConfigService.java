package net.lab1024.sa.admin.module.business.mes.ai.setting.service;

import cn.hutool.core.text.CharSequenceUtil;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.ai.core.config.AssistantConfig;
import net.lab1024.sa.admin.module.business.mes.ai.setting.domain.form.AssistantConfigUpadteForm;
import net.lab1024.sa.admin.module.business.mes.ai.setting.domain.vo.AssistantConfigVO;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import org.springframework.stereotype.Service;

@Service
public class AssistantConfigService {

    @Resource
    private AssistantConfig assistantConfig;

    public AssistantConfigVO getConfig() {
        AssistantConfig.Config config = assistantConfig.getConfig();
        return SmartBeanUtil.copy(config, AssistantConfigVO.class);
    }

    public void updateConfig(AssistantConfigUpadteForm form) {
        Boolean userRecommendPromptFlag = form.getUserRecommendPromptFlag();
        if(Boolean.TRUE.equals(userRecommendPromptFlag)){
            if(form.getUserRecommendPromptModelId() == null){
                throw new BusinessException("请选择推荐提示语模型");
            }
            if(CharSequenceUtil.isBlank(form.getUserRecommendSystemPrompt())){
                throw new BusinessException("请输入推荐提示语");
            }
        }

        AssistantConfig.Config config =  SmartBeanUtil.copy(form, AssistantConfig.Config.class);
        assistantConfig.updateConfig(config);
    }
}
