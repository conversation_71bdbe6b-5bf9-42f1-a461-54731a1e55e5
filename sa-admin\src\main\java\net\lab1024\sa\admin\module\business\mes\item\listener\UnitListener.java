package net.lab1024.sa.admin.module.business.mes.item.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.event.base.UnitDeleteCheckEvent;
import net.lab1024.sa.admin.module.business.mes.item.dao.ItemDao;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemEntity;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;

@Component("itemUnitListener")
public class UnitListener {

    @Resource
    private ItemDao itemDao;

    @EventListener
    public void unitDeleteCheck(UnitDeleteCheckEvent event){
        Long id = event.getId();
        Long count = itemDao.selectCount(new LambdaQueryWrapper<ItemEntity>()
                .eq(ItemEntity::getUnitId, id));
        if (count > 0) {
            throw new BusinessException("该单位正在被物料使用中，无法删除");
        }

    }
}
