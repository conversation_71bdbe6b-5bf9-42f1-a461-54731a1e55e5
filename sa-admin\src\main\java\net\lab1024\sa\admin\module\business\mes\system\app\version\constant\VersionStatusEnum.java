package net.lab1024.sa.admin.module.business.mes.system.app.version.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 状态枚举
 */
@Getter
@AllArgsConstructor
public enum VersionStatusEnum implements BaseEnum {

    PUBLISHED("published", "已发布"),

    UN_PUBLISH("un_publish", "未发布");


    private String value;

    private String desc;

}
