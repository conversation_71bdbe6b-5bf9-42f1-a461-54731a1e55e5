package net.lab1024.sa.admin.module.business.mes.stock.in.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.lab1024.sa.admin.module.business.mes.common.bill.constant.BillType;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockBillStatusEnum;
import net.lab1024.sa.admin.module.business.mes.stock.in.constant.InTypeConstant;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class StkProdReturnMtrlUpdateForm extends StkInStockUpdateForm {

    @Schema(description = "单据状态;", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "单据状态; 不能为空")
    @CheckEnum(value = StockBillStatusEnum.class, message = "单据状态; 值非法")
    private String status;

    @Valid
    @NotEmpty(message = "入库单详情 不能为空")
    private List<DetailUpdateForm> details;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DetailUpdateForm extends StkInStockDetailUpdateForm {

        @NotBlank(message = "单据来源详情类型 不能为空")
        @CheckEnum(value = BillType.class, message = "单据来源详情类型 值非法",required = true)
        @Schema(description = "单据来源详情类型")
        private String originDetailType;

        @NotNull(message = "单据来源详情ID 不能为空")
        @Schema(description = "单据来源详情ID")
        private Long originDetailId;

        @Schema(description = "单据来源详情行号")
        private Integer originDetailSeq;

        /**
         * 来源单类型
         */
        @NotBlank(message = "来源订单类型 不能为空")
        @Schema(description = "来源订单类型")
        @CheckEnum(value = BillType.class, message = "来源订单类型 值非法",required = true)
        private String originOrderBillType;

        /**
         * 来源单ID
         */
        @NotNull(message = "来源订单ID 不能为空")
        @Schema(description = "来源订单ID")
        private Long originOrderBillId;

        /**
         * 来源单号
         */
        @NotBlank(message = "来源订单编号 不能为空")
        @Schema(description = "来源订单编号")
        private String originOrderBillNumber;

        /**
         * 入库类型
         */
        @NotBlank(message = "入库类型 不能为空")
        @Schema(description = "入库类型")
        @CheckEnum(value = InTypeConstant.ProduceReturnMaterial.class, message = "入库类型 非法",required = true)
        private String inType;
    }
}
