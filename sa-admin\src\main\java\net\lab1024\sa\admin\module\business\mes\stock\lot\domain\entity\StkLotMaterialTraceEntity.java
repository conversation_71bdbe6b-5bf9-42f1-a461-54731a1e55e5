package net.lab1024.sa.admin.module.business.mes.stock.lot.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 批号跟踪信息 实体类
 *
 * <AUTHOR>
 * @Date 2025-02-08 19:57:37
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_stk_lot_material_trace")
public class StkLotMaterialTraceEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 批号ID
     */
    private Long lotMasterId;

    /**
     * 跟踪方向;  come来源 to去向
     */
    private String stockDirect;

    /**
     * 单据类型
     */
    private String billType;

    /**
     * 单据ID
     */
    private Long billId;

    /**
     * 单据编号
     */
    private String billNumber;

    /**
     * 单据时间
     */
    private LocalDateTime billTime;

    /**
     * 单据详情行号
     */
    private Integer billDetailSeq;

    /**
     * 单据详情ID
     */
    private Long billDetailId;

    /**
     * 数量
     */
    private BigDecimal qty;

    /**
     * 单位ID
     */
    private Long unitId;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 货主类型;保留
     */
    private String ownerType;

    /**
     * 货主id;保留
     */
    private Long ownerId;

    /**
     * 货主名称;保留
     */
    private String ownerName;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库编号
     */
    private String warehouseNumber;

    /**
     * 库位ID
     */
    private Long locationId;

    /**
     * 库位编号
     */
    private String locationNumber;

}
