package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.item.constant.ItemAttributeEnum;
import net.lab1024.sa.admin.module.business.mes.item.constant.ItemCategoryEnum;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.constant.ProduceInstructOrderOriginEnum;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.constant.ProduceInstructOrderPrioityEnum;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.constant.ProduceInstructOrderProduceTypeEnum;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.json.deserializer.FileKeyVoDeserializer;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 生产指令单 新建表单
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:22:24
 * @Copyright zscbdic
 */

@Data
public class ProduceInstructOrderAddForm {
    /**
     * 单据编号
     */
    @Schema(description = "单据编号", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "单据编号 不能为空")
    private String instructNumber;

    /**
     * 生产类型;0自产,1自裁委外，2整件委外
     */
    @Schema(description = "生产类型;0自产,1自裁委外，2整件委外", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "生产类型;0自产,1自裁委外，2整件委外 不能为空")
//    @CheckEnum(value = ProduceInstructOrderProduceTypeEnum.class, required = true, message = "生产类型错误")
    private String produceType;

    /**
     * 客户id
     */
    @Schema(description = "客户id")
    private Long customerId;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String customerName;

    /**
     * 销售单号
     */
    @Schema(description = "销售单号")
    private String salesOrderNumber;

    /**
     * 计划开工日期 yyyy-MM-dd
     */
    @Schema(description = "计划开工日期")
    private LocalDate planStartTime;

    /**
     * 计划完工日期 yyyy-MM-dd
     */
    @Schema(description = "计划完工日期")
    @NotNull(message = "计划完工日期 不能为空")
    private LocalDate planFinishTime;

//    @Schema(description = "实际开工日期")
//    private LocalDateTime realStartTime;
//
//    @Schema(description = "实际完工日期")
//    private LocalDateTime realFinishTime;

    /**
     * 交货日期 yyyy-MM-dd
     */
    @Schema(description = "交货日期")
    @NotNull(message = "交货日期 不能为空")
    private LocalDate deliverTime;

//    @Schema(description = "下达日期")
//    private LocalDateTime issuedTime;

    /**
     * 生产业务状态;0计划，1下达，2开工，3完工
     */
    @Schema(description = "生产业务状态;0计划，1下达，2开工，3完工", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "生产业务状态;0计划，1下达，2开工，3完工 不能为空")
    private String produceStatus;

    /**
     * 优先级;0一般,1紧急,2非常紧急
     */
    @Schema(description = "优先级;0一般,1紧急,2非常紧急", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "优先级;0一般,1紧急,2非常紧急 不能为空")
    @CheckEnum(value = ProduceInstructOrderPrioityEnum.class, required = true, message = "优先级错误")
    private String priority;

    /**
     * 单据来源;0直接下达
     */
    @Schema(description = "单据来源;0直接下达")
    @NotBlank(message = "单据来源;0直接下达 不能为空")
    @CheckEnum(value = ProduceInstructOrderOriginEnum.class, required = true, message = "单据来源错误")
    private String orderOrigin;

    /**
     * 物料id
     */
    @Schema(description = "物料id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物料id 不能为空")
    private Long itemId;

    /**
     * 物料编号
     */
    @Schema(description = "物料编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "物料编号 不能为空")
    private String itemNumber;

    /**
     * 规格型号
     */
    @Schema(description = "规格型号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String model;

    /**
     * 物料名称
     */
    @Schema(description = "物料名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "物料名称 不能为空")
    private String itemName;

    /**
     * 类型;0半成品 1成品
     */
    @Schema(description = "类型;0半成品 1成品", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "类型;0半成品 1成品 不能为空")
    @CheckEnum(value = ItemCategoryEnum.class, message = "类型;0半成品 1成品 值不合法",required = true)
    private String category;

    /**
     * 属性;0面料，1其他，2成衣
     */
    @Schema(description = "属性;0面料，1辅料", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "属性;0面料，1辅料 不能为空")
    @CheckEnum(value = ItemAttributeEnum.class, message = "属性;0面料，1其他，2成衣 值不合法",required = true)
    private String attribute;

    /**
     * 单位id
     */
    @Schema(description = "单位id")
    @NotNull(message = "单位id 不能为空")
    private Long unitId;

    /**
     * 单位名称
     */
    @Schema(description = "单位名称")
    @NotBlank(message = "单位名称 不能为空")
    private String unitName;

    /**
     * 指令单名称
     */
    @Schema(description = "指令单名称", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "指令单名称 不能为空")
    private String name;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 图片地址
     */
    @JsonDeserialize(using = FileKeyVoDeserializer.class)
    @Schema(description = "图片地址")
//    @NotNull(message = "图片地址 不能为空")
    private String imgUrl;


    /**
     * 指令单物料明细
     */
//    @Valid
    @Schema(description = "指令单物料明细")
    private ValidateList<ProduceInstructOrderItemAddForm> itemList;

    /**
     * 指令单工序明细
     */
//    @Valid
    @Schema(description = "指令单工序明细")
    private ValidateList<ProduceInstructOrderProcessAddForm> processList;

    /**
     * 指令单成衣明细
     */
//    @Valid
    @Schema(description = "指令单成衣明细")
    private ValidateList<ProduceInstructOrderClothesAddForm> clothesList;

    /**
     * 指令单安排明细
     */
//    @Valid
    @Schema(description = "指令单安排明细")
    private ValidateList<ProduceInstructOrderArrangeAddForm> arrangeList;

}
