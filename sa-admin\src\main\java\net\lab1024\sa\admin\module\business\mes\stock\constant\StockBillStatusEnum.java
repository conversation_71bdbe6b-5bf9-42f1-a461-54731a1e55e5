package net.lab1024.sa.admin.module.business.mes.stock.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 库存单据状态枚举
 */
@Getter
@AllArgsConstructor
public enum StockBillStatusEnum implements BaseEnum {
    /**
     * 未审核
     */
    UN_AUDIT("UN_AUDIT", "未审核"),
    /**
     * 已审核
     */
    AUDIT("AUDIT", "已审核"),

    ;

    private String value;
    private String desc;

    public static StockBillStatusEnum getByValue(String value) {
        for (StockBillStatusEnum e : values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return null;
    }
}
