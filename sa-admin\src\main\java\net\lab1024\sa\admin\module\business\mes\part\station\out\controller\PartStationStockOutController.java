package net.lab1024.sa.admin.module.business.mes.part.station.out.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.admin.module.business.mes.part.station.out.domain.form.PartStationStockOutByBoxForm;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.annotation.PartStationStoreManageMode;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.constant.StorageManageModeConfigEnum;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import net.lab1024.sa.admin.module.business.mes.part.station.out.domain.form.PartStationStockOutByTicketForm;
import net.lab1024.sa.admin.module.business.mes.part.station.out.service.PartStationStockOutService;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 裁片驿站出库 Controller
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:44:34
 * @Copyright zscbdic
 */
@RestController
@Tag(name = "")
public class PartStationStockOutController {

    @Resource
    private PartStationStockOutService partStationStockOutService;


    /**
     * 单扎裁片出库
     *
     * @param form
     * @return
     */
    @Operation(summary = "单扎裁片出库")
    @PostMapping("/partStationStockOut/stockOut")
    @PartStationStoreManageMode(mode = StorageManageModeConfigEnum.FE_TICKET_MANAGE_MODE)
    public ResponseDTO<String> stockOut(@RequestBody @Valid PartStationStockOutByTicketForm form) {
        return partStationStockOutService.stockOut(form);
    }

    /**
     * 裁片出库（周转箱）
     * @param form
     * @return
     */
    @Operation(summary = "裁片出库（周转箱）")
    @PostMapping("/partStationStockOut/stockOutByBox")
    @PartStationStoreManageMode(mode = StorageManageModeConfigEnum.TURN_BOX_MANAGE_MODE)
    public ResponseDTO<String> stockOutByBox(@RequestBody @Valid PartStationStockOutByBoxForm form) {
        return partStationStockOutService.stockOutByBox(form);
    }

}
