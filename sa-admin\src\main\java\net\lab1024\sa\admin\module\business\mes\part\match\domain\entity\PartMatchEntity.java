package net.lab1024.sa.admin.module.business.mes.part.match.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 裁片配扎情况 实体类
 *
 * <AUTHOR>
 * @Date 2024-11-07 15:17:40
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_part_match")
public class PartMatchEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 备注
     */
    private String remark;

    /**
     * 菲票id
     */
    private Long ticketId;

}
