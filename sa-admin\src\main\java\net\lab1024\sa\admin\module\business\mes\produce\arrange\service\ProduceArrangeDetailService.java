package net.lab1024.sa.admin.module.business.mes.produce.arrange.service;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.dao.ProduceArrangeDetailDao;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.entity.ProduceArrangeDetailEntity;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.form.ProduceArrangeDetailAddForm;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.form.ProduceArrangeDetailQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.form.ProduceArrangeDetailUpdateForm;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.vo.ProduceArrangeDetailVO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 生产安排信息 Service
 *
 * <AUTHOR>
 * @Date 2024-07-22 18:44:38
 * @Copyright zscbdic
 */

@Service
public class ProduceArrangeDetailService {

    @Resource
    private ProduceArrangeDetailDao produceArrangeDetailDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<ProduceArrangeDetailVO> queryPage(ProduceArrangeDetailQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ProduceArrangeDetailVO> list = produceArrangeDetailDao.queryPage(page, queryForm);
        PageResult<ProduceArrangeDetailVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(ProduceArrangeDetailAddForm addForm) {
        ProduceArrangeDetailEntity produceArrangeDetailEntity = SmartBeanUtil.copy(addForm, ProduceArrangeDetailEntity.class);
        produceArrangeDetailDao.insert(produceArrangeDetailEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(ProduceArrangeDetailUpdateForm updateForm) {
        ProduceArrangeDetailEntity produceArrangeDetailEntity = SmartBeanUtil.copy(updateForm, ProduceArrangeDetailEntity.class);
        produceArrangeDetailDao.updateById(produceArrangeDetailEntity);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id){
            return ResponseDTO.ok();
        }

        produceArrangeDetailDao.updateDeleted(id,true);
        return ResponseDTO.ok();
    }
}
