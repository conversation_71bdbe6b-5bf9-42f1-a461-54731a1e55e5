package net.lab1024.sa.admin.module.business.mes.base.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 单位表 新建表单
 *
 * <AUTHOR>
 * @Date 2024-07-04 11:12:45
 * @Copyright zscbdic
 */

@Data
public class UnitAddForm {

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "单位编码")
    private String unitCode;

    @Schema(description = "单位名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "单位名称 不能为空")
    private String name;

}
