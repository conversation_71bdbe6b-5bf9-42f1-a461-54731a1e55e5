package net.lab1024.sa.admin.module.business.mes.stock.out.controller;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.common.bill.constant.BillType;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockBillStatusEnum;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.form.StkOtherOutStockAddForm;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.form.StkOtherOutStockUpdateForm;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.form.StkOutStockQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.vo.StkOtherOutStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.vo.StkOutStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.out.service.StkOtherOutStockService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 其他出库单 Controller
 *
 * <AUTHOR>
 * @Date 2025-02-03 15:55:23
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class StkOtherOutStockController {

    @Resource
    private StkOtherOutStockService stkOtherOutStockService;

    @Resource
    private SerialNumberService serialNumberService;


    /**
     * 分页查询
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/stkOtherOutStock/queryPage")
    public ResponseDTO<PageResult<StkOutStockVO>> queryPage(@RequestBody StkOutStockQueryForm queryForm) {
        queryForm.setType(BillType.STOCK_OTHER_OUT.getValue());
        return stkOtherOutStockService.queryPage(queryForm);
    }


    /**
     * 添加
     *
     * @param form
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/stkOtherOutStock/add")
    public ResponseDTO<String> otherInStock(@RequestBody @Valid StkOtherOutStockAddForm form) {
        form.setType(BillType.STOCK_OTHER_OUT.getValue());
        if (StrUtil.isBlank(form.getStatus())) {
            form.setStatus(StockBillStatusEnum.UN_AUDIT.getValue());
        }
        if (StrUtil.isBlank(form.getNumber())) {
            form.setNumber(serialNumberService.generate(SerialNumberIdEnum.STOCK_OUT_OTHER));
        }
        return stkOtherOutStockService.add(form);
    }

    /**
     * 修改其他出库单
     *
     * @param form
     * @return
     */
    @Operation(summary = "修改其他出库单 <AUTHOR>
    @PostMapping("/stkOtherOutStock/update")
    public ResponseDTO<String> update(@RequestBody @Valid StkOtherOutStockUpdateForm form) {
        return stkOtherOutStockService.update(form);
    }

    /**
     * 根据id查询其他入库单
     *
     * @param id
     * @return
     */
    @Operation(summary = "根据id查询其他出库单 <AUTHOR>
    @GetMapping("stkOtherOutStock/byId")
    public ResponseDTO<StkOtherOutStockVO> queryById(@RequestParam("id") Long id) {
        return stkOtherOutStockService.queryById(id);
    }


    /**
     * 修改单据状态
     *
     * @param id
     * @return
     */
    @Operation(summary = "修改单据状态 <AUTHOR>
    @GetMapping("stkOtherOutStock/status")
    public ResponseDTO<String> updateStatus(@RequestParam("id") Long id) {
        return stkOtherOutStockService.updateStatus(id);
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @Operation(summary = "删除 <AUTHOR>
    @GetMapping("stkOtherOutStock/delete")
    public ResponseDTO<String> delete(@RequestParam("id") Long id) {
        return stkOtherOutStockService.delete(id);
    }
}
