package net.lab1024.sa.admin.module.business.mes.base.manager;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.lab1024.sa.admin.module.business.mes.base.dao.UnitDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.UnitEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.UnitAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.UnitUpdateForm;
import net.lab1024.sa.base.common.exception.BusinessException;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * 单位表  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-04 11:12:45
 * @Copyright zscbdic
 */
@Service
public class UnitManager extends ServiceImpl<UnitDao, UnitEntity> {
    /**
     * @return 以单位id为键，名称为值的Map
     */
    public Map<Long, String> processMap() {
        return query().list()
                .stream()
                .collect(Collectors.toMap(UnitEntity::getId, UnitEntity::getName));
    }

    public void addCheck(UnitAddForm addForm) {
        String unitCode = addForm.getUnitCode();
        Long count = this.lambdaQuery().eq(UnitEntity::getUnitCode, unitCode).count();
        if (count > 0) {
            throw new BusinessException("单位编码已存在");
        }
        String name = addForm.getName();
        count = this.lambdaQuery().eq(UnitEntity::getName, name).count();
        if (count > 0) {
            throw new BusinessException("单位名称已存在");
        }
    }

    public void updateCheck(UnitUpdateForm updateForm) {
        String unitCode = updateForm.getUnitCode();
        Long count = this.lambdaQuery().eq(UnitEntity::getUnitCode, unitCode).ne(UnitEntity::getId, updateForm.getId()).count();
        if (count > 0) {
            throw new BusinessException("单位编码已存在");
        }
        String name = updateForm.getName();
        count = this.lambdaQuery().eq(UnitEntity::getName, name).ne(UnitEntity::getId, updateForm.getId()).count();
        if (count > 0) {
            throw new BusinessException("单位名称已存在");
        }
    }
}
