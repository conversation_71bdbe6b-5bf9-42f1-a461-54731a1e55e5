package net.lab1024.sa.admin.module.business.mes.produce.instruct.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.event.work.WorkReportEvent;

/**
 * 报工事件监听
 * <AUTHOR>
 */
@Slf4j
@Component("produceWorkReportListener")
public class WorkReportListener {

//    @Async(AsyncConfig.ASYNC_EXECUTOR_THREAD_NAME)
    @EventListener
    public void workReport(WorkReportEvent workReportEvent) {


    }
}
