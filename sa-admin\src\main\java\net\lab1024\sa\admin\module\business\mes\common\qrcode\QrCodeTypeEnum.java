package net.lab1024.sa.admin.module.business.mes.common.qrcode;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 二维码类型
 */
@Getter
@AllArgsConstructor
public enum QrCodeTypeEnum implements BaseEnum {

    /**
     * 菲票二维码
     */
    FE_TICKET("feTicket", "菲票二维码", "feTicket"),

    /**
     * 裁片驿站货位二维码
     */
    PART_STATION_BIN("partStationBin", "裁片驿站货位二维码", "partStationBin"),

    /**
     * 裁片驿站周转箱二维码
     */
    PART_STATION_TURN_BOX("partStationTurnBox", "裁片驿站周转箱二维码", "partStationTurnBox"),

    /**
     * app扫码登录
     */
    APP_SCAN_LOGIN("appScanLogin", "app扫码登录", "appScanLogin"),

    /**
     * 物料二维码
     */
    MATERIAL("material", "物料二维码", "material"),

    /**
     * 松布架二维码
     */
    FRAME("frame", "松布架二维码", "frame"),

    /**
     * 松布架卡二维码
     */
    SONG_CARD("songCard", "松布架卡二维码", "songCard"),

    ;

    private final String value;

    private final String desc;

    private final String prefix;
}
