package net.lab1024.sa.admin.module.business.mes.ai.tool.service;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.ai.tool.dao.LLMToolRoleDao;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.entity.LLMToolEntity;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.entity.LLMToolRoleEntity;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.form.LLMToolRoleUpdateForm;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.vo.LLMToolRoleVO;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.vo.LLMToolVO;
import net.lab1024.sa.admin.module.business.mes.ai.tool.manager.LLMToolManager;
import net.lab1024.sa.admin.module.business.mes.ai.tool.manager.LLMToolRoleManager;
import net.lab1024.sa.admin.module.system.employee.dao.EmployeeDao;
import net.lab1024.sa.admin.module.system.employee.domain.entity.EmployeeEntity;
import net.lab1024.sa.admin.module.system.login.service.LoginService;
import net.lab1024.sa.admin.module.system.role.dao.RoleDao;
import net.lab1024.sa.admin.module.system.role.domain.entity.RoleEntity;
import net.lab1024.sa.admin.module.system.role.domain.vo.RoleVO;
import net.lab1024.sa.admin.module.system.role.service.RoleEmployeeService;
import net.lab1024.sa.base.common.code.UserErrorCode;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 大模型工具角色权限表 Service
 *
 * <AUTHOR>
 * @Date 2025-04-06 15:15:25
 * @Copyright zscbdic
 */

@Service
public class LLMToolRoleService {

    @Resource
    private LLMToolRoleDao lLMToolRoleDao;

    @Resource
    private LLMToolRoleManager llmToolRoleManager;

    @Resource
    private RoleDao roleDao;

    @Resource
    private LLMToolManager llmToolManager;

    @Resource
    private RoleEmployeeService roleEmployeeService;

    @Resource
    private EmployeeDao employeeDao;

    @Resource
    private LoginService loginService;

    /**
     * 更新角色工具权限
     *
     * @param form
     * @return
     */
    public ResponseDTO<String> updateRoleTool(LLMToolRoleUpdateForm form) {
        //查询角色是否存在
        Long roleId = form.getRoleId();
        RoleEntity roleEntity = roleDao.selectById(roleId);
        if (null == roleEntity) {
            return ResponseDTO.error(UserErrorCode.DATA_NOT_EXIST);
        }
        List<LLMToolRoleEntity> roleToolEntityList = Lists.newArrayList();
        LLMToolRoleEntity toolRoleEntity;
        for (Long toolId : form.getTools()) {
            toolRoleEntity = new LLMToolRoleEntity();
            toolRoleEntity.setRoleId(roleId);
            toolRoleEntity.setLlmToolId(toolId);
            roleToolEntityList.add(toolRoleEntity);
        }
        llmToolRoleManager.updateRoleTool(form.getRoleId(), roleToolEntityList);
        return ResponseDTO.ok();
    }

    /**
     * 获取角色关联大模型工具权限
     *
     * @param roleId
     * @return
     */
    public ResponseDTO<LLMToolRoleVO> getRoleSelectedTool(Long roleId) {
        LLMToolRoleVO res = new LLMToolRoleVO();
        res.setRoleId(roleId);
        //查询角色ID选择的大模型工具权限
        List<Long> selectedToolIds = llmToolRoleManager.lambdaQuery()
                .eq(LLMToolRoleEntity::getRoleId, roleId)
                .list()
                .stream()
                .map(LLMToolRoleEntity::getLlmToolId)
                .toList();
        res.setSelectedToolIds(selectedToolIds);
        //查询菜单权限
        List<LLMToolEntity> toolEntityList = llmToolManager.lambdaQuery().eq(LLMToolEntity::getEnableFlag, Boolean.TRUE).list();
        List<LLMToolVO> llmToolVOS = SmartBeanUtil.copyList(toolEntityList, LLMToolVO.class);

        res.setToolList(llmToolVOS);
        return ResponseDTO.ok(res);
    }

    public Object[] getToolList(Long userId) {
        EmployeeEntity employeeEntity = employeeDao.selectById(userId);
        if(employeeEntity == null){
            throw new BusinessException("用户不存在");
        }
        if(Boolean.TRUE.equals(employeeEntity.getAdministratorFlag())){
            return llmToolRoleManager.getToolList(Collections.emptyList(), true);
        }

        List<RoleVO> roleList = roleEmployeeService.getRoleIdList(userId);
        if(CollUtil.isEmpty(roleList)){
            return Collections.emptyList().toArray();
        }
        List<Long> roleIds = roleList.stream().map(RoleVO::getRoleId).toList();
        return llmToolRoleManager.getToolList(roleIds, employeeEntity.getAdministratorFlag());
    }


}
