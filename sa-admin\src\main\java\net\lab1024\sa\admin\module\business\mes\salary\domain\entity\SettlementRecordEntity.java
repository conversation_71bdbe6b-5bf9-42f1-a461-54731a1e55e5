package net.lab1024.sa.admin.module.business.mes.salary.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 薪酬结算记录 实体类
 *
 * <AUTHOR>
 * @Date 2024-11-20 20:48:59
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_salary_settlement_record")
@AllArgsConstructor
@NoArgsConstructor
public class SettlementRecordEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 员工id
     */
    private Long employeeId;

    /**
     * 员工姓名
     */
    private String actualName;

    /**
     * 归属月份
     */
    private Date belongMonth;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 总件数
     */
    private Integer totalNum;

    /**
     * 总次数
     */
    private Integer totalCount;

    /**
     * 结算方式
     */
    private String settlementWay;

    /**
     * 结算时间
     */
    private LocalDateTime settlementTime;

    /**
     * 是否发放
     */
    private Boolean payoffFlag;

    public SettlementRecordEntity(Long employeeId,String actualName,
                                  Date belongMonth,
                                  String settlementWay,
                                  Integer totalNum, Integer totalCount,
                                  BigDecimal totalAmount) {
        this.employeeId = employeeId;
        this.actualName = actualName;
        this.belongMonth = belongMonth;
        this.settlementWay = settlementWay;
        this.totalNum = totalNum;
        this.totalCount = totalCount;
        this.totalAmount = totalAmount;
        this.settlementTime = LocalDateTime.now();
    }

}
