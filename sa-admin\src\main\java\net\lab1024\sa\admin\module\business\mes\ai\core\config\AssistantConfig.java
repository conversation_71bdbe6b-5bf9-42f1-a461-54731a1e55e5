package net.lab1024.sa.admin.module.business.mes.ai.core.config;

import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.ai.core.constant.LLMConfigConstant;
import net.lab1024.sa.base.module.support.config.ConfigService;
import net.lab1024.sa.base.module.support.config.domain.ConfigAddForm;
import net.lab1024.sa.base.module.support.config.domain.ConfigUpdateForm;
import net.lab1024.sa.base.module.support.config.domain.ConfigVO;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AssistantConfig {

    @Resource
    private ConfigService configService;

    private static final String DEFAULT_SYSTEM_PROMPT = "你的名字叫小裁，你是由电子科技大学中山学院大数据与智能计算实验室开发训练的自然语言大模型，你是一名服装生产MES系统的智能助手";


    private static final String DEFAULT_USER_RECOMMEND_SYSTEM_PROMPT = """
            You are an AI assistant tasked with predicting the user's next question based on the conversation history. Your goal is to generate 3 potential questions that will guide the user to continue the conversation. When generating these questions, adhere to the following rules:

            1. Use the same language as the user's last question in the conversation history.
            2. Keep each question under 20 characters in length.

            Analyze the conversation history provided to you and use it as context to generate relevant and engaging follow-up questions. Your predictions should be logical extensions of the current topic or related areas that the user might be interested in exploring further.

            Remember to maintain consistency in tone and style with the""";

    /**
     * 获取配置
     *
     * @return
     */
    public AssistantConfig.Config getConfig() {
        ConfigVO config = configService.getConfig(LLMConfigConstant.ASSISTANT);
        if (config == null) {
            AssistantConfig.Config configDTO = new AssistantConfig.Config();
            configDTO.setAgentName("智能助手");
            configDTO.setSystemPrompt(DEFAULT_SYSTEM_PROMPT);
            configDTO.setUserRecommendPromptFlag(false);
            configDTO.setUserRecommendSystemPrompt(DEFAULT_USER_RECOMMEND_SYSTEM_PROMPT);

            // 添加配置
            ConfigAddForm addForm = new ConfigAddForm();
            addForm.setConfigKey(LLMConfigConstant.ASSISTANT);
            addForm.setConfigValue(JSON.toJSONString(configDTO));
            addForm.setConfigName("智能体配置");
            configService.add(addForm);

            return configDTO;
        }
        String configValue = config.getConfigValue();
        AssistantConfig.Config configDTO = JSON.parseObject(configValue, AssistantConfig.Config.class);
        configDTO.setConfigId(config.getConfigId());
        return configDTO;
    }

    /**
     * 更新配置
     *
     * @param config
     */
    public void updateConfig(Config config) {
        String configValue = JSON.toJSONString(config);

        ConfigUpdateForm updateForm = new ConfigUpdateForm();
        updateForm.setConfigKey(LLMConfigConstant.ASSISTANT);
        updateForm.setConfigValue(configValue);
        updateForm.setRemark("LLM配置-智能体配置");
        updateForm.setConfigName("LLM配置-智能体配置");
        updateForm.setConfigId(config.getConfigId());
        configService.updateConfig(updateForm);
    }

    @Data
    public static class Config {

        private Long configId;

        /**
         * 智能体名称
         */
        private String agentName;

        /**
         * 智能体头像
         */
        private String agentAvatar;

        /**
         * 系统提示
         */
        private String systemPrompt;


        /**
         * 用户推荐提示
         */
        private Boolean userRecommendPromptFlag;

        /**
         * 用户推荐提示模型ID
         */
        private Long userRecommendPromptModelId;

        /**
         * 用户推荐系统提示词
         */
        private String userRecommendSystemPrompt;
    }
}
