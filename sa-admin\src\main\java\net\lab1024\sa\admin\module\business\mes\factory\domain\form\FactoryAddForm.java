package net.lab1024.sa.admin.module.business.mes.factory.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 工厂信息表 新建表单
 *
 * <AUTHOR>
 * @Date 2024-07-04 11:11:50
 * @Copyright zscbdic
 */

@Data
public class FactoryAddForm {

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "工厂名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "工厂名称 不能为空")
    private String name;

    @Schema(description = "地址", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "地址 不能为空")
    private String address;

    @Schema(description = "邮政编码", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "邮政编码 不能为空")
    private String postalCode;

    @Schema(description = "联系人", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "联系人 不能为空")
    private String contact;

    @Schema(description = "电话", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotBlank(message = "电话 不能为空")
    private String telephone;

}
