package net.lab1024.sa.admin.module.business.mes.base.manager;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.base.dao.SizeDao;
import net.lab1024.sa.admin.module.business.mes.base.dao.SizeTemplateDao;
import net.lab1024.sa.admin.module.business.mes.base.dao.StyleDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.SizeEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.SizeTemplateEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.StyleEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.StyleTreeVO;
import net.lab1024.sa.base.common.code.UserErrorCode;
import net.lab1024.sa.base.common.constant.StringConst;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static net.lab1024.sa.base.common.code.UserErrorCode.PARAM_ERROR;

/**
 * 款式品类表  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-05 15:45:34
 * @Copyright zscbdic
 */
@Service
public class StyleManager extends ServiceImpl<StyleDao, StyleEntity> {


    @Resource
    private StyleDao styleDao;

    @Resource
    private SizeTemplateDao sizeTemplateDao;

    @Resource
    private SizeDao sizeDao;

    public List<StyleTreeVO> queryStyleTree(Long parentId, String queryKey) {
        List<StyleEntity> allTypeEntityList = styleDao.queryByKey(queryKey, false);

        List<StyleEntity> typeEntityList = allTypeEntityList.stream().filter(e -> e.getParentId().equals(parentId)).toList();

        List<StyleTreeVO> treeList = SmartBeanUtil.copyList(typeEntityList, StyleTreeVO.class);
        treeList.forEach(e -> {
            e.setValue(e.getId());
            e.setLabel(e.getStyleName());
            e.setFullName(e.getStyleName());
            setTemplateSizeMessage(e);
        });
        this.queryAndSetSubType(treeList, allTypeEntityList);
        return treeList;
    }

    private void queryAndSetSubType(List<StyleTreeVO> treeList, List<StyleEntity> allTypeEntityList) {
        if (CollectionUtils.isEmpty(treeList)) {
            return;
        }
        List<Long> parentIdList = treeList.stream().map(StyleTreeVO::getId).toList();
        List<StyleEntity> typeEntityList = allTypeEntityList.stream().filter(e -> parentIdList.contains(e.getParentId())).toList();
        Map<Long, List<StyleEntity>> typeSubMap = typeEntityList.stream().collect(Collectors.groupingBy(StyleEntity::getParentId));
        treeList.forEach(e -> {
            List<StyleEntity> childrenEntityList = typeSubMap.getOrDefault(e.getId(), Lists.newArrayList());
            List<StyleTreeVO> childrenVOList = SmartBeanUtil.copyList(childrenEntityList, StyleTreeVO.class);
            childrenVOList.forEach(item -> {
                item.setValue(item.getId());
                item.setLabel(item.getStyleName());
                item.setFullName(e.getFullName() + StringConst.SEPARATOR_SLASH + item.getStyleName());
                setTemplateSizeMessage(item);
            });
            this.queryAndSetSubType(childrenVOList, allTypeEntityList);
            e.setChildren(childrenVOList);
        });
    }

    /**
     * 检查是否存在父目录以及是否存在当前插入元素
     * parentId = ""
     * @param styleEntity
     * @param isUpdate
     * @return
     */
    public ResponseDTO<String> checkStyleType(StyleEntity styleEntity, boolean isUpdate) {
        Long parentId = styleEntity.getParentId();
        if (parentId != null) {
            if (Objects.equals(styleEntity.getId(), parentId)) {
                return ResponseDTO.userErrorParam("父级类目与自己相同了");
            }
            if (!Objects.equals(parentId, NumberUtils.LONG_ZERO)) {
                StyleEntity parentEntity = this.getById(parentId);
                Optional<StyleEntity> optional = Optional.ofNullable(parentEntity);
                if (optional.isEmpty()) {
                    return ResponseDTO.error(UserErrorCode.DATA_NOT_EXIST, "父级分类不存在~");
                }
            }
        } else {
            parentId = NumberUtils.LONG_ZERO;
        }

        StyleEntity queryEntity = new StyleEntity();
        queryEntity.setParentId(parentId);
        queryEntity.setStyleName(styleEntity.getStyleName());
        queryEntity.setDeletedFlag(false);
        queryEntity = styleDao.selectOne(new QueryWrapper<>(queryEntity));
        StyleEntity entity = query().eq("id", styleEntity.getId()).one();
        if (queryEntity != null) {
            if (isUpdate) {
                if (!Objects.equals(queryEntity.getId(), styleEntity.getId())) {
                    return ResponseDTO.userErrorParam("同级下已存在相同类目~");
                }else {
                    //校验品类代码
                    return handleCode(styleEntity,entity);
                }
            } else {
                return ResponseDTO.userErrorParam("同级下已存在相同类目~");
            }
        }else if(!isUpdate){
            StyleEntity doubleCheck = new StyleEntity();
            doubleCheck.setStyleCode(styleEntity.getStyleCode());
            doubleCheck = styleDao.selectOne(new QueryWrapper<>(doubleCheck));
            if (doubleCheck != null) {
                return ResponseDTO.userErrorParam("已存在相同品类代码 ~");
            }
        }
        return ResponseDTO.ok();
    }

    public void setTemplateSizeMessage(StyleTreeVO styleTreeVO) {
        Long id = styleTreeVO.getDefaultSizeId();
        StringBuilder stringBuilder = new StringBuilder();
        SizeTemplateEntity templateEntity = sizeTemplateDao.selectById(id);
        if (templateEntity == null) {
            stringBuilder.append("对应模版表不存在");
            styleTreeVO.setTemplateSizeMessage(stringBuilder.toString());
        } else {
            stringBuilder.append(templateEntity.getTemplateName()).append(" ");
            List<SizeEntity> sizeEntityList = sizeDao.selectList(new QueryWrapper<SizeEntity>().eq("template_id", id));
            for (SizeEntity sizeEntity : sizeEntityList) {
                stringBuilder.append(sizeEntity.getSizeMessage()).append(",");
            }
            styleTreeVO.setTemplateSizeMessage(stringBuilder.toString());
        }

    }

    public List<Long> queryStyleSubId(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<StyleEntity> styleEntityList = Lists.newArrayList();
        ids.forEach(e -> styleEntityList.addAll(querySubStyle(e)));
        Map<Long, List<StyleEntity>> subStyleMap = styleEntityList.stream().collect(Collectors.groupingBy(StyleEntity::getId));
        ids = subStyleMap.values().stream().flatMap(Collection::stream).map(StyleEntity::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        ids.addAll(this.queryStyleSubId(ids));
        return ids;
    }

    public List<StyleEntity> querySubStyle(Long parentId) {
        return styleDao.queryByParentId(Lists.newArrayList(parentId), false);
    }

    //校验品类代码防重复
    public ResponseDTO<String> handleCode(StyleEntity styleEntity,StyleEntity entity){
        //检查styleCode是否被修改
        if (BeanUtil.isEmpty(entity)) {

            return ResponseDTO.error(PARAM_ERROR);
        }
        if (Objects.equals(entity.getStyleCode(),styleEntity.getStyleCode())) {
            //没修改
            return ResponseDTO.ok();
        }
        else{
            //被修改,检查是否存在相同品类代码
            Long count = query().eq("style_code", styleEntity.getStyleCode()).count();
            if (count>0) {
                return ResponseDTO.userErrorParam("已存在相同品类代码 ~");
            }
        }
        return ResponseDTO.ok();
    }

}
