package net.lab1024.sa.admin.module.business.mes.stock.in.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.entity.StkInStockEntity;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.form.StkInStockQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.vo.StkInStockVO;

import java.util.List;

/**
 * 入库单 Dao
 *
 * <AUTHOR>
 * @Date 2025-02-03 15:54:07
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface StkInStockDao extends BaseMapper<StkInStockEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<StkInStockVO> queryPage(Page page, @Param("queryForm") StkInStockQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
