package net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 即时库存 实体类
 *
 * <AUTHOR>
 * @Date 2025-01-14 15:20:23
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_stk_inventory")
public class StkInventoryEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 备注
     */
    private String remark;

    /**
     * 版本号
     */
    @Version
    @TableField(fill = FieldFill.INSERT)
    private Integer version;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 货位id
     */
    private Long locationId;

    /**
     * 物料id
     */
    private Long materielId;

    /**
     * 批次id
     */
    private Long lotId;

    /**
     * sn ID
     */
    private Long snId;

    /**
     * 货主类型;保留
     */
    private String ownerType;

    /**
     * 货主id;保留
     */
    private Long ownerId;

    /**
     * 单位id
     */
    private Long unitId;

    /**
     * 实际库存
     */
    private BigDecimal qty;

    /**
     * 可用库存
     */
    private BigDecimal avbQty;

    /**
     * 锁定库存
     */
    private BigDecimal lockQty;

    /**
     * 预计库存
     */
    private BigDecimal predictQty;

}
