package net.lab1024.sa.admin.module.business.mes.produce.follow.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.util.NumberUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.base.dao.CustomerDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.CustomerEntity;
import net.lab1024.sa.admin.module.business.mes.base.manager.CustomerManager;
import net.lab1024.sa.admin.module.business.mes.produce.follow.dao.ProduceInstructFollowDao;
import net.lab1024.sa.admin.module.business.mes.produce.follow.domain.entity.ProduceInstructFollowEntity;
import net.lab1024.sa.admin.module.business.mes.produce.follow.domain.form.ProduceInstructFollowAddForm;
import net.lab1024.sa.admin.module.business.mes.produce.follow.domain.form.ProduceInstructFollowUpdateForm;
import net.lab1024.sa.admin.module.business.mes.produce.follow.domain.vo.ProduceInstructFollowVO;
import net.lab1024.sa.admin.module.business.mes.produce.follow.manager.ProduceInstructFollowManager;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderArrangeDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderArrangeEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderArrangeVO;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderVO;
import net.lab1024.sa.admin.module.system.employee.dao.EmployeeDao;
import net.lab1024.sa.base.common.code.UserErrorCode;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import net.lab1024.sa.base.common.util.SmartRequestUtil;

import jakarta.annotation.Resource;

/**
 * 生产跟单 Service
 *
 * <AUTHOR>
 * @Date 2024-07-26 21:36:36
 * @Copyright zscbdic
 */

@Service
@Slf4j
public class ProduceInstructFollowService {

    @Resource
    private ProduceInstructFollowDao produceInstructFollowDao;

    @Resource
    private ProduceInstructOrderDao produceInstructOrderDao;

    @Resource
    private ProduceInstructFollowManager produceInstructFollowManager;

    @Resource
    private CustomerDao customerDao;

    @Resource
    private EmployeeDao employeeDao;

    @Resource
    private CustomerManager customerManager;
    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<ProduceInstructFollowVO> queryPage(ProduceInstructOrderQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
//        List<ProduceInstructFollowVO> list = produceInstructFollowDao.queryPage(page, queryForm);
        List<ProduceInstructOrderVO> pioList = produceInstructOrderDao.queryPage(page, queryForm);
        //字段映射
        Map<String,String> map = new HashMap<>();
        map.put("id","instructOrderId");
        List<ProduceInstructFollowVO> list = BeanUtil.copyToList(pioList, ProduceInstructFollowVO.class,
                CopyOptions.create().setFieldMapping(map));
        //设置信息
        produceInstructFollowManager.handleInfo(list);
        return SmartPageUtil.convert2PageResult(page, list);
    }

    /**
     * 添加
     */
//    @Transactional
//    public ResponseDTO<String> add(ProduceInstructFollowAddForm addForm) {
////        ProduceInstructFollowEntity Entity = SmartBeanUtil.copy(addForm, ProduceInstructFollowEntity.class);
//        addForm.getEmployeeId().forEach(e->{
//            ProduceInstructFollowEntity pifEntity = new ProduceInstructFollowEntity();
//            pifEntity.setEmployeeId(e);
//            log.info(SmartRequestUtil.getRequestUserId()+"请求者ID");
//            pifEntity.setDelegateId(SmartRequestUtil.getRequestUserId());
//            pifEntity.setInstructOrderId(addForm.getInstructOrderId());
//            produceInstructFollowDao.insert(pifEntity);
//        });
//        return ResponseDTO.ok();
//    }

    /**
     * 自主跟单
     * @param addForm
     * @return
     */
    public ResponseDTO<String> add(ProduceInstructFollowAddForm addForm) {
        ProduceInstructFollowEntity selfFollow = SmartBeanUtil.copy(addForm, ProduceInstructFollowEntity.class);
        selfFollow.setEmployeeId(SmartRequestUtil.getRequestUserId());
        //重复校验
        Long count = produceInstructFollowDao.selectCount(new QueryWrapper<>(selfFollow));
        log.info("跟单条数:"+count);
        if (count>0) {
            return ResponseDTO.ok("请勿重复跟单");
        }
        selfFollow.setDelegateId(SmartRequestUtil.getRequestUserId());
        produceInstructFollowDao.insert(selfFollow);
        return ResponseDTO.ok("跟单成功");
    }

    /**
     * 查询我的跟单信息
     * @param employeeId 员工id
     * @return 所有指令单信息
     */
    public ResponseDTO<List<ProduceInstructFollowVO>> instructInfo(Long employeeId){
        Map<Long, List<CustomerEntity>> customerMap = customerManager.processMap();
        List<Long> instructIds = produceInstructFollowDao.selectList(new LambdaQueryWrapper<ProduceInstructFollowEntity>()
                        .eq(ProduceInstructFollowEntity::getEmployeeId, employeeId))
                        .stream().map(ProduceInstructFollowEntity::getInstructOrderId).collect(Collectors.toList());
        List<ProduceInstructOrderEntity> list = produceInstructOrderDao.selectBatchIds(instructIds);
        List<ProduceInstructFollowVO> vos = BeanUtil.copyToList(list, ProduceInstructFollowVO.class);
        vos.forEach(e->{
            Long delegateId = null;
            if (e.getCustomerId()!=null) {
                e.setTelephone(customerMap.get(e.getCustomerId()).get(0).getTelephone());
            }
            List<ProduceInstructFollowEntity> selectList = produceInstructFollowDao
                    .selectList(new LambdaQueryWrapper<ProduceInstructFollowEntity>()
                            .eq(ProduceInstructFollowEntity::getInstructOrderId, e.getId()));
            if(CollectionUtil.isNotEmpty(selectList)){
                delegateId = selectList.get(0).getDelegateId();
            }

            if (delegateId!=null) {
                e.setDelegateName(customerMap.get(delegateId).get(0).getName());
            }
        });
        return ResponseDTO.ok(vos);
    }


//    /**
//     * 查看指令单动态信息
//     * @param instructId 指令单id
//     * @return 该条指令单动态信息
//     */
//    public ResponseDTO<ProduceInstructOrderVO> instructDynamicInfo(Long instructId){
//        ProduceInstructOrderEntity entity = produceInstructOrderDao.selectById(instructId);
//        //对是否删除进行判断
//        ProduceInstructOrderVO vo = BeanUtil.copyProperties(entity, ProduceInstructOrderVO.class);
//        List<ProduceInstructOrderArrangeEntity> arrangeEntities =
//                produceInstructOrderArrangeDao.selectList(new LambdaQueryWrapper<ProduceInstructOrderArrangeEntity>()
//                .eq(ProduceInstructOrderArrangeEntity::getOrderId, instructId));
//        vo.setArrangeList(SmartBeanUtil.copyList(arrangeEntities, ProduceInstructOrderArrangeVO.class));
//        return ResponseDTO.ok(vo);
//    }

    /**
     * 更新跟单员
     *
     * @param updateForm 跟单员id 指令单id 委派人id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(ProduceInstructFollowUpdateForm updateForm) {
        List<Long> employeeId = updateForm.getEmployeeId();
        //先删除原有的信息
        long l = produceInstructFollowDao.updateByInstructId(updateForm.getInstructOrderId(), true);
        //新增修改后的信息
        employeeId.forEach(e->{
           ProduceInstructFollowEntity pioEntity = new ProduceInstructFollowEntity();
           pioEntity.setDelegateId(updateForm.getDelegateId());
           pioEntity.setInstructOrderId(updateForm.getInstructOrderId());
           pioEntity.setEmployeeId(e);
           produceInstructFollowDao.insert(pioEntity);
        });
        return ResponseDTO.ok();
    }

    /**
     * 取消跟单
     * @param addForm 指令单id
     * @return  指令单不存在返回30002
     */
    public ResponseDTO<String> delete(ProduceInstructFollowAddForm addForm) {
        ProduceInstructFollowEntity selfFollow = SmartBeanUtil.copy(addForm, ProduceInstructFollowEntity.class);
        selfFollow.setEmployeeId(SmartRequestUtil.getRequestUserId());
        //空值校验
        Long count = produceInstructFollowDao.selectCount(new QueryWrapper<>(selfFollow));
        log.info("跟单条数:"+count);
        if (count==1) {
            produceInstructFollowDao.cancelFollow(addForm.getInstructOrderId(),selfFollow.getEmployeeId(),true);
            return ResponseDTO.ok("取消成功");
        }
        return ResponseDTO.error(UserErrorCode.DATA_NOT_EXIST);
    }


}
