package net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkWarehouseEntity;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@Data
public class StkWarehouseQuickAddForm extends StkWarehouseAddForm {

    /**
     * 货架数量
     */
    @NotNull(message = "货架数量不能为空")
    @Min(value = 1, message = "货架数量不能小于1")
    private Integer rackNum;

    /**
     * 层数
     */
    @NotNull(message = "层数不能为空")
    @Min(value = 1, message = "层数不能小于1")
    private Integer layerNum;

    public StkWarehouseEntity toWarehouseEntity() {
        StkWarehouseEntity entity = new StkWarehouseEntity();
        entity.setNumber(this.getNumber());
        entity.setName(this.getName());
        entity.setAddress(this.getAddress());
        entity.setPrincipalId(this.getPrincipalId());
        entity.setPrincipalName(this.getPrincipalName());
        entity.setTel(this.getTel());
        entity.setOpenLocationFlag(this.getOpenLocationFlag());
//        entity.setAllowLockFlag(this.getAllowLockFlag());
        entity.setRemark(this.getRemark());
        entity.setAllowNegativeFlag(this.getAllowNegativeFlag());
        return entity;
    }
}
