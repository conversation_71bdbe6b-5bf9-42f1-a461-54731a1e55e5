package net.lab1024.sa.admin.module.business.mes.stock.out.domain.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 生产退库单
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StkProdOutStockVO extends StkOutStockVO {

    private List<StkProdOutStockVO.DetailVO> details;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DetailVO extends StkOutStockDetailVO {

    }
}
