package net.lab1024.sa.admin.module.business.mes.system.app.version.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.URL;
import net.lab1024.sa.admin.module.business.mes.system.app.version.constant.PackageTypeEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * app版本管理 更新表单
 *
 * <AUTHOR>
 * @Date 2025-02-25 14:44:40
 * @Copyright zscbdic
 */

@Data
public class AppVersionUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "app_id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "app_id 不能为空")
    private String appId;

    @Schema(description = "app名称")
    @NotBlank(message = "app名称 不能为空")
    private String appName;


    @Schema(description = "安卓下载链接", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "安卓下载链接 不能为空")
    @URL(message = "链接非法")
//    @JsonDeserialize(using = FileKeyVoDeserializer.class)
    private String androidUrl;

    @Schema(description = "版本号排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "版本号排序 不能为空")
    private Double versionSort;

    @Schema(description = "版本名称（号）")
    @NotBlank(message = "版本名称（号） 不能为空")
    private String versionName;

    @Schema(description = "描述")
    private String versionDescription;

    @Schema(description = "包类型;0是整包升级，1是wgt升级")
    @NotNull(message = "包类型;0是整包升级，1是wgt升级 不能为空")
    @CheckEnum(value = PackageTypeEnum.class, required = true, message = "包类型非法")
    private String packageType;

    @Schema(description = "是否强制更新;0否1是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否强制更新;0否1是 不能为空")
    private Boolean forceUpdate;

//    @Schema(description = "状态;published（已发布）、draft（草稿）")
//    private String status;
//
//    @Schema(description = "发布日期")
//    private LocalDateTime releaseDate;

}
