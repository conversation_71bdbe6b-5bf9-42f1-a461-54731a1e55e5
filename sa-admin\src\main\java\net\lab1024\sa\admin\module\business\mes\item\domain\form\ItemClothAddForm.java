package net.lab1024.sa.admin.module.business.mes.item.domain.form;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.json.deserializer.FileKeyVoDeserializer;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class ItemClothAddForm {

    /**
     * 物料分类id;关联t_item_type
     */
    @Schema(description = "物料分类id;关联t_item_type", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "物料分类id;关联t_item_type 不能为空")
    private Long typeId;

    /**
     * 物料名称;
     */
    @Schema(description = "物料名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "物料名称 不能为空")
    private String name;

    /**
     * 供应商id;关联t_item_supplier
     */
    @Schema(description = "供应商id;关联t_item_supplier")
    private Long supplierId;

    /**
     * 规格型号;
     */
    @Schema(description = "规格型号")
    private String model;

    /**
     * spu编号 无自动生成;
     */
    @Schema(description = "spu编号", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "spu编号 不能为空")
    private String number;

    /**
     * 单位id;关联t_unit
     */
    @Schema(description = "单位id;关联t_unit", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "单位id;关联t_unit 不能为空")
    private Long unitId;

    /**
     * 停用标识;0启用，1停用
     */
    @Schema(description = "停用标识;0启用，1停用")
    @NotNull(message = "停用标识;0启用，1停用 不能为空")
    private Boolean enableFlag;

    /**
     * 类型;0半成品 1成品
     */
//    @Schema(description = "类型;0半成品 1成品", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "类型;0半成品 1成品 不能为空")
//    @CheckEnum(value = ItemCategoryEnum.class, message = "类型;0半成品 1成品 值不合法")
    private String category;

    /**
     * 属性;0面料，1其他，2成衣
     */
    @Schema(description = "属性;0面料，1其他，2成衣", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "属性;0面料，1其他，2成衣 不能为空")
//    @CheckEnum(value = ItemAttributeEnum.class, message = "属性;0面料，1其他，2成衣 值不合法")
    private String attribute;

    /**
     * sku属性
     */
    @Valid
    @Schema(description = "sku属性", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "sku属性 不能为空")
    @NotEmpty(message = "sku属性 不能为空")
    private ValidateList<ItemClothSkuAddBo> skuAttributes;

    /**
     * 图片url
     */
//    @Schema(description = "图片url")
//    @JsonDeserialize(using = FileKeyVoDeserializer.class)
//    private String imgUrl;

    /**
     * 价格;
     */
//    @Schema(description = "价格;")
//    @Min(value = 0, message = "价格;不能小于0")
//    private BigDecimal price = BigDecimal.ZERO;

    //--------------布料信息开始-------------------

    /**
     * 克重;克每平方米
     */
    @Schema(description = "克重;克每平方米", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "克重;克每平方米 不能为空")
    @Min(value = 1, message = "克重;克每平方米 最小值为1")
    private Integer gramWeight;

    /**
     * 幅宽;厘米
     */
    @Schema(description = "幅宽;厘米", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "幅宽;厘米 不能为空")
    @Min(value = 1, message = "幅宽;厘米 最小值为1")
    private Integer width;

    /**
     * 布重;千克
     */
    @Schema(description = "布重;千克")
    @Min(value = 1, message = "布重;千克 最小值为1")
    private Double weight;

    /**
     * 布长;米
     */
    @Schema(description = "布长;米")
    @Min(value = 1, message = "布长;米 最小值为1")
    private Double length;

    /**
     * 成分
     */
    @Schema(description = "成分")
    private String ingredient;

    /**
     * 净价
     */
    @Schema(description = "净价")
    @Min(value = 0, message = "净价 不能小于0")
    private BigDecimal netPrice = BigDecimal.ZERO;

    /**
     * 米净价
     */
    @Schema(description = "米净价")
    @Min(value = 0, message = "米净价 不能小于0")
    private BigDecimal metreNetPrice = BigDecimal.ZERO;

    /**
     * 千克净价
     */
    @Schema(description = "公斤净价")
    @Min(value = 0, message = "公斤净价 不能小于0")
    private BigDecimal kgNetPrice = BigDecimal.ZERO;


    @Data
    public static class ItemClothSkuAddBo {
        /**
         * 颜色编号
         */
        @Schema(description = "色号")
        private String colorNum;

        /**
         * 颜色名称
         */
        @NotBlank(message = "颜色名称不能为空")
        @Schema(description = "颜色名称")
        private String colorName;

        /**
         * 参考色
         */
        @Schema(description = "参考色")
        private String referColor;

        /**
         * sku编号 无自动生成
         */
//        @NotBlank(message = "物料编号不能为空")
        private String skuNumber;

        /**
         * 图片url
         */
        @Schema(description = "图片url")
        @JsonDeserialize(using = FileKeyVoDeserializer.class)
        private String imgUrl;

        /**
         * 价格
         */
        @Schema(description = "价格")
        @Min(value = 0, message = "价格 不能小于0")
        private BigDecimal price = BigDecimal.ZERO;


    }
}
