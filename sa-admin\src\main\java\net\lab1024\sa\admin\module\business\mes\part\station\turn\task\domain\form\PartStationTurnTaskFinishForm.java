package net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class PartStationTurnTaskFinishForm {

//    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "主键 不能为空")
//    private Long id;

    @Schema(description = "终点货位ID")
    @NotNull(message = "终点货位ID 不能为空")
    private Long endLocationId;

    @Schema(description = "周转箱ID")
    @NotNull(message = "周转箱ID 不能为空")
    private Long turnoverBoxId;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;
}
