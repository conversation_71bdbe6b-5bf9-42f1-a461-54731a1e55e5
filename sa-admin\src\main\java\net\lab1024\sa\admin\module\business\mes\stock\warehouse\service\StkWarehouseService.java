package net.lab1024.sa.admin.module.business.mes.stock.warehouse.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.manager.StkInventoryManager;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.dao.StkLocationDao;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.dao.StkRackDao;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.dao.StkWarehouseDao;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkLocationEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkRackEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkWarehouseEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.*;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.vo.StkWarehouseVO;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.manager.StkLocationManager;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.manager.StkRackManager;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.manager.StkWarehouseManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 仓库 Service
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:13:18
 * @Copyright zscbdic
 */

@Service
public class StkWarehouseService {

    @Resource
    private StkWarehouseDao stkWarehouseDao;

    @Resource
    private StkWarehouseManager stkWarehouseManager;

    @Resource
    private StkRackDao stkRackDao;

    @Resource
    private StkLocationDao stkLocationDao;

    @Resource
    private StkLocationManager stkLocationManager;

    @Resource
    private StkRackManager stkRackManager;

    @Resource
    private StkInventoryManager stkInventoryManager;

    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<StkWarehouseVO> queryPage(StkWarehouseQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<StkWarehouseVO> list = stkWarehouseDao.queryPage(page, queryForm);
        PageResult<StkWarehouseVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(StkWarehouseAddForm addForm) {

        stkWarehouseManager.addCheck(addForm);

        StkWarehouseEntity stkWarehouseEntity = SmartBeanUtil.copy(addForm, StkWarehouseEntity.class);
        stkWarehouseDao.insert(stkWarehouseEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(StkWarehouseUpdateForm updateForm) {
        stkWarehouseManager.updateCheck(updateForm);
        StkWarehouseEntity stkWarehouseEntity = SmartBeanUtil.copy(updateForm, StkWarehouseEntity.class);

        StkWarehouseEntity warehouse = stkWarehouseDao.selectById(updateForm.getId());

        //仓位设置有不同
        Boolean openLocationFlag = updateForm.getOpenLocationFlag();
        if (!openLocationFlag.equals(warehouse.getOpenLocationFlag())) {
            if (openLocationFlag) {
                //启用仓位管理检查
                stkWarehouseManager.openLocationCheck(updateForm.getId());
            } else {
                //合并仓位库存
                stkInventoryManager.mergeLocationInventory(updateForm.getId());
            }
        }
        Boolean allowNegativeFlag = updateForm.getAllowNegativeFlag();
        if(!allowNegativeFlag.equals(warehouse.getAllowNegativeFlag())){
            if(!allowNegativeFlag){
                //关闭负库存检查
                stkWarehouseManager.closeNegativeStockCheck(updateForm.getId());
            }
        }

        stkWarehouseDao.updateById(stkWarehouseEntity);
        return ResponseDTO.ok();
    }


    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }
        stkWarehouseManager.deleteCheck(id);
        stkWarehouseManager.deleteWarehouse(id);
        return ResponseDTO.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> quickAdd(StkWarehouseQuickAddForm addForm) {
        stkWarehouseManager.addCheck(addForm);

        StkWarehouseEntity warehouse = addForm.toWarehouseEntity();
        stkWarehouseDao.insert(warehouse);

        for (int i = 1; i <= addForm.getRackNum(); i++) {
            StkRackEntity rack = new StkRackEntity();
            rack.setNumber(addForm.getNumber() + "-" + i);
            rack.setWarehouseId(warehouse.getId());

            stkRackDao.insert(rack);
            List<StkLocationEntity> tempAddLocations = new ArrayList<>();
            for (int j = 1; j <= addForm.getLayerNum(); j++) {
                StkLocationEntity location = new StkLocationEntity();
                location.setRackId(rack.getId());
                location.setNumber(rack.getNumber() + "-" + j);

                tempAddLocations.add(location);
            }
            stkLocationManager.saveBatch(tempAddLocations);
        }


        return ResponseDTO.ok();
    }

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    public ResponseDTO<List<StkWarehouseVO>> queryList(StkWarehouseQuery query) {
        List<StkWarehouseEntity> list = stkWarehouseDao.selectList(null);
        return ResponseDTO.ok(SmartBeanUtil.copyList(list, StkWarehouseVO.class));
    }
}
