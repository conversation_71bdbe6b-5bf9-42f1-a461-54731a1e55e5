package net.lab1024.sa.admin.module.business.mes.item.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

@Data
public class ItemClothesQueryForm extends PageParam {

    /**
     * 物料编号
     */
    private String number;

    /**
     * sku编号
     */
    private String skuNumber;

    /**
     * 物料名称
     */
    private String name;

    /**
     * 关键字查询
     */
    private String queryKey;

    /**
     * 停用标识;0启用，1停用
     */
    @Schema(description = "停用标识;0启用，1停用")
    private Boolean enableFlag;
}
