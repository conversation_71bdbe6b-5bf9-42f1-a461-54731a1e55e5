package net.lab1024.sa.admin.module.business.mes.report.stock.inout.domain.vo;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class ProInAndOutStockReportVO {

    /**
     * 生产指令单数据ID
     */
    private Long produceClothesDataId;

    /**
     * 生产指令单号
     */
    private String produceInstructOrderNumber;
    /**
     * 生产优先级
     */
    private String producePriority;
    /**
     * 生产状态
     */
    private String produceStatus;
    /**
     * 发布时间
     */
    private LocalDateTime produceIssuedTime;
    /**
     * 交货时间
     */
    private LocalDate produceDeliverTime;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 物料型号
     */
    private String materialModel;
    /**
     * 物料SPU编号
     */
    private String materialSpuNumber;
    /**
     * 物料SKU编号
     */
    private String materialSkuNumber;
    /**
     * 款式颜色
     */
    private String style;
    /**
     * 尺码
     */
    private String size;
    /**
     * 单位ID
     */
    private Integer unitId;
    /**
     * 单位名称
     */
    private String unitName;
    /**
     * 数量
     */
    private Integer produceNum = 0;

    /**
     * 合格品数量
     */
    private Double qualityInQty = 0.0;

    /**
     * 不合格品数量
     */
    private Double nonQualityInQty = 0.0;

    /**
     * 报废品数量
     */
    private Double scrapInQty = 0.0;

    /**
     * 返工品数量
     */
    private Double reworkInQty = 0.0;

    /**
     * 退库数量
     */
    private Double returnOutQty = 0.0;
}
