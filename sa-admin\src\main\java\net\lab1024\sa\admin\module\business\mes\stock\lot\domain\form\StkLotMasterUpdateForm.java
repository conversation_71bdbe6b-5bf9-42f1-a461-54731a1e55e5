package net.lab1024.sa.admin.module.business.mes.stock.lot.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 批号主档 更新表单
 *
 * <AUTHOR>
 * @Date 2025-02-03 19:28:09
 * @Copyright zscbdic
 */

@Data
public class StkLotMasterUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "批次编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "批次编号 不能为空")
    private String number;

    @Schema(description = "物料ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物料ID 不能为空")
    private Long materielId;

    @Schema(description = "批号状态;0 未生效 1 生效 2 断号（保留）")
    private String lotStatus;

    @Schema(description = "业务类型;保留")
    private String bizType;

    @Schema(description = "货主类型;保留")
    private String ownerType;

    @Schema(description = "货主id;保留")
    private Long ownerId;

    @Schema(description = "货主名称;保留")
    private String ownerName;

}
