package net.lab1024.sa.admin.module.business.mes.stock.lot.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

import java.time.LocalDate;

/**
 * 批号跟踪信息 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-02-08 19:57:37
 * @Copyright zscbdic
 */

@Data
public class StkLotMaterialTraceQueryForm extends PageParam{

    @Schema(description = "批号ID")
    private Long lotMasterId;

    @Schema(description = "关键字查询")
    private String queryKey;

    @Schema(description = "单据类型")
    private String billType;

    @Schema(description = "跟踪方向")
    private String stockDirect;

    @Schema(description = "单据时间")
    private LocalDate billTimeBegin;

    @Schema(description = "单据时间")
    private LocalDate billTimeEnd;

    @Schema(description = "物料名称")
    private String materielName;

    @Schema(description = "物料spu编码")
    private String materielSpuNumber;

    @Schema(description = "物料sku编码")
    private String materielSkuNumber;

}
