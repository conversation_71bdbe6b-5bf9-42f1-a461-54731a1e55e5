package net.lab1024.sa.admin.module.business.mes.stock.in.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * 入库单详情 更新表单
 *
 * <AUTHOR>
 * @Date 2025-02-03 15:55:23
 * @Copyright zscbdic
 */

@Data
public class StkInStockDetailUpdateForm {

//    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "主键 不能为空")
//    private Long id;

    @Schema(description = "备注")
    private String remark;

//    @Schema(description = "入库单ID", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "入库单ID 不能为空")
//    private Long inStockId;

//    @Schema(description = "单据来源详情类型")
//    private String originDetailType;
//
//    @Schema(description = "单据来源详情ID")
//    private Long originDetailId;
//
//    @Schema(description = "单据来源详情行号")
//    private Integer originDetailSeq;

    @Schema(description = "物料ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物料ID 不能为空")
    private Long materielId;

    @Schema(description = "批次ID")
    private Long lotId;

    @Schema(description = "批次编号")
    private String lotNumber;

    @Schema(description = "SN集合")
    private String sns;

    @Schema(description = "仓位id")
    private Long locationId;

    @Schema(description = "仓位编号")
    private String locationNumber;

    @Schema(description = "单位id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "单位id 不能为空")
    private Long unitId;

    @Schema(description = "单位名称")
    private String unitName;

    @Schema(description = "实收数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "实收数量 不能为空")
    @Positive(message = "实收数量必须大于0")
    private BigDecimal qty;

    @Schema(description = "关联数量")
    @Positive(message = "关联数量必须大于0")
    private BigDecimal joinQty;

    @Schema(description = "单价")
    @Min(value = 0, message = "单价必须大于0")
    private BigDecimal price;

    @Schema(description = "金额")
    @Min(value = 0, message = "金额必须大于0")
    private BigDecimal amount;

}
