package net.lab1024.sa.admin.module.business.mes.factory.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 生产小组成员 更新表单
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:33:31
 * @Copyright zscbdic
 */

@Data
public class ProduceTeamMemberUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "小组id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "小组id 不能为空")
    private Long teamId;

    @Schema(description = "成员id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "成员id 不能为空")
    private Long memberId;

}
