package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationBinDao;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.dao.PartStationInventoryDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationRackDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationWarehouseDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationBinEntity;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationWarehouseEntity;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.entity.PartStationInventoryEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationRackEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationBinAddForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationBinUpdateForm;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 裁片货位  Manager
 *
 * <AUTHOR>
 * @Date 2024-10-06 20:17:14
 * @Copyright zscbdic
 */
@Service
public class PartStationBinManager extends ServiceImpl<PartStationBinDao, PartStationBinEntity> {

    @Resource
    private PartStationInventoryDao partStationInventoryDao;

    @Resource
    private PartStationRackDao rackDao;

    @Resource
    private PartStationBinDao binDao;

    @Resource
    private PartStationWarehouseDao partStationWarehouseDao;

    /**
     * 添加校验
     *
     * @param addForm
     */
    public void addCheck(PartStationBinAddForm addForm) {
        String binCode = addForm.getBinCode();
//        if (binCode.contains(QrCodeTypeUtil.SEPARATOR)) {
//            throw new BusinessException("货位编码不能包含" + QrCodeTypeUtil.SEPARATOR);
//        }

        long count = this.count(new LambdaQueryWrapper<PartStationBinEntity>()
                .eq(PartStationBinEntity::getBinCode, binCode));
        if (count > 0) {
            throw new RuntimeException("货位编码已存在");
        }
    }

    /**
     * 更新校验
     *
     * @param updateForm
     */
    public void updateCheck(PartStationBinUpdateForm updateForm) {
        String binCode = updateForm.getBinCode();
//        if (binCode.contains(QrCodeTypeUtil.SEPARATOR)) {
//            throw new BusinessException("货位编码不能包含" + QrCodeTypeUtil.SEPARATOR);
//        }

        long count = this.count(new LambdaQueryWrapper<PartStationBinEntity>()
                .eq(PartStationBinEntity::getBinCode, binCode)
                .ne(PartStationBinEntity::getId, updateForm.getId()));
        if (count > 0) {
            throw new BusinessException("货位编码已存在");
        }
    }

    /**
     * 删除校验
     *
     * @param id
     */
    public void deleteCheck(Long id) {
        this.deleteCheck(Lists.newArrayList(id));
    }


    /**
     * 批量删除校验
     * @param ids
     */
    public void deleteCheck(List<Long> ids){
        Long count = partStationInventoryDao.selectCount(new LambdaQueryWrapper<PartStationInventoryEntity>()
                .in(PartStationInventoryEntity::getBinId, ids));
        if (count > 0) {
            throw new BusinessException("货位存在裁片，无法删除");
        }
    }

    /**
     * 根据仓库id获取货位ids
     *
     * @param warehouseId
     * @return 不可修改ids
     */
    public List<Long> getBinIdsByWarehouseId(Long warehouseId) {
        List<PartStationRackEntity> racks = rackDao.selectList(new LambdaQueryWrapper<PartStationRackEntity>()
                .select(PartStationRackEntity::getId)
                .eq(PartStationRackEntity::getWarehouseId, warehouseId));
        if (CollUtil.isEmpty(racks)) {
            return Collections.emptyList();
        }

        List<Long> rackIds = racks.stream().map(PartStationRackEntity::getId).collect(Collectors.toList());
        List<PartStationBinEntity> bins = binDao.selectList(new LambdaQueryWrapper<PartStationBinEntity>()
                .select(PartStationBinEntity::getId)
                .in(PartStationBinEntity::getRackId, rackIds));
        if (CollUtil.isEmpty(bins)) {
            return Collections.emptyList();
        }
        return bins.stream().map(PartStationBinEntity::getId).collect(Collectors.toList());
    }

    /**
     * 根据id获取仓库
     * @param startLocationId
     * @return
     */
    public PartStationWarehouseEntity getWarehouseById(Long startLocationId) {
        PartStationBinEntity bin = this.getById(startLocationId);
        if (bin == null) {
            return null;
        }
        Long rackId = bin.getRackId();
        PartStationRackEntity rack = rackDao.selectById(rackId);
        if (rack == null) {
            return null;
        }
        Long warehouseId = rack.getWarehouseId();
        return partStationWarehouseDao.selectById(warehouseId);
    }
}
