package net.lab1024.sa.admin.module.business.mes.stock.lot.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

/**
 * 批号主档 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-02-03 19:28:09
 * @Copyright zscbdic
 */

@Data
public class StkLotMasterQueryForm extends PageParam{

    @Schema(description = "关键字查询")
    private String queryKey;

    @Schema(description = "批号编号")
    private String lotNumber;

    @Schema(description = "物料名称")
    private String materielName;

    @Schema(description = "物料SPU编号")
    private String materielSpuNumber;

    @Schema(description = "物料SKU编号")
    private String materielSkuNumber;



}
