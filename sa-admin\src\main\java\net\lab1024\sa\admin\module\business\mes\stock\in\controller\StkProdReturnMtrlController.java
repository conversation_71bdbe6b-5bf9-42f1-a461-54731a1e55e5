package net.lab1024.sa.admin.module.business.mes.stock.in.controller;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.common.bill.constant.BillType;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockBillStatusEnum;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.form.StkInStockQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.form.StkProdReturnMtrlAddForm;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.form.StkProdReturnMtrlUpdateForm;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.vo.StkInStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.vo.StkProdReturnMtrlVO;
import net.lab1024.sa.admin.module.business.mes.stock.in.service.StkProdReturnMtrlService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 生产退料单 Controller
 *
 * <AUTHOR>
 * @Date 2025-02-03 15:55:23
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class StkProdReturnMtrlController {

    @Resource
    private StkProdReturnMtrlService stkProdReturnMtrlService;

    @Resource
    private SerialNumberService serialNumberService;


    /**
     * 分页查询
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/stkProdReturnMtrl/queryPage")
    public ResponseDTO<PageResult<StkInStockVO>> queryPage(@RequestBody StkInStockQueryForm queryForm) {
        queryForm.setType(BillType.STOCK_PRODUCE_RETURN_MATERIAL_IN.getValue());
        return stkProdReturnMtrlService.queryPage(queryForm);
    }


    /**
     * 添加
     *
     * @param form
     * @return
     */
    @PostMapping("/stkProdReturnMtrl/add")
    public ResponseDTO<String> otherInStock(@RequestBody @Valid StkProdReturnMtrlAddForm form) {
        form.setType(BillType.STOCK_PRODUCE_RETURN_MATERIAL_IN.getValue());
        if (StrUtil.isBlank(form.getStatus())) {
            form.setStatus(StockBillStatusEnum.UN_AUDIT.getValue());
        }
        if (StrUtil.isBlank(form.getNumber())) {
            form.setNumber(serialNumberService.generate(SerialNumberIdEnum.STOCK_IN_PRODUCE_RETURN_MATERIAL));
        }
        return stkProdReturnMtrlService.add(form);
    }

    /**
     * 修改
     *
     * @param form
     * @return
     */
    @PostMapping("/stkProdReturnMtrl/update")
    public ResponseDTO<String> update(@RequestBody @Valid StkProdReturnMtrlUpdateForm form) {
        return stkProdReturnMtrlService.update(form);
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @GetMapping("stkProdReturnMtrl/byId")
    public ResponseDTO<StkProdReturnMtrlVO> queryById(@RequestParam("id") Long id) {
        return stkProdReturnMtrlService.queryById(id);
    }


    /**
     * 修改单据状态
     *
     * @param id
     * @return
     */
    @GetMapping("stkProdReturnMtrl/status")
    public ResponseDTO<String> updateStatus(@RequestParam("id") Long id) {
        return stkProdReturnMtrlService.updateStatus(id);
    }

    /**
     * 删除单据
     *
     * @param id
     * @return
     */
    @Operation(summary = "删除单据 <AUTHOR>
    @GetMapping("stkProdReturnMtrl/delete")
    public ResponseDTO<String> delete(@RequestParam("id") Long id) {
        return stkProdReturnMtrlService.delete(id);
    }
}
