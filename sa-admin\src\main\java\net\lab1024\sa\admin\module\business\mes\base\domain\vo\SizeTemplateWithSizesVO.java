package net.lab1024.sa.admin.module.business.mes.base.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.SizeEntity;

import java.util.List;

/**
 * 尺码表 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-05 22:37:21
 * @Copyright zscbdic
 */

@Data
@AllArgsConstructor
public class SizeTemplateWithSizesVO {

    /**
     * 尺码模板名称
     */
    @Schema(description = "尺码模板名称")
    private String templateName;

    /**
     * 尺码信息
     */
    @Schema(description = "尺码信息")
    private List<SizeEntity> sizeInfo;

}
