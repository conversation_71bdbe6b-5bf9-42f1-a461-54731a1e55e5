package net.lab1024.sa.admin.module.business.mes.salary.service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.web.bind.annotation.RequestParam;
import net.lab1024.sa.admin.module.business.mes.salary.dao.PayoffRecordDetailDao;
import net.lab1024.sa.admin.module.business.mes.salary.dao.SettlementRecordDao;
import net.lab1024.sa.admin.module.business.mes.salary.dao.SettlementRecordDetailDao;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.SettlementRecordDetailEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.SettlementRecordEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.SettlementRecordQueryForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.vo.SettlementRecordVO;
import net.lab1024.sa.admin.module.business.mes.salary.manager.SettlementManager;
import net.lab1024.sa.admin.module.business.mes.work.dao.WorkRecordDao;
import net.lab1024.sa.admin.module.business.mes.work.domain.entity.WorkRecordEntity;
import net.lab1024.sa.admin.module.business.mes.work.domain.vo.WorkRecordVO;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 薪酬结算记录 Service
 *
 * <AUTHOR>
 * @Date 2024-11-20 20:48:59
 * @Copyright zscbdic
 */

@Service
public class SettlementRecordService {

    @Resource
    private SettlementRecordDao settlementRecordDao;

    @Resource
    private SettlementRecordDetailDao settlementRecordDetailDao;

    @Resource
    private SettlementManager settlementManager;

    @Resource
    private WorkRecordDao workRecordDao;


    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<SettlementRecordVO> queryPage(SettlementRecordQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<SettlementRecordVO> list = settlementRecordDao.queryPage(page, queryForm);
        PageResult<SettlementRecordVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 取消结算
     *
     * @param ids 结算记录ID
     */
    public ResponseDTO<String> cancelSettlement(List<Long> ids) {
        List<SettlementRecordDetailEntity> detailEntities = settlementRecordDetailDao.selectList(new LambdaQueryWrapper<SettlementRecordDetailEntity>()
                .in(SettlementRecordDetailEntity::getMainId, ids));
        if (CollUtil.isEmpty(detailEntities)) {
            return ResponseDTO.userErrorParam("结算记录不存在");
        }
        List<SettlementRecordEntity> settleRecords = settlementRecordDao.selectBatchIds(ids);
        if (CollUtil.isEmpty(settleRecords)) {
            return ResponseDTO.userErrorParam("结算记录不存在");
        }
        long payoffCount = settleRecords.stream().filter(SettlementRecordEntity::getPayoffFlag).count();
        if (payoffCount > 0) {
            return ResponseDTO.userErrorParam("结算记录中存在已发放的结算记录，无法取消");
        }

        List<Long> workIds = detailEntities.stream()
                .map(SettlementRecordDetailEntity::getWorkRecordId)
                .collect(Collectors.toList());

        settlementManager.cancelSettlement(ids, workIds);
        return ResponseDTO.ok();
    }

    public ResponseDTO<List<WorkRecordVO>> queryDetails(Long id) {
        List<SettlementRecordDetailEntity> detailEntities = settlementRecordDetailDao.selectList(new LambdaQueryWrapper<SettlementRecordDetailEntity>()
                .eq(SettlementRecordDetailEntity::getMainId, id));
        if (CollUtil.isEmpty(detailEntities)) {
            return ResponseDTO.ok(Collections.emptyList());
        }
        List<Long> workIds = detailEntities.stream()
                .map(SettlementRecordDetailEntity::getWorkRecordId)
                .collect(Collectors.toList());
        List<WorkRecordEntity> list = workRecordDao.selectBatchIds(workIds);
        List<WorkRecordVO> vos = SmartBeanUtil.copyList(list, WorkRecordVO.class);
        return ResponseDTO.ok(vos);
    }
}
