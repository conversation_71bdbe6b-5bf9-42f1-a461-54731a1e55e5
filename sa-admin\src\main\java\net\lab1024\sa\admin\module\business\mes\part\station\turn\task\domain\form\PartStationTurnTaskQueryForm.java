package net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.form;

import net.lab1024.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 驿站任务表 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-03-14 20:13:46
 * @Copyright zscbdic
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class PartStationTurnTaskQueryForm extends PageParam {

    @Schema(description = "关键字查询")
    private String queryKey;

    @Schema(description = "任务优先级")
    private String priority;

    @Schema(description = "任务类型")
    private String type;

    @Schema(description = "任务状态")
    private String status;

    @Schema(description = "任务状态列表")
    private List<String> statusList;

    @Schema(description = "提交人ID")
    private String submitterId;

    @Schema(description = "执行人ID")
    private String executorId;

    @Schema(description = "提交时间")
    private LocalDate submitTimeBegin;

    @Schema(description = "提交时间")
    private LocalDate submitTimeEnd;

    @Schema(description = "领取时间")
    private LocalDate getTimeBegin;

    @Schema(description = "领取时间")
    private LocalDate getTimeEnd;

    @Schema(description = "开始时间")
    private LocalDate beginTimeBegin;

    @Schema(description = "开始时间")
    private LocalDate beginTimeEnd;

    @Schema(description = "结束时间")
    private LocalDate endTimeBegin;

    @Schema(description = "结束时间")
    private LocalDate endTimeEnd;

    @Schema(description = "起点货位ID")
    private Long startLocationId;

    @Schema(description = "终点货位ID")
    private Long endLocationId;

}
