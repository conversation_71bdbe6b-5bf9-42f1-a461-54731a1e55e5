package net.lab1024.sa.admin.module.business.mes.stock.in.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 入库单 更新表单
 *
 * <AUTHOR>
 * @Date 2025-02-03 15:54:07
 * @Copyright zscbdic
 */

@Data
public class StkInStockUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

    @Schema(description = "备注")
    private String remark;

//    @Schema(description = "单据来源类型")
//    private String originType;
//
//    @Schema(description = "单据来源ID")
//    private Long originId;
//
//    @Schema(description = "单据来源编号")
//    private String originNumber;

//    @Schema(description = "单据类型", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "单据类型 不能为空")
//    private String type;

//    @Schema(description = "单据方式")
//    private String way;
//
//    @Schema(description = "单据状态;", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "单据状态; 不能为空")
//    private String status;
//
//    @Schema(description = "单据编号", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "单据编号 不能为空")
//    private String number;

    @Schema(description = "仓库ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "仓库ID 不能为空")
    private Long warehouseId;

    @Schema(description = "入库时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "入库时间 不能为空")
    private LocalDateTime inStockTime;

    @Schema(description = "仓管员ID")
    private Long stockerId;

    @Schema(description = "仓管员名称")
    private String stockerName;

    @Schema(description = "货主类型")
    private String ownerType;

    @Schema(description = "货主id")
    private Long ownerId;

    @Schema(description = "货主名称")
    private String ownerName;

    @Schema(description = "申请人ID")
    private Long applicantId;

    @Schema(description = "申请人名称")
    private String applicantName;

    @Schema(description = "申请时间")
    private LocalDateTime applyTime;

    @Schema(description = "审核人ID")
    private Long auditorId;

    @Schema(description = "审核人名称")
    private String auditorName;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

}
