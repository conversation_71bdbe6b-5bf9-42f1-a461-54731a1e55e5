package net.lab1024.sa.admin.module.business.mes.ai.tool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.form.LLMToolAddForm;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.form.LLMToolQueryForm;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.form.LLMToolUpdateForm;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.vo.LLMToolVO;
import net.lab1024.sa.admin.module.business.mes.ai.tool.service.LLMToolService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import org.springframework.web.bind.annotation.*;

/**
 * 大模型工具表 Controller
 *
 * <AUTHOR>
 * @Date 2025-04-05 14:27:51
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "大模型工具表")
public class LLMToolController {

    @Resource
    private LLMToolService lLMToolService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/ai/LLMTool/queryPage")
//    @SaCheckPermission("lLMTool:query")
    public ResponseDTO<PageResult<LLMToolVO>> queryPage(@RequestBody @Valid LLMToolQueryForm queryForm) {
        return ResponseDTO.ok(lLMToolService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/ai/LLMTool/add")
//    @SaCheckPermission("lLMTool:add")
    public ResponseDTO<String> add(@RequestBody @Valid LLMToolAddForm addForm) {
        return lLMToolService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/ai/LLMTool/update")
//    @SaCheckPermission("lLMTool:update")
    public ResponseDTO<String> update(@RequestBody @Valid LLMToolUpdateForm updateForm) {
        return lLMToolService.update(updateForm);
    }

    /**
     * 批量删除
     * @param idList
     * @return
     */
    @Operation(summary = "批量删除 <AUTHOR>
    @PostMapping("/ai/LLMTool/batchDelete")
//    @SaCheckPermission("lLMTool:delete")
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> idList) {
        return lLMToolService.batchDelete(idList);
    }

    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/ai/LLMTool/delete/{id}")
//    @SaCheckPermission("lLMTool:delete")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return lLMToolService.delete(id);
    }
}
