package net.lab1024.sa.admin.module.business.mes.produce.arrange.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.process.domain.vo.ProcessLibraryVO;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.dao.ProduceArrangeDao;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.entity.ProduceArrangeEntity;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.form.ProduceArrangeAddForm;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.form.ProduceArrangeDetailAddForm;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.form.ProduceArrangeQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.form.ProduceArrangeUpdateForm;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.vo.ProduceArrangeVO;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.manager.ProduceArrangeManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 生产安排信息 Service
 *
 * <AUTHOR>
 * @Date 2024-07-22 18:44:38
 * @Copyright zscbdic
 */

@Service
public class ProduceArrangeService {

    @Resource
    private ProduceArrangeDao produceArrangeDao;
    @Resource
    private ProduceArrangeManager produceArrangeManager;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<ProduceArrangeVO> queryPage(ProduceArrangeQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ProduceArrangeVO> list = produceArrangeDao.queryPage(page, queryForm);


        PageResult<ProduceArrangeVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(ProduceArrangeAddForm addForm) {
        ProduceArrangeEntity produceArrangeEntity = SmartBeanUtil.copy(addForm, ProduceArrangeEntity.class);
        List<ProduceArrangeDetailAddForm> produceArrangeDetailAddForms = addForm.getProduceArrangeDetailAddForms();
        produceArrangeManager.add(produceArrangeEntity, produceArrangeDetailAddForms);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(ProduceArrangeUpdateForm updateForm) {
        ProduceArrangeEntity produceArrangeEntity = SmartBeanUtil.copy(updateForm, ProduceArrangeEntity.class);
        produceArrangeManager.updateProduceArrange(produceArrangeEntity, updateForm.getProduceArrangeDetailAddForms());
        return ResponseDTO.ok();
    }


    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }
        produceArrangeManager.delete(id);
        return ResponseDTO.ok();
    }

    public ResponseDTO<ProduceArrangeVO> queryById(Long id) {
        if (id == null) {
            ResponseDTO.ok();
        }
        ProduceArrangeVO produceArrangeVO = produceArrangeManager.queryById(id);
        return ResponseDTO.ok(produceArrangeVO);
    }

    public List<ProcessLibraryVO> queryList() {
        List<ProduceArrangeEntity> list = produceArrangeDao.selectList(null);
        return BeanUtil.copyToList(list, ProcessLibraryVO.class);
    }
}
