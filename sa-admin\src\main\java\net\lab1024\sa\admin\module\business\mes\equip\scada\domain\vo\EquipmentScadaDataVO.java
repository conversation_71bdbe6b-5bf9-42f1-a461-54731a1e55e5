package net.lab1024.sa.admin.module.business.mes.equip.scada.domain.vo;

import lombok.Data;

import java.util.Date;

@Data
public class EquipmentScadaDataVO {
    /**
     * 设备id
     */
    private Long equipmentId;

    /**
     * 设备编号
     */
    private String equipmentNumber;

    /**
     * 设备编号
     */
    private String equipmentName;

    /**
     * 设备类型id
     */
    private Long equipmentTypeId;

    /**
     * 设备类型名称
     */
    private String equipmentTypeName;

    /**
     * 设备状态
     */
    private String equipmentStatus;

    /**
     * 车间id
     */
    private Long workShopId;

    /**
     * 车间名称
     */
    private String workShopName;

    /**
     * scada平台
     */
    private String scadaPlatform;

    /**
     * 对接设备类型
     */
    private String scadaEquipmentType;


    /**
     * scada设备id
     */
    private String scadaEquipmentId;

    /**
     * scada产品编号
     */
    private String scadaProductCode;

    /**
     * scada设备编号
     */
    private String scadaEquipmentCode;

    /**
     * 设备运行状态
     */
    private String equipmentRunStatus;

    /**
     * 设备在线状态
     */
    private String equipmentOnlineStatus;

    /**
     * 最后请求时间
     */
    private Date lastRequestTime;

    /**
     * 设备scada运行状态
     */
    private String equipmentScadaRunStatus;

    /**
     * 设备scada在线状态
     */
    private String equipmentScadaOnlineStatus;

    /**
     * 最后scada数据更新时间
     */
    private Date lastScadaDataUpdateTime;

    /**
     * 最后scada设备上线时间
     */
    private Date lastScadaEquipmentUpTime;

    /**
     * 最后scada设备下线时间
     */
    private Date lastScadaEquipmentDownTime;


}
