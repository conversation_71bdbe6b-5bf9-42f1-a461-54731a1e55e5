package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 裁片货架 列表VO
 *
 * <AUTHOR>
 * @Date 2024-10-06 16:49:27
 * @Copyright zscbdic
 */

@Data
public class PartStationRackVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "所属仓库id")
    private Long warehouseId;

    @Schema(description = "货架编码")
    private String rackCode;

    @Schema(description = "位置")
    private String location;

}
