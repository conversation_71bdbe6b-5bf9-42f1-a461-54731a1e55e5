package net.lab1024.sa.admin.module.business.mes.part.station.move.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.lab1024.sa.admin.event.tailor.FeTicketTraceLogEvent;
import net.lab1024.sa.admin.module.business.mes.part.station.move.domain.form.PartStationStockMoveByBoxForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxInsideDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxInsideEntity;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.constant.FeTicketTraceLogTemplateEnum;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.constant.PartStationInventoryOptTypeEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationBinDao;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.dao.PartStationInventoryDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationBinEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.entity.PartStationInventoryEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.move.domain.form.PartStationStockMoveByTicketForm;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.manager.PartStationInventoryManager;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.dao.FeTicketDao;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.domain.entity.FeTicketEntity;
import net.lab1024.sa.base.common.code.UserErrorCode;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;

import java.util.List;

@Service
public class PartStationStockMoveService {

    @Resource
    private PartStationInventoryManager partStationInventoryManager;

    @Resource
    private PartStationBinDao partStationBinDao;

    @Resource
    private FeTicketDao feTicketDao;

    @Resource
    private PartStationInventoryDao partStationInventoryDao;

    @Resource
    private PartStationTurnBoxDao partStationTurnBoxDao;

    @Resource
    private PartStationTurnBoxInsideDao partStationTurnBoxInsideDao;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    public ResponseDTO<String> stockMove(PartStationStockMoveByTicketForm form) {
        Long binId = form.getBinId();
        Long feTicketId = form.getFeTicketId();

        FeTicketEntity ticket = feTicketDao.selectById(feTicketId);
        if (ticket == null) {
            return ResponseDTO.userErrorParam("菲票不存在");
        }

        PartStationBinEntity bin = partStationBinDao.selectById(binId);
        if (bin == null) {
            return ResponseDTO.userErrorParam("库位不存在");
        }

        Long count = partStationInventoryDao.selectCount(new LambdaQueryWrapper<PartStationInventoryEntity>()
                .eq(PartStationInventoryEntity::getFeTicketId, feTicketId));
        if (count <= 0) {
            return new ResponseDTO<>(UserErrorCode.PARAM_ERROR,
                    false,
                    "菲票未入库",
                    PartStationInventoryOptTypeEnum.IN.getValue());
        }

        String optDesc = String.format("裁片已移动至 库位【%s】", bin.getBinCode());
        partStationInventoryManager.stockMove(feTicketId, binId, optDesc);


        return ResponseDTO.ok("移库成功");

    }

    /**
     * 移库（周转箱）
     *
     * @param form
     * @return
     */
    public ResponseDTO<String> stockMoveByBox(PartStationStockMoveByBoxForm form) {
        Long binId = form.getBinId();
        Long turnBoxId = form.getTurnBoxId();

        PartStationTurnBoxEntity box = partStationTurnBoxDao.selectById(turnBoxId);
        if (box == null) {
            return ResponseDTO.userErrorParam("周转箱不存在");
        }
        PartStationBinEntity bin = partStationBinDao.selectById(binId);
        if (bin == null) {
            return ResponseDTO.userErrorParam("库位不存在");
        }

        if (!box.getInsideFlag()) {
            return new ResponseDTO<>(UserErrorCode.PARAM_ERROR, false, "周转箱未入库", PartStationInventoryOptTypeEnum.IN.getValue());
        }

        List<Long> ticketIds = partStationTurnBoxInsideDao.selectList(new LambdaQueryWrapper<PartStationTurnBoxInsideEntity>()
                        .eq(PartStationTurnBoxInsideEntity::getTurnBoxId, turnBoxId))
                .stream()
                .map(PartStationTurnBoxInsideEntity::getFeTicketId)
                .toList();
        List<PartStationInventoryEntity> invs = null;
        if (CollUtil.isNotEmpty(ticketIds)) {
            invs = partStationInventoryDao.selectList(new LambdaQueryWrapper<PartStationInventoryEntity>()
                    .in(PartStationInventoryEntity::getFeTicketId, ticketIds));
            if (invs.size() < ticketIds.size()) {
                return ResponseDTO.userErrorParam("存在未入库菲票");
            }
        }

        if (CollUtil.isEmpty(invs) || CollUtil.isEmpty(ticketIds)) {
            //没有库存数据，周转箱移库
            partStationInventoryManager.stockMoveByBox(null, turnBoxId, binId, null);
            return ResponseDTO.ok("移库成功");
        }

        List<Long> binIds = invs.stream().map(PartStationInventoryEntity::getBinId).distinct().toList();
        if (binIds.size() > 1) {
            return ResponseDTO.userErrorParam("该周转箱内菲票存在多个库位");
        }

        String optDesc = String.format("裁片已移动至 库位【%s】", bin.getBinCode());
        partStationInventoryManager.stockMoveByBox(ticketIds, turnBoxId, binId, optDesc);
        return ResponseDTO.ok("移库成功");
    }
}
