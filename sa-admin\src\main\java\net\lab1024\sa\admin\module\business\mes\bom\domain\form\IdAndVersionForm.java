package net.lab1024.sa.admin.module.business.mes.bom.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class IdAndVersionForm {
    @Schema(description = "Bom编号")
    @NotNull(message = "Bom编号 不能为空")
    private Integer id;

    @Schema(description = "Bom版本")
    @NotNull(message = "Bom版本 不能为空")
    private Integer versionNumber;
}
