package net.lab1024.sa.admin.module.business.mes.factory.manager;

import net.lab1024.sa.admin.module.business.mes.factory.dao.ProduceTeamMemberDao;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.ProduceTeamMemberEntity;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 生产小组成员  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:33:31
 * @Copyright zscbdic
 */
@Service
public class ProduceTeamMemberManager extends ServiceImpl<ProduceTeamMemberDao, ProduceTeamMemberEntity> {


}
