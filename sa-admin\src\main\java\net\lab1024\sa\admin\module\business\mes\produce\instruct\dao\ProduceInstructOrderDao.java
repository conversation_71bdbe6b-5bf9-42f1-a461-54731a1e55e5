package net.lab1024.sa.admin.module.business.mes.produce.instruct.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderVO;

import java.util.List;

/**
 * 生产指令单 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:22:24
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface ProduceInstructOrderDao extends BaseMapper<ProduceInstructOrderEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<ProduceInstructOrderVO> queryPage(Page page, @Param("queryForm") ProduceInstructOrderQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 更新完成数量
     * @param id 指令单id
     * @param num 数量
     */
    void updateFinishNum(@Param("id") Long id,@Param("num") Integer num);

    /**
     * 更新为完成状态
     * @param id
     */
    void updateFinish(@Param("id") Long id);
}
