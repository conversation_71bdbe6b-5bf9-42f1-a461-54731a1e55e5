package net.lab1024.sa.admin.module.business.mes.item.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

/**
 * 物料分类表 分页查询表单
 *
 * <AUTHOR>
 * @Date 2024-07-02 09:54:23
 * @Copyright zscbdic
 */

@Data
public class ItemTypeQueryForm extends PageParam{

    @Schema(description = "名称查询|可选")
    private String queryKey;

    @Schema(description = "父级类目id|可选")
    private Long parentId;
}
