package net.lab1024.sa.admin.event.tailor;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.context.ApplicationEvent;

import java.io.Serializable;
import java.util.List;

/**
 * 裁剪计划更新裁床事件
 */
@Getter
public class CutPlanUpdateBedEvent extends ApplicationEvent {

    private CutPlanUpdateBedBo bo;

    public CutPlanUpdateBedEvent(Object source, CutPlanUpdateBedBo bo) {
        super(source);
        this.bo = bo;
    }

    @Data
    public static class CutPlanUpdateBedBo implements Serializable {

        /**
         * 裁床id
         */
        private Long cutBedSheetId;

        /**
         * 颜色和对应铺布数量
         */
        private List<ColorBo> colors;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ColorBo implements Serializable{

        /**
         * 颜色
         */
        private String color;

        /**
         * 铺布数量
         */
        private Integer num;
    }
}
