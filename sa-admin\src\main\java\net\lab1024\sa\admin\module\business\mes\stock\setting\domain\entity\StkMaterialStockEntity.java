package net.lab1024.sa.admin.module.business.mes.stock.setting.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 物料库存属性 实体类
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:18:19
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_stk_material_stock")
public class StkMaterialStockEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 物料id
     */
    private Long materialId;

    /**
     * 作用范围;ALL所有仓库 ONE 单一仓库
     */
    private String scope;

    /**
     * 仓库限制;0停用 1启用
     */
    private Boolean warehouseLimit;

    /**
     * 仓库id;保留
     */
    private Long warehouseId;

    /**
     * 仓位限制;0停用 1启用
     */
    private Boolean locationLimit;

    /**
     * 仓位id;保留
     */
    private Long locationId;

    /**
     * 最小库存
     */
    private BigDecimal minStock;

    /**
     * 最大库存
     */
    private BigDecimal maxStock;

    /**
     * 安全库存
     */
    private BigDecimal safeStock;

    /**
     * 再订货点
     */
    private BigDecimal reorderGood;

    /**
     * 启用最小库存;0停用 1启用
     */
    private Boolean minStockFlag;

    /**
     * 启用最大库存;0停用 1启用
     */
    private Boolean maxStockFlag;

    /**
     * 启用安全库存;0停用 1启用
     */
    private Boolean safeStockFlag;

    /**
     * 启用再订货点;0停用 1启用
     */
    private Boolean reorderGoodFlag;

    /**
     * 批号管理;0停用 1启用
     */
    private Boolean lotManageFlag;

    /**
     * SN管理;0停用 1启用
     */
    private Boolean snManageFlag;

}
