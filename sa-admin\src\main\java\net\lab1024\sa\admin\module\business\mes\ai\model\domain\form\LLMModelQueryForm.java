package net.lab1024.sa.admin.module.business.mes.ai.model.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.lab1024.sa.base.common.domain.PageParam;

/**
 * 大模型表 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-04-17 11:25:27
 * @Copyright zscbdic
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class LLMModelQueryForm extends PageParam {

    @Schema(description = "模型类型")
    private String modelType;

    @Schema(description = "关键字查询")
    private String queryKey;

    @Schema(description = "工具调用能力")
    private Boolean useToolFlag;

    @Schema(description = "知识库调用能力")
    private Boolean knowledgeUseFlag;

    @Schema(description = "视觉调用能力")
    private Boolean visionUseFlag;

}
