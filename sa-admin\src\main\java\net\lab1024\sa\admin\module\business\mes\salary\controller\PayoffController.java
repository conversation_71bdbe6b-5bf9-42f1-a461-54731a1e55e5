package net.lab1024.sa.admin.module.business.mes.salary.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.PayoffForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.PayoffQueryForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.vo.PayoffVO;
import net.lab1024.sa.admin.module.business.mes.salary.service.PayoffService;
import net.lab1024.sa.admin.module.business.mes.work.domain.vo.WorkRecordVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 工资发放 Controller
 */
@RestController
@Tag(name = "工资发放接口")
public class PayoffController {

    @Resource
    private PayoffService payoffService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/payoff/queryPage")
    public ResponseDTO<PageResult<PayoffVO>> queryPage(@Valid @RequestBody PayoffQueryForm queryForm) {
        return ResponseDTO.ok(payoffService.queryPage(queryForm));
    }

    /**
     * 计件详情
     * @param queryForm 查询条件 employeeIds无用
     * @param employeeId 员工id
     * @return
     */
    @Operation(summary   = "计件详情 <AUTHOR>
    @PostMapping("/payoff/queryDetails/{employeeId}")
    public ResponseDTO<PageResult<WorkRecordVO>> queryDetails(@Valid @RequestBody PayoffQueryForm queryForm, @PathVariable("employeeId") Long employeeId) {
        return ResponseDTO.ok(payoffService.queryDetails(queryForm, employeeId));
    }

    /**
     * 发放
     * @param payoffForm
     * @return
     */
    @Operation(summary = "发放 <AUTHOR>
    @PostMapping("/payoff/payoff")
    public ResponseDTO<String> payoff(@Valid @RequestBody PayoffForm payoffForm) {
        return payoffService.payoff(payoffForm);
    }
}
