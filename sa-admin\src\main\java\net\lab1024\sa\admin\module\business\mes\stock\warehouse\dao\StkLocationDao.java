package net.lab1024.sa.admin.module.business.mes.stock.warehouse.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkLocationEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkLocationQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.vo.StkLocationVO;

import java.util.List;

/**
 * 货位 Dao
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:14:20
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface StkLocationDao extends BaseMapper<StkLocationEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<StkLocationVO> queryPage(Page page, @Param("queryForm") StkLocationQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 根据仓库id查询仓位id集合
     * @param warehouseId
     * @return
     */
    List<Long> queryIdsByWarehouseId(@Param("warehouseId")Long warehouseId);

}
