package net.lab1024.sa.admin.module.business.mes.salary.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.SettlementQueryForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.vo.SettlementVO;
import net.lab1024.sa.admin.module.business.mes.work.domain.vo.WorkRecordVO;

import java.util.List;

/**
 * 计件结算Dao
 */
@Mapper
@Component
public interface SettlementDao {

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    List<SettlementVO> queryPage(@Param("queryForm") SettlementQueryForm queryForm,@Param("offset") Long offset,@Param("limit") Long limit);



    /**
     * 分页查询总数
     * @param queryForm
     * @return
     */
    long queryPageCount(@Param("queryForm") SettlementQueryForm queryForm);

    /**
     * 查询结算明细
     * @param queryForm
     * @return
     */
    List<WorkRecordVO> querySettlementDetails(@Param("queryForm") SettlementQueryForm queryForm);
}
