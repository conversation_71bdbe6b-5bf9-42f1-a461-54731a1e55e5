package net.lab1024.sa.admin.module.business.mes.produce.instruct.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderArrangeEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderArrangeQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderArrangeVO;

import java.util.List;

/**
 * 指令单安排信息 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:26:34
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface ProduceInstructOrderArrangeDao extends BaseMapper<ProduceInstructOrderArrangeEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<ProduceInstructOrderArrangeVO> queryPage(Page page, @Param("queryForm") ProduceInstructOrderArrangeQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Integer id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Integer> idList,@Param("deletedFlag")boolean deletedFlag);

}
