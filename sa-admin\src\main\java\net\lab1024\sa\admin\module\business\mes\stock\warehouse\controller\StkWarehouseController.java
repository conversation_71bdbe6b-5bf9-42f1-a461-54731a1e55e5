package net.lab1024.sa.admin.module.business.mes.stock.warehouse.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.*;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.vo.StkWarehouseVO;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.service.StkWarehouseService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 仓库 Controller
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:13:18
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class StkWarehouseController {

    @Resource
    private StkWarehouseService stkWarehouseService;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/stkWarehouse/queryPage")
    public ResponseDTO<PageResult<StkWarehouseVO>> queryPage(@RequestBody @Valid StkWarehouseQueryForm queryForm) {
        return ResponseDTO.ok(stkWarehouseService.queryPage(queryForm));
    }

    /**
     * 添加
     *
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/stkWarehouse/add")
    public ResponseDTO<String> add(@RequestBody @Valid StkWarehouseAddForm addForm) {
        return stkWarehouseService.add(addForm);
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/stkWarehouse/update")
    public ResponseDTO<String> update(@RequestBody @Valid StkWarehouseUpdateForm updateForm) {
        return stkWarehouseService.update(updateForm);
    }

    /**
     * 单个删除
     *
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/stkWarehouse/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return stkWarehouseService.delete(id);
    }

    /**
     * 快速建仓
     */
    @Operation(summary = "快速建仓 <AUTHOR>
    @PostMapping("/stkWarehouse/quickAdd")
    public ResponseDTO<String> quickAdd(@RequestBody @Valid StkWarehouseQuickAddForm addForm) {
        return stkWarehouseService.quickAdd(addForm);
    }


    /**
     * 查询列表
     * @param query
     * @return
     */
    @Operation(summary = "查询列表")
    @PostMapping("/stkWarehouse/queryList")
    public ResponseDTO<List<StkWarehouseVO>> queryList(@RequestBody @Valid StkWarehouseQuery query) {
        return stkWarehouseService.queryList(query);
    }
}
