package net.lab1024.sa.admin.module.business.mes.stock.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.dao.StkInventoryLogDao;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.form.StkInventoryLogQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.vo.StkInventoryLogVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 即时库存更新日志 Service
 *
 * <AUTHOR>
 * @Date 2025-01-15 11:20:19
 * @Copyright zscbdic
 */

@Service
public class StkInventoryLogService {

    @Resource
    private StkInventoryLogDao stkInventoryLogDao;

    /*
     * 分页查询 即时库存更新日志
     */
    public ResponseDTO<PageResult<StkInventoryLogVO>> queryPage(StkInventoryLogQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<StkInventoryLogVO> list = stkInventoryLogDao.queryPage(page, queryForm);
        PageResult<StkInventoryLogVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return ResponseDTO.ok(pageResult);

    }
}
