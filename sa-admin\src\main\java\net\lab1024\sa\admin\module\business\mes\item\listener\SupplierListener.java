package net.lab1024.sa.admin.module.business.mes.item.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.event.base.SupplierDeleteCheckEvent;
import net.lab1024.sa.admin.module.business.mes.item.dao.ItemDao;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemEntity;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;

@Component
public class SupplierListener {

    @Resource
    private ItemDao itemDao;

    @EventListener(SupplierDeleteCheckEvent.class)
    public void supplierDeleteCheck(SupplierDeleteCheckEvent event) {
        Long count = itemDao.selectCount(new LambdaQueryWrapper<ItemEntity>()
                .eq(ItemEntity::getSupplierId, event.getId()));
        if (count > 0) {
            throw new BusinessException("该供应商正在被物料使用中，无法删除");
        }
    }
}
