package net.lab1024.sa.admin.module.business.mes.salary.controller;

import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.PayoffRecordQueryForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.vo.PayoffRecordVO;
import net.lab1024.sa.admin.module.business.mes.salary.service.PayoffRecordService;
import net.lab1024.sa.admin.module.business.mes.work.domain.vo.WorkRecordVO;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.util.SmartRequestUtil;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 薪酬发放记录表 Controller
 *
 * <AUTHOR>
 * @Date 2024-12-03 21:37:41
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class PayoffRecordController {

    @Resource
    private PayoffRecordService payoffRecordService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/payoffRecord/queryPage")
    public ResponseDTO<PageResult<PayoffRecordVO>> queryPage(@RequestBody @Valid PayoffRecordQueryForm queryForm) {
        return ResponseDTO.ok(payoffRecordService.queryPage(queryForm));
    }

    /**
     * 记录计件详情
     * @param id 发放记录id
     * @return
     */
    @Operation(summary = "计件详情 <AUTHOR>
    @PostMapping("/payoffRecord/queryDetails/{id}")
    public ResponseDTO<List<WorkRecordVO>> queryDetails(@PathVariable Long id) {
        return payoffRecordService.queryDetails(id);
    }

    /**
     * 取消发放
     * @param ids 薪酬发放记录ids
     * @return
     */
    @Operation(summary = "取消发放 <AUTHOR>
    @GetMapping("/payoffRecord/cancelPayoff")
    public ResponseDTO<String> cancelPayoff(@RequestParam("ids") ValidateList<Long> ids) {
        return payoffRecordService.cancelPayoff(ids);
    }


    /**
     * 个人工资条
     * @param queryForm
     * @return
     */
    @Operation(summary = "个人工资条")
    @PostMapping("/payoffRecord/personalSalary")
    public ResponseDTO<PageResult<PayoffRecordVO>> personalSalary(@RequestBody @Valid PayoffRecordQueryForm queryForm) {
        Long userId = SmartRequestUtil.getRequestUserId();
        queryForm.setEmployeeId(userId);
        return ResponseDTO.ok(payoffRecordService.queryPage(queryForm));
    }


}
