package net.lab1024.sa.admin.module.business.mes.item.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 物料分类表 新建表单
 *
 * <AUTHOR>
 * @Date 2024-07-02 09:54:23
 * @Copyright zscbdic
 */

@Data
public class ItemTypeAddForm {

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "父级id")
    private Long parentId;

    @Schema(description = "分类名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "分类名称 不能为空")
    private String name;

    @Schema(description = "排序")
    private Integer sort;

}
