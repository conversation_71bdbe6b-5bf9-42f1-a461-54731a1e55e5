package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationBinDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationRackDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationWarehouseDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationBinEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationRackEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationWarehouseEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationWarehouseAddForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationWarehouseQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationWarehouseQuickAddForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationWarehouseUpdateForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.vo.PartStationWarehouseVO;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.manager.PartStationBinManager;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.manager.PartStationWarehouseManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;

import java.util.ArrayList;
import java.util.List;

/**
 * 裁片仓库表 Service
 *
 * <AUTHOR>
 * @Date 2024-10-06 16:48:11
 * @Copyright zscbdic
 */

@Service
public class PartStationWarehouseService {

    @Resource
    private PartStationWarehouseDao partStationWarehouseDao;

    @Resource
    private PartStationWarehouseManager partStationWarehouseManager;

    @Resource
    private PartStationRackDao partStationRackDao;

    @Resource
    private PartStationBinDao partStationBinDao;

    @Resource
    private PartStationBinManager partStationBinManager;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<PartStationWarehouseVO> queryPage(PartStationWarehouseQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<PartStationWarehouseVO> list = partStationWarehouseDao.queryPage(page, queryForm);
        PageResult<PartStationWarehouseVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(PartStationWarehouseAddForm addForm) {
        partStationWarehouseManager.addCheck(addForm);

        PartStationWarehouseEntity partStationWarehouseEntity = SmartBeanUtil.copy(addForm, PartStationWarehouseEntity.class);
        if (CollUtil.isEmpty(addForm.getTaskerIds())) {
            partStationWarehouseEntity.setTaskerIds("[]");
        } else {
            partStationWarehouseEntity.setTaskerIds(JSON.toJSONString(addForm.getTaskerIds()));
        }

        partStationWarehouseDao.insert(partStationWarehouseEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(PartStationWarehouseUpdateForm updateForm) {
        partStationWarehouseManager.updateCheck(updateForm);

        PartStationWarehouseEntity partStationWarehouseEntity = SmartBeanUtil.copy(updateForm, PartStationWarehouseEntity.class);
        if (CollUtil.isEmpty(updateForm.getTaskerIds())) {
            partStationWarehouseEntity.setTaskerIds("[]");
        } else {
            partStationWarehouseEntity.setTaskerIds(JSON.toJSONString(updateForm.getTaskerIds()));
        }
        partStationWarehouseDao.updateById(partStationWarehouseEntity);
        return ResponseDTO.ok();
    }


    /**
     * 单个删除
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }

//        partStationWarehouseManager.deleteCheck(id);
        List<Long> binIds = partStationWarehouseDao.queryBinIds(id);
        if (CollUtil.isNotEmpty(binIds)) {
            partStationBinManager.deleteCheck(binIds);
        }


        partStationWarehouseDao.updateDeleted(id, true);
        partStationRackDao.delete(new LambdaQueryWrapper<PartStationRackEntity>().eq(PartStationRackEntity::getWarehouseId, id));
        if(CollUtil.isNotEmpty(binIds)){
            partStationBinDao.delete(new LambdaQueryWrapper<PartStationBinEntity>().in(PartStationBinEntity::getId, binIds));
        }
        return ResponseDTO.ok();
    }

    /**
     * 获取全部数据
     */
    public ResponseDTO<List<PartStationWarehouseVO>> getAll() {
        List<PartStationWarehouseEntity> list = partStationWarehouseDao.selectList(null);
        return ResponseDTO.ok(SmartBeanUtil.copyList(list, PartStationWarehouseVO.class));
    }

    /**
     * 快速建仓
     *
     * @param addForm
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> quickAdd(PartStationWarehouseQuickAddForm addForm) {
        PartStationWarehouseAddForm form = new PartStationWarehouseAddForm();
        form.setWarehouseCode(addForm.getWarehouseCode());
        partStationWarehouseManager.addCheck(form);

        PartStationWarehouseEntity warehouseEntity = addForm.toWarehouseEntity();
        partStationWarehouseDao.insert(warehouseEntity);

        List<String> rackCodes = new ArrayList<>(addForm.getRackNum());
        List<String> binCodes = new ArrayList<>(addForm.getRackNum() * addForm.getLayerNum() * addForm.getBinNum());
        for (int i = 1; i <= addForm.getRackNum(); i++) {
            String rackCode = warehouseEntity.getWarehouseCode() + "-" + i;
            rackCodes.add(rackCode);
            for (int j = 1; j <= addForm.getLayerNum(); j++) {
                String prefixBinCode = rackCode + "-" + j;
                for (int k = 1; k <= addForm.getBinNum(); k++) {
                    String binCode = prefixBinCode + "-" + k;
                    binCodes.add(binCode);
                }
            }
        }

        Long count = partStationRackDao.selectCount(new LambdaQueryWrapper<PartStationRackEntity>()
                .in(PartStationRackEntity::getRackCode, rackCodes));
        if (count > 0) {
            throw new BusinessException("货架编码重复");
        }
        count = partStationBinDao.selectCount(new LambdaQueryWrapper<PartStationBinEntity>()
                .in(PartStationBinEntity::getBinCode, binCodes));
        if (count > 0) {
            throw new BusinessException("货位编码重复");
        }

        for (int i = 1; i <= addForm.getRackNum(); i++) {
            String rackCode = warehouseEntity.getWarehouseCode() + "-" + i;
            PartStationRackEntity rack = new PartStationRackEntity();
            rack.setWarehouseId(warehouseEntity.getId());
            rack.setRackCode(rackCode);
            partStationRackDao.insert(rack);
            for (int j = 1; j <= addForm.getLayerNum(); j++) {
                String prefixBinCode = rackCode + "-" + j;
                for (int k = 1; k <= addForm.getBinNum(); k++) {
                    String binCode = prefixBinCode + "-" + k;
                    PartStationBinEntity bin = new PartStationBinEntity();
                    bin.setRackId(rack.getId());
                    bin.setBinCode(binCode);
                    bin.setCapacity(addForm.getCapacity());
                    partStationBinDao.insert(bin);
                }
            }
        }
//        for (int i = 1; i <= addForm.getRackNum(); i++) {
//            PartStationRackEntity entity = new PartStationRackEntity();
//            entity.setWarehouseId(warehouseEntity.getId());
//            entity.setRackCode(warehouseEntity.getWarehouseCode() + "-" + i);
////            System.out.println("货架："+ entity.getRackCode());
//            partStationRackDao.insert(entity);
//
//            for (int j = 1; j <= addForm.getLayerNum(); j++) {
//                PartStationBinEntity bin = new PartStationBinEntity();
//                bin.setRackId(entity.getId());
//                bin.setBinCode(entity.getRackCode() + "-" + j);
//                bin.setCapacity(addForm.getCapacity());
////                System.out.println("    货位："+ bin.getBinCode());
//                partStationBinDao.insert(bin);
//            }
//        }

        return ResponseDTO.ok();
    }

    /**
     * 获取单个数据
     *
     * @param id
     * @return
     */
    public PartStationWarehouseVO getById(Long id) {
        PartStationWarehouseEntity warehouse = partStationWarehouseManager.getById(id);
        if (warehouse == null) {
            throw new BusinessException("仓库不存在");
        }
        PartStationWarehouseVO warehouseVO = SmartBeanUtil.copy(warehouse, PartStationWarehouseVO.class);
        if (StrUtil.isNotBlank(warehouse.getTaskerIds())) {
            List<Long> taskerIds = JSON.parseArray(warehouse.getTaskerIds(), Long.class);
            warehouseVO.setTaskerIdList(taskerIds);
        }
        return warehouseVO;
    }
}
