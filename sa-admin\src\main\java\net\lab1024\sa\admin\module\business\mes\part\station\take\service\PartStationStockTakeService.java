package net.lab1024.sa.admin.module.business.mes.part.station.take.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.lab1024.sa.admin.event.tailor.FeTicketTraceLogEvent;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.constant.FeTicketTraceLogTemplateEnum;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.constant.PartStationInventoryOptTypeEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationBinDao;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.dao.PartStationInventoryDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationBinEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.entity.PartStationInventoryEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.take.domain.form.PartStationStockTakeForm;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.manager.PartStationInventoryManager;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.dao.FeTicketDao;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.domain.entity.FeTicketEntity;
import net.lab1024.sa.base.common.code.UserErrorCode;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;

import java.util.List;

@Service
public class PartStationStockTakeService {

    @Resource
    private FeTicketDao feTicketDao;

    @Resource
    private PartStationBinDao partStationBinDao;

    @Resource
    private PartStationInventoryManager partStationInventoryManager;

    @Resource
    private PartStationInventoryDao partStationInventoryDao;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 盘库
     * @param form
     * @return
     */
    public ResponseDTO<String> stockTake(PartStationStockTakeForm form) {
        Long binId = form.getBinId();
        Long feTicketId = form.getFeTicketId();

        FeTicketEntity ticket = feTicketDao.selectById(feTicketId);
        if (ticket == null) {
            return ResponseDTO.userErrorParam("菲票不存在");
        }

        PartStationBinEntity bin = partStationBinDao.selectById(binId);
        if (bin == null) {
            return ResponseDTO.userErrorParam("库位不存在");
        }

        PartStationInventoryEntity inventory = partStationInventoryDao.selectOne(new LambdaQueryWrapper<PartStationInventoryEntity>()
                .eq(PartStationInventoryEntity::getFeTicketId, feTicketId));
        if (inventory == null) {
            return new ResponseDTO<>(UserErrorCode.PARAM_ERROR,
                    false,
                    "菲票未入库",
                    PartStationInventoryOptTypeEnum.IN.getValue());
        }

        if(inventory.getBinId().equals(binId)){
            String optDesc = String.format("裁片已盘库至 库位【%s】", bin.getBinCode());
            partStationInventoryManager.stockTake(feTicketId, binId,optDesc);

            // 发布事件
            FeTicketTraceLogEvent.Payload payload = new FeTicketTraceLogEvent.Payload(feTicketId, FeTicketTraceLogTemplateEnum.PART_STATION_STOCK_TAKE.getContent(),
                    optDesc, null, null, null);
            FeTicketTraceLogEvent event = new FeTicketTraceLogEvent(this, List.of(payload));
            eventPublisher.publishEvent(event);

            return ResponseDTO.ok("已盘库");
        }else {
            return new ResponseDTO<>(UserErrorCode.PARAM_ERROR,
                    false,
                    "非本货位菲票",
                    PartStationInventoryOptTypeEnum.MOVE.getValue());
        }


    }
}
