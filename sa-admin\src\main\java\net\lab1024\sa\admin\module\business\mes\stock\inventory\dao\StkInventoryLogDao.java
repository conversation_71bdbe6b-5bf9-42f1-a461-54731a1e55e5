package net.lab1024.sa.admin.module.business.mes.stock.inventory.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.entity.StkInventoryLogEntity;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.form.StkInventoryLogQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.vo.StkInventoryLogVO;

import java.util.List;

/**
 * 即时库存更新日志 Dao
 *
 * <AUTHOR>
 * @Date 2025-01-15 11:20:19
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface StkInventoryLogDao extends BaseMapper<StkInventoryLogEntity> {


    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 分页查询
     * @param page
     * @param queryForm
     * @return
     */
    List<StkInventoryLogVO> queryPage(Page<?> page,@Param("queryForm") StkInventoryLogQueryForm queryForm);
}
