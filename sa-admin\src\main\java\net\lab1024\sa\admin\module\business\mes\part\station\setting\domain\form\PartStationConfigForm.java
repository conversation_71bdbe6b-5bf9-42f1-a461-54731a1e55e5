package net.lab1024.sa.admin.module.business.mes.part.station.setting.domain.form;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.constant.StorageManageModeConfigEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;

import java.util.List;

/**
 * 裁片驿站配置
 */
@Data
public class PartStationConfigForm {



    /**
     * 存储时间配置
     */
    @Valid
    @NotNull(message = "存储时间配置不能为空")
    private StorageTimeConfigForm storageTimeConfig;

    /**
     * 存储压力配置
     */
    @Valid
    @NotNull(message = "存储压力配置不能为空")
    private StoragePressureConfigForm storagePressureConfig;

    /**
     * 存储管理模式配置
     */
    @Valid
    @NotNull(message = "存储管理模式配置不能为空")
    private StorageManageModeConfigForm storageManageModeConfig;

    @Data
    public static class StorageTimeConfigForm {

        /**
         * 主键
         */
        @NotNull(message = "主键不能为空")
        private Long configId;

        /**
         * 是否启用
         */
        private Boolean enableFlag;

        /**
         * 最小存放时间
         */
        @NotNull(message = "最小存放时间不能为空")
        @Min(value = 1, message = "最小存放时间不能小于1天")
        private Integer minDay;

        /**
         * 最大存放时间
         */
        @NotNull(message = "最大存放时间不能为空")
        @Min(value = 1, message = "最大存放时间不能小于1天")
        private Integer maxDay;

        /**
         * 通知员工id
         */
        private List<Long> employeeIds;
    }

    @Data
    public static class StoragePressureConfigForm{
        /**
         * 主键
         */
        private Long configId;

        /**
         * 是否启用
         */
        private Boolean enableFlag;

        /**
         * 最大使用率 0-100
         */
        @NotNull(message = "最大使用率不能为空")
        @Min(value = 1, message = "最大使用率不能小于1")
        private Integer maxUsageRate;

        /**
         * 通知员工id
         */
        private List<Long> employeeIds;
    }

    @Data
    public static class StorageManageModeConfigForm{
        /**
         * 主键
         */
        private Long configId;

        /**
         * 存储管理模式
         */
        @CheckEnum(value = StorageManageModeConfigEnum.class, required = true,message = "存储管理模式非法")
        private String manageMode;
    }
}
