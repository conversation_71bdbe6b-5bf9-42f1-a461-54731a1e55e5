package net.lab1024.sa.admin.module.business.mes.salary.domain.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.salary.constant.SettlementWayEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 结算表单
 * <AUTHOR>
 */
@Data
public class SettlementForm {

    @NotNull(message = "员工id 不能为空")
    @NotEmpty(message = "员工id 不能为空")
    private List<Long> employeeIds;

    /**
     * 结算方式 1-日结 2-月结
     */
    @NotNull(message = "结算方式 不能为空")
    @CheckEnum(value = SettlementWayEnum.class, message = "结算方式 不能为空")
    private String settlementWay;

    /**
     * 归属日期 yyyy-MM
     */
    @JsonFormat(pattern = "yyyy-MM")
    @NotNull(message = "归属日期 不能为空")
    private Date belongDate;

    /**
     * 开始时间 yyyy-MM-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 结束时间 yyyy-MM-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
}
