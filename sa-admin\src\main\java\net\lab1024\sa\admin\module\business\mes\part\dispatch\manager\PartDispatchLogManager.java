package net.lab1024.sa.admin.module.business.mes.part.dispatch.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.stream.StreamUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.event.tailor.FeTicketTraceLogEvent;
import net.lab1024.sa.admin.module.business.mes.base.dao.SupplierDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.SupplierEntity;
import net.lab1024.sa.admin.module.business.mes.factory.dao.ProduceTeamDao;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.ProduceTeamEntity;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.constant.ActionEnum;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.constant.DispatchRangeEnum;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.entity.PartDispatchLogEntity;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.dao.PartDispatchLogDao;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.constant.FeTicketTraceLogTemplateEnum;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.dao.FeTicketDao;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.domain.entity.FeTicketEntity;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.entity.PartDispatchReceiveForm;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.form.PartDispatchSendForm;
import net.lab1024.sa.base.common.exception.BusinessException;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StreamUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 裁片收发日志  Manager
 *
 * <AUTHOR>
 * @Date 2024-11-06 15:25:13
 * @Copyright zscbdic
 */
@Service
public class PartDispatchLogManager extends ServiceImpl<PartDispatchLogDao, PartDispatchLogEntity> {


    @Resource
    private FeTicketDao feTicketDao;

    @Resource
    private SupplierDao supplierDao;

    @Resource
    private ProduceTeamDao produceTeamDao;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    public void sendCheck(PartDispatchSendForm sendForm) {
        Long supplierId = sendForm.getSupplierId();
        Long produceTeamId = sendForm.getProduceTeamId();
        if (supplierId == null && produceTeamId == null) {
            throw new BusinessException("下发对象不能为空");
        }
        if (supplierId != null && produceTeamId != null) {
            throw new BusinessException("下发对象只能选择一个");
        }
        Long count = this.lambdaQuery()
                .eq(PartDispatchLogEntity::getTicketId, sendForm.getTicketId())
                .eq(PartDispatchLogEntity::getActionStatus, ActionEnum.SEND.getValue()).count();
        if (count > 0) {
            throw new BusinessException("该菲票已下发");
        }

//        ActionEnum nowAction = this.getNowAction(sendForm.getTicketId());
//        if (nowAction == ActionEnum.SEND) {
//            throw new BusinessException("该菲票已下发");
//        }
    }

    public ActionEnum getNowAction(Long ticketId) {
        /**
         * 单菲票不允许多条发，可能情况
         * 1.没有收发记录
         * 2.只有收
         * 3.只有1条发
         * 4.1条发，多条收
         */
        List<PartDispatchLogEntity> list = this.lambdaQuery()
                .select(PartDispatchLogEntity::getActionStatus)
                .eq(PartDispatchLogEntity::getTicketId, ticketId)
                .list();
        if (CollUtil.isEmpty(list)) {
            return ActionEnum.NONE;
        }
        long sendCont = list.stream().filter(item -> item.getActionStatus().equals(ActionEnum.SEND.getValue()))
                .count();
        if (sendCont > 0) {
            return ActionEnum.SEND;
        } else {
            return ActionEnum.RECEIVE;
        }
    }

    public void receiveCheck(PartDispatchReceiveForm receiveForm) {
        Long count = this.lambdaQuery().eq(PartDispatchLogEntity::getTicketId, receiveForm.getTicketId())
                .eq(PartDispatchLogEntity::getActionStatus, ActionEnum.SEND.getValue())
                .count();
        if (count == 0) {
            throw new BusinessException("该菲票未下发");
        }

//        ActionEnum nowAction = this.getNowAction(receiveForm.getTicketId());
//        if (nowAction == ActionEnum.RECEIVE) {
//            throw new BusinessException("该菲票已回收");
//        } else if (nowAction == ActionEnum.NONE) {
//            throw new BusinessException("该菲票未下发");
//        }
    }

    /**
     * 转换为日志
     *
     * @param sendForm
     * @return
     */
    public PartDispatchLogEntity transformToLog(PartDispatchSendForm sendForm, RequestUser user) {
        List<PartDispatchLogEntity> list = this.transformToLog(List.of(sendForm), user);
        if (CollUtil.isEmpty(list)) {
            throw new BusinessException("日志转换失败");
        }
        return list.get(0);
    }

    /**
     * 转换为日志
     *
     * @param sendFormList
     * @return
     */
    public List<PartDispatchLogEntity> transformToLog(List<PartDispatchSendForm> sendFormList, RequestUser user) {
        List<Long> ticketIds = sendFormList.stream().map(PartDispatchSendForm::getTicketId).toList();
        List<Long> supplierIds = sendFormList.stream().map(PartDispatchSendForm::getSupplierId).filter(Objects::nonNull).toList();
        List<Long> produceTeamIds = sendFormList.stream().map(PartDispatchSendForm::getProduceTeamId).filter(Objects::nonNull).toList();
        if (CollUtil.isEmpty(ticketIds)) {
            throw new BusinessException("菲票不能为空");
        }
        if (CollUtil.isEmpty(supplierIds) && CollUtil.isEmpty(produceTeamIds)) {
            throw new BusinessException("下发对象不能为空");
        }
        // 查询菲票信息
        List<FeTicketEntity> tickets = feTicketDao.selectBatchIds(ticketIds);
        if (CollUtil.isEmpty(tickets)) {
            throw new BusinessException("菲票不存在");
        }
        Map<Long, FeTicketEntity> ticketMap = tickets.stream().collect(Collectors.toMap(FeTicketEntity::getId, t -> t));
        // 查询供应商信息
        Map<Long, SupplierEntity> supplierMap;
        if (CollUtil.isNotEmpty(supplierIds)) {
            List<SupplierEntity> suppliers = supplierDao.selectBatchIds(supplierIds);
            if (CollUtil.isEmpty(suppliers)) {
                throw new BusinessException("供应商不存在");
            }
            supplierMap = suppliers.stream().collect(Collectors.toMap(SupplierEntity::getId, s -> s));
        } else {
            supplierMap = null;
        }
        // 查询生产组信息
        Map<Long, ProduceTeamEntity> teamMap;
        if (CollUtil.isNotEmpty(produceTeamIds)) {
            List<ProduceTeamEntity> teams = produceTeamDao.selectBatchIds(produceTeamIds);
            if (CollUtil.isEmpty(teams)) {
                throw new BusinessException("生产组不存在");
            }
            teamMap = teams.stream().collect(Collectors.toMap(ProduceTeamEntity::getId, t -> t));
        } else {
            teamMap = null;
        }

        // 遍历封装
        List<PartDispatchLogEntity> logs = sendFormList.stream().map(sendForm -> {
            FeTicketEntity ticket = ticketMap.get(sendForm.getTicketId());
            if (ticket == null) {
                return null;
            }

            PartDispatchLogEntity log = BeanUtil.copyProperties(ticket, PartDispatchLogEntity.class,
                    "createTime", "updateTime", "createBy", "updateBy", "id");

            DispatchRangeEnum rangeEnum = null;
            String optDesc = null;
            if (sendForm.getSupplierId() != null && CollUtil.isNotEmpty(supplierMap)) {
                rangeEnum = DispatchRangeEnum.OUTSIDE;
                SupplierEntity supplier = supplierMap.get(sendForm.getSupplierId());
                if (supplier == null) {
                    return null;
                }
                optDesc = "发往供应商：【 " + supplier.getName() + " 】";
                log.setSupplierName(supplier.getName());
                log.setSupplierId(supplier.getId());
            } else if (CollUtil.isNotEmpty(teamMap) && sendForm.getProduceTeamId() != null) {
                rangeEnum = DispatchRangeEnum.INSIDE;
                ProduceTeamEntity team = teamMap.get(sendForm.getProduceTeamId());
                if (team == null) {
                    throw new BusinessException("生产组不存在");
                }
                optDesc = "发往生产组：【 " + team.getTeamName() + " 】";
                log.setProduceTeamName(team.getTeamName());
                log.setProduceTeamId(team.getId());
            }

            log.setTicketId(ticket.getId());
            log.setSenderId(user.getUserId());
            log.setSenderName(user.getUserName());
            log.setSendTime(LocalDateTime.now());
            log.setActionStatus(ActionEnum.SEND.getValue());
            log.setSendDesc(optDesc);
            log.setDispatchRange(rangeEnum.getValue());

            return log;

        }).filter(Objects::nonNull).toList();
        return logs;


    }

    /**
     * 收回
     *
     * @param receiveForm
     * @param user
     */
    public void receive(PartDispatchReceiveForm receiveForm, RequestUser user) {
        this.receive(List.of(receiveForm), user);
    }

    /**
     * 收回
     *
     * @param receiveForms
     * @param user
     */
    public void receive(List<PartDispatchReceiveForm> receiveForms, RequestUser user) {
        List<Long> ticketIds = receiveForms.stream().map(PartDispatchReceiveForm::getTicketId).toList();
        if (CollUtil.isEmpty(ticketIds)) {
            throw new BusinessException("菲票不能为空");
        }
        this.lambdaUpdate()
                .in(PartDispatchLogEntity::getTicketId, ticketIds)
                .eq(PartDispatchLogEntity::getActionStatus, ActionEnum.SEND.getValue())
                .set(PartDispatchLogEntity::getActionStatus, ActionEnum.RECEIVE.getValue())
                .set(PartDispatchLogEntity::getReceiverId, user.getUserId())
                .set(PartDispatchLogEntity::getReceiverName, user.getUserName())
                .set(PartDispatchLogEntity::getReceiveTime, LocalDateTime.now())
                .set(PartDispatchLogEntity::getReceiveDesc, "已回收")
                .update();

        // 发布菲票跟踪事件
        List<FeTicketTraceLogEvent.Payload> traceLogEventPayloads = receiveForms.stream().map(e -> {
            return new FeTicketTraceLogEvent.Payload(
                    e.getTicketId(),
                    FeTicketTraceLogTemplateEnum.PART_RECEIVE.getContent(),
                    "裁片已回收", null, null, null);
        }).toList();
        eventPublisher.publishEvent(new FeTicketTraceLogEvent(this, traceLogEventPayloads));
    }

    /**
     * 下发裁片
     *
     * @param log
     */
    @Transactional(rollbackFor = Exception.class)
    public void send(PartDispatchLogEntity log) {
        save(log);

        FeTicketTraceLogEvent.Payload traceEventPayload = new FeTicketTraceLogEvent.Payload(
                log.getTicketId(),
                FeTicketTraceLogTemplateEnum.PART_SEND.getContent(),
                log.getSendDesc(), null, null, null);
        eventPublisher.publishEvent(new FeTicketTraceLogEvent(this, List.of(traceEventPayload)));
    }
}
