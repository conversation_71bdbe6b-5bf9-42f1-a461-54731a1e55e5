package net.lab1024.sa.admin.module.business.mes.factory.domain.form;

import net.lab1024.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 工厂信息表 分页查询表单
 *
 * <AUTHOR>
 * @Date 2024-07-04 11:11:50
 * @Copyright zscbdic
 */

@Data
public class FactoryQueryForm extends PageParam{

    @Schema(description = "关键字查询")
    private String queryKey;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "邮政编码")
    private String postalCode;

    @Schema(description = "联系人")
    private String contact;

    @Schema(description = "电话")
    private String telephone;

}
