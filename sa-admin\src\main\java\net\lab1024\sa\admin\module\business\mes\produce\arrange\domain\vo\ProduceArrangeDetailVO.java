package net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 生产安排信息 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-22 18:44:38
 * @Copyright zscbdic
 */

@Data
public class ProduceArrangeDetailVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "主表id")
    private Long arrangeId;

    @Schema(description = "序号")
    private Integer serialNumber;

    @Schema(description = "节点名称")
    private String nodeName;

    @Schema(description = "负责人id")
    private Long headId;

    @Schema(description = "负责人名称")
    private String headName;

    @Schema(description = "末道节点;0否 1是")
    private Boolean endFlag;

}
