package net.lab1024.sa.admin.module.business.mes.factory.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.WorkshopAddForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.WorkshopQuery;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.WorkshopQueryForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.WorkshopUpdateForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.vo.WorkshopVO;
import net.lab1024.sa.admin.module.business.mes.factory.service.WorkshopService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 车间信息 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:31:51
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "车间信息接口")
public class WorkshopController {

    @Resource
    private WorkshopService workshopService;

    @Resource
    private SerialNumberService serialNumberService;
    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/workshop/queryPage")
    public ResponseDTO<PageResult<WorkshopVO>> queryPage(@RequestBody @Valid WorkshopQueryForm queryForm) {
        return ResponseDTO.ok(workshopService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/workshop/add")
    public ResponseDTO<String> add(@RequestBody @Valid WorkshopAddForm addForm) {
        return workshopService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/workshop/update")
    public ResponseDTO<String> update(@RequestBody @Valid WorkshopUpdateForm updateForm) {
        return workshopService.update(updateForm);
    }

//    @Operation(summary = "批量删除 <AUTHOR>
//    @PostMapping("/workshop/batchDelete")
//    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> idList) {
//        return workshopService.batchDelete(idList);
//    }

    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/workshop/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return workshopService.delete(id);
    }

    /**
     * 下拉列表
     * @return
     */
    @Operation(summary = "下拉列表 <AUTHOR>
    @PostMapping("/workshop/queryList")
    public ResponseDTO<List<WorkshopVO>> queryList(@RequestBody @Valid WorkshopQuery query){
        return workshopService.queryList(query);
    }


}
