package net.lab1024.sa.admin.module.business.mes.stock.setting.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * 物料库存属性 新建表单
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:18:19
 * @Copyright zscbdic
 */

@Data
public class StkMaterialStockAddForm {

    /**
     * 物料id
     */
    @Schema(description = "物料id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物料id 不能为空")
    private Long materialId;

    /**
     * 作用范围;ALL所有仓库 ONE 单一仓库
     */
    @Schema(description = "作用范围;ALL所有仓库 ONE 单一仓库", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "作用范围;ALL所有仓库 ONE 单一仓库 不能为空")
    private String scope;

    /**
     * 仓库限制;0停用 1启用
     */
    @Schema(description = "仓库限制;0停用 1启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "仓库限制;0停用 1启用 不能为空")
    private Boolean warehouseLimit;

    /**
     * 仓库id;保留
     */
//    @Schema(description = "仓库id;保留")
//    private Long warehouseId;

    /**
     * 仓位限制;0停用 1启用
     */
//    @Schema(description = "仓位限制;0停用 1启用", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "仓位限制;0停用 1启用 不能为空")
//    private Boolean locationLimit;

    /**
     * 仓位id;保留
     */
//    @Schema(description = "仓位id;保留")
//    private Long locationId;

    /**
     * 最小库存
     */
    @Schema(description = "最小库存")
    @Positive(message = "最小库存 不能小于0")
    private BigDecimal minStock;

    /**
     * 最大库存
     */
    @Schema(description = "最大库存")
    @Positive(message = "最大库存 不能小于0")
    private BigDecimal maxStock;

    /**
     * 安全库存
     */
    @Schema(description = "安全库存")
    @Positive(message = "安全库存 不能小于0")
    private BigDecimal safeStock;

    /**
     * 再订货点
     */
    @Schema(description = "再订货点")
    @Positive(message = "再订货点 不能小于0")
    private BigDecimal reorderGood;

    /**
     * 启用最小库存;0停用 1启用
     */
    @Schema(description = "启用最小库存;0停用 1启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "启用最小库存;0停用 1启用 不能为空")
    private Boolean minStockFlag;

    /**
     * 启用最大库存;0停用 1启用
     */
    @Schema(description = "启用最大库存;0停用 1启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "启用最大库存;0停用 1启用 不能为空")
    private Boolean maxStockFlag;

    /**
     * 启用安全库存;0停用 1启用
     */
    @Schema(description = "启用安全库存;0停用 1启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "启用安全库存;0停用 1启用 不能为空")
    private Boolean safeStockFlag;

    /**
     * 启用再订货点;0停用 1启用
     */
    @Schema(description = "启用再订货点;0停用 1启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "启用再订货点;0停用 1启用 不能为空")
    private Boolean reorderGoodFlag;

    /**
     * 批号管理;0停用 1启用
     */
    @Schema(description = "批号管理;0停用 1启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "批号管理;0停用 1启用 不能为空")
    private Boolean lotManageFlag;

    /**
     * SN管理;0停用 1启用
     */
    @Schema(description = "SN管理;0停用 1启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "SN管理;0停用 1启用 不能为空")
    private Boolean snManageFlag;

}
