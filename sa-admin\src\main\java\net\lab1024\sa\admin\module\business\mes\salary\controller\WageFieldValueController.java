package net.lab1024.sa.admin.module.business.mes.salary.controller;

import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.WageFieldValueUpdateForm;
import net.lab1024.sa.admin.module.business.mes.salary.service.WageFieldValueService;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.math.BigDecimal;

/**
 * 工资字段值 Controller
 *
 * <AUTHOR>
 * @Date 2024-11-25 20:28:37
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class WageFieldValueController {

    @Resource
    private WageFieldValueService wageFieldValueService;

    /**
     * 修改员工自定义工资项值
     * @param fieldValueId 字段值id
     * @param value 值
     * @return
     */
    @Operation(summary = "更新员工工资项值 <AUTHOR>
    @GetMapping("/wageFieldValue/updateValue")
    public ResponseDTO<String> update(@RequestParam("fieldValueId") Long fieldValueId, @RequestParam("value")BigDecimal value) {
        return wageFieldValueService.updateValue(fieldValueId,value);
    }

    /**
     * 批量调整员工工资项值
     * @param updateForm
     * @return
     */
    @Operation(summary = "批量调整员工工资项值")
    @PostMapping("/wageFieldValue/batchUpdateValue")
    public ResponseDTO<String> batchUpdateValue(@RequestBody @Valid WageFieldValueUpdateForm updateForm) {
        return wageFieldValueService.batchUpdateValue(updateForm);
    }
}
