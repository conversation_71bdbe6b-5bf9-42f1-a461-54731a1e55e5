package net.lab1024.sa.admin.module.business.mes.produce.instruct.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

@Getter
@AllArgsConstructor
public enum ProduceInstructOrderProduceTypeEnum implements BaseEnum {

    /**
     * 自产
     */
    SELF_PRODUCE("0", "自产"),

    /**
     * 自裁委外
     * @return
     */
    SELF_CUT_OUTSIDE("1", "自裁委外"),

    /**
     * 整件委外
     * @return
     */
    WHOLE_OUTSIDE("2", "整件委外");


    private final String value;

    private final String desc;
}
