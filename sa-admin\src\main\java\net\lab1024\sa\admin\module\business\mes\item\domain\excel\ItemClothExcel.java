package net.lab1024.sa.admin.module.business.mes.item.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class ItemClothExcel {

    @ExcelProperty(value = "物料名称")
    @Schema(description = "物料名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "物料名称 不能为空")
    private String name;


    /**
     * 规格型号;
     */
    @ExcelProperty(value = "规格型号")
    @Schema(description = "规格型号")
    private String model;

    /**
     * sku编号 无自动生成;
     */
    @ExcelProperty(value = "物料SKU编号")
    @Schema(description = "物料SKU编号", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "spu编号 不能为空")
    private String skuNumber;

    @ExcelProperty(value = "物料SPU编号")
    @Schema(description = "物料编号")
    @NotNull(message = "物料SPU编号 不能为空")
    @NotBlank(message = "物料SPU编号 不能为空")
    private String number;

    /**
     * 价格
     */
    @ExcelProperty(value = "价格")
    @Min(value = 0, message = "价格不能小于0")
    private BigDecimal price = BigDecimal.ZERO;

    /**
     * 单位id;关联t_unit
     */
    @ExcelProperty(value = "单位")
    @Schema(description = "单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "单位 不能为空")
    private String unitName;

    /**
     * 单位id;
     */
    @ExcelIgnore
    private Long unitId;

    /**
     * 停用标识;0启用，1停用
     */
    @Schema(description = "停用标识;0启用，1停用")
//    @NotNull(message = "停用标识;0启用，1停用 不能为空")
    @ExcelIgnore
    private Boolean enableFlag;

    /**
     * 类型;0半成品 1成品
     */
//    @Schema(description = "类型;0半成品 1成品", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "类型;0半成品 1成品 不能为空")
//    @CheckEnum(value = ItemCategoryEnum.class, message = "类型;0半成品 1成品 值不合法")
    @ExcelIgnore
    private String category;

    /**
     * 属性;0面料，1其他，2成衣
     */
    @Schema(description = "属性;0面料，1其他，2成衣", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "属性;0面料，1其他，2成衣 不能为空")
//    @CheckEnum(value = ItemAttributeEnum.class, message = "属性;0面料，1其他，2成衣 值不合法")
    @ExcelIgnore
    private String attribute;

    //----------------------------------------------------------


    /**
     * 克重;克每平方米
     */
    @ExcelProperty(value = "克重")
    @Schema(description = "克重;克每平方米", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "克重;克每平方米 不能为空")
    @Min(value = 1, message = "克重;克每平方米 最小值为1")
    private Integer gramWeight;

    /**
     * 幅宽;厘米
     */
    @ExcelProperty(value = "幅宽")
    @Schema(description = "幅宽;厘米", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "幅宽;厘米 不能为空")
    @Min(value = 1, message = "幅宽;厘米 最小值为1")
    private Integer width;

    /**
     * 布重;千克
     */
    @ExcelProperty(value = "布重")
    @Schema(description = "布重;千克")
    @Min(value = 1, message = "布重;千克 最小值为1")
    private Double weight;

    /**
     * 布长;米
     */
    @ExcelProperty(value = "布长")
    @Schema(description = "布长;米")
    @Min(value = 1, message = "布长;米 最小值为1")
    private Double length;

    /**
     * 成分
     */
    @ExcelProperty(value = "成分")
    @Schema(description = "成分")
    private String ingredient;


    /**
     * 颜色编号
     */
    @ExcelProperty(value = "色号")
    @Schema(description = "色号")
    private String colorNum;

    /**
     * 颜色名称
     */
    @ExcelProperty(value = "颜色名称")
    @NotBlank(message = "颜色名称不能为空")
    @Schema(description = "颜色名称")
    private String colorName;


}
