package net.lab1024.sa.admin.module.business.mes.stock.warehouse.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.dao.StkLocationDao;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkLocationEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkLocationAddForm;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkLocationQuery;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkLocationQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkLocationUpdateForm;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.vo.StkLocationVO;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.manager.StkLocationManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 货位 Service
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:14:20
 * @Copyright zscbdic
 */

@Service
public class StkLocationService {

    @Resource
    private StkLocationDao stkLocationDao;

    @Resource
    private StkLocationManager stkLocationManager;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<StkLocationVO> queryPage(StkLocationQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<StkLocationVO> list = stkLocationDao.queryPage(page, queryForm);
        PageResult<StkLocationVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(StkLocationAddForm addForm) {
        stkLocationManager.addCheck(addForm);
        StkLocationEntity stkLocationEntity = SmartBeanUtil.copy(addForm, StkLocationEntity.class);
        stkLocationDao.insert(stkLocationEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(StkLocationUpdateForm updateForm) {
        stkLocationManager.updateCheck(updateForm);
        StkLocationEntity stkLocationEntity = SmartBeanUtil.copy(updateForm, StkLocationEntity.class);
        stkLocationDao.updateById(stkLocationEntity);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }
        stkLocationManager.deleteCheck(id);
        stkLocationDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    public ResponseDTO<List<StkLocationVO>> queryList(StkLocationQuery query) {
        List<Long> locationIds = null;
        if (query.getWarehouseId() != null) {
            locationIds = stkLocationDao.queryIdsByWarehouseId(query.getWarehouseId());
        }

        List<StkLocationEntity> list = stkLocationDao.selectList(new LambdaQueryWrapper<StkLocationEntity>()
                .like(StrUtil.isNotBlank(query.getLocationNumber()), StkLocationEntity::getNumber, query.getLocationNumber())
                .in(CollUtil.isNotEmpty(locationIds), StkLocationEntity::getId, locationIds));
        return ResponseDTO.ok(SmartBeanUtil.copyList(list, StkLocationVO.class));
    }
}
