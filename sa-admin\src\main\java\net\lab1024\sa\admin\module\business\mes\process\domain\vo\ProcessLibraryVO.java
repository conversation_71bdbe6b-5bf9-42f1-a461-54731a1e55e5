package net.lab1024.sa.admin.module.business.mes.process.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;
import net.bytebuddy.agent.builder.AgentBuilder;

import jakarta.validation.Valid;

/**
 * 工序库 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-13 15:19:57
 * @Copyright zscbdic
 */

@Data
public class ProcessLibraryVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "工艺库编号码")
    private String processLibraryNumber;

    @Schema(description = "停用标识;0启用，1停用")
    private Boolean enableFlag;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "详细工序信息")
    @Valid
    private List<ProcessLibraryDetailsVO> processLibraryDetailsVOList;

}
