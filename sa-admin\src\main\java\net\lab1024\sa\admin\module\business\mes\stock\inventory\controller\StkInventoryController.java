package net.lab1024.sa.admin.module.business.mes.stock.inventory.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.form.StkInventoryQuery;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.form.StkInventoryQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.vo.StkInventoryVO;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.service.StkInventoryService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 即时库存 Controller
 *
 * <AUTHOR>
 * @Date 2025-01-14 15:20:23
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class StkInventoryController {

    @Resource
    private StkInventoryService stkInventoryService;

    /**
     * 即时库存分页查询
     *
     * @param queryForm
     * @return
     */
    @Operation(summary = "即时库存分页查询")
    @PostMapping("/stkInventory/queryInventoryPage")
    public ResponseDTO<PageResult<StkInventoryVO>> queryInventoryPage(@RequestBody @Valid StkInventoryQueryForm queryForm) {
        return stkInventoryService.queryInventoryPage(queryForm);
    }

    /**
     * 多维库存分页查询
     *
     * @param queryForm
     * @return
     */
    @Operation(summary = "多维库存分页查询")
    @PostMapping("/stkInventory/multiBalanceInventoryPage")
    public ResponseDTO<PageResult<StkInventoryVO>> queryMultiBalanceInventoryPage(@RequestBody @Valid StkInventoryQueryForm queryForm) {
        return stkInventoryService.queryMultiBalanceInventoryPage(queryForm);
    }

    /**
     * 单物料库存查询
     * @param query
     * @return
     */
    @Operation(summary = "物料库存")
    @PostMapping("/stkInventory/queryMaterialInventory")
    public ResponseDTO<StkInventoryVO> queryMaterialInventory(@RequestBody @Valid StkInventoryQuery query) {
        return stkInventoryService.queryMaterialInventory(query);
    }


}
