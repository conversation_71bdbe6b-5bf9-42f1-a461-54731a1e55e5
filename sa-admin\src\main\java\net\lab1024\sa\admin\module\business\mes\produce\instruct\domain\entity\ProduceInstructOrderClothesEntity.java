package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 指令单成衣信息 实体类
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:25:32
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_produce_instruct_order_clothes")
public class ProduceInstructOrderClothesEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 指令单id
     */
    private Long orderId;

    /**
     * 物料id
     */
    private Long itemId;

    /**
     * 款式颜色
     */
    private String styleColor;

    /**
     * 尺码
     */
    private String size;

    /**
     * 数量
     */
    private Integer num;

}
