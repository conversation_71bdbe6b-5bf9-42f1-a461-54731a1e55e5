package net.lab1024.sa.admin.module.business.mes.stock.lot.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.stock.lot.dao.StkLotMaterialTraceDao;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.form.StkLotMaterialTraceQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.vo.StkLotMaterialTraceVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 批号跟踪信息 Service
 *
 * <AUTHOR>
 * @Date 2025-02-08 19:57:37
 * @Copyright zscbdic
 */

@Service
public class StkLotMaterialTraceService {

    @Resource
    private StkLotMaterialTraceDao stkLotMaterialTraceDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<StkLotMaterialTraceVO> queryPage(StkLotMaterialTraceQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<StkLotMaterialTraceVO> list = stkLotMaterialTraceDao.queryPage(page, queryForm);
        PageResult<StkLotMaterialTraceVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }


    public PageResult<StkLotMaterialTraceVO> queryPageWithExtra(StkLotMaterialTraceQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<StkLotMaterialTraceVO> list = stkLotMaterialTraceDao.queryPageWithExtra(page, queryForm);
        PageResult<StkLotMaterialTraceVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }
}
