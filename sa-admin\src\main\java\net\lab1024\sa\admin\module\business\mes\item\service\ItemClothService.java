package net.lab1024.sa.admin.module.business.mes.item.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.event.item.ItemDeleteCheckEvent;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.UnitEntity;
import net.lab1024.sa.admin.module.business.mes.base.manager.UnitManager;
import net.lab1024.sa.admin.module.business.mes.common.excel.service.ExcelBaseService;
import net.lab1024.sa.admin.module.business.mes.common.qrcode.QrCodeTypeEnum;
import net.lab1024.sa.admin.module.business.mes.common.qrcode.utils.QrCodeTypeUtil;
import net.lab1024.sa.admin.module.business.mes.item.constant.ItemAttributeEnum;
import net.lab1024.sa.admin.module.business.mes.item.constant.ItemCategoryEnum;
import net.lab1024.sa.admin.module.business.mes.item.dao.ItemClothDao;
import net.lab1024.sa.admin.module.business.mes.item.dao.ItemDao;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemClothEntity;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemEntity;
import net.lab1024.sa.admin.module.business.mes.item.domain.excel.ItemClothExcel;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemClothAddForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemClothQueryForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemClothUpdateForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.vo.ItemClothVO;
import net.lab1024.sa.admin.module.business.mes.item.manager.ItemClothManager;
import net.lab1024.sa.admin.module.business.mes.item.manager.ItemManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 布料 service
 */
@Service
public class ItemClothService {

    @Resource
    private SerialNumberService serialNumberService;

    @Resource
    private ItemManager itemManager;

    @Resource
    private ItemDao itemDao;

    @Resource
    private ItemClothDao itemClothDao;

    @Resource
    private ItemClothManager itemClothManager;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private ExcelBaseService excelBaseService;

    @Resource
    private UnitManager unitManager;

    /**
     * 添加
     *
     * @param addForm
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(ItemClothAddForm addForm) {
        int count = 0;
        itemClothManager.spuNumberCheck(addForm.getNumber());

        long colorCount = addForm.getSkuAttributes().stream().map(ItemClothAddForm.ItemClothSkuAddBo::getColorName).distinct().count();
        if (colorCount != addForm.getSkuAttributes().size()) {
            return ResponseDTO.userErrorParam("颜色名称不能重复");
        }

        for (ItemClothAddForm.ItemClothSkuAddBo skuAttribute : addForm.getSkuAttributes()) {
            ItemEntity item = SmartBeanUtil.copy(addForm, ItemEntity.class);
            if (CharSequenceUtil.isBlank(skuAttribute.getSkuNumber())) {
                item.setSkuNumber(addForm.getNumber() + "." + (++count));
            } else {
                item.setSkuNumber(skuAttribute.getSkuNumber());
            }
            itemManager.skuNumberCheck(skuAttribute.getSkuNumber(), null);

            item.setPrice(skuAttribute.getPrice());
            item.setImgUrl(skuAttribute.getImgUrl());
            item.setModel(CharSequenceUtil.isBlank(item.getModel()) ? skuAttribute.getColorName() : item.getModel() + "|" + skuAttribute.getColorName());

            ItemClothEntity itemCloth = SmartBeanUtil.copy(addForm, ItemClothEntity.class);
            itemCloth.setColorName(skuAttribute.getColorName());
            itemCloth.setColorNum(skuAttribute.getColorNum());
            itemCloth.setReferColor(skuAttribute.getReferColor());

            itemDao.insert(item);
            itemCloth.setItemId(item.getId());
            itemClothDao.insert(itemCloth);
        }
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(ItemClothUpdateForm updateForm) {
        ItemEntity itemEntity = itemDao.selectById(updateForm.getId());
        if (null == itemEntity) {
            return ResponseDTO.userErrorParam("物料不存在");
        }
        itemClothManager.spuNumberCheck(updateForm.getNumber());

        // 校验物料编号sku
        itemManager.skuNumberCheck(updateForm.getSkuNumber(), Lists.newArrayList(updateForm.getId()));
        //校验颜色
        itemClothManager.colorCheck(updateForm.getNumber(), updateForm.getColorName(), updateForm.getId());

        ItemEntity item = SmartBeanUtil.copy(updateForm, ItemEntity.class);
        ItemClothEntity itemCloth = SmartBeanUtil.copy(updateForm, ItemClothEntity.class);
        // 设置物料型号
        if(CharSequenceUtil.isBlank(item.getModel())){
            item.setModel(updateForm.getColorName());
        }

        itemCloth.setItemId(updateForm.getId());
        itemCloth.setId(null);
        // 更新
        transactionTemplate.execute(s -> {
            itemDao.updateById(item);
            itemClothDao.update(itemCloth, new LambdaQueryWrapper<ItemClothEntity>().eq(ItemClothEntity::getItemId, updateForm.getId()));

            return null;
        });

        return ResponseDTO.ok();
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    public ResponseDTO<ItemClothVO> getById(Long id) {
        ItemEntity itemEntity = itemManager.getById(id);
        if (null == itemEntity) {
            return ResponseDTO.userErrorParam("物料不存在");
        }
        ItemClothEntity itemClothEntity = itemClothDao.selectOne(new LambdaQueryWrapper<ItemClothEntity>().eq(ItemClothEntity::getItemId, id)
                .last("limit 1"));
        ItemClothVO vo = SmartBeanUtil.copy(itemClothEntity, ItemClothVO.class);
        SmartBeanUtil.copyProperties(itemEntity, vo);
        vo.setId(id);
        return ResponseDTO.ok(vo);
    }

    public ResponseDTO<PageResult<ItemClothVO>> queryPage(ItemClothQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ItemClothVO> list = itemClothDao.queryClothPage(page, queryForm);
        if (CollUtil.isEmpty(list)) {
            return ResponseDTO.ok(SmartPageUtil.emptyPage(queryForm.getPageNum(), queryForm.getPageSize()));
        }

        // 查询单位
        List<Long> unitIds = list.stream().map(ItemClothVO::getUnitId).toList();
        Map<Long, String> unitMap = unitManager.listByIds(unitIds).stream().collect(Collectors.toMap(UnitEntity::getId, UnitEntity::getName));
        list.forEach(e -> e.setUnitName(unitMap.get(e.getUnitId())));

        PageResult<ItemClothVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return ResponseDTO.ok(pageResult);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long id) {
        eventPublisher.publishEvent(new ItemDeleteCheckEvent(this, id));

        itemDao.updateDeleted(id, true);
        itemClothDao.delete(new LambdaQueryWrapper<ItemClothEntity>().eq(ItemClothEntity::getItemId, id));
        return ResponseDTO.ok();
    }

    public ResponseDTO<String> importExcel(MultipartFile file) {
        AtomicInteger count = new AtomicInteger(0);
        excelBaseService.importExcel(file, ItemClothExcel.class, 1,
                e -> {
                    //数据校验与处理
                    String verify = SmartBeanUtil.verify(e);
                    if (verify != null) {
                        throw new BusinessException(verify);
                    }

                    if (CharSequenceUtil.isBlank(e.getSkuNumber())) {
                        e.setSkuNumber(e.getNumber() + "." + count.incrementAndGet());
                    }

                    itemClothManager.spuNumberCheck(e.getNumber());
                    itemManager.skuNumberCheck(e.getSkuNumber(), null);

                    if (e.getPrice() == null) {
                        e.setPrice(BigDecimal.ZERO);
                    }
                    e.setAttribute(ItemAttributeEnum.CLOTH.getValue());
                    e.setEnableFlag(false);
                    e.setCategory(ItemCategoryEnum.FINISHED_PRODUCT.getValue());
                    e.setModel(CharSequenceUtil.isBlank(e.getModel()) ? e.getColorName() : e.getModel() + "|" + e.getColorName());
                    return e;
                }, list -> {
                    Map<String, List<ItemClothExcel>> spuList = list.stream().collect(Collectors.groupingBy(ItemClothExcel::getNumber));
                    spuList.forEach((k, v) -> {
                        long colorCount = v.stream().map(ItemClothExcel::getColorName).distinct().count();
                        if (colorCount != v.size()) {
                            throw new BusinessException("同spu下颜色名称不能重复");
                        }
                    });

                    //处理单位
                    List<String> unitNames = list.stream().map(ItemClothExcel::getUnitName).distinct().toList();
                    List<UnitEntity> units = unitManager.lambdaQuery().in(UnitEntity::getName, unitNames).list();
                    if (CollUtil.isEmpty(units) || units.size() != unitNames.size()) {
                        throw new BusinessException("单位名称不存在");
                    }
                    Map<String, Long> unitMap = units.stream().collect(Collectors.toMap(UnitEntity::getName, UnitEntity::getId));
                    list.forEach(e -> e.setUnitId(unitMap.get(e.getUnitName())));


                    List<ItemEntity> itemEntities = SmartBeanUtil.copyList(list, ItemEntity.class);
                    List<ItemClothEntity> clothEntities = SmartBeanUtil.copyList(list, ItemClothEntity.class);

                    transactionTemplate.execute(s -> {
                        for (int i = 0; i < itemEntities.size(); i++) {
                            itemManager.save(itemEntities.get(i));
                            clothEntities.get(i).setItemId(itemEntities.get(i).getId());
                            itemClothDao.insert(clothEntities.get(i));
                        }
                        return null;
                    });


                });
        return ResponseDTO.ok();
    }

    public ResponseDTO<String> getQrCodeContent(String clothId) {
        String qrCodeString = QrCodeTypeUtil.toQrCodeString(QrCodeTypeEnum.MATERIAL, clothId);
        return ResponseDTO.ok(qrCodeString);
    }
}
