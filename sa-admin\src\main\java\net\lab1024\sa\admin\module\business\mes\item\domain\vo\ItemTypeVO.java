package net.lab1024.sa.admin.module.business.mes.item.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 物料分类表 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-02 09:54:23
 * @Copyright zscbdic
 */

@Data
public class ItemTypeVO {

    @Schema(description = "主键")
    private Long id;


    @Schema(description = "父级id")
    private Long parentId;

    @Schema(description = "分类名称")
    private String name;


    @Schema(description = "排序")
    private Integer sort;

}
