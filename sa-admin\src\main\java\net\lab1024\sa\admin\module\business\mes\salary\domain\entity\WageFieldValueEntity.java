package net.lab1024.sa.admin.module.business.mes.salary.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 工资字段值 实体类
 *
 * <AUTHOR>
 * @Date 2024-11-25 20:28:37
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_salary_wage_field_value")
@AllArgsConstructor
@NoArgsConstructor
public class WageFieldValueEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 价格
     */
    private BigDecimal value;

    /**
     * 字段id
     */
    private Long fieldId;

    /**
     * 员工id
     */
    private Long employeeId;


    public WageFieldValueEntity(Long fieldId, Long employeeId, BigDecimal value){
        this.fieldId = fieldId;
        this.employeeId = employeeId;
        this.value = value;
    }

}
