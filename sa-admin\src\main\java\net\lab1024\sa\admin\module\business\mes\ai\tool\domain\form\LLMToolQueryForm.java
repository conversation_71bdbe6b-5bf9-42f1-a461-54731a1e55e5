package net.lab1024.sa.admin.module.business.mes.ai.tool.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.lab1024.sa.base.common.domain.PageParam;

/**
 * 大模型工具表 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-04-05 14:27:51
 * @Copyright zscbdic
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class LLMToolQueryForm extends PageParam {

    @Schema(description = "关键字查询")
    private String queryKey;

    @Schema(description = "启用状态")
    private Boolean enableFlag;

    @Schema(description = "工具类型")
    private String type;

}
