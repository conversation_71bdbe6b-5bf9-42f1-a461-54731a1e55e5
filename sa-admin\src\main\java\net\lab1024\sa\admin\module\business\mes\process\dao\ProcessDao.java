package net.lab1024.sa.admin.module.business.mes.process.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.process.domain.entity.ProcessEntity;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessQueryForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.vo.ProcessVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 工序信息 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-13 15:19:57
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface ProcessDao extends BaseMapper<ProcessEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<ProcessVO> queryPage(Page page, @Param("queryForm") ProcessQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

}
