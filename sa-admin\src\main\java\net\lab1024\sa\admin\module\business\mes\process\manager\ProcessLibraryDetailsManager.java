package net.lab1024.sa.admin.module.business.mes.process.manager;

import net.lab1024.sa.admin.module.business.mes.process.domain.entity.ProcessLibraryDetailsEntity;
import net.lab1024.sa.admin.module.business.mes.process.dao.ProcessLibraryDetailsDao;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 工序库详情工序  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-13 15:19:57
 * @Copyright zscbdic
 */
@Service
public class ProcessLibraryDetailsManager extends ServiceImpl<ProcessLibraryDetailsDao, ProcessLibraryDetailsEntity> {


}
