package net.lab1024.sa.admin.module.business.mes.base.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.event.base.SupplierDeleteCheckEvent;
import net.lab1024.sa.admin.module.business.mes.base.dao.SupplierDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.SupplierEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SupplierAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SupplierQuery;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SupplierQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SupplierUpdateForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.SupplierVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 供应商表 Service
 *
 * <AUTHOR>
 * @Date 2024-07-03 09:43:14
 * @Copyright zscbdic
 */

@Service
public class SupplierService {

    @Resource
    private SupplierDao supplierDao;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<SupplierVO> queryPage(SupplierQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<SupplierVO> list = supplierDao.queryPage(page, queryForm);
        PageResult<SupplierVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(SupplierAddForm addForm) {
        if (isRepeat(addForm.getName(), false)) {
            return ResponseDTO.userErrorParam("供应商名称重复");
        }
        SupplierEntity supplierEntity = SmartBeanUtil.copy(addForm, SupplierEntity.class);
        supplierDao.insert(supplierEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(SupplierUpdateForm updateForm) {
        if (isRepeat(updateForm.getName(), true)) {
            return ResponseDTO.userErrorParam("供应商名称重复");
        }
        SupplierEntity supplierEntity = SmartBeanUtil.copy(updateForm, SupplierEntity.class);
        supplierDao.updateById(supplierEntity);
        return ResponseDTO.ok();
    }


    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }
        eventPublisher.publishEvent(new SupplierDeleteCheckEvent(this, id));

        supplierDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }

    /**
     * 获取全部
     *
     * @param queryForm
     * @return
     */
    public List<SupplierVO> queryAll(SupplierQuery queryForm) {

        LambdaQueryWrapper<SupplierEntity> lq = new LambdaQueryWrapper<>();
        boolean notBlank = StrUtil.isNotBlank(queryForm.getQueryKey());
        if (notBlank) {
            lq.and(q -> {
                q.like(notBlank, SupplierEntity::getName, queryForm.getQueryKey())
                        .like(notBlank, SupplierEntity::getFastName, queryForm.getQueryKey());

            });
        }
        lq.eq(queryForm.getEnableFlag() != null, SupplierEntity::getEnableFlag, queryForm.getEnableFlag());


        List<SupplierEntity> list = supplierDao.selectList(lq);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<SupplierVO> supplierVOS = SmartBeanUtil.copyList(list, SupplierVO.class);
        return supplierVOS;

    }

    /**
     * 重复字段校验
     *
     * @param field    要校验的字段
     * @param isUpdate 是否为更新
     * @return 重复返回true
     */
    public boolean isRepeat(String field, boolean isUpdate) {
        Integer num = isUpdate ? 1 : 0;
        LambdaQueryWrapper<SupplierEntity> wrapper = new LambdaQueryWrapper<>();
        Long count = supplierDao.selectCount(wrapper.eq(SupplierEntity::getName, field));
        return count > num;
    }
}
