package net.lab1024.sa.admin.module.business.mes.stock.inventory.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.entity.StkInventoryEntity;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.form.StkInventoryQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.vo.StkInventoryVO;

import java.util.List;

/**
 * 即时库存 Dao
 *
 * <AUTHOR>
 * @Date 2025-01-14 15:20:23
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface StkInventoryDao extends BaseMapper<StkInventoryEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<StkInventoryVO> queryInventorPage(@Param("queryForm") StkInventoryQueryForm queryForm, @Param("offset") Long offset, @Param("limit") Long limit);

    /**
     * 分页 查询总数
     *
     * @param queryForm
     * @return
     */
    Long queryInventorPageCount(@Param("queryForm") StkInventoryQueryForm queryForm);


    /**
     * 多维库存分页查询
     *
     * @param queryForm
     * @param offset
     * @param limit
     * @return
     */
    List<StkInventoryVO> queryMultiBalanceInventoryPage(@Param("queryForm") StkInventoryQueryForm queryForm, @Param("offset") Long offset, @Param("limit") Long limit);

    /**
     * 多维库存分页查询总数
     * @param queryForm
     * @return
     */
    Long queryMultiBalanceInventoryPageCount(@Param("queryForm") StkInventoryQueryForm queryForm);

    /**
     * 查询合并仓位库存数据
     *
     * @param warehouseId
     * @return
     */
    List<StkInventoryEntity> queryMergeLocationInventory(@Param("warehouseId") Long warehouseId);


    /**
     * 查询合并批次库存数据
     *
     * @param materielId
     * @return
     */
    List<StkInventoryEntity> queryMergeLotInventory(@Param("materielId") Long materielId);


    /**
     * 查询库存数据
     *
     * @param list
     * @return
     */
    List<StkInventoryEntity> queryList(@Param("list") List<StkInventoryEntity> list);


}
