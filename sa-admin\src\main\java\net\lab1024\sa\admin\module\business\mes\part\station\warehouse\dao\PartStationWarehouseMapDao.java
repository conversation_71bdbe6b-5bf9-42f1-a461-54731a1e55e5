package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationWarehouseMapEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 裁片仓库地图 Dao
 *
 * <AUTHOR>
 * @Date 2024-11-05 16:55:45
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface PartStationWarehouseMapDao extends BaseMapper<PartStationWarehouseMapEntity> {



    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
