package net.lab1024.sa.admin.module.business.mes.equip.equipment.controller;

import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentQuery;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentAddForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentQueryForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentUpdateForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.vo.EquipmentVO;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.service.EquipmentService;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 设备 Controller
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:12:13
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class EquipmentController {

    @Resource
    private EquipmentService equipmentService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/equipment/queryPage")
    public ResponseDTO<PageResult<EquipmentVO>> queryPage(@RequestBody @Valid EquipmentQueryForm queryForm) {
        return ResponseDTO.ok(equipmentService.queryPage(queryForm));
    }

    /**
     * 列表查询
     * @param query
     * @return
     */
    @Operation(summary = "列表查询 <AUTHOR>
    @PostMapping("/equipment/queryList")
    public ResponseDTO<List<EquipmentVO>> queryList(@RequestBody @Valid EquipmentQuery query) {
        return ResponseDTO.ok(equipmentService.queryList(query));
    }


    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/equipment/add")
    public ResponseDTO<String> add(@RequestBody @Valid EquipmentAddForm addForm) {
        return equipmentService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/equipment/update")
    public ResponseDTO<String> update(@RequestBody @Valid EquipmentUpdateForm updateForm) {
        return equipmentService.update(updateForm);
    }

    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/equipment/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return equipmentService.delete(id);
    }

    /**
     * 详情
     * @param id
     * @return
     */
    @Operation(summary = "详情 <AUTHOR>
    @GetMapping("/equipment/byId/{id}")
    public ResponseDTO<EquipmentVO> byId(@PathVariable("id") Long id) {
        return equipmentService.queryById(id);
    }
}
