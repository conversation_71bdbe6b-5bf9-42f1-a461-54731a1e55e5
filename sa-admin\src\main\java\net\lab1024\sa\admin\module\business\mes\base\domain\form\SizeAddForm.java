package net.lab1024.sa.admin.module.business.mes.base.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 尺码表 新建表单
 *
 * <AUTHOR>
 * @Date 2024-07-05 22:37:21
 * @Copyright zscbdic
 */

@Data
public class SizeAddForm {


    /**
     * 模板id
     */
    @Schema(description = "关联t_sizing_template", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "关联t_sizing_template 不能为空")
    private Long templateId;

    /**
     * 尺码信息（如：S码、M码、L码等）
     */
    @Schema(description = "尺码信息（如：S码、M码、L码等）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "尺码信息（如：S码、M码、L码等） 不能为空")
    private String sizeMessage;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

}
