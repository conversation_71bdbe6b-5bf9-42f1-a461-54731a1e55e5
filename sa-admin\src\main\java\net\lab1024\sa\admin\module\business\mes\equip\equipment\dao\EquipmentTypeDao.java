package net.lab1024.sa.admin.module.business.mes.equip.equipment.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.entity.EquipmentTypeEntity;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentTypeQueryForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.vo.EquipmentTypeVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 设备类别 Dao
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:12:33
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface EquipmentTypeDao extends BaseMapper<EquipmentTypeEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<EquipmentTypeVO> queryPage(Page page, @Param("queryForm") EquipmentTypeQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
