package net.lab1024.sa.admin.module.business.mes.produce.instruct.listener;

import cn.hutool.core.collection.CollUtil;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.event.stock.ProducePickMaterialEvent;
import net.lab1024.sa.admin.event.stock.ProduceReturnMaterialEvent;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderItemEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.manager.ProduceInstructOrderItemManager;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component("produceProduceNeedMaterialStockListener")
public class ProduceNeedMaterialStockListener {

    @Resource
    private ProduceInstructOrderItemManager produceInstructOrderItemManager;

    /**
     * 领料事件
     *
     * @param event
     */
    @EventListener(value = ProducePickMaterialEvent.class)
    public void producePickMaterial(ProducePickMaterialEvent event) {
        List<ProducePickMaterialEvent.Material> materials = event.getMaterials();
        if (CollUtil.isEmpty(materials)) {
            return;
        }
        // 获取用料信息
        List<Long> ids = materials.stream()
                .map(ProducePickMaterialEvent.Material::getProduceInstructOrderMaterielId)
                .collect(Collectors.toList());
        List<ProduceInstructOrderItemEntity> orderItems = produceInstructOrderItemManager.listByIds(ids);
        if (CollUtil.isEmpty(orderItems)) {
            return;
        }

        Map<Long, ProducePickMaterialEvent.Material> materialIdToMaterialMap = materials.stream()
                .collect(Collectors.toMap(ProducePickMaterialEvent.Material::getProduceInstructOrderMaterielId, Function.identity()));
        orderItems.forEach(i -> {
            ProducePickMaterialEvent.Material material = materialIdToMaterialMap.get(i.getId());
            if (material != null) {
                //累加领料数量
                i.setGiveQty(i.getGiveQty() == null ? 0.0 : i.getGiveQty() + material.getQty().doubleValue());
            }
        });

        produceInstructOrderItemManager.updateBatchById(orderItems);
    }

    @EventListener(value = ProduceReturnMaterialEvent.class)
    public void produceReturnMaterial(ProduceReturnMaterialEvent event) {
        List<ProduceReturnMaterialEvent.Material> materials = event.getMaterials();
        if (CollUtil.isEmpty(materials)) {
            return;
        }
        // 获取用料信息
        List<Long> ids = materials.stream()
                .map(ProduceReturnMaterialEvent.Material::getProduceInstructOrderMaterielId)
                .collect(Collectors.toList());
        List<ProduceInstructOrderItemEntity> orderItems = produceInstructOrderItemManager.listByIds(ids);
        if (CollUtil.isEmpty(orderItems)) {
            return;
        }

        Map<Long, ProduceReturnMaterialEvent.Material> materialIdToMaterialMap = materials.stream()
                .collect(Collectors.toMap(ProduceReturnMaterialEvent.Material::getProduceInstructOrderMaterielId, Function.identity()));
        orderItems.forEach(i -> {
            ProduceReturnMaterialEvent.Material material = materialIdToMaterialMap.get(i.getId());
            if (material != null) {
                //累加领料数量
                i.setBackQty(i.getGiveQty() == null ? 0.0 : i.getBackQty() + material.getQty().doubleValue());
            }
        });

        produceInstructOrderItemManager.updateBatchById(orderItems);
    }
}
