package net.lab1024.sa.admin.module.business.mes.base.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

/**
 * 尺码表 分页查询表单
 *
 * <AUTHOR>
 * @Date 2024-07-05 22:37:21
 * @Copyright zscbdic
 */

@Data
public class SizeQueryForm extends PageParam{

    /**
     * 关联尺寸模板id
     */
    @Schema(description = "尺寸模板id")
    private Long templateId;

    /**
     * 查询关键字
     */
    @Schema(description = "查询关键字")
    private String queryKey;

}
