package net.lab1024.sa.admin.module.business.mes.produce.instruct.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderArrangeQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderArrangeVO;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderVO;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.service.ProduceInstructOrderArrangeService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 指令单安排信息 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:26:34
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class ProduceInstructOrderArrangeController {

    @Resource
    private ProduceInstructOrderArrangeService produceInstructOrderArrangeService;

    /**
     * 指令单安排分页查询
     * @param queryForm
     * @return
     */
    @Operation
    @PostMapping("/produceInstructOrderArrange/queryPage")
    public ResponseDTO<PageResult<ProduceInstructOrderArrangeVO>> queryPage(@RequestBody @Valid ProduceInstructOrderArrangeQueryForm queryForm) {
        return ResponseDTO.ok(produceInstructOrderArrangeService.queryPage(queryForm));
    }


    /**
     * 指令单安排进度分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/produceInstructOrderArrange/queryArrangePage")
    public ResponseDTO<PageResult<ProduceInstructOrderVO>> queryPage(@RequestBody @Valid ProduceInstructOrderQueryForm queryForm) {
        return ResponseDTO.ok(produceInstructOrderArrangeService.queryInstructOrderWithArrangePage(queryForm));
    }

    /**
     * 我的指令单安排分页查询
     * @param queryForm
     * @return
     */
    @PostMapping("/produceInstructOrderArrange/querySelfArrangePage")
    @Operation(summary = "个人安排任务分页查询 <AUTHOR>
    public ResponseDTO<PageResult<ProduceInstructOrderArrangeVO>> querySelfArrangePage(@RequestBody @Valid ProduceInstructOrderArrangeQueryForm queryForm) {
        return ResponseDTO.ok(produceInstructOrderArrangeService.querySelfArrangePage(queryForm));
    }

    /**
     * 修改任务安排状态
     * @param id 任务安排id
     * @return
     */
    @Operation(summary = "修改任务安排状态  <AUTHOR>
    @GetMapping("/produceInstructOrderArrange/updateArrangeStatus/{id}")
    public ResponseDTO<String> updateArrangeStatus(@PathVariable Long id) {
        return produceInstructOrderArrangeService.updateStatus(id);
    }

//    @Operation(summary = "分页查询 <AUTHOR>
//    @PostMapping("/produceInstructOrderArrange/queryPage")
//    public ResponseDTO<PageResult<ProduceInstructOrderArrangeVO>> queryPage(@RequestBody @Valid ProduceInstructOrderArrangeQueryForm queryForm) {
//        return ResponseDTO.ok(produceInstructOrderArrangeService.queryPage(queryForm));
//    }
//
//    @Operation(summary = "添加 <AUTHOR>
//    @PostMapping("/produceInstructOrderArrange/add")
//    public ResponseDTO<String> add(@RequestBody @Valid ProduceInstructOrderArrangeAddForm addForm) {
//        return produceInstructOrderArrangeService.add(addForm);
//    }
//
//    @Operation(summary = "更新 <AUTHOR>
//    @PostMapping("/produceInstructOrderArrange/update")
//    public ResponseDTO<String> update(@RequestBody @Valid ProduceInstructOrderArrangeUpdateForm updateForm) {
//        return produceInstructOrderArrangeService.update(updateForm);
//    }
//
//    @Operation(summary = "批量删除 <AUTHOR>
//    @PostMapping("/produceInstructOrderArrange/batchDelete")
//    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Integer> idList) {
//        return produceInstructOrderArrangeService.batchDelete(idList);
//    }
//
//    @Operation(summary = "单个删除 <AUTHOR>
//    @GetMapping("/produceInstructOrderArrange/delete/{id}")
//    public ResponseDTO<String> batchDelete(@PathVariable Integer id) {
//        return produceInstructOrderArrangeService.delete(id);
//    }
}
