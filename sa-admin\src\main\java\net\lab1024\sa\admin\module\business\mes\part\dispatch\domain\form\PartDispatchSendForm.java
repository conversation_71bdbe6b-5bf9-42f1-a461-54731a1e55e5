package net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.form;

import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.constant.ActionEnum;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.constant.DispatchRangeEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;

import jakarta.validation.constraints.NotNull;

@Data
public class PartDispatchSendForm {

    /**
     * 菲票id
     */
    @NotNull(message = "菲票id不能为空")
    private Long ticketId;

//    /**
//     * 收发范围
//     */
//    @CheckEnum(value = DispatchRangeEnum.class, required = true, message = "收发范围")
//    private String dispatchRange;

    /**
     * 收或发
     */
//    @CheckEnum(value = ActionEnum.class, required = true, message = "收或发")
//    private String action;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 生产组id
     */
    private  Long produceTeamId;
}
