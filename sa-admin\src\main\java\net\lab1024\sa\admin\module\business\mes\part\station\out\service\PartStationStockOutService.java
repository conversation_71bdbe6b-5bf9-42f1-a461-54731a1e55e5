package net.lab1024.sa.admin.module.business.mes.part.station.out.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.lab1024.sa.admin.event.tailor.FeTicketTraceLogEvent;
import net.lab1024.sa.admin.module.business.mes.part.station.out.domain.form.PartStationStockOutByBoxForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxInsideDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxInsideEntity;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.constant.FeTicketTraceLogTemplateEnum;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationBinDao;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.dao.PartStationInventoryDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationBinEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.entity.PartStationInventoryEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.out.domain.form.PartStationStockOutByTicketForm;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.manager.PartStationInventoryManager;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.dao.FeTicketDao;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.domain.entity.FeTicketEntity;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;

import java.util.List;

@Service
public class PartStationStockOutService {

    @Resource
    private PartStationInventoryDao partStationInventoryDao;

    @Resource
    private FeTicketDao feTicketDao;

    @Resource
    private PartStationBinDao partStationBinDao;

    @Resource
    private PartStationInventoryManager partStationInventoryManager;

    @Resource
    private PartStationTurnBoxInsideDao partStationTurnBoxInsideDao;

    @Resource
    private PartStationTurnBoxDao partStationTurnBoxDao;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 裁片出库
     *
     * @param form
     * @return
     */
    public ResponseDTO<String> stockOut(PartStationStockOutByTicketForm form) {
        Long feTicketId = form.getFeTicketId();
        FeTicketEntity ticket = feTicketDao.selectById(feTicketId);
        if (ticket == null) {
            return ResponseDTO.userErrorParam("菲票不存在");
        }

        PartStationInventoryEntity inventory = partStationInventoryDao.selectOne(new LambdaQueryWrapper<PartStationInventoryEntity>()
                .eq(PartStationInventoryEntity::getFeTicketId, feTicketId).last("LIMIT 1"));
        if (inventory == null) {
            return ResponseDTO.userErrorParam("该菲票未入库");
        }

        PartStationBinEntity bin = partStationBinDao.selectById(inventory.getBinId());
        if (bin == null) {
            return ResponseDTO.userErrorParam("库位不存在");
        }

        String optDesc = String.format("裁片已从 库位【%s】 取出", bin.getBinCode());
        partStationInventoryManager.stockOut(feTicketId, bin.getId(), optDesc);


        return ResponseDTO.ok("出库成功");
    }

    public ResponseDTO<String> stockOutByBox(PartStationStockOutByBoxForm form) {
        Long turnBoxId = form.getTurnBoxId();
        PartStationTurnBoxEntity box = partStationTurnBoxDao.selectById(turnBoxId);
        if (box == null) {
            return ResponseDTO.userErrorParam("周转箱不存在");
        }
        if (!box.getInsideFlag()) {
            return ResponseDTO.userErrorParam("该周转箱未入库");
        }
        List<Long> ticketIds = partStationTurnBoxInsideDao.selectList(new LambdaQueryWrapper<PartStationTurnBoxInsideEntity>()
                        .eq(PartStationTurnBoxInsideEntity::getTurnBoxId, turnBoxId))
                .stream()
                .map(PartStationTurnBoxInsideEntity::getFeTicketId)
                .toList();

        List<PartStationInventoryEntity> invs = null;
        if (CollUtil.isNotEmpty(ticketIds)) {
            //箱内存在菲票
            invs = partStationInventoryDao.selectList(new LambdaQueryWrapper<PartStationInventoryEntity>()
                    .in(PartStationInventoryEntity::getFeTicketId, ticketIds));
            if (invs.size() < ticketIds.size()) {
                return ResponseDTO.userErrorParam("存在未入库菲票");
            }
        }
        if (CollUtil.isEmpty(invs) || CollUtil.isEmpty(ticketIds)) {
            //没有库存数据，周转箱出库
            partStationInventoryManager.stockOutByBox(null, turnBoxId, null, null);
            return ResponseDTO.ok("出库成功");
        }

        List<Long> binIds = invs.stream().map(PartStationInventoryEntity::getBinId).distinct().toList();
        if (binIds.size() > 1) {
            return ResponseDTO.userErrorParam("该周转箱内菲票存在多个库位");
        }

        PartStationBinEntity bin = partStationBinDao.selectById(invs.get(0).getBinId());
        if (bin == null) {
            return ResponseDTO.userErrorParam("库位不存在");
        }
        String optDesc = String.format("裁片已从 库位【%s】 取出", bin.getBinCode());

        partStationInventoryManager.stockOutByBox(ticketIds, turnBoxId, bin.getId(), optDesc);

        return ResponseDTO.ok("出库成功");
    }
}
