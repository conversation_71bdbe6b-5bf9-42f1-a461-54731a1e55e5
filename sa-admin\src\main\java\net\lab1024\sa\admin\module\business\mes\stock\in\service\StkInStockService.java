package net.lab1024.sa.admin.module.business.mes.stock.in.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.StrUtil;
import net.lab1024.sa.admin.module.business.mes.common.qrcode.QrCodeTypeEnum;
import net.lab1024.sa.admin.module.business.mes.common.qrcode.utils.QrCodeTypeUtil;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemEntity;
import net.lab1024.sa.admin.module.business.mes.item.manager.ItemManager;
import net.lab1024.sa.admin.module.business.mes.stock.constant.OwnerTypeEnum;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.vo.StkPrintCodeVO;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.exception.BusinessException;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockBillStatusEnum;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockOptTypeEnum;
import net.lab1024.sa.admin.module.business.mes.stock.in.dao.StkInStockDao;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.bo.StkInStockBO;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.entity.StkInStockDetailEntity;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.entity.StkInStockEntity;
import net.lab1024.sa.admin.module.business.mes.stock.in.manager.StkInStockDetailManager;
import net.lab1024.sa.admin.module.business.mes.stock.in.manager.StkInStockManager;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.entity.StkInventoryEntity;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.entity.StkInventoryLogEntity;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.manager.StkInventoryLogManager;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.manager.StkInventoryManager;
import net.lab1024.sa.admin.module.business.mes.stock.lot.constant.StockDirectEnum;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.entity.StkLotMasterEntity;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.entity.StkLotMaterialTraceEntity;
import net.lab1024.sa.admin.module.business.mes.stock.lot.manager.StkLotMasterManager;
import net.lab1024.sa.admin.module.business.mes.stock.lot.manager.StkLotMaterialTraceManager;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartRequestUtil;

import jakarta.annotation.Resource;

import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 入库单 Service
 *
 * <AUTHOR>
 * @Date 2025-02-03 15:54:07
 * @Copyright zscbdic
 */

@Service
public class StkInStockService {

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private StkInStockDao stkInStockDao;

    @Resource
    private StkInventoryManager stkInventoryManager;

    @Resource
    private StkInStockManager stkInStockManager;

    @Resource
    private StkInStockDetailManager stkInStockDetailManager;

    @Resource
    private StkInventoryLogManager stkInventoryLogManager;

    @Resource
    private StkLotMaterialTraceManager stkLotMaterialTraceManager;

    @Resource
    private StkLotMasterManager stkLotMasterManager;

    @Resource
    private ItemManager itemManager;


    // 通过ApplicationContext获取代理对象
    @Resource
    @Lazy
    private StkInStockService selfProxy;

    public void stockInCheck(StkInStockBO bo) {
        StkInStockEntity stkInStock = bo.getStkInStock();
        List<StkInStockDetailEntity> details = bo.getDetails();

        //检验相关
        //校验单据编号
        stkInStockManager.checkNumber(stkInStock.getNumber(), stkInStock.getId());

        Long warehouseId = stkInStock.getWarehouseId();
        //校验库位
        stkInStockDetailManager.checkNeedLocation(warehouseId, details);

        List<Long> materielIds = details.stream()
                .map(StkInStockDetailEntity::getMaterielId)
                .collect(Collectors.toList());
        //校验批次
        stkLotMasterManager.checkInNeedLot(warehouseId, materielIds, details);
    }


    /**
     * 入库
     */
    @Transactional(rollbackFor = Exception.class)
    public void stockIn(StkInStockBO bo) {
        //校验
        stockInCheck(bo);

        StkInStockEntity stkInStock = bo.getStkInStock();
        List<StkInStockDetailEntity> details = bo.getDetails();

        //更新单据状态
        RequestUser user = SmartRequestUtil.getRequestUser();
        stkInStockManager.lambdaUpdate()
                .eq(StkInStockEntity::getId, stkInStock.getId())
                .set(StkInStockEntity::getStatus, StockBillStatusEnum.AUDIT.getValue())
                .set(StkInStockEntity::getAuditorId, stkInStock.getAuditorId() == null ? user.getUserId() : stkInStock.getAuditorId())
                .set(StkInStockEntity::getAuditorName, stkInStock.getAuditorId() == null ? user.getUserName() : stkInStock.getAuditorName())
                .set(StkInStockEntity::getAuditTime, stkInStock.getAuditTime() == null ? LocalDateTime.now() : stkInStock.getAuditTime())
                .update();

        //转化日志
        List<StkInventoryLogEntity> logs = stkInventoryLogManager.parseInLogs(bo, StockOptTypeEnum.IN);
        //保存日志
        stkInventoryLogManager.saveBatch(logs);

        //转化批次追踪日志
        List<StkLotMaterialTraceEntity> lotLogs = stkLotMaterialTraceManager.parseInLogs(bo, StockDirectEnum.COME);
        //保存
        if (CollUtil.isNotEmpty(lotLogs)) {
            stkLotMaterialTraceManager.saveBatch(lotLogs);
        }

        //更新库存
        List<StkInventoryEntity> inventorys = details.stream().map(d -> {
            StkInventoryEntity entity = SmartBeanUtil.copy(d, StkInventoryEntity.class);
            entity.setWarehouseId(stkInStock.getWarehouseId());
            entity.setAvbQty(d.getQty());
            //TODO 暂不处理货主纬度
//            entity.setOwnerId(stkInStock.getOwnerId());
//            entity.setOwnerType(stkInStock.getOwnerType());
            return entity;
        }).collect(Collectors.toList());
        stkInventoryManager.batchUpdateInventory(inventorys, true);


    }

    /**
     * 入库
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAndStockIn(StkInStockBO bo) {
        stockInCheck(bo);

        //处理批号
        processLotNumbers(bo.getDetails());

        //保存单据
        stkInStockManager.saveBill(bo);

        //入库
        selfProxy.stockIn(bo);


    }


    public void saveBill(StkInStockBO bo) {
        stockInCheck(bo);
        //处理批号
        processLotNumbers(bo.getDetails());
        stkInStockManager.saveBill(bo);
    }


    /**
     * 更新入库单
     *
     * @param bo
     */

    public void updateBill(StkInStockBO bo) {
        stockInCheck(bo);
        //处理批号
        processLotNumbers(bo.getDetails());
        //更新单据
        stkInStockManager.updateBill(bo);

    }

    /**
     * 更新入库单并入库
     *
     * @param bo
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAndStockIn(StkInStockBO bo) {
        stockInCheck(bo);
        //处理批号
        processLotNumbers(bo.getDetails());
        //更新单据
        stkInStockManager.updateBill(bo);
        //入库
        selfProxy.stockIn(bo);

    }

    /**
     * 处理批号逻辑
     *
     * @param details 入库明细列表
     */
    private void processLotNumbers(List<StkInStockDetailEntity> details) {
        // 构建需要查询批号的列表，只处理有批号的明细
        List<StkLotMasterEntity> lots = details.stream()
                .filter(detail -> StrUtil.isNotBlank(detail.getLotNumber()))
                .map(detail -> {
                    StkLotMasterEntity entity = new StkLotMasterEntity();
                    entity.setMaterielId(detail.getMaterielId());
                    entity.setNumber(detail.getLotNumber());
                    return entity;
                })
                .collect(Collectors.toList());

        // 如果没有批号需要查询，直接返回
        if (CollUtil.isEmpty(lots)) {
            return;
        }

        // 查询批号信息
        List<StkLotMasterEntity> lotData = stkLotMasterManager.queryLotsWithId(lots);

        // 将查询结果转换为Map，方便后续查找
        if (CollUtil.isNotEmpty(lotData)) {
            Map<String, StkLotMasterEntity> lotDataMap = lotData.stream()
                    .collect(Collectors.toMap(e -> e.getNumber() + ":" + e.getMaterielId(), v -> v));

            // 设置批号ID
            details.forEach(detail -> {
                if (StrUtil.isNotBlank(detail.getLotNumber())) {
                    StkLotMasterEntity lot = lotDataMap.get(detail.getLotNumber() + ":" + detail.getMaterielId());
                    if (lot != null) {
                        detail.setLotId(lot.getId());
                    }
                }
            });
        }
    }

    /**
     * 打印标签
     *
     * @param id
     * @return
     */
    public List<StkPrintCodeVO> getPrintCode(Long id) {
        StkInStockEntity inStockEntity = stkInStockManager.getById(id);
        if (inStockEntity == null) {
            throw new BusinessException("单据不存在");
        }

        List<StkInStockDetailEntity> details = stkInStockDetailManager.lambdaQuery()
                .eq(StkInStockDetailEntity::getInStockId, id)
                .list();
        if (CollUtil.isEmpty(details)) {
            return Collections.emptyList();
        }
        //查询物料
        List<Long> materielId = details.stream().map(StkInStockDetailEntity::getMaterielId).toList();
        List<ItemEntity> materiels = itemManager.listByIds(materielId);
        if (CollUtil.isEmpty(materiels)) {
            return Collections.emptyList();
        }
        Map<Long, ItemEntity> materielMap = materiels.stream().collect(Collectors.toMap(ItemEntity::getId, item -> item));

        //查询批次
        List<Long> lotId = details.stream().map(StkInStockDetailEntity::getLotId).toList();
        List<StkLotMasterEntity> lots = null;
        Map<Long, StkLotMasterEntity> lotMap;
        if (CollUtil.isNotEmpty(lotId)) {
            lots = stkLotMasterManager.listByIds(lotId);
            lotMap = lots.stream().collect(Collectors.toMap(StkLotMasterEntity::getId, lot -> lot));
        } else {
            lotMap = null;
        }
        //封装数据
        String ownerType = inStockEntity.getOwnerType();
        String ownerName = inStockEntity.getOwnerName();
        LocalDate inStockDate = inStockEntity.getInStockTime().toLocalDate();
        return details.stream().map(e -> {
            //单据
            StkPrintCodeVO stkPrintCodeVO = new StkPrintCodeVO();
            if(CharSequenceUtil.isNotBlank(ownerType)){
                stkPrintCodeVO.setOwnerType(OwnerTypeEnum.getByValue(ownerType).getDesc());
                stkPrintCodeVO.setOwnerName(ownerName);
            }
            stkPrintCodeVO.setInStockDate(inStockDate);
            stkPrintCodeVO.setQty(e.getQty().setScale(0, RoundingMode.CEILING).intValue());
            ItemEntity m = materielMap.get(e.getMaterielId());
            if (m == null) {
                return null;
            }
            //物料
            stkPrintCodeVO.setMaterialSpuNumber(m.getNumber());
            stkPrintCodeVO.setMaterialSkuNumber(m.getSkuNumber());
            stkPrintCodeVO.setMaterialName(m.getName());
            stkPrintCodeVO.setMaterialModel(m.getModel());
            if (CollUtil.isNotEmpty(lotMap) && e.getLotId() != null && lotMap.containsKey(e.getLotId())) {
                StkLotMasterEntity lot = lotMap.get(e.getLotId());
                stkPrintCodeVO.setLotNumber(lot.getNumber());
                stkPrintCodeVO.setCode(QrCodeTypeUtil.toQrCodeString(QrCodeTypeEnum.MATERIAL, e.getMaterielId(), String.valueOf(lot.getId())));
            } else {
                stkPrintCodeVO.setCode(QrCodeTypeUtil.toQrCodeString(QrCodeTypeEnum.MATERIAL, e.getMaterielId()));
            }
            return stkPrintCodeVO;
        }).filter(Objects::nonNull).toList();

    }
}
