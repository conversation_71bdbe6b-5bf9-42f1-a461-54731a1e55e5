package net.lab1024.sa.admin.module.business.mes.equip.scada.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentQueryForm;
import net.lab1024.sa.admin.module.business.mes.equip.scada.domain.vo.EquipmentScadaDataVO;
import net.lab1024.sa.admin.module.business.mes.equip.scada.domain.vo.EquipmentScadaPropertyVO;
import net.lab1024.sa.admin.module.business.mes.equip.scada.serivce.EquipmentScadaDataService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 设备scada联网数据 Controller
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:13:22
 * @Copyright zscbdic
 */
@RestController
@Tag(name = "")
public class EquipmentScadaDataController {

    @Resource
    private EquipmentScadaDataService equipmentScadaDataService;

    /**
     * scada设备基础数据分页查询
     */
    @Operation(summary = "scada设备基础数据分页查询 <AUTHOR>
    @PostMapping("/equipmentScadaData/queryScadaDataPage")
    public ResponseDTO<PageResult<EquipmentScadaDataVO>> queryScadaDataPage(@RequestBody @Valid EquipmentQueryForm queryForm) {
        return equipmentScadaDataService.queryScadaDataPage(queryForm);
    }

    /**
     * scada设备基础数据刷新
     * @param equipId
     * @return
     */
    @Operation(summary = "scada设备基础数据刷新")
    @GetMapping("/equipmentScadaData/refreshScadaData/{equipId}")
    public ResponseDTO<EquipmentScadaDataVO> refreshScadaData(@PathVariable("equipId") Long equipId) {
        return equipmentScadaDataService.refreshScadaData(equipId);
    }

    /**
     * scada设备基础数据获取
     * @param equipId
     * @return
     */
    @Operation(summary = "scada设备基础数据获取")
    @GetMapping("/equipmentScadaData/queryScadaData/{equipId}")
    public ResponseDTO<EquipmentScadaDataVO> queryScadaData(@PathVariable("equipId") Long equipId) {
        return equipmentScadaDataService.queryScadaData(equipId);
    }

    /**
     * 属性查询
     * @param equipId
     * @return
     */
    @Operation(summary = "属性查询 ")
    @GetMapping("/equipmentScadaData/queryScadaProperties/{equipId}")
    public ResponseDTO<EquipmentScadaPropertyVO> queryScadaProperties(@PathVariable("equipId")  Long equipId) {
        return equipmentScadaDataService.queryScadaProperties(equipId);
    }


    /**
     * 属性查询（对象格式）
     * @param equipId
     * @return
     */
    @Operation(summary = "属性查询（对象格式）")
    @GetMapping("/equipmentScadaData/queryScadaPropertiesObj/{equipId}")
    public ResponseDTO<EquipmentScadaPropertyVO> queryScadaPropertiesObj(@PathVariable("equipId")  Long equipId) {
        return equipmentScadaDataService.queryScadaPropertiesObj(equipId);
    }


}
