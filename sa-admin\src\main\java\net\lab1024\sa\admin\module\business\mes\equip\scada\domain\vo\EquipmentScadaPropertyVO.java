package net.lab1024.sa.admin.module.business.mes.equip.scada.domain.vo;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class EquipmentScadaPropertyVO {

    /**
     * 设备id
     */
    private Long equipmentId;

    /**
     * scada设备id
     */
    private String scadaEquipmentId;

    /**
     * scada产品编号
     */
    private String scadaProductCode;

    /**
     * scada设备编号
     */
    private String scadaEquipmentCode;

    /**
     * 最后scada数据更新时间
     */
    private Date lastScadaDataUpdateTime;

    /**
     * 最后请求时间
     */
    private Date lastRequestTime;

    /**
     * 属性
     */
    private JSONObject propertyObj;

    /**
     * 属性
     */
    private List<EquipmentScadaPropertyVO.Property> properties;

    @Data
    public static class Property {

        /**
         * 字段名称
         */
        private String fieldName;

        /**
         * 字段key
         */
        private String fieldKey;

        /**
         * 字段值
         */
        private String fieldValueStr;

        /**
         * 数据类型
         */
        private String dataType;

        /**
         * 单位名称
         */
        private String unitName;

        /**
         * 更新时间
         */
        private Date updateTime;

        /**
         * 请求时间
         */
        private Date requestTime;

        /**
         * 排序
         */
        private Integer sort=0;

    }
}
