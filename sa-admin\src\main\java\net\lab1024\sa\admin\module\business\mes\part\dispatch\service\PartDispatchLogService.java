package net.lab1024.sa.admin.module.business.mes.part.dispatch.service;

import java.time.LocalDateTime;
import java.util.List;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import net.lab1024.sa.admin.module.business.mes.base.dao.SupplierDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.SupplierEntity;
import net.lab1024.sa.admin.module.business.mes.factory.dao.ProduceTeamDao;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.ProduceTeamEntity;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.constant.ActionEnum;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.constant.DispatchRangeEnum;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.dao.PartDispatchLogDao;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.entity.PartDispatchLogEntity;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.entity.PartDispatchReceiveForm;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.form.PartDispatchQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.form.PartDispatchSendForm;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.vo.PartDispatchLogVO;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.manager.PartDispatchLogManager;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.dao.FeTicketDao;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.domain.entity.FeTicketEntity;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.manager.FeTicketManager;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import net.lab1024.sa.base.common.util.SmartRequestUtil;

import jakarta.annotation.Resource;

/**
 * 裁片收发日志 Service
 *
 * <AUTHOR>
 * @Date 2024-11-06 15:25:13
 * @Copyright zscbdic
 */

@Service
public class PartDispatchLogService {

    @Resource
    private FeTicketDao feTicketDao;

    @Resource
    private PartDispatchLogDao partDispatchLogDao;

    @Resource
    private FeTicketManager feTicketManager;

    @Resource
    private PartDispatchLogManager partDispatchLogManager;

    @Resource
    private SupplierDao supplierDao;

    @Resource
    private ProduceTeamDao produceTeamDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<PartDispatchLogVO> queryPage(PartDispatchQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<PartDispatchLogVO> list = partDispatchLogDao.queryPage(page, queryForm);
        PageResult<PartDispatchLogVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }


    /**
     * 下发
     * @param sendForm
     * @return
     */
    public ResponseDTO<String> send(PartDispatchSendForm sendForm) {
        partDispatchLogManager.sendCheck(sendForm);

        RequestUser user = SmartRequestUtil.getRequestUser();
        PartDispatchLogEntity log = partDispatchLogManager.transformToLog(sendForm,user);

        partDispatchLogManager.send(log);

        return ResponseDTO.ok();

    }

    /**
     * 收回
     * @param receiveForm
     * @return
     */
    public ResponseDTO<String> receive(PartDispatchReceiveForm receiveForm) {
        partDispatchLogManager.receiveCheck(receiveForm);
        FeTicketEntity ticket = feTicketDao.selectById(receiveForm.getTicketId());
        if (ticket == null) {
            return ResponseDTO.userErrorParam("菲票不存在");
        }

        RequestUser user = SmartRequestUtil.getRequestUser();
        partDispatchLogManager.receive(receiveForm,user);


        return ResponseDTO.ok();
    }
}
