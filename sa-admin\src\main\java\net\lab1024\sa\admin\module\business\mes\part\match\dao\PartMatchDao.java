package net.lab1024.sa.admin.module.business.mes.part.match.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.part.match.domain.entity.PartMatchEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 裁片配扎情况 Dao
 *
 * <AUTHOR>
 * @Date 2024-11-07 15:17:40
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface PartMatchDao extends BaseMapper<PartMatchEntity> {

    public void saveBatch(@Param("list") List<PartMatchEntity> list);
    // 根据菲票id去判断是否已经配包


}
