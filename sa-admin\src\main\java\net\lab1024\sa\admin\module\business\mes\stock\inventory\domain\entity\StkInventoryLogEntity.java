package net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 即时库存更新日志 实体类
 *
 * <AUTHOR>
 * @Date 2025-01-15 11:20:19
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_stk_inventory_log")
public class StkInventoryLogEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库编号
     */
    private String warehouseNumber;

    /**
     * 仓位id
     */
    private Long locationId;

    /**
     * 仓位编码
     */
    private String locationNumber;

    /**
     * 物料id
     */
    private Long materielId;

    /**
     * 物料名称
     */
    private String materielName;

    /**
     * 物料规格型号
     */
    private String materielModel;

    /**
     * 物料spu编号
     */
    private String materielSpuNumber;

    /**
     * 物料sku编号
     */
    private String materielSkuNumber;

    /**
     * 单位id
     */
    private Long unitId;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 货主类型
     */
    private String ownerType;

    /**
     * 货主id
     */
    private Long ownerId;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 批次id
     */
    private Long lotId;

    /**
     * 批次编号
     */
    private String lotNumber;

    /**
     * SN ID
     */
    private Long snId;

    /**
     * SN编号
     */
    private String snNumber;

    /**
     * 操作类型
     */
    private String optType;

    /**
     * 操作数量;影响数量
     */
    private BigDecimal optQty;

    /**
     * 剩余数量;影响结果后数量
     */
    private BigDecimal optAfterQty;

    /**
     * 操作前数量
     */
    private BigDecimal optBeforeQty;

    /**
     * 操作人id
     */
    private Long optUserId;

    /**
     * 操作人名称
     */
    private String optUserName;

    /**
     * 操作时间
     */
    private LocalDateTime optTime;

    /**
     * 业务单据类型
     */
    private String bizFormType;

    /**
     * 业务单据id
     */
    private Long bizFormId;

    /**
     * 业务单据编号
     */
    private String bizFormNumber;

    /**
     * 业务单据详情ID
     */
    private Long bizFormDetailId;

    /**
     * 作业单据类型
     */
    private String workFormType;

    /**
     * 作业单据ID
     */
    private Long workFormId;

    /**
     * 作业单据编号
     */
    private String workFormNumber;

    /**
     * 作业单据详情ID
     */
    private Long workFormDetailId;

}
