package net.lab1024.sa.admin.module.business.mes.part.station.turn.box.service;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.common.qrcode.QrCodeTypeEnum;
import net.lab1024.sa.admin.module.business.mes.common.qrcode.utils.QrCodeTypeUtil;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form.PartStationTurnBoxAddForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form.PartStationTurnBoxBatchAddForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form.PartStationTurnBoxQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form.PartStationTurnBoxUpdateForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.vo.PartStationTurnBoxVO;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.manager.PartStationTurnBoxManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Stream;

/**
 * 裁片周转箱 Service
 *
 * <AUTHOR>
 * @Date 2025-03-14 11:36:51
 * @Copyright zscbdic
 */

@Service
public class PartStationTurnBoxService {

    @Resource
    private PartStationTurnBoxDao partStationTurnBoxDao;

    @Resource
    private PartStationTurnBoxManager partStationTurnBoxManager;


    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<PartStationTurnBoxVO> queryPage(PartStationTurnBoxQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<PartStationTurnBoxVO> list = partStationTurnBoxDao.queryPage(page, queryForm);
        return SmartPageUtil.convert2PageResult(page, list);
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(PartStationTurnBoxAddForm addForm) {
        partStationTurnBoxManager.checkNumber(addForm.getNumber(), null);


        PartStationTurnBoxEntity partStationTurnBoxEntity = SmartBeanUtil.copy(addForm, PartStationTurnBoxEntity.class);
        partStationTurnBoxEntity.setInsideFlag(false);
        partStationTurnBoxDao.insert(partStationTurnBoxEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(PartStationTurnBoxUpdateForm updateForm) {
        partStationTurnBoxManager.checkNumber(updateForm.getNumber(), updateForm.getId());

        PartStationTurnBoxEntity partStationTurnBoxEntity = SmartBeanUtil.copy(updateForm, PartStationTurnBoxEntity.class);
        partStationTurnBoxDao.updateById(partStationTurnBoxEntity);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    public ResponseDTO<String> batchDelete(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return ResponseDTO.ok();
        }
        partStationTurnBoxManager.deleteCheck(idList);

        partStationTurnBoxDao.batchUpdateDeleted(idList, true);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }
        partStationTurnBoxManager.deleteCheck(List.of(id));

        partStationTurnBoxDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }

    /**
     * 获取二维码base64
     *
     * @param id
     * @return
     */
    public ResponseDTO<String> getQrCode(Long id) {
        PartStationTurnBoxEntity box = partStationTurnBoxDao.selectById(id);
        if (null == box) {
            throw new BusinessException("周转箱不存在");
        }
        String qrCodeString = QrCodeTypeUtil.toQrCodeString(QrCodeTypeEnum.PART_STATION_TURN_BOX, id);
        String base64 = QrCodeUtil.generateAsBase64(qrCodeString, new QrConfig(100, 100), ImgUtil.IMAGE_TYPE_JPG);
        return ResponseDTO.ok(base64);
    }

    /**
     * 批量添加
     *
     * @param form
     * @return
     */
    public ResponseDTO<String> batchAdd(PartStationTurnBoxBatchAddForm form) {
        Integer num = form.getNum();
        String numberPrefix = form.getNumberPrefix();
        String namePrefix = form.getNamePrefix();

        List<String> tempNumbers = Stream.iterate(1, n -> n + 1)
                .limit(num)
                .map(i -> numberPrefix + "-" + i)
                .toList();
        Long count = partStationTurnBoxDao.selectCount(new LambdaQueryWrapper<PartStationTurnBoxEntity>()
                .in(PartStationTurnBoxEntity::getNumber, tempNumbers));
        if (count > 0) {
            throw new BusinessException("系统生成周转箱编号重复");
        }
        List<PartStationTurnBoxEntity> boxs = Stream.iterate(1, n -> n + 1)
                .limit(num)
                .map(i -> {
                    PartStationTurnBoxEntity box = new PartStationTurnBoxEntity();
                    box.setNumber(numberPrefix + "-" + i);
                    box.setName(namePrefix + "-" + i);
                    box.setCapacity(form.getCapacity());
                    box.setInsideFlag(false);
                    return box;
                })
                .toList();

        partStationTurnBoxManager.saveBatch(boxs);
        return ResponseDTO.ok();
    }

    /**
     * 通过id查询
     * @param id
     * @return
     */
    public ResponseDTO<PartStationTurnBoxVO> getById(Long id) {
        PartStationTurnBoxEntity entity = partStationTurnBoxDao.selectById(id);
        if (null == entity) {
            return ResponseDTO.userErrorParam("周转箱不存在");
        }
        PartStationTurnBoxVO vo = SmartBeanUtil.copy(entity, PartStationTurnBoxVO.class);
        return ResponseDTO.ok(vo);
    }
}
