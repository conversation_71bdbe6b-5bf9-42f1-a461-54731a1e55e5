package net.lab1024.sa.admin.module.business.mes.ai.tool.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.ai.tool.dao.LLMToolDao;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.entity.LLMToolEntity;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.form.LLMToolAddForm;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.form.LLMToolQueryForm;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.form.LLMToolUpdateForm;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.vo.LLMToolVO;
import net.lab1024.sa.admin.module.business.mes.ai.tool.manager.LLMToolManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 大模型工具表 Service
 *
 * <AUTHOR>
 * @Date 2025-04-05 14:27:51
 * @Copyright zscbdic
 */

@Service
public class LLMToolService {

    @Resource
    private LLMToolDao lLMToolDao;

    @Resource
    private LLMToolManager llmToolManager;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<LLMToolVO> queryPage(LLMToolQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<LLMToolVO> list = lLMToolDao.queryPage(page, queryForm);
        return SmartPageUtil.convert2PageResult(page, list);
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(LLMToolAddForm addForm) {
        llmToolManager.addCheck(addForm);

        LLMToolEntity lLMToolEntity = SmartBeanUtil.copy(addForm, LLMToolEntity.class);
        lLMToolDao.insert(lLMToolEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(LLMToolUpdateForm updateForm) {
        llmToolManager.updateCheck(updateForm);

        LLMToolEntity lLMToolEntity = SmartBeanUtil.copy(updateForm, LLMToolEntity.class);
        lLMToolDao.updateById(lLMToolEntity);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    public ResponseDTO<String> batchDelete(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return ResponseDTO.ok();
        }

        lLMToolDao.batchUpdateDeleted(idList, true);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id){
            return ResponseDTO.ok();
        }

        lLMToolDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }
}
