package net.lab1024.sa.admin.module.business.mes.salary.domain.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class PayoffForm {

    @NotNull(message = "员工ID不能为空")
    @NotEmpty(message = "员工ID不能为空")
    private List<Long> employeeIds;

    @NotNull(message = "归属月份 不能为空")
    @JsonFormat(pattern = "yyyy-MM")
    private Date belongMonth;

    private String remark;



}
