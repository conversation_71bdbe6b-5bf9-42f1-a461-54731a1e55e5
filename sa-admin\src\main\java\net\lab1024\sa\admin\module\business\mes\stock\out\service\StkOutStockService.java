package net.lab1024.sa.admin.module.business.mes.stock.out.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockBillStatusEnum;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockOptTypeEnum;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.entity.StkInventoryEntity;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.entity.StkInventoryLogEntity;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.manager.StkInventoryLogManager;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.manager.StkInventoryManager;
import net.lab1024.sa.admin.module.business.mes.stock.lot.constant.StockDirectEnum;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.entity.StkLotMasterEntity;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.entity.StkLotMaterialTraceEntity;
import net.lab1024.sa.admin.module.business.mes.stock.lot.manager.StkLotMasterManager;
import net.lab1024.sa.admin.module.business.mes.stock.lot.manager.StkLotMaterialTraceManager;
import net.lab1024.sa.admin.module.business.mes.stock.out.dao.StkOutStockDao;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.bo.StkOutStockBO;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity.StkOutStockDetailEntity;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity.StkOutStockEntity;
import net.lab1024.sa.admin.module.business.mes.stock.out.manager.StkOutStockDetailManager;
import net.lab1024.sa.admin.module.business.mes.stock.out.manager.StkOutStockManager;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkWarehouseEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.manager.StkWarehouseManager;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartRequestUtil;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 出库单 Service
 *
 * <AUTHOR>
 * @Date 2025-02-09 19:58:18
 * @Copyright zscbdic
 */

@Service
public class StkOutStockService {

    @Resource
    private StkOutStockDao stkOutStockDao;

    @Resource
    private StkLotMasterManager stkLotMasterManager;

    @Resource
    private StkInventoryLogManager stkInventoryLogManager;

    @Resource
    private StkLotMaterialTraceManager stkLotMaterialTraceManager;

    @Resource
    private StkInventoryManager stkInventoryManager;

    @Resource
    private StkOutStockDetailManager stkOutStockDetailManager;

    @Resource
    private StkOutStockManager stkOutStockManager;

    @Resource
    private StkWarehouseManager stkWarehouseManager;

    // 自己代理
    @Resource
    @Lazy
    private StkOutStockService selfProxy;


    public void stockOutCheck(StkOutStockBO bo) {
        StkOutStockEntity stkOutStock = bo.getStkOutStock();
        List<StkOutStockDetailEntity> details = bo.getDetails();
        Long warehouseId = stkOutStock.getWarehouseId();

        // 校验单据号
        stkOutStockManager.checkNumber(stkOutStock.getNumber(), stkOutStock.getId());

        //校验库位
        stkOutStockDetailManager.checkNeedLocation(warehouseId, details);

        List<Long> materielIds = details.stream()
                .map(StkOutStockDetailEntity::getMaterielId)
                .collect(Collectors.toList());
        //校验批次
        stkLotMasterManager.checkOutNeedLot(warehouseId, materielIds, details);

        StkWarehouseEntity warehouse = stkWarehouseManager.getById(warehouseId);
        //不允许负库存
        if (!warehouse.getAllowNegativeFlag()) {
            details.forEach(d -> {
                StkInventoryEntity query = SmartBeanUtil.copy(d, StkInventoryEntity.class);
                query.setWarehouseId(stkOutStock.getWarehouseId());
                //TODO 暂不处理货主纬度
                //entity.setOwnerId(stkInStock.getOwnerId());
                //entity.setOwnerType(stkInStock.getOwnerType());
                StkInventoryEntity inventoryData = stkInventoryManager.getInventoryData(query,false);
                boolean isSufficientStock = inventoryData.getAvbQty() != null && d.getQty() != null
                        && BigDecimal.ZERO.compareTo(inventoryData.getAvbQty().subtract(d.getQty())) > 0;
                if (isSufficientStock) {
                    throw new BusinessException(warehouse.getName()+"不允许负库存,请调整第" + d.getSeq() + "行数量");
                }
            });
        }


    }

    /**
     * 入库
     */
    @Transactional(rollbackFor = Exception.class)
    public void stockOut(StkOutStockBO bo) {
        //校验相关
        stockOutCheck(bo);

        StkOutStockEntity stkOutStock = bo.getStkOutStock();
        List<StkOutStockDetailEntity> details = bo.getDetails();

        //更新单据状态
        RequestUser user = SmartRequestUtil.getRequestUser();
        stkOutStockManager.lambdaUpdate()
                .eq(StkOutStockEntity::getId, stkOutStock.getId())
                .set(StkOutStockEntity::getStatus, StockBillStatusEnum.AUDIT.getValue())
                .set(StkOutStockEntity::getAuditorId, stkOutStock.getAuditorId() == null ? user.getUserId() : stkOutStock.getAuditorId())
                .set(StkOutStockEntity::getAuditorName, stkOutStock.getAuditorId() == null ? user.getUserName() : stkOutStock.getAuditorName())
                .set(StkOutStockEntity::getAuditTime, stkOutStock.getAuditTime() == null ? LocalDateTime.now() : stkOutStock.getAuditTime())
                .update();

        //转化日志
        List<StkInventoryLogEntity> logs = stkInventoryLogManager.parseOutLogs(bo, StockOptTypeEnum.OUT);
        //保存日志
        stkInventoryLogManager.saveBatch(logs);

        //转化批次追踪日志
        List<StkLotMaterialTraceEntity> lotLogs = stkLotMaterialTraceManager.parseOutLogs(bo, StockDirectEnum.TO);
        //保存
        if (CollUtil.isNotEmpty(lotLogs)) {
            stkLotMaterialTraceManager.saveBatch(lotLogs);
        }

        //更新库存
        List<StkInventoryEntity> inventorys = details.stream().map(d -> {
            StkInventoryEntity entity = SmartBeanUtil.copy(d, StkInventoryEntity.class);
            entity.setWarehouseId(stkOutStock.getWarehouseId());
            entity.setAvbQty(d.getQty());
            //TODO 暂不处理货主纬度
//            entity.setOwnerId(stkInStock.getOwnerId());
//            entity.setOwnerType(stkInStock.getOwnerType());
            return entity;
        }).collect(Collectors.toList());
        stkInventoryManager.batchUpdateInventory(inventorys, false);


    }


    /**
     * 保存并出库
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAndStockOut(StkOutStockBO bo) {
        StkOutStockEntity stkOutStock = bo.getStkOutStock();
        List<StkOutStockDetailEntity> details = bo.getDetails();

        //校验相关
        stockOutCheck(bo);

        //处理批号
        processLotNumbers(details);
        //保存单据
        stkOutStockManager.saveBill(bo);
        //出库
        selfProxy.stockOut(bo);

    }


    /**
     * 保存单据
     *
     * @param bo
     */
    public void saveStockOutBill(StkOutStockBO bo) {
        List<StkOutStockDetailEntity> details = bo.getDetails();
        StkOutStockEntity stkOutStock = bo.getStkOutStock();
        //校验相关
        stockOutCheck(bo);

        //处理批号
        processLotNumbers(details);
        //保存单据
        stkOutStockManager.saveBill(bo);
    }

    /**
     * 更新出库单
     *
     * @param bo
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStockOutBill(StkOutStockBO bo) {
        List<StkOutStockDetailEntity> details = bo.getDetails();
        StkOutStockEntity stkOutStock = bo.getStkOutStock();
        //校验相关
        stockOutCheck(bo);
        //处理批号
        processLotNumbers(details);
        stkOutStockManager.updateBill(bo);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAndStockOut(StkOutStockBO bo) {
        List<StkOutStockDetailEntity> details = bo.getDetails();
        StkOutStockEntity stkOutStock = bo.getStkOutStock();

        //校验相关
        stockOutCheck(bo);

        //处理批号
        processLotNumbers(details);
        stkOutStockManager.updateBill(bo);
        //出库
        selfProxy.stockOut(bo);
    }


    /**
     * 处理批号逻辑
     *
     * @param details 出库明细列表
     */
    private void processLotNumbers(List<StkOutStockDetailEntity> details) {
        // 构建需要查询批号的列表，只处理有批号的明细
        List<StkLotMasterEntity> lots = details.stream()
                .filter(detail -> StrUtil.isNotBlank(detail.getLotNumber()))
                .map(detail -> {
                    StkLotMasterEntity entity = new StkLotMasterEntity();
                    entity.setMaterielId(detail.getMaterielId());
                    entity.setNumber(detail.getLotNumber());
                    return entity;
                })
                .collect(Collectors.toList());

        // 如果没有批号需要查询，直接返回
        if (CollUtil.isEmpty(lots)) {
            return;
        }

        // 查询批号信息
        List<StkLotMasterEntity> lotData = stkLotMasterManager.queryLotsWithId(lots);

        // 将查询结果转换为Map，方便后续查找
        if (CollUtil.isNotEmpty(lotData)) {
            Map<String, StkLotMasterEntity> lotDataMap = lotData.stream()
                    .collect(Collectors.toMap(e -> e.getNumber() + ":" + e.getMaterielId(), v -> v));

            // 设置批号ID
            details.forEach(detail -> {
                if (StrUtil.isNotBlank(detail.getLotNumber())) {
                    StkLotMasterEntity lot = lotDataMap.get(detail.getLotNumber() + ":" + detail.getMaterielId());
                    if (lot != null) {
                        detail.setLotId(lot.getId());
                    }
                }
            });
        }
    }
}
