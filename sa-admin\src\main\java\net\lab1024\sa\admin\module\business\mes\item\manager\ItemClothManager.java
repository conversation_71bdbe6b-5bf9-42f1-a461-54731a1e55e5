package net.lab1024.sa.admin.module.business.mes.item.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.lab1024.sa.admin.module.business.mes.item.domain.vo.ItemClothVO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.item.constant.ItemAttributeEnum;
import net.lab1024.sa.admin.module.business.mes.item.dao.ItemClothDao;
import net.lab1024.sa.admin.module.business.mes.item.dao.ItemDao;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemClothEntity;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemEntity;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 布料信息表  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-02 12:03:01
 * @Copyright zscbdic
 */
@Service
public class ItemClothManager extends ServiceImpl<ItemClothDao, ItemClothEntity> {


    @Resource
    private ItemDao itemDao;


    @Resource
    private ItemClothDao itemClothDao;

    public void spuNumberCheck(String spuNumber) {
        Long count = itemDao.selectCount(new LambdaQueryWrapper<ItemEntity>()
                .eq(ItemEntity::getNumber, spuNumber)
                .in(ItemEntity::getAttribute, ItemAttributeEnum.FINISHED_CLOTHES.getValue(), ItemAttributeEnum.OTHER.getValue()));
        if (count > 0) {
            throw new BusinessException("spu编号 " + spuNumber + " 已在辅料或成衣中存在");
        }
    }

    /**
     * 删除布料信息
     *
     * @param itemId
     * @param clothId
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteCloth(Long itemId, Long clothId) {
        itemClothDao.deleteById(clothId);
        itemDao.updateDeleted(itemId, true);
    }

    public void colorCheck(String spuNumber, String color, Long excludeItemId) {
        List<ItemEntity> items = itemDao.selectList(new LambdaQueryWrapper<ItemEntity>()
                .eq(ItemEntity::getNumber, spuNumber)
                .select(ItemEntity::getId));
        if (CollUtil.isEmpty(items)) {
            return;
        }
        List<Long> ids = items.stream().map(ItemEntity::getId).collect(Collectors.toList());
        Long count = itemClothDao.selectCount(new LambdaQueryWrapper<ItemClothEntity>()
                .in(ItemClothEntity::getItemId, ids)
                .eq(ItemClothEntity::getColorName, color)
                .ne(excludeItemId != null, ItemClothEntity::getItemId, excludeItemId));
        if (count > 0) {
            throw new BusinessException(color + "已存在");
        }
    }

    public List<ItemClothVO> queryByIds(List<Long> ids){
        List<ItemEntity> itemEntities = itemDao.selectBatchIds(ids);
        if(CollUtil.isEmpty(itemEntities)){
            return Collections.emptyList();
        }
        List<ItemClothVO> itemClothVOS = SmartBeanUtil.copyList(itemEntities, ItemClothVO.class);
        List<ItemClothEntity> cloths = itemClothDao.selectList(new LambdaQueryWrapper<ItemClothEntity>().in(ItemClothEntity::getItemId, ids));
        if(CollUtil.isEmpty(cloths)){
            return itemClothVOS;
        }
        Map<Long, ItemClothEntity> clothMap = cloths.stream().collect(Collectors.toMap(ItemClothEntity::getItemId, cloth -> cloth));
        itemClothVOS.forEach(itemClothVO -> {
            ItemClothEntity cloth = clothMap.get(itemClothVO.getId());
            if(cloth != null){
                BeanUtil.copyProperties(cloth, itemClothVO,"id");
            }
        });
        return itemClothVOS;
    }


}
