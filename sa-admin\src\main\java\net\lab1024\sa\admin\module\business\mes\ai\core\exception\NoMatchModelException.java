package net.lab1024.sa.admin.module.business.mes.ai.core.exception;

import net.lab1024.sa.base.common.code.ErrorCode;

/**
 * 无匹配的模型异常
 */
public class NoMatchModelException extends RuntimeException{

    public NoMatchModelException() {
    }

    public NoMatchModelException(ErrorCode errorCode) {
        super(errorCode.getMsg());
    }

    public NoMatchModelException(String message) {
        super(message);
    }

    public NoMatchModelException(String message, Throwable cause) {
        super(message, cause);
    }

    public NoMatchModelException(Throwable cause) {
        super(cause);
    }

    public NoMatchModelException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
