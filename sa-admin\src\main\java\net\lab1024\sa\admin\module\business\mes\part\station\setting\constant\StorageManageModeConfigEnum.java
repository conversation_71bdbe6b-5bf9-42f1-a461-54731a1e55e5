package net.lab1024.sa.admin.module.business.mes.part.station.setting.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

@Getter
@AllArgsConstructor
public enum StorageManageModeConfigEnum implements BaseEnum {

    /**
     * 菲票管理模式
     */
    FE_TICKET_MANAGE_MODE("feTicketManageMode", "单菲票管理模式"),

    /**
     * 料箱/周转箱管理模式
     */
    TURN_BOX_MANAGE_MODE("turnBoxManageMode", "料箱/周转箱管理模式"),
    ;



    private final String value;

    private final String desc;
}
