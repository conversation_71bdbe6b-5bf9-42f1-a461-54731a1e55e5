package net.lab1024.sa.admin.event.part.station;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

import java.io.Serializable;
import java.util.List;

/**
 * 驿站任务完成事件
 */
@Getter
@Setter
public class PartStationTurnTaskFinishEvent extends ApplicationEvent {

    private Payload payload;

    public PartStationTurnTaskFinishEvent(Object source, Payload payload) {
        super(source);
        this.payload = payload;
    }

    @Data
    public static class Payload implements Serializable {
        private Long taskId;

        private List<Long> ticketIds;
    }
}
