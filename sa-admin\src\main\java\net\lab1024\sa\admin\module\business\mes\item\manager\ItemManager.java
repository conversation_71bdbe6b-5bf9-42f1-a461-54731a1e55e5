package net.lab1024.sa.admin.module.business.mes.item.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.item.constant.ItemAttributeEnum;
import net.lab1024.sa.admin.module.business.mes.item.dao.ItemDao;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemEntity;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 主物料表  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-02 08:33:07
 * @Copyright zscbdic
 */
@Slf4j
@Service
public class ItemManager extends ServiceImpl<ItemDao, ItemEntity> {

    @Resource
    private ItemDao itemDao;

    /**
     * 物料编号校验
     *
     * @param number
     * @param
     */
    public void spuNumberCheck(String number) {
        Long count = itemDao.selectCount(new LambdaQueryWrapper<ItemEntity>()
                .eq(ItemEntity::getNumber, number)
                .in(ItemEntity::getAttribute, ItemAttributeEnum.FINISHED_CLOTHES.getValue(), ItemAttributeEnum.CLOTH.getValue()));
        if (count > 0) {
            throw new BusinessException("spu编号 " + number + " 已在成衣或布料中存在");
        }
    }

    /**
     * sku编号校验
     *
     * @param skuNumber
     * @param excludeIds
     */
    public void skuNumberCheck(String skuNumber, List<Long> excludeIds) {
        Long count = this.lambdaQuery().eq(ItemEntity::getSkuNumber, skuNumber)
                .notIn(CollUtil.isNotEmpty(excludeIds), ItemEntity::getId, excludeIds)
                .count();
        if (count > 0) {
            throw new BusinessException("sku编号 " + skuNumber + " 已存在");
        }
    }


}
