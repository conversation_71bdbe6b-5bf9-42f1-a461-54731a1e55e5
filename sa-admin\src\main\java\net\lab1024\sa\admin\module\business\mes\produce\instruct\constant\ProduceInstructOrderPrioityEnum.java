package net.lab1024.sa.admin.module.business.mes.produce.instruct.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 生产指令单优先级枚举
 */
@Getter
@AllArgsConstructor
public enum ProduceInstructOrderPrioityEnum implements BaseEnum {

    /**
     * 一般
     */
    NORMAL("0", "一般"),

    /**
     * 紧急
     */
    URGENT("1", "紧急"),

    /**
     * 非常紧急
     */
    VERY_URGENT("2", "非常紧急");

    private final String value;

    private final String desc;

    public static ProduceInstructOrderPrioityEnum matchValue(String desc){
        ProduceInstructOrderPrioityEnum result = null;
        for (ProduceInstructOrderPrioityEnum value : values()) {
            if (value.getDesc().equals(desc)) {
                result=value;
                break;
            }
        }
        return result;
    }



}
