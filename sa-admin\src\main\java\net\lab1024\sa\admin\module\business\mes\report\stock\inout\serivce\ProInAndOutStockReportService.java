package net.lab1024.sa.admin.module.business.mes.report.stock.inout.serivce;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.common.bill.constant.BillType;
import net.lab1024.sa.admin.module.business.mes.report.stock.inout.dao.ProInAndOutStockReportDao;
import net.lab1024.sa.admin.module.business.mes.report.stock.inout.domain.form.ProInAndOutStockReportQueryForm;
import net.lab1024.sa.admin.module.business.mes.report.stock.inout.domain.vo.ProInAndOutStockReportVO;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockBillStatusEnum;
import net.lab1024.sa.admin.module.business.mes.stock.in.constant.InTypeConstant;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.entity.StkInStockDetailEntity;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.entity.StkInStockEntity;
import net.lab1024.sa.admin.module.business.mes.stock.in.manager.StkInStockDetailManager;
import net.lab1024.sa.admin.module.business.mes.stock.in.manager.StkInStockManager;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity.StkOutStockDetailEntity;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity.StkOutStockEntity;
import net.lab1024.sa.admin.module.business.mes.stock.out.manager.StkOutStockDetailManager;
import net.lab1024.sa.admin.module.business.mes.stock.out.manager.StkOutStockManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 生产入库出库报表
 */
@Service
public class ProInAndOutStockReportService {

    @Resource
    private ProInAndOutStockReportDao proInAndOutStockReportDao;

    @Resource
    private StkInStockManager stkInStockManager;

    @Resource
    private StkOutStockManager stkOutStockManager;

    @Resource
    private StkInStockDetailManager stkInStockDetailManager;

    @Resource
    private StkOutStockDetailManager stkOutStockDetailManager;

    /**
     * 生产入库出库报表
     *
     * @param queryForm
     * @return
     */
    public ResponseDTO<PageResult<ProInAndOutStockReportVO>> querySummaryPage(ProInAndOutStockReportQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ProInAndOutStockReportVO> vos = proInAndOutStockReportDao.queryProduceMaterialPage(page, queryForm);
        if (CollUtil.isEmpty(vos)) {
            return ResponseDTO.ok(SmartPageUtil.emptyPage(queryForm.getPageNum(), queryForm.getPageSize()));
        }
        List<Long> dataIds = vos.stream().map(ProInAndOutStockReportVO::getProduceClothesDataId).collect(Collectors.toList());

        //查询审核通过生产入库单
        List<StkInStockEntity> inBills = stkInStockManager.lambdaQuery().select(StkInStockEntity::getId)
                .eq(StkInStockEntity::getType, BillType.STOCK_PRODUCE_IN.getValue())
                .eq(StkInStockEntity::getStatus, StockBillStatusEnum.AUDIT.getValue())
                .list();
        if (CollUtil.isNotEmpty(inBills)) {
            List<Long> inBillIds = inBills.stream().map(StkInStockEntity::getId).collect(Collectors.toList());
            List<StkInStockDetailEntity> inList = stkInStockDetailManager.lambdaQuery()
                    .select(StkInStockDetailEntity::getInType, StkInStockDetailEntity::getQty, StkInStockDetailEntity::getOriginDetailId)
                    .in(StkInStockDetailEntity::getInStockId,inBillIds)
                    .in(StkInStockDetailEntity::getOriginDetailId, dataIds)
                    .eq(StkInStockDetailEntity::getOriginDetailType, BillType.PRODUCE_INSTRUCT_ORDER_CLOTHES.getValue())
                    .isNotNull(StkInStockDetailEntity::getInType)
                    .list();
            if (CollUtil.isNotEmpty(inList)) {
                //根据生产指令单成衣信息分组
                Map<Long, List<StkInStockDetailEntity>> inMap = inList
                        .stream()
                        .collect(Collectors.groupingBy(StkInStockDetailEntity::getOriginDetailId));
                for (ProInAndOutStockReportVO vo : vos) {
                    List<StkInStockDetailEntity> subIns = inMap.get(vo.getProduceClothesDataId());
                    //获取该项生产入库信息
                    if (CollUtil.isEmpty(subIns)) {
                        continue;
                    }
                    //该项对应入库数据根据入库类型分组
                    Map<String, List<StkInStockDetailEntity>> inTypeMap = subIns.stream().collect(Collectors.groupingBy(StkInStockDetailEntity::getInType));
                    //合格入库
                    List<StkInStockDetailEntity> qualityInQtyList = inTypeMap.get(InTypeConstant.ProduceInStock.QUALITY_IN.getValue());
                    if (CollUtil.isNotEmpty(qualityInQtyList)) {
                        double sum = qualityInQtyList.stream().mapToDouble((e) -> e.getQty().doubleValue()).sum();
                        vo.setQualityInQty(sum);
                    }
                    //不合格入库
                    List<StkInStockDetailEntity> nonQualityInQtyList = inTypeMap.get(InTypeConstant.ProduceInStock.NON_QUALITY_IN.getValue());
                    if (CollUtil.isNotEmpty(nonQualityInQtyList)) {
                        double sum = nonQualityInQtyList.stream().mapToDouble((e) -> e.getQty().doubleValue()).sum();
                        vo.setNonQualityInQty(sum);
                    }
                    //
                    List<StkInStockDetailEntity> scrapInQtyList = inTypeMap.get(InTypeConstant.ProduceInStock.SCRAP_IN.getValue());
                    if (CollUtil.isNotEmpty(scrapInQtyList)) {
                        double sum = scrapInQtyList.stream().mapToDouble((e) -> e.getQty().doubleValue()).sum();
                        vo.setScrapInQty(sum);
                    }
                    //
                    List<StkInStockDetailEntity> reworkInQtyList = inTypeMap.get(InTypeConstant.ProduceInStock.REWORK_IN.getValue());
                    if (CollUtil.isNotEmpty(reworkInQtyList)) {
                        double sum = reworkInQtyList.stream().mapToDouble((e) -> e.getQty().doubleValue()).sum();
                        vo.setReworkInQty(sum);
                    }
                }

            }
        }


        //查询审核通过生产出库单
        List<StkOutStockEntity> outBills = stkOutStockManager.lambdaQuery().select(StkOutStockEntity::getId)
                .eq(StkOutStockEntity::getType, BillType.STOCK_PRODUCE_OUT.getValue())
                .eq(StkOutStockEntity::getStatus, StockBillStatusEnum.AUDIT.getValue())
                .list();
        if(CollUtil.isNotEmpty(outBills)){
            List<Long> outBillIds = outBills.stream().map(StkOutStockEntity::getId).collect(Collectors.toList());
            List<StkOutStockDetailEntity> outList = stkOutStockDetailManager.lambdaQuery()
                    .select(StkOutStockDetailEntity::getQty, StkOutStockDetailEntity::getOriginDetailId)
                    .in(StkOutStockDetailEntity::getOutStockId,outBillIds)
                    .in(StkOutStockDetailEntity::getOriginDetailId, dataIds)
                    .eq(StkOutStockDetailEntity::getOriginDetailType, BillType.PRODUCE_INSTRUCT_ORDER_CLOTHES.getValue())
                    .list();
            if(CollUtil.isNotEmpty(outList)){
                Map<Long, List<StkOutStockDetailEntity>> outMap = outList
                        .stream()
                        .collect(Collectors.groupingBy(StkOutStockDetailEntity::getOriginDetailId));
                for (ProInAndOutStockReportVO vo : vos) {
                    List<StkOutStockDetailEntity> subOuts = outMap.get(vo.getProduceClothesDataId());
                    //获取该项生产入库信息
                    if (CollUtil.isEmpty(subOuts)) {
                        continue;
                    }
                    double sum = subOuts.stream().mapToDouble(e -> e.getQty().doubleValue()).sum();
                    vo.setReturnOutQty(sum);
                }
            }
        }


        PageResult<ProInAndOutStockReportVO> pageResult = SmartPageUtil.convert2PageResult(page, vos);
        return ResponseDTO.ok(pageResult);

    }
}
