package net.lab1024.sa.admin.module.business.mes.base.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.StyleEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.StyleQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.StyleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 款式品类表 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-05 15:45:34
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface StyleDao extends BaseMapper<StyleEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<StyleVO> queryPage(Page page, @Param("queryForm") StyleQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);


    /**
     * 根据名称关键字查询
     * @param queryKey
     * @param deletedFlag
     * @return
     */
    List<StyleEntity> queryByKey(@Param("queryKey") String queryKey, @Param("deletedFlag") boolean deletedFlag);

    List<StyleEntity> queryByParentId(@Param("parentIds") List<Long> parentIds, @Param("deletedFlag") boolean deletedFlag);
}


