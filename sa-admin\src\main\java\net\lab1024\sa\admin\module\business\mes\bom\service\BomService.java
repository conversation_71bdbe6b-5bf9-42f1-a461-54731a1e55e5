package net.lab1024.sa.admin.module.business.mes.bom.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.bom.dao.BomDao;
import net.lab1024.sa.admin.module.business.mes.bom.dao.BomDetailDao;
import net.lab1024.sa.admin.module.business.mes.bom.domain.entity.BomDetailEntity;
import net.lab1024.sa.admin.module.business.mes.bom.domain.entity.BomEntity;
import net.lab1024.sa.admin.module.business.mes.bom.domain.form.BomAddForm;
import net.lab1024.sa.admin.module.business.mes.bom.domain.form.BomDetailAddForm;
import net.lab1024.sa.admin.module.business.mes.bom.domain.form.BomQueryForm;
import net.lab1024.sa.admin.module.business.mes.bom.domain.form.BomUpdateForm;
import net.lab1024.sa.admin.module.business.mes.bom.domain.vo.BomDetailVO;
import net.lab1024.sa.admin.module.business.mes.bom.domain.vo.BomVO;
import net.lab1024.sa.admin.module.business.mes.bom.manager.BomManager;
import net.lab1024.sa.admin.module.business.mes.item.dao.ItemDao;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemEntity;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemTypeEntity;
import net.lab1024.sa.admin.module.business.mes.item.domain.vo.ItemVO;
import net.lab1024.sa.admin.module.business.mes.item.manager.ItemManager;
import net.lab1024.sa.admin.module.business.mes.item.manager.ItemTypeManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 物料BOM表 Service
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:25:22
 * @Copyright zscbdic
 */

@Service
public class BomService {

    @Resource
    private BomDao bomDao;

    @Resource
    private BomDetailDao bomDetailDao;

    @Resource
    private BomManager bomManager;

    @Resource
    private ItemTypeManager itemTypeManager;

    @Resource
    private ItemManager itemManager;

    @Resource
    private ItemDao itemDao;

    /**
     * BOM下拉查询
     *
     * @param
     * @return
     */
    public List<BomVO> queryAll() {
        List<BomEntity> bomEntities = bomDao.selectList(null);
        List<BomVO> list = BeanUtil.copyToList(bomEntities, BomVO.class);
        return list;
    }

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<BomVO> queryPage(BomQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<BomVO> list = bomDao.queryPage(page, queryForm);
        if (CollUtil.isEmpty(list)) {
            return SmartPageUtil.convert2PageResult(page, list);
        }

        // 物料类型
        List<Long> itemTypeIds = list.stream().map(BomVO::getItemTypeId).collect(Collectors.toList());
        List<ItemTypeEntity> itemTypeEntities = itemTypeManager.lambdaQuery().in(ItemTypeEntity::getId, itemTypeIds).list();
        Map<Long, String> typeMap = null;
        if (CollectionUtils.isNotEmpty(itemTypeEntities)) {
            typeMap = itemTypeEntities.stream().collect(Collectors.toMap(ItemTypeEntity::getId, ItemTypeEntity::getName));
        }
        if (typeMap != null) {
            for (BomVO vo : list) {
                vo.setItemTypeName(typeMap.get(vo.getItemTypeId()));
            }
        }
        // 物料
        List<Long> itemIds = list.stream().map(BomVO::getItemId).collect(Collectors.toList());
        List<ItemEntity> items = itemManager.lambdaQuery().in(ItemEntity::getId, itemIds).list();
        Map<Long, ItemEntity> itemMap = null;
        if (CollectionUtils.isNotEmpty(items)) {
            itemMap = items.stream().collect(Collectors.toMap(ItemEntity::getId, e -> e));
        }
        if (itemMap != null) {
            for (BomVO vo : list) {
                vo.setItemName(itemMap.get(vo.getItemId()).getName());
                vo.setItemNumber(itemMap.get(vo.getItemId()).getNumber());
                vo.setItemModel(itemMap.get(vo.getItemId()).getModel());
                vo.setItemCategory(itemMap.get(vo.getItemId()).getCategory());
                vo.setItemAttribute(itemMap.get(vo.getItemId()).getAttribute());
            }
        }

        PageResult<BomVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(BomAddForm addForm) {

        bomManager.addCheck(addForm);

        BomEntity bomEntity = SmartBeanUtil.copy(addForm, BomEntity.class);
        bomEntity.setVersionNumber(1);
        ValidateList<BomDetailAddForm> bomDetailList = addForm.getBomDetailList();
        bomManager.addBom(bomEntity, bomDetailList);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(BomUpdateForm updateForm) {
        bomManager.updateCheck(updateForm);

        BomEntity bomEntity = SmartBeanUtil.copy(updateForm, BomEntity.class);
        ValidateList<BomDetailAddForm> bomDetailAddFormList = updateForm.getBomDetailList();
        bomManager.updateBom(bomEntity, bomDetailAddFormList);
        return ResponseDTO.ok();
    }


    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }
        bomManager.deleteAllBom(id);
        return ResponseDTO.ok();
    }

    /**
     * 查看bom详细信息
     *
     * @param id
     * @return
     */
    public ResponseDTO<BomVO> queryById(Long id) {
        if (id == null) {
            return ResponseDTO.ok();
        }
        //主表
        BomEntity bomEntity = bomDao.selectById(id);
        BomVO bomVO = SmartBeanUtil.copy(bomEntity, BomVO.class);
        Long itemId = bomEntity.getItemId();
        ItemEntity itemEntity = itemDao.selectById(itemId);
        bomVO.setItemCategory(itemEntity.getCategory());
        bomVO.setItemAttribute(itemEntity.getAttribute());
        bomVO.setItemModel(itemEntity.getModel());
        bomVO.setItemName(itemEntity.getName());
        bomVO.setItemNumber(itemEntity.getNumber());

        //详情表
        List<BomDetailEntity> bomDetailEntities = bomDetailDao.selectList(new LambdaQueryWrapper<BomDetailEntity>()
                .eq(BomDetailEntity::getBomId, id)
                .eq(BomDetailEntity::getDeletedFlag, false));
        if (CollUtil.isEmpty(bomDetailEntities)) {
            return ResponseDTO.ok(bomVO);
        }
        List<Long> itemIds = bomDetailEntities.stream().map(BomDetailEntity::getItemId).collect(Collectors.toList());
        List<ItemVO> itemEntities = itemDao.queryByIdsWithUnit(itemIds);
        Map<Long, ItemVO> itemMap = itemEntities.stream().collect(Collectors.toMap(ItemVO::getId, e -> e));
        List<BomDetailVO> detailVOS = bomDetailEntities.stream().map(e -> {
            BomDetailVO vo = SmartBeanUtil.copy(e, BomDetailVO.class);
            vo.setItemName(itemMap.get(e.getItemId()).getName());
            vo.setItemNumber(itemMap.get(e.getItemId()).getNumber());
            vo.setItemModel(itemMap.get(e.getItemId()).getModel());
            vo.setItemCategory(itemMap.get(e.getItemId()).getCategory());
            vo.setItemAttribute(itemMap.get(e.getItemId()).getAttribute());
            vo.setItemUnitId(itemMap.get(e.getItemId()).getUnitId());
            vo.setItemTypeId(itemMap.get(e.getItemId()).getTypeId());
            vo.setItemUnitName(itemMap.get(e.getItemId()).getUnitName());
            return vo;
        }).collect(Collectors.toList());

        bomVO.setBomDetailList(detailVOS);

        return ResponseDTO.ok(bomVO);
    }

    /**
     * 新建版本
     *
     * @param addForm
     * @return
     */
//    public ResponseDTO<String> copy(BomAddForm addForm) {
//        BomEntity bomEntity = SmartBeanUtil.copy(addForm, BomEntity.class);
//
//        String bomNumber = bomEntity.getBomNumber();
//        List<BomEntity> bomEntityList = bomDao.selectList(new LambdaQueryWrapper<BomEntity>().
//                eq(BomEntity::getBomNumber, bomNumber));
//
//        BomEntity maxBom = bomEntityList.stream().max(Comparator.comparing(BomEntity::getVersionNumber)).get();
//
//        //目前最新bom版本+1
//        bomEntity.setVersionNumber(maxBom.getVersionNumber() + 1);
//
//        ValidateList<BomDetailAddForm> bomDetailList = addForm.getBomDetailList();
//
//        bomManager.addBom(bomEntity, bomDetailList);
//        return ResponseDTO.ok();
//    }

    /**
     * 根据id和版本号查询
     *
     * @param idAndVersionForm
     * @return
     */
//    public ResponseDTO<BomVO> query(IdAndVersionForm idAndVersionForm) {
//        if (idAndVersionForm == null) {
//            return ResponseDTO.ok();
//        }
//        BomVO bomVO = bomManager.queryByIdAndVersionNumber(idAndVersionForm.getId(), idAndVersionForm.getVersionNumber());
//        return ResponseDTO.ok(bomVO);
//    }

    /**
     * 查询所有版本
     *
     * @param bomNumber
     * @return
     */
//    public ResponseDTO<List<Integer>> allVersionNumber(String bomNumber) {
//        if (bomNumber == null) {
//            return ResponseDTO.ok();
//        }
//        List<Integer> bomVersions = bomManager.allVersionNumber(bomNumber);
//        return ResponseDTO.ok(bomVersions);
//    }
}
