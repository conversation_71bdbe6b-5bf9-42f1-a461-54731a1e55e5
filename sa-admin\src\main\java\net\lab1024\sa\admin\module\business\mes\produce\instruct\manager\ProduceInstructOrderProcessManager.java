package net.lab1024.sa.admin.module.business.mes.produce.instruct.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.factory.dao.WorkshopDao;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.WorkshopEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderProcessDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderProcessEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderProcessAddForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderProcessUpdateForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderProcessVO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 生产指令工序信息  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:24:08
 * @Copyright zscbdic
 */
@Service
public class ProduceInstructOrderProcessManager extends ServiceImpl<ProduceInstructOrderProcessDao, ProduceInstructOrderProcessEntity> {

    @Resource
    private WorkshopDao workshopDao;

    /**
     * 解析工序列表
     * @param processList
     * @return
     */
    public List<ProduceInstructOrderProcessEntity> parseAddForm(List<ProduceInstructOrderProcessAddForm> processList) {
        if(CollUtil.isEmpty(processList)){
            return Collections.emptyList();
        }

        // 排序
        List<ProduceInstructOrderProcessEntity> processEntities = SmartBeanUtil.copyList(processList, ProduceInstructOrderProcessEntity.class);
        processEntities.sort(Comparator.comparing(ProduceInstructOrderProcessEntity::getSerialNumber));

        // 获取车间信息
        List<Long> workshopIds = processEntities.stream()
                .map(ProduceInstructOrderProcessEntity::getWorkshopId)
                .collect(Collectors.toList());
        Map<Long, WorkshopEntity> workshopMap = workshopDao.selectBatchIds(workshopIds).stream().collect(Collectors.toMap(WorkshopEntity::getId, e -> e));

        for (int i = 0; i < processEntities.size(); i++) {
            ProduceInstructOrderProcessEntity entity = processEntities.get(i);
            entity.setSerialNumber(i + 1);
            entity.setEndFlag(false);

            if(entity.getWorkshopId() != null && workshopMap.containsKey(entity.getWorkshopId())){
                entity.setWorkshopName(workshopMap.get(entity.getWorkshopId()).getName());
            }

            if (entity.getUnitPrice1() == null) {
                entity.setUnitPrice1(BigDecimal.ZERO);
            }
            if (entity.getUnitPrice2() == null) {
                entity.setUnitPrice2(BigDecimal.ZERO);
            }
            if (entity.getUnitPrice3() == null) {
                entity.setUnitPrice3(BigDecimal.ZERO);
            }
            if (entity.getUnitPrice4() == null) {
                entity.setUnitPrice4(BigDecimal.ZERO);
            }
            if (entity.getUnitPrice5() == null) {
                entity.setUnitPrice5(BigDecimal.ZERO);
            }
            if (entity.getUnitPrice6() == null) {
                entity.setUnitPrice6(BigDecimal.ZERO);
            }
        }

        // 最后一条设置为结束
        processEntities.get(processEntities.size()-1).setEndFlag(true);
        return processEntities;
    }

    /**
     * 解析工序列表
     * @param processList
     * @return
     */
    public List<ProduceInstructOrderProcessEntity> parseUpdateForm(List<ProduceInstructOrderProcessUpdateForm> processList) {
        if(CollUtil.isEmpty(processList)){
            return Collections.emptyList();
        }
        // 排序
        List<ProduceInstructOrderProcessEntity> processEntities = SmartBeanUtil.copyList(processList, ProduceInstructOrderProcessEntity.class);
        processEntities.sort(Comparator.comparing(ProduceInstructOrderProcessEntity::getSerialNumber));

        // 获取车间信息
        List<Long> workshopIds = processEntities.stream()
                .map(ProduceInstructOrderProcessEntity::getWorkshopId)
                .collect(Collectors.toList());
        Map<Long, WorkshopEntity> workshopMap = workshopDao.selectBatchIds(workshopIds).stream().collect(Collectors.toMap(WorkshopEntity::getId, e -> e));


        for (int i = 0; i < processEntities.size(); i++) {
            ProduceInstructOrderProcessEntity entity = processEntities.get(i);
            entity.setSerialNumber(i + 1);
            entity.setEndFlag(false);

            // 车间名称
            if(entity.getWorkshopId() != null && workshopMap.containsKey(entity.getWorkshopId())){
                entity.setWorkshopName(workshopMap.get(entity.getWorkshopId()).getName());
            }

            if (entity.getUnitPrice1() == null) {
                entity.setUnitPrice1(BigDecimal.ZERO);
            }
            if (entity.getUnitPrice2() == null) {
                entity.setUnitPrice2(BigDecimal.ZERO);
            }
            if (entity.getUnitPrice3() == null) {
                entity.setUnitPrice3(BigDecimal.ZERO);
            }
            if (entity.getUnitPrice4() == null) {
                entity.setUnitPrice4(BigDecimal.ZERO);
            }
            if (entity.getUnitPrice5() == null) {
                entity.setUnitPrice5(BigDecimal.ZERO);
            }
            if (entity.getUnitPrice6() == null) {
                entity.setUnitPrice6(BigDecimal.ZERO);
            }


        }

        processEntities.get(processEntities.size()-1).setEndFlag(true);
        return processEntities;
    }

    /**
     * 获取工序信息
     * @param orderId
     * @return
     */
    public List<ProduceInstructOrderProcessVO> getProcessInfo(Long orderId) {
        List<ProduceInstructOrderProcessEntity> processEntityList = this.lambdaQuery().eq(ProduceInstructOrderProcessEntity::getOrderId, orderId).list();
        if(CollUtil.isEmpty(processEntityList)){
            return Collections.emptyList();
        }
        return SmartBeanUtil.copyList(processEntityList, ProduceInstructOrderProcessVO.class);
    }

//    /**
//     * 更新完成数量
//     * @param id
//     * @param num
//     */
//    public void updateFinishNum(Long id, Integer num) {
//        this.baseMapper.updateFinishNum(id, num);
//    }
}
