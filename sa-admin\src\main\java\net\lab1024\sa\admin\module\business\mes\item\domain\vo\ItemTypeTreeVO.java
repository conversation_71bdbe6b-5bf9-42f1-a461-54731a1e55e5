package net.lab1024.sa.admin.module.business.mes.item.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ItemTypeTreeVO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "主键")
    private Long value;

    @Schema(description = "父级id")
    private Long parentId;

    @Schema(description = "分类名称")
    private String name;

    @Schema(description = "分类名称")
    private String label;

    @Schema(description = "层级全称")
    private String fullName;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "子类")
    private List<ItemTypeTreeVO> children;
}
