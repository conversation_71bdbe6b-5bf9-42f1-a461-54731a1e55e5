package net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import net.lab1024.sa.base.common.domain.ValidateList;

import java.util.List;

/**
 * 生产安排信息 新建表单
 *
 * <AUTHOR>
 * @Date 2024-07-22 18:44:38
 * @Copyright zscbdic
 */

@Data
public class ProduceArrangeAddForm {

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 名称
     */
    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "名称 不能为空")
    private String name;

    /**
     * 详细生成安排
     */
    @Schema(description = "详细生成安排")
    @Valid
    private List<ProduceArrangeDetailAddForm> produceArrangeDetailAddForms;

}
