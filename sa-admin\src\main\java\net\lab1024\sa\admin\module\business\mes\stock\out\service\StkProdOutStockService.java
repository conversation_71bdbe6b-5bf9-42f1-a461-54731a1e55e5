package net.lab1024.sa.admin.module.business.mes.stock.out.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.event.stock.ProducePickMaterialEvent;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockBillStatusEnum;
import net.lab1024.sa.admin.module.business.mes.stock.out.dao.StkOutStockDao;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.bo.StkOutStockBO;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity.StkOutStockDetailEntity;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity.StkOutStockEntity;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.form.StkOutStockQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.form.StkProdOutStockAddForm;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.form.StkProdOutStockUpdateForm;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.vo.StkOutStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.vo.StkProdOutStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.out.manager.StkOutStockDetailManager;
import net.lab1024.sa.admin.module.business.mes.stock.out.manager.StkOutStockManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 生产领料单 service
 */
@Service
public class StkProdOutStockService {

    @Resource
    private StkOutStockDao stkOutStockDao;

    @Resource
    private StkOutStockManager stkOutStockManager;

    @Resource
    private StkOutStockDetailManager stkOutStockDetailManager;


    @Resource
    private StkOutStockService stkOutStockService;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public ResponseDTO<PageResult<StkOutStockVO>> queryPage(StkOutStockQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<StkOutStockVO> list = stkOutStockDao.queryPage(page, queryForm);
        PageResult<StkOutStockVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 新增单据
     *
     * @param form
     * @return
     */
    public ResponseDTO<String> add(StkProdOutStockAddForm form) {
        StkOutStockEntity outStock = SmartBeanUtil.copy(form, StkOutStockEntity.class);
        List<StkOutStockDetailEntity> details = SmartBeanUtil.copyList(form.getDetails(), StkOutStockDetailEntity.class);
        StkOutStockBO stockBO = new StkOutStockBO();
        stockBO.setStkOutStock(outStock);
        stockBO.setDetails(details);

        if (StockBillStatusEnum.UN_AUDIT.getValue().equals(form.getStatus())) {
            //保存单据
            stkOutStockService.saveStockOutBill(stockBO);
        } else if (StockBillStatusEnum.AUDIT.getValue().equals(form.getStatus())) {
            //保存并出库
            stkOutStockService.saveAndStockOut(stockBO);
            //发送领料事件
            pushProducePickMaterialEvent(stockBO);

        }
        return ResponseDTO.ok();
    }

    /**
     * 修改单据
     *
     * @param form
     * @return
     */
    public ResponseDTO<String> update(StkProdOutStockUpdateForm form) {
        Long id = form.getId();
        StkOutStockEntity stockEntity = stkOutStockManager.getById(id);
        if (stockEntity == null) {
            return ResponseDTO.userErrorParam("单据不存在");
        }

        String status = stockEntity.getStatus();
        stkOutStockManager.updateBillStatusCheck(status);

        StkOutStockBO stockBO = new StkOutStockBO();
        stockBO.setStkOutStock(SmartBeanUtil.copy(form, StkOutStockEntity.class));
        stockBO.setDetails(SmartBeanUtil.copyList(form.getDetails(), StkOutStockDetailEntity.class));
        if (StockBillStatusEnum.AUDIT.getValue().equals(form.getStatus())) {
            stkOutStockService.updateAndStockOut(stockBO);
            //发送领料事件
            pushProducePickMaterialEvent(stockBO);
        } else if (StockBillStatusEnum.UN_AUDIT.getValue().equals(form.getStatus())) {
            stkOutStockService.updateStockOutBill(stockBO);
        }
        return ResponseDTO.ok();
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    public ResponseDTO<StkProdOutStockVO> queryById(Long id) {
        StkOutStockEntity stockEntity = stkOutStockManager.getById(id);
        if (stockEntity == null) {
            return ResponseDTO.userErrorParam("单据不存在");
        }
        StkProdOutStockVO vo = SmartBeanUtil.copy(stockEntity, StkProdOutStockVO.class);
        List<StkOutStockDetailEntity> detailEntities = stkOutStockDetailManager.lambdaQuery()
                .eq(StkOutStockDetailEntity::getOutStockId, id)
                .list();
        List<StkProdOutStockVO.DetailVO> detailVOS = SmartBeanUtil.copyList(detailEntities, StkProdOutStockVO.DetailVO.class);
        vo.setDetails(detailVOS);
        return ResponseDTO.ok(vo);
    }

    public ResponseDTO<String> updateStatus(Long id) {
        StkOutStockEntity stockEntity = stkOutStockManager.getById(id);
        if (stockEntity == null) {
            return ResponseDTO.userErrorParam("单据不存在");
        }
        String status = stockEntity.getStatus();
        stkOutStockManager.updateBillStatusCheck(status);

        if (StockBillStatusEnum.UN_AUDIT.getValue().equals(status)) {
            List<StkOutStockDetailEntity> detailEntities = stkOutStockDetailManager.lambdaQuery()
                    .eq(StkOutStockDetailEntity::getOutStockId, id)
                    .list();
            StkOutStockBO stockBO = new StkOutStockBO();
            stockBO.setStkOutStock(stockEntity);
            stockBO.setDetails(detailEntities);
            //出库
            stkOutStockService.stockOut(stockBO);

            //发送领料事件
            pushProducePickMaterialEvent(stockBO);

        }
        return ResponseDTO.ok();
    }

    /**
     * 删除单据
     *
     * @param id
     * @return
     */
    public ResponseDTO<String> delete(Long id) {
        StkOutStockEntity stockEntity = stkOutStockManager.getById(id);
        if (stockEntity == null) {
            return ResponseDTO.userErrorParam("单据不存在");
        }
        String status = stockEntity.getStatus();
        stkOutStockManager.updateBillStatusCheck(status);
        stkOutStockManager.deleteBill(id);
        return ResponseDTO.ok();
    }


    /**
     * 推送领料事件
     *
     * @param stockBO
     */
    private void pushProducePickMaterialEvent(StkOutStockBO stockBO) {
        //发送领料事件
        List<ProducePickMaterialEvent.Material> mList = stockBO.getDetails().stream().map(d -> {
            ProducePickMaterialEvent.Material m = new ProducePickMaterialEvent.Material();
            m.setMaterielId(d.getMaterielId());
            m.setQty(d.getQty());
            m.setProduceInstructOrderMaterielId(d.getOriginDetailId());
            return m;
        }).toList();
        eventPublisher.publishEvent(new ProducePickMaterialEvent(this, mList));
    }
}
