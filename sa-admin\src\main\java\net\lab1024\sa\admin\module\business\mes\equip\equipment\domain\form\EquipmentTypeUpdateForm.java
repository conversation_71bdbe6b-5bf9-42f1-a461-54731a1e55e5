package net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 设备类别 更新表单
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:12:33
 * @Copyright zscbdic
 */

@Data
public class EquipmentTypeUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "设备类别编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "设备类别编号 不能为空")
    private String number;

    @Schema(description = "设备类别名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "设备类别名称 不能为空")
    private String name;

}
