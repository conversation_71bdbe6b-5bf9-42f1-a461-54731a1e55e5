package net.lab1024.sa.admin.module.business.mes.salary.manager;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.salary.constant.SettlementWayEnum;
import net.lab1024.sa.admin.module.business.mes.salary.dao.SettlementRecordDao;
import net.lab1024.sa.admin.module.business.mes.salary.dao.SettlementRecordDetailDao;
import net.lab1024.sa.admin.module.business.mes.salary.domain.bo.SettlementBo;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.SettlementRecordDetailEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.SettlementRecordEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.SettlementForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.SettlementQueryForm;
import net.lab1024.sa.admin.module.business.mes.work.constant.WorkRecordAuditFlagEnum;
import net.lab1024.sa.admin.module.business.mes.work.constant.WorkRecordStatusEnum;
import net.lab1024.sa.admin.module.business.mes.work.dao.WorkRecordDao;
import net.lab1024.sa.admin.module.business.mes.work.domain.entity.WorkRecordEntity;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class SettlementManager {

    @Resource
    private SettlementRecordDao settlementRecordDao;

    @Resource
    private SettlementRecordDetailDao settlementRecordDetailDao;

    @Resource
    private WorkRecordDao workRecordDao;


    /**
     * 结算方式校验
     * @param form
     */
    public void settlementWayCheck(SettlementQueryForm form) {
        SettlementWayEnum value = SettlementWayEnum.getByValue(form.getSettlementWay());
        if (value == null) {
            throw new BusinessException("结算方式不存在");
        }
        if (value == SettlementWayEnum.BY_DAY || value == SettlementWayEnum.BY_MONTH) {
            if (form.getStartDate() == null || form.getEndDate() == null) {
                throw new BusinessException("开始日期和结束日期不能为空");
            }
        }
    }

    /**
     * 结算方式校验
     * @param form
     */
    public void settlementWayCheck(SettlementForm form) {
        SettlementWayEnum value = SettlementWayEnum.getByValue(form.getSettlementWay());
        if (value == null) {
            throw new BusinessException("结算方式不存在");
        }
        if (value == SettlementWayEnum.BY_DAY || value == SettlementWayEnum.BY_MONTH) {
            if (form.getStartDate() == null || form.getEndDate() == null) {
                throw new BusinessException("开始日期和结束日期不能为空");
            }
        }
    }

    /**
     * 构建查询条件
     * @param form
     * @return
     */
    public SettlementQueryForm buildQueryForm(SettlementQueryForm form) {
        if(StrUtil.isEmpty(form.getSettlementWay())){
            throw new BusinessException("结算方式不能为空");
        }
        SettlementQueryForm queryForm = new SettlementQueryForm();
        queryForm.setSettlementWay(form.getSettlementWay());
        queryForm.setAuditFlag(WorkRecordAuditFlagEnum.AUDITED.getValue());
        queryForm.setRecordStatus(WorkRecordStatusEnum.NORMAL.getValue());
        queryForm.setPageNum(form.getPageNum());
        queryForm.setPageSize(form.getPageSize());

        if(form.getSettlementWay().equals(SettlementWayEnum.BY_DAY.getValue())){
            //按天结算
            queryForm.setStartDate(DateUtil.beginOfDay(form.getStartDate()));
            queryForm.setEndDate(DateUtil.endOfDay(form.getEndDate()));
        } else if (form.getSettlementWay().equals(SettlementWayEnum.BY_MONTH.getValue())) {
            //按月结算
            queryForm.setStartDate(DateUtil.beginOfDay(form.getStartDate()));
            queryForm.setEndDate(DateUtil.endOfDay(form.getEndDate()));
        }
        return queryForm;
    }

    /**
     * 构建查询条件
     * @param form
     * @return
     */
     public SettlementQueryForm buildQueryForm(SettlementForm form) {
        if(StrUtil.isEmpty(form.getSettlementWay())){
            throw new BusinessException("结算方式不能为空");
        }
        SettlementQueryForm queryForm = new SettlementQueryForm();
        queryForm.setSettlementWay(form.getSettlementWay());
        queryForm.setAuditFlag(WorkRecordAuditFlagEnum.AUDITED.getValue());
        queryForm.setRecordStatus(WorkRecordStatusEnum.NORMAL.getValue());

        if(form.getSettlementWay().equals(SettlementWayEnum.BY_DAY.getValue())){
            //按天结算
            queryForm.setStartDate(DateUtil.beginOfDay(form.getStartDate()));
            queryForm.setEndDate(DateUtil.endOfDay(form.getEndDate()));
        }else if (form.getSettlementWay().equals(SettlementWayEnum.BY_MONTH.getValue())) {
            //按月结算
            queryForm.setStartDate(DateUtil.beginOfDay(form.getStartDate()));
            queryForm.setEndDate(DateUtil.endOfDay(form.getEndDate()));
        }
        return queryForm;

    }


    /**
     * 取消结算
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelSettlement(List<Long> recordIds, List<Long> workRecordIds) {
        settlementRecordDao.batchUpdateDeleted(recordIds, true);
        settlementRecordDetailDao.delete(new LambdaQueryWrapper<SettlementRecordDetailEntity>()
                .in(SettlementRecordDetailEntity::getMainId, recordIds));
        workRecordDao.update(null, new LambdaUpdateWrapper<WorkRecordEntity>()
                .in(WorkRecordEntity::getId, workRecordIds)
                .eq(WorkRecordEntity::getSettlementFlag, true)
                .set(WorkRecordEntity::getSettlementFlag, false));
    }


    /**
     * 保存结算记录
     * @param settlementBo
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveSettlement(SettlementBo settlementBo) {
        //修改报工记录结算状态
        workRecordDao.update(null, new LambdaUpdateWrapper<WorkRecordEntity>()
                .set(WorkRecordEntity::getSettlementFlag, true)
                .in(WorkRecordEntity::getId, settlementBo.getWorkRecordIds()));
        //保存结算记录主表
        List<SettlementBo.RecordBo> records = settlementBo.getSettlementRecords();
        records.forEach(recordBo -> {
            SettlementRecordEntity record = recordBo.getRecord();
            settlementRecordDao.insert(record);
            Long mainId = record.getId();
            List<SettlementRecordDetailEntity> details = recordBo.getDetails();
            details.forEach(detail -> {detail.setMainId(mainId);});
            settlementRecordDetailDao.batchInsert(details);
        });

//        log.info("保存结算记录，{}",settlementBo);
    }
}
