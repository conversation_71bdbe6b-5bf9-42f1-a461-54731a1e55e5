package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

import jakarta.annotation.Resource;

/**
 * 指令单用料信息 分页查询表单
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:24:54
 * @Copyright zscbdic
 */

@Data
public class ProduceInstructOrderItemQueryForm extends PageParam {

    @Schema(description = "关键字查询")
    private String queryKey;

    @Schema(description = "指令单id")
    private Long orderId;

    @Schema(description = "指令单编号")
    private String orderNumber;

//    @Schema(description = "指令单物料名称(成衣)")
//    private String orderItemName;
//
//    @Schema(description = "指令单物料编号(成衣)")
//    private String orderItemNumber;

    @Schema(description = "物料id")
    private Long itemId;

    @Schema(description = "物料名称")
    private String itemName;

    @Schema(description = "物料spu编号")
    private String itemNumber;

    @Schema(description = "物料sku编号")
    private String itemSkuNumber;

    @Schema(description = "物料分类id")
    private Long itemTypeId;



}
