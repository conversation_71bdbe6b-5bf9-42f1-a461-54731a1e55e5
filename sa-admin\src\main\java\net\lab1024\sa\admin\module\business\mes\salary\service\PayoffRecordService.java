package net.lab1024.sa.admin.module.business.mes.salary.service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.lab1024.sa.admin.module.business.mes.factory.dao.WorkshopDao;
import net.lab1024.sa.admin.module.business.mes.salary.dao.PayoffRecordDao;
import net.lab1024.sa.admin.module.business.mes.salary.dao.PayoffRecordDetailDao;
import net.lab1024.sa.admin.module.business.mes.salary.dao.SettlementRecordDetailDao;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.PayoffRecordDetailEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.SettlementRecordDetailEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.PayoffRecordQueryForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.vo.PayoffRecordVO;
import net.lab1024.sa.admin.module.business.mes.salary.domain.vo.WageFieldValueVO;
import net.lab1024.sa.admin.module.business.mes.salary.manager.PayOffManager;
import net.lab1024.sa.admin.module.business.mes.work.dao.WorkRecordDao;
import net.lab1024.sa.admin.module.business.mes.work.domain.entity.WorkRecordEntity;
import net.lab1024.sa.admin.module.business.mes.work.domain.vo.WorkRecordVO;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 薪酬发放记录表 Service
 *
 * <AUTHOR>
 * @Date 2024-12-03 21:37:41
 * @Copyright zscbdic
 */

@Service
public class PayoffRecordService {

    @Resource
    private PayoffRecordDao payoffRecordDao;

    @Resource
    private PayoffRecordDetailDao payoffRecordDetailDao;

    @Resource
    private PayOffManager payOffManager;

    @Resource
    private SettlementRecordDetailDao settlementRecordDetailDao;

    @Resource
    private WorkRecordDao workRecordDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<PayoffRecordVO> queryPage(PayoffRecordQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<PayoffRecordVO> list = payoffRecordDao.queryPage(page, queryForm);

        for (PayoffRecordVO vo : list) {
            if (StrUtil.isEmpty(vo.getOtherAmountData())) {
                continue;
            }
            String otherAmountDataStr = vo.getOtherAmountData();
            vo.setOtherAmounts(JSONObject.parseArray(otherAmountDataStr, WageFieldValueVO.class));
        }

        PageResult<PayoffRecordVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }


    /**
     * 取消薪酬发放
     *
     * @param ids 薪酬发放记录ids
     * @return
     */
    public ResponseDTO<String> cancelPayoff(ValidateList<Long> ids) {
        List<PayoffRecordDetailEntity> detailEntities = payoffRecordDetailDao.selectList(new LambdaQueryWrapper<PayoffRecordDetailEntity>()
                .in(PayoffRecordDetailEntity::getMainId, ids));
        if (CollUtil.isEmpty(detailEntities)) {
            return ResponseDTO.userErrorParam("发放记录不存在");
        }

        List<Long> settleIds = detailEntities.stream()
                .map(PayoffRecordDetailEntity::getSettlementRecordId)
                .collect(Collectors.toList());
        payOffManager.cancelPayoff(ids, settleIds);
        return ResponseDTO.ok();

    }

    /**
     * 查询发放记录详情
     *
     * @param id
     * @return
     */
    public ResponseDTO<List<WorkRecordVO>> queryDetails(Long id) {
        //查询发放记录详情
        List<PayoffRecordDetailEntity> detailEntities = payoffRecordDetailDao.selectList(new LambdaQueryWrapper<PayoffRecordDetailEntity>()
                .eq(PayoffRecordDetailEntity::getMainId, id));
        if (CollUtil.isEmpty(detailEntities)) {
            return ResponseDTO.userErrorParam("计价记录不存在");
        }
        //查询结算记录详情
        List<Long> settleIds = detailEntities.stream()
                .map(PayoffRecordDetailEntity::getSettlementRecordId)
                .collect(Collectors.toList());
        List<SettlementRecordDetailEntity> settleDetails = settlementRecordDetailDao.selectList(new LambdaQueryWrapper<SettlementRecordDetailEntity>()
                .in(SettlementRecordDetailEntity::getMainId, settleIds));
        if (CollUtil.isEmpty(settleDetails)) {
            return ResponseDTO.ok(Collections.emptyList());
        }

        List<Long> workIds = settleDetails.stream()
                .map(SettlementRecordDetailEntity::getWorkRecordId)
                .collect(Collectors.toList());
        List<WorkRecordEntity> list = workRecordDao.selectBatchIds(workIds);
        if (CollUtil.isEmpty(list)) {
            return ResponseDTO.ok(Collections.emptyList());
        }
        List<WorkRecordVO> vos = SmartBeanUtil.copyList(list, WorkRecordVO.class);
        return ResponseDTO.ok(vos);
    }
}
