package net.lab1024.sa.admin.module.business.mes.salary.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.PayoffRecordEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.PayoffRecordQueryForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.vo.PayoffRecordVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 薪酬发放记录表 Dao
 *
 * <AUTHOR>
 * @Date 2024-12-03 21:37:41
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface PayoffRecordDao extends BaseMapper<PayoffRecordEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<PayoffRecordVO> queryPage(Page page, @Param("queryForm") PayoffRecordQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
