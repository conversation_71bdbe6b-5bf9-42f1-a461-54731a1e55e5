package net.lab1024.sa.admin.module.business.mes.item.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemTypeEntity;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemTypeQueryForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.vo.ItemTypeVO;

import java.util.List;

/**
 * 物料分类表 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-02 09:54:23
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface ItemTypeDao extends BaseMapper<ItemTypeEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<ItemTypeVO> queryPage(Page page, @Param("queryForm") ItemTypeQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 某个类型的所有
     */
    List<ItemTypeEntity> queryByKey(@Param("queryKey") String queryKey, @Param("deletedFlag")Boolean deletedFlag);

    /**
     * 根据父id查询子类
     * @param parentIds 父级id集合
     * @param deletedFlag 删除标识
     * @return
     */
    List<ItemTypeEntity> queryByParentId(@Param("parentIds") List<Long> parentIds,@Param("deletedFlag") Boolean deletedFlag);
}
