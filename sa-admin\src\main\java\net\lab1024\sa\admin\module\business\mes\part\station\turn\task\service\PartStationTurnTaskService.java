package net.lab1024.sa.admin.module.business.mes.part.station.turn.task.service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import net.lab1024.sa.admin.module.business.mes.factory.dao.ProduceTeamDao;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.ProduceTeamEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxInsideDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxInsideEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskExecutorTypeEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskStatusEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskTypeEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.dao.PartStationTurnTaskDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.entity.PartStationTurnTaskEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.form.PartStationTurnTaskFinishForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.form.PartStationTurnTaskQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.form.PartStationTurnTaskBeginForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.form.PartStationTurnTaskSumbitForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.vo.PartStationTurnTaskVO;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.manager.PartStationTurnTaskManager;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationBinDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationBinEntity;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.dao.FeTicketDao;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.domain.entity.FeTicketEntity;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 驿站任务表 Service
 *
 * <AUTHOR>
 * @Date 2025-03-14 20:13:46
 * @Copyright zscbdic
 */

@Service
public class PartStationTurnTaskService {

    @Resource
    private PartStationTurnTaskDao partStationTurnTaskDao;

    @Resource
    private PartStationBinDao partStationBinDao;

    @Resource
    private ProduceTeamDao produceTeamDao;


    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<PartStationTurnTaskVO> queryPage(PartStationTurnTaskQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<PartStationTurnTaskVO> list = partStationTurnTaskDao.queryPage(page, queryForm);
        if (CollUtil.isEmpty(list)) {
            return SmartPageUtil.convert2PageResult(page, Collections.emptyList());
        }

        // 货位信息
        Map<Long, PartStationBinEntity> locationMap = null;
        List<Long> locationIds = list.stream()
                .flatMap(obj -> Stream.of(obj.getStartLocationId(), obj.getEndLocationId()))
                .filter(Objects::nonNull).distinct().toList();
        if (CollUtil.isNotEmpty(locationIds)) {
            List<PartStationBinEntity> bins = partStationBinDao.selectBatchIds(locationIds);
            if (CollUtil.isNotEmpty(bins)) {
                locationMap = bins.stream()
                        .collect(Collectors.toMap(PartStationBinEntity::getId, e -> e));
            }
        }
        // 生产小组信息
        Map<Long, ProduceTeamEntity> teamMap = null;
        List<Long> teamIds = list.stream()
                .map(PartStationTurnTaskVO::getEndProduceTeamId)
                .filter(Objects::nonNull)
                .distinct().toList();
        if (CollUtil.isNotEmpty(teamIds)) {
            List<ProduceTeamEntity> teams = produceTeamDao.selectBatchIds(teamIds);
            if (CollUtil.isNotEmpty(teams)) {
                teamMap = teams.stream()
                        .collect(Collectors.toMap(ProduceTeamEntity::getId, e -> e));
            }
        }

        for (PartStationTurnTaskVO vo : list) {
            if (vo.getStartLocationId() != null && locationMap != null) {
                PartStationBinEntity startLocation = locationMap.get(vo.getStartLocationId());
                if (startLocation != null) {
                    vo.setStartLocationNumber(startLocation.getBinCode());
                    vo.setStartLocationDesc(startLocation.getBinDesc());
                }
            }
            if (vo.getEndLocationId() != null && locationMap != null) {
                PartStationBinEntity endLocation = locationMap.get(vo.getEndLocationId());
                if (endLocation != null) {
                    vo.setEndLocationNumber(endLocation.getBinCode());
                    vo.setEndLocationDesc(endLocation.getBinDesc());
                }
            }
            if (vo.getEndProduceTeamId() != null && teamMap != null) {
                ProduceTeamEntity team = teamMap.get(vo.getEndProduceTeamId());
                if (team != null) {
                    vo.setEndProduceTeamName(team.getTeamName());
                }
            }
        }

        PageResult<PartStationTurnTaskVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }


    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    public ResponseDTO<String> batchDelete(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return ResponseDTO.ok();
        }

        partStationTurnTaskDao.batchUpdateDeleted(idList, true);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }

        partStationTurnTaskDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }


}
