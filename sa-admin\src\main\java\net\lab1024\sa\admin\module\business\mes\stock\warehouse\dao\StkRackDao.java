package net.lab1024.sa.admin.module.business.mes.stock.warehouse.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkRackEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkRackQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.vo.StkRackVO;

import java.util.List;

/**
 * 货架 Dao
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:13:43
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface StkRackDao extends BaseMapper<StkRackEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<StkRackVO> queryPage(Page page, @Param("queryForm") StkRackQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
