package net.lab1024.sa.admin.module.business.mes.stock.in.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 入库类型
 */
public class InTypeConstant {

    /**
     * 生产退料类型
     */
    @Getter
    @AllArgsConstructor
    public enum ProduceReturnMaterial implements BaseEnum {

        /**
         * 良品退料
         */
        QUALITY_RETURN("QUALITY_RETURN", "良品退料"),

        /**
         * 来料不良退料
         */
        RECEIVED_DEFECT_RETURN("RECEIVED_DEFECT_RETURN", "来料不良退料"),

        /**
         * 作业不良退料
         */
        WORK_DEFECT_RETURN("WORK_DEFECT_RETURN", "作业不良退料"),
        ;


        private String value;

        private String desc;

    }

    /**
     * 生产入库类型
     */
    @Getter
    @AllArgsConstructor
    public enum ProduceInStock implements BaseEnum{

        /**
         * 合格品入库
         */
        QUALITY_IN("QUALITY_IN", "合格品入库"),


        /**
         * 不合格品入库
         */
        NON_QUALITY_IN ("NON_QUALITY_IN", "不合格品入库"),

        /**
         * 报废品入库
         */
        SCRAP_IN("SCRAP_IN", "报废品入库"),

        /**
         * 返工品入库
         */
        REWORK_IN("REWORK_IN", "返工品入库"),
        ;

        private String value;

        private String desc;

    }
}
