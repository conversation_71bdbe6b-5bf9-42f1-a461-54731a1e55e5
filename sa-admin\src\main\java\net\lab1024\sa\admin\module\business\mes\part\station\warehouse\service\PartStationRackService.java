package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationRackDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationBinEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationRackEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationRackAddForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationRackQuery;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationRackQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationRackUpdateForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.vo.PartStationRackVO;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.manager.PartStationBinManager;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.manager.PartStationRackManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 裁片货架 Service
 *
 * <AUTHOR>
 * @Date 2024-10-06 16:49:27
 * @Copyright zscbdic
 */

@Service
public class PartStationRackService {

    @Resource
    private PartStationRackDao partStationRackDao;

    @Resource
    private PartStationRackManager partStationRackManager;

    @Resource
    private PartStationBinManager partStationBinManager;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<PartStationRackVO> queryPage(PartStationRackQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<PartStationRackVO> list = partStationRackDao.queryPage(page, queryForm);
        PageResult<PartStationRackVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(PartStationRackAddForm addForm) {
        partStationRackManager.addCheck(addForm);

        PartStationRackEntity partStationRackEntity = SmartBeanUtil.copy(addForm, PartStationRackEntity.class);
        partStationRackDao.insert(partStationRackEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(PartStationRackUpdateForm updateForm) {
        partStationRackManager.updateCheck(updateForm);

        PartStationRackEntity partStationRackEntity = SmartBeanUtil.copy(updateForm, PartStationRackEntity.class);
        partStationRackDao.updateById(partStationRackEntity);
        return ResponseDTO.ok();
    }


    /**
     * 单个删除
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }

//        partStationRackManager.deleteCheck(id);
        List<PartStationBinEntity> binList = partStationBinManager.lambdaQuery()
                .eq(PartStationBinEntity::getRackId, id)
                .select(PartStationBinEntity::getId).list();
        if (CollUtil.isNotEmpty(binList)) {
            // 删除库位校验
            List<Long> binIds = binList.stream().map(PartStationBinEntity::getId).collect(Collectors.toList());
            partStationBinManager.deleteCheck(binIds);
            //删除库位
            partStationBinManager.removeBatchByIds(binIds);
        }


        partStationRackDao.updateDeleted(id, true);

        return ResponseDTO.ok();
    }

    /*
     * 获取所有数据
     */
    public ResponseDTO<List<PartStationRackVO>> getAll(PartStationRackQuery queryForm) {
        List<PartStationRackEntity> list = partStationRackDao.selectList(
                new LambdaQueryWrapper<PartStationRackEntity>()
                        .eq(ObjectUtil.isNotEmpty(queryForm.getWarehouseId()), PartStationRackEntity::getWarehouseId, queryForm.getWarehouseId())
        );
        List<PartStationRackVO> vos = SmartBeanUtil.copyList(list, PartStationRackVO.class);
        return ResponseDTO.ok(vos);
    }


    public ResponseDTO<Long> getRackNum(Long warehouseId) {
        LambdaQueryWrapper<PartStationRackEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjectUtil.isNotEmpty(warehouseId), PartStationRackEntity::getWarehouseId, warehouseId);
        Long count = partStationRackDao.selectCount(wrapper);
        return ResponseDTO.ok(count);
    }
}
