package net.lab1024.sa.admin.module.business.mes.part.station.anslyse.dao;

import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.dto.PartStationBinInvDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface PartStationAnalyseDao {

    /**
     * 查询货位库存
     * @param warehouseId
     * @return
     */
    List<PartStationBinInvDTO> queryPartStationBinInv(@Param("warehouseId") Long warehouseId);
}
