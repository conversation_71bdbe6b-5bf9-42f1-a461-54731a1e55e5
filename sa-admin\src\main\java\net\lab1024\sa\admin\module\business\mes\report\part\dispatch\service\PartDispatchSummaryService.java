package net.lab1024.sa.admin.module.business.mes.report.part.dispatch.service;

import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.dao.PartDispatchLogDao;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.form.PartDispatchQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.vo.PartDispatchSummaryVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.List;

@Service
public class PartDispatchSummaryService {

    @Resource
    private PartDispatchLogDao partDispatchLogDao;

    /**
     * 收发汇总分页
     * @param queryForm
     * @return
     */
    public ResponseDTO<PageResult<PartDispatchSummaryVO>> summaryPage(PartDispatchQueryForm queryForm) {
        Long pageNum = queryForm.getPageNum();
        Long pageSize = queryForm.getPageSize();
        Long offset = (pageNum - 1) * pageSize;

        List<PartDispatchSummaryVO> vos = partDispatchLogDao.querySummaryPage(queryForm, offset, pageSize);

        PageResult<PartDispatchSummaryVO> page = new PageResult<>();
        page.setList(vos);
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        page.setTotal(partDispatchLogDao.querySummaryTotal(queryForm));
        return ResponseDTO.ok(page);
    }

    /**
     * 收发汇总详情
     * @param produceInstructOrderId
     * @return
     */
    public ResponseDTO<List<PartDispatchSummaryVO.DetailVO>> summaryDetail(Long produceInstructOrderId) {
        List<PartDispatchSummaryVO.DetailVO> vos = partDispatchLogDao.querySummaryDetail(produceInstructOrderId);
        return ResponseDTO.ok(vos);
    }
}
