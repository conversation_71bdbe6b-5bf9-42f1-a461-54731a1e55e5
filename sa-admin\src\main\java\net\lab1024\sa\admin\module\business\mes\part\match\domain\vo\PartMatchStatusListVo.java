package net.lab1024.sa.admin.module.business.mes.part.match.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class PartMatchStatusListVo {
    @Schema(description = "生产指令单号")
    private String instructOrderNumber;

    @Schema(description = "裁床单号")
    private String cutBedSheetNumber;

    @Schema(description = "床次")
    private Integer cutNum;

    @Schema(description = "物料编码")
    private String itemNumber;


    @Schema(description = "裁片数量")
    private Integer Num;

    @Schema(description = "尺码")
    private String size;

    @Schema(description = "款式颜色")
    private String styleColor;

    @Schema(description = "款号")
    private String itemName;

    @Schema(description = "扎号")
    private Integer tieNum;

    @Schema(description = "已配部件的列表")
    private List<PartMatchPositionsStatusVo> parts;


}
