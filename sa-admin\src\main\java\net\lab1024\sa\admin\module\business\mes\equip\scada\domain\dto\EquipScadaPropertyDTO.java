package net.lab1024.sa.admin.module.business.mes.equip.scada.domain.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class EquipScadaPropertyDTO {

    /**
     * scada设备id
     */
    private String scadaEquipmentId;

    /**
     * scada产品编号
     */
    private String scadaProductCode;

    /**
     * scada设备编号
     */
    private String scadaEquipmentCode;

    /**
     * 最后scada数据更新时间
     */
    private Date lastScadaDataUpdateTime;


    private List<EquipScadaPropertyDTO.Property> properties;

    @Data
    public static class Property {

        private String fieldName;

        private String fieldKey;

        private String dataType;

        private String fieldValueStr;

        private String unitName;

        private Date updateTime;

        private Date requestTime;

    }
}
