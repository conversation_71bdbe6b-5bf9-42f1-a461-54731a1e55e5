package net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 仓库 列表VO
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:13:18
 * @Copyright zscbdic
 */

@Data
public class StkWarehouseVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

//    @Schema(description = "删除标识;0未删除，1删除")
//    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "仓库编号")
    private String number;

    @Schema(description = "仓库名称")
    private String name;

    @Schema(description = "仓库地址")
    private String address;

    @Schema(description = "负责人id")
    private Long principalId;

    @Schema(description = "负责人名称")
    private String principalName;

    @Schema(description = "负责人电话")
    private String tel;

    @Schema(description = "是否启用仓位管理;0否 1是")
    private Boolean openLocationFlag;

    @Schema(description = "允许锁库;0否 1是（保留字段）")
    private Boolean allowLockFlag;

    /**
     * 允许负库存;0否 1是
     */
    @Schema(description = "允许负库存;0否 1是")
    private Boolean allowNegativeFlag;

}
