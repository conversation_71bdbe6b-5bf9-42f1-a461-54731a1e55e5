package net.lab1024.sa.admin.module.business.mes.equip.equipment.service;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.dao.EquipmentTypeDao;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.entity.EquipmentTypeEntity;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentTypeAddForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentTypeQueryForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentTypeUpdateForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.vo.EquipmentTypeVO;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.manager.EquipmentTypeManager;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 设备类别 Service
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:12:33
 * @Copyright zscbdic
 */

@Service
public class EquipmentTypeService {

    @Resource
    private EquipmentTypeDao equipmentTypeDao;

    @Resource
    private EquipmentTypeManager equipmentTypeManager;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<EquipmentTypeVO> queryPage(EquipmentTypeQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<EquipmentTypeVO> list = equipmentTypeDao.queryPage(page, queryForm);
        PageResult<EquipmentTypeVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(EquipmentTypeAddForm addForm) {
        equipmentTypeManager.addCheck(addForm);
        EquipmentTypeEntity equipmentTypeEntity = SmartBeanUtil.copy(addForm, EquipmentTypeEntity.class);
        equipmentTypeDao.insert(equipmentTypeEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(EquipmentTypeUpdateForm updateForm) {
        equipmentTypeManager.updateCheck(updateForm);
        EquipmentTypeEntity equipmentTypeEntity = SmartBeanUtil.copy(updateForm, EquipmentTypeEntity.class);
        equipmentTypeDao.updateById(equipmentTypeEntity);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    public ResponseDTO<String> batchDelete(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return ResponseDTO.ok();
        }
        equipmentTypeManager.deleteCheck(idList);
        equipmentTypeDao.batchUpdateDeleted(idList, true);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id){
            return ResponseDTO.ok();
        }

        equipmentTypeManager.deleteCheck(id);
        equipmentTypeDao.updateDeleted(id,true);
        return ResponseDTO.ok();
    }
}
