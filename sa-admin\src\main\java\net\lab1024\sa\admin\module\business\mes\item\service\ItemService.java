package net.lab1024.sa.admin.module.business.mes.item.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;
import net.lab1024.sa.admin.event.item.ItemDeleteCheckEvent;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.UnitEntity;
import net.lab1024.sa.admin.module.business.mes.base.manager.SupplierManager;
import net.lab1024.sa.admin.module.business.mes.base.manager.UnitManager;
import net.lab1024.sa.admin.module.business.mes.common.excel.service.ExcelBaseService;
import net.lab1024.sa.admin.module.business.mes.item.constant.ItemAttributeEnum;
import net.lab1024.sa.admin.module.business.mes.item.constant.ItemCategoryEnum;
import net.lab1024.sa.admin.module.business.mes.item.dao.ItemDao;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemEntity;
import net.lab1024.sa.admin.module.business.mes.item.domain.excel.ItemExcel;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemAddForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemQuery;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemQueryForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemUpdateForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.vo.ItemVO;
import net.lab1024.sa.admin.module.business.mes.item.manager.ItemManager;
import net.lab1024.sa.admin.module.business.mes.item.manager.ItemTypeManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 主物料表 Service
 *
 * <AUTHOR>
 * @Date 2024-07-02 08:33:07
 * @Copyright zscbdic
 */

@Service
public class ItemService {

    @Resource
    private ItemDao itemDao;

    @Resource
    private ItemManager itemManager;

    @Resource
    private ItemTypeManager itemTypeManage;

    @Resource
    private SupplierManager supplierManager;

    @Resource
    private UnitManager unitManager;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private ExcelBaseService excelBaseService;

    @Resource
    private SerialNumberService serialNumberService;

    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<ItemVO> queryPage(ItemQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ItemVO> list = itemDao.queryPage(page, queryForm);
        //设置供应商名称，单位名称
        setName(list);
        PageResult<ItemVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(ItemAddForm addForm) {
        itemManager.spuNumberCheck(addForm.getNumber());
        itemManager.skuNumberCheck(addForm.getSkuNumber(), null);

        ItemEntity item = SmartBeanUtil.copy(addForm, ItemEntity.class);
        itemDao.insert(item);

        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(ItemUpdateForm updateForm) {
//        itemManager.spuNumberCheck(updateForm.getSpuNumber(), Collections.singletonList(updateForm.getId()));
        itemManager.spuNumberCheck(updateForm.getNumber());
        itemManager.skuNumberCheck(updateForm.getSkuNumber(), Collections.singletonList(updateForm.getId()));

        ItemEntity item = SmartBeanUtil.copy(updateForm, ItemEntity.class);
        itemDao.updateById(item);
        return ResponseDTO.ok();
    }


    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }
        eventPublisher.publishEvent(new ItemDeleteCheckEvent(this, id));

        itemManager.removeById(id);

        return ResponseDTO.ok();
    }


    /**
     * 根据供应商和单位id设置对应的名称
     *
     * @param list
     */
    public void setName(List<ItemVO> list) {
        Map<Long, String> supplierMap = supplierManager.processMap();
        Map<Long, String> unitmap = unitManager.processMap();
        Map<Long, String> itemTypeMap = itemTypeManage.processMap();
        list.forEach(e -> {
            e.setSupplierName(supplierMap.get(e.getSupplierId()));
            e.setUnitName(unitmap.get(e.getUnitId()));
            e.setTypeName(itemTypeMap.get(e.getTypeId()));
        });
    }

    public List<ItemVO> queryList(ItemQuery query) {
        List<ItemEntity> itemEntities = itemDao.selectList(null);
        return BeanUtil.copyToList(itemEntities, ItemVO.class);

    }

    /**
     * 导入
     *
     * @param file
     * @return
     */
    public ResponseDTO<String> importExcel(MultipartFile file) {
        AtomicInteger count = new AtomicInteger(0);

        excelBaseService.importExcel(file, ItemExcel.class, 1,
                e -> {
                    //数据校验与处理
                    String verify = SmartBeanUtil.verify(e);
                    if (verify != null) {
                        throw new BusinessException(verify);
                    }

                    if(StrUtil.isBlank(e.getNumber())){
                        String number = serialNumberService.generate(SerialNumberIdEnum.ITEM);
                        e.setNumber(number);
                    }
                    if (StrUtil.isBlank(e.getSkuNumber())) {
                       e.setSkuNumber(e.getNumber() + "." + count.incrementAndGet());
                    }
                    e.setNumber(e.getSkuNumber());

                    itemManager.spuNumberCheck(e.getNumber());
                    itemManager.skuNumberCheck(e.getSkuNumber(), null);

                    if (e.getPrice() == null) {
                        e.setPrice(BigDecimal.ZERO);
                    }
                    e.setAttribute(ItemAttributeEnum.OTHER.getValue());
                    e.setEnableFlag(false);
                    e.setCategory(ItemCategoryEnum.FINISHED_PRODUCT.getValue());
                    return e;
                }, list -> {
                    //业务校验与保存
//                    if (count.get() > 0) {
//                        //处理默认物料编号
//                        List<String> numbers = serialNumberService.generate(SerialNumberIdEnum.ITEM, count.get());
//                        list.forEach(e -> {
//                            if (StrUtil.isEmpty(e.getSkuNumber())) {
//                                e.setSkuNumber(numbers.get(0));
//                                numbers.remove(0);
//                            }
//                        });
//                    }

                    //处理单位
                    List<String> unitNames = list.stream().map(ItemExcel::getUnitName).distinct().collect(Collectors.toList());
                    List<UnitEntity> units = unitManager.lambdaQuery().in(UnitEntity::getName, unitNames).list();
                    if (CollUtil.isEmpty(units) || units.size() != unitNames.size()) {
                        throw new BusinessException("单位名称不存在");
                    }
                    Map<String, Long> unitMap = units.stream().collect(Collectors.toMap(UnitEntity::getName, UnitEntity::getId));
                    list.forEach(e -> {
                        e.setUnitId(unitMap.get(e.getUnitName()));
                        //设置spu编号
                        e.setNumber(e.getSkuNumber());
                    });


                    List<ItemEntity> itemEntities = SmartBeanUtil.copyList(list, ItemEntity.class);
                    transactionTemplate.execute(s -> {
                        itemManager.saveBatch(itemEntities, 100);
                        return null;
                    });

                });
        return ResponseDTO.ok();
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    public ItemVO getById(Long id) {
        ItemEntity itemEntity = itemManager.getById(id);
        return SmartBeanUtil.copy(itemEntity, ItemVO.class);
    }
}

