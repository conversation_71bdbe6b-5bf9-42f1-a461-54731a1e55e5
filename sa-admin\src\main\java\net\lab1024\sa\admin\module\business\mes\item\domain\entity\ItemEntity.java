package net.lab1024.sa.admin.module.business.mes.item.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 主物料表 实体类
 *
 * <AUTHOR>
 * @Date 2024-07-02 08:33:07
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_item")
public class ItemEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 物料分类id;关联t_item_type
     */
    private Long typeId;

    /**
     * 物料名称
     */
    private String name;

    /**
     * 供应商id;关联t_item_supplier
     */
    private Long supplierId;

    /**
     * 规格型号
     */
    private String model;

    /**
     * 物料编号 sku
     */
    private String number;

    /**
     * sku编号
     */
    private String skuNumber;

    /**
     * 单位id;关联t_unit
     */
    private Long unitId;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 图片url
     */
    private String imgUrl;

    /**
     * 停用标识;0启用，1停用
     */
    private Boolean enableFlag;

    /**
     * 类型;0半成品 1成品
     */
    private String category;

    /**
     * 属性;0面料，1其他，2成衣
     */
    private String attribute;


}
