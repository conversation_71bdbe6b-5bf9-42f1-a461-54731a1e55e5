package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.json.serializer.FileKeyVoSerializer;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 生产指令单 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:22:24
 * @Copyright zscbdic
 */

@Data
public class ProduceInstructOrderVO {

    @Schema(description = "id")
    private Long id;


    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;


    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 单据编号
     */
    @Schema(description = "单据编号")
    private String instructNumber;

    /**
     * 生产类型;0自产,1自裁委外，2整件委外
     */
    @Schema(description = "生产类型;0自产,1自裁委外，2整件委外")
    private String produceType;

    /**
     * 客户id
     */
    @Schema(description = "客户id")
    private Long customerId;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String customerName;

    /**
     * 销售单号
     */
    @Schema(description = "销售单号")
    private String salesOrderNumber;

    /**
     * 计划开工日期
     */
    @Schema(description = "计划开工日期")
    private LocalDate planStartTime;

    /**
     * 计划完工日期
     */
    @Schema(description = "计划完工日期")
    private LocalDate planFinishTime;

    /**
     * 实际开工日期
     */
    @Schema(description = "实际开工日期")
    private LocalDateTime realStartTime;

    /**
     * 实际完工日期
     */
    @Schema(description = "实际完工日期")
    private LocalDateTime realFinishTime;

    /**
     * 生产业务状态;0计划，1下达，2开工，3完工
     */
    @Schema(description = "生产业务状态;0计划，1下达，2开工，3完工")
    private String produceStatus;

    /**
     * 优先级;0一般,1紧急,2非常紧急
     */
    @Schema(description = "优先级;0一般,1紧急,2非常紧急")
    private String priority;

    /**
     * 交货日期
     */
    @Schema(description = "交货日期")
    private LocalDate deliverTime;

    /**
     * 下达日期
     */
    @Schema(description = "下达日期")
    private LocalDateTime issuedTime;

    /**
     * 物料id
     */
    @Schema(description = "物料id")
    private Long itemId;

    /**
     * 物料编号
     */
    @Schema(description = "物料编号")
    private String itemNumber;

    /**
     * 规格型号
     */
    @Schema(description = "规格型号")
    private String model;

    /**
     * 物料名称
     */
    @Schema(description = "物料名称")
    private String itemName;

    /**
     * 类型;0半成品 1成品
     */
    @Schema(description = "类型;0半成品 1成品")
    private String category;

    /**
     * 属性;0面料，1其他，2成衣
     */
    @Schema(description = "属性;0面料，1其他，2成衣")
    private String attribute;

    /**
     * 指令单名称
     */
    @Schema(description = "指令单名称")
    private String name;

    /**
     * 单位id
     */
    @Schema(description = "单位id")
    private Long unitId;

    /**
     * 单位名称
     */
    @Schema(description = "单位名称")
    private String unitName;


    @JsonSerialize(using = FileKeyVoSerializer.class)
    @Schema(description = "图片地址")
    private String imgUrl;

    /**
     * 完成数量
     */
    @Schema(description = "完成数量")
    private Integer finishNum;

    /**
     * 生产数量
     */
    @Schema(description = "生产数量")
    private Integer produceNum;

    /**
     * 总需生产数量
     */
    private Integer allShouldNum;

    /**
     * 总需完成数量
     */
    private Integer allFinishNum;


    /**
     * 指令单物料用量详情
     */
    @Schema(description = "指令单物料用量详情")
    private List<ProduceInstructOrderItemVO> itemList;

    /**
     * 指令单成衣信息详情
     */
    @Schema(description = "指令单成衣信息详情")
    private List<ProduceInstructOrderClothesVO> clothesList;

    /**
     * 指令单安排详情
     */
    @Schema(description = "指令单安排详情")
    private List<ProduceInstructOrderArrangeVO> arrangeList;

    /**
     * 指令单工序详情
     */
    @Schema(description = "指令单工序详情")
    private List<ProduceInstructOrderProcessVO> processList;
}
