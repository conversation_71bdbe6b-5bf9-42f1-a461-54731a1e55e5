package net.lab1024.sa.admin.module.business.mes.ai.tool.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.lab1024.sa.base.common.domain.PageParam;

/**
 * 大模型工具角色权限表 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-04-06 15:15:25
 * @Copyright zscbdic
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class LLMToolRoleQueryForm extends PageParam {

    @Schema(description = "工具ID")
    private Long llmToolId;

    @Schema(description = "角色ID")
    private Long roleId;

}
