package net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form;

import jakarta.validation.constraints.NotNull;
import net.lab1024.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 周转箱内容 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-03-14 15:03:20
 * @Copyright zscbdic
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class PartStationTurnBoxInsideQueryForm extends PageParam {

    @Schema(description = "周转箱ID")
    @NotNull(message = "周转箱ID 不能为空")
    private Long turnBoxId;

    @Schema(description = "菲票ID")
    private Long feTicketId;

    @Schema(description = "入箱时间")
    private LocalDate inTimeBegin;

    @Schema(description = "入箱时间")
    private LocalDate inTimeEnd;

}
