package net.lab1024.sa.admin.module.business.mes.equip.scada.manager;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.dao.EquipmentScadaDao;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.entity.EquipmentScadaEntity;
import net.lab1024.sa.admin.module.business.mes.equip.scada.strategy.ScadaDataStrategy;
import net.lab1024.sa.admin.module.business.mes.equip.scada.strategy.ScadaDataStrategyFactory;

import jakarta.annotation.Resource;

@Service
public class EquipmentScadaDataManager {

    @Resource
    private EquipmentScadaDao equipmentScadaDao;

    @Resource
    private ScadaDataStrategyFactory scadaDataStrategyFactory;

    /**
     * 根据设备ID获取SCADA策略
     *
     * @param equipId
     * @return
     */
    public ScadaDataStrategy getScadaStrategyByEquipId(Long equipId) {
        EquipmentScadaEntity scadaEntity = equipmentScadaDao.selectOne(new LambdaQueryWrapper<EquipmentScadaEntity>()
                .eq(EquipmentScadaEntity::getEquipmentId, equipId)
                .last("limit 1"));
        if (scadaEntity == null) {
            return null;
        }

        String platform = scadaEntity.getIotNetworkPlatform();
        Boolean iotNetworkFlag = scadaEntity.getIotNetworkFlag();
        if (!iotNetworkFlag || StrUtil.isEmpty(platform)) {
            return null;
        }

        ScadaDataStrategy strategy = scadaDataStrategyFactory.getStrategy(platform);

        return strategy;
    }
}
