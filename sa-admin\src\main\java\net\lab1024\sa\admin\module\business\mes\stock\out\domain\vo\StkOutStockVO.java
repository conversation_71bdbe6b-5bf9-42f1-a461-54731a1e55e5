package net.lab1024.sa.admin.module.business.mes.stock.out.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 出库单 列表VO
 *
 * <AUTHOR>
 * @Date 2025-02-09 19:58:18
 * @Copyright zscbdic
 */

@Data
public class StkOutStockVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

//    @Schema(description = "删除标识;0未删除，1删除")
//    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "单据来源类型")
    private String originType;

    @Schema(description = "单据来源ID")
    private Long originId;

    @Schema(description = "单据来源编号")
    private String originNumber;

    @Schema(description = "单据类型")
    private String type;

    @Schema(description = "单据方式")
    private String way;

    @Schema(description = "单据状态;")
    private String status;

    @Schema(description = "单据编号")
    private String number;

    @Schema(description = "仓库ID")
    private Long warehouseId;

    @Schema(description = "出库时间")
    private LocalDateTime outStockTime;

    @Schema(description = "仓管员ID")
    private Long stockerId;

    @Schema(description = "仓管员名称")
    private String stockerName;

    @Schema(description = "货主类型")
    private String ownerType;

    @Schema(description = "货主id")
    private Long ownerId;

    @Schema(description = "货主名称")
    private String ownerName;

    @Schema(description = "申请人ID")
    private Long applicantId;

    @Schema(description = "申请人名称")
    private String applicantName;

    @Schema(description = "申请时间")
    private LocalDateTime applyTime;

    @Schema(description = "审核人ID")
    private Long auditorId;

    @Schema(description = "审核人名称")
    private String auditorName;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

}
