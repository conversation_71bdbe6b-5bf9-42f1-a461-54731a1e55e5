package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.common.qrcode.QrCodeTypeEnum;
import net.lab1024.sa.admin.module.business.mes.common.qrcode.utils.QrCodeTypeUtil;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationBinDao;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.dao.PartStationInventoryDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationRackDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationBinEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.entity.PartStationInventoryEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationRackEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationBinAddForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationBinQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationBinUpdateForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.vo.PartStationBinVO;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.vo.PartStationInventoryVO;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.manager.PartStationBinManager;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.dao.FeTicketDao;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.domain.entity.FeTicketEntity;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 裁片货位 Service
 *
 * <AUTHOR>
 * @Date 2024-10-06 20:17:14
 * @Copyright zscbdic
 */

@Service
public class PartStationBinService {

    @Resource
    private PartStationBinDao partStationBinDao;

    @Resource
    private PartStationBinManager partStationBinManager;

    @Resource
    private PartStationInventoryDao partStationInventoryDao;

    @Resource
    private FeTicketDao feTicketDao;

    @Resource
    private PartStationRackDao partStationRackDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<PartStationBinVO> queryPage(PartStationBinQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<PartStationBinVO> list = partStationBinDao.queryPage(page, queryForm);
        PageResult<PartStationBinVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(PartStationBinAddForm addForm) {
        partStationBinManager.addCheck(addForm);

        PartStationBinEntity partStationBinEntity = SmartBeanUtil.copy(addForm, PartStationBinEntity.class);
        partStationBinDao.insert(partStationBinEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(PartStationBinUpdateForm updateForm) {
        partStationBinManager.updateCheck(updateForm);

        PartStationBinEntity partStationBinEntity = SmartBeanUtil.copy(updateForm, PartStationBinEntity.class);
        partStationBinDao.updateById(partStationBinEntity);
        return ResponseDTO.ok();
    }


    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        partStationBinManager.deleteCheck(id);

        if (null == id){
            return ResponseDTO.ok();
        }

        partStationBinDao.updateDeleted(id,true);
        return ResponseDTO.ok();
    }

    /**
     * 获取二维码
     * @param id
     * @return
     */
    public ResponseDTO<String> getQrCode(Long id) {
        PartStationBinEntity bin = partStationBinDao.selectById(id);
        if(null == bin){
            throw new BusinessException("货位不存在");
        }
        String qrCodeString = QrCodeTypeUtil.toQrCodeString(QrCodeTypeEnum.PART_STATION_BIN, id);
        String base64 = QrCodeUtil.generateAsBase64(qrCodeString, new QrConfig(100, 100), ImgUtil.IMAGE_TYPE_JPG);
        return ResponseDTO.ok(base64);
    }

    /**
     * 获取单个
     * @param id
     * @return
     */
    public ResponseDTO<PartStationBinVO> getById(Long id) {
        PartStationBinEntity bin = partStationBinManager.getById(id);
        if(null == bin){
            return ResponseDTO.userErrorParam("货位不存在");
        }
        PartStationBinVO binVO = SmartBeanUtil.copy(bin, PartStationBinVO.class);
        return ResponseDTO.ok(binVO);
    }

    /**
     * 获取货位下菲票列表
     * @param id
     * @return
     */
    public ResponseDTO<List<PartStationInventoryVO>> getFeTicketList(Long id) {
        List<PartStationInventoryEntity> inventoryEntities = partStationInventoryDao.selectList(new LambdaQueryWrapper<PartStationInventoryEntity>()
                .eq(PartStationInventoryEntity::getBinId, id));
        if (CollUtil.isEmpty(inventoryEntities)) {
            return ResponseDTO.ok(Collections.emptyList());
        }
        List<Long> ticketIds = inventoryEntities.stream()
                .map(PartStationInventoryEntity::getFeTicketId).collect(Collectors.toList());
        List<FeTicketEntity> feTicketList = feTicketDao.selectList(new LambdaQueryWrapper<FeTicketEntity>()
                .in(FeTicketEntity::getId, ticketIds));
        if (CollUtil.isEmpty(feTicketList)) {
            return ResponseDTO.ok(Collections.emptyList());
        }

        Map<Long, PartStationInventoryEntity> inventoryEntityMap = inventoryEntities.stream()
                .collect(Collectors.toMap(PartStationInventoryEntity::getFeTicketId, e -> e));
        List<PartStationInventoryVO> vos = feTicketList.stream()
                .map(e ->{
                    PartStationInventoryVO vo = SmartBeanUtil.copy(e, PartStationInventoryVO.class);
                    vo.setFeTicketId(e.getId());
                    if(inventoryEntityMap.containsKey(e.getId())){
                        vo.setLastCheckTime(inventoryEntityMap.get(e.getId()).getLastCheckTime());
                        vo.setStockInTime(inventoryEntityMap.get(e.getId()).getStockInTime());
                    }
                    return vo;
                }).collect(Collectors.toList());

        return ResponseDTO.ok(vos);
    }

    public ResponseDTO<Long> getBinNum(Long warehouseId) {
        LambdaQueryWrapper<PartStationRackEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjectUtil.isNotEmpty(warehouseId),PartStationRackEntity::getWarehouseId,warehouseId);
        List<PartStationRackEntity> rackEntities = partStationRackDao.selectList(wrapper);
        if(CollUtil.isEmpty(rackEntities)){
            return ResponseDTO.ok(0L);
        }
        List<Long> rackIds = rackEntities.stream().map(PartStationRackEntity::getId).collect(Collectors.toList());
        Long count = partStationBinDao.selectCount(new LambdaQueryWrapper<PartStationBinEntity>().in(PartStationBinEntity::getRackId, rackIds));
        return ResponseDTO.ok(count);

    }

    /**
     * 获取货位二维码内容
     * @param ids
     * @return
     */
    public ResponseDTO<List<String>> getQrCodeContent(ValidateList<Long> ids) {
        List<String> collect = ids.stream().map(e -> QrCodeTypeUtil.toQrCodeString(QrCodeTypeEnum.PART_STATION_BIN, e)).collect(Collectors.toList());
        return ResponseDTO.ok(collect);
    }
}
