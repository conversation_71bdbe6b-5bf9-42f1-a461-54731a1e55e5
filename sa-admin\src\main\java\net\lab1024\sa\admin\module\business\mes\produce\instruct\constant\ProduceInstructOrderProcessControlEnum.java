package net.lab1024.sa.admin.module.business.mes.produce.instruct.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 工序控制枚举
 */
@Getter
@AllArgsConstructor
public enum ProduceInstructOrderProcessControlEnum implements BaseEnum {

    /**
     * 自制
     */
    SELF_MADE("0", "自制"),


    /**
     * 委外
     */
    OUTSOURCING("1", "委外"),

    /**
     * 不限
     */
    UNLIMITED("2", "不限");

    private final String value;

    private final String desc;
}
