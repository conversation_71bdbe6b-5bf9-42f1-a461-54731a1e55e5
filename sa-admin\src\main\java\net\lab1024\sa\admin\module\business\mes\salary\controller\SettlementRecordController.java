package net.lab1024.sa.admin.module.business.mes.salary.controller;

import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.SettlementRecordQueryForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.vo.SettlementRecordVO;
import net.lab1024.sa.admin.module.business.mes.salary.service.SettlementRecordService;
import net.lab1024.sa.admin.module.business.mes.work.domain.vo.WorkRecordVO;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import net.lab1024.sa.base.common.domain.ValidateList;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 薪酬结算记录 Controller
 *
 * <AUTHOR>
 * @Date 2024-11-20 20:48:59
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class SettlementRecordController {

    @Resource
    private SettlementRecordService settlementRecordService;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/settlementRecord/queryPage")
    public ResponseDTO<PageResult<SettlementRecordVO>> queryPage(@RequestBody @Valid SettlementRecordQueryForm queryForm) {
        return ResponseDTO.ok(settlementRecordService.queryPage(queryForm));
    }

    /**
     * 记录详情
     *
     * @param id
     * @return
     */
    @Operation(summary = "记录详情 <AUTHOR>
    @GetMapping("/settlementRecord/queryDetails/{id}")
    public ResponseDTO<List<WorkRecordVO>> queryDetails(@PathVariable("id") Long id) {
        return settlementRecordService.queryDetails(id);
    }

    /**
     * 取消结算
     *
     * @param ids 结算记录id
     * @return
     */
    @Operation(summary = "取消结算 <AUTHOR>
    @GetMapping("/settlementRecord/cancelSettlement")
    public ResponseDTO<String> cancelSettlement(@RequestParam("ids") ValidateList<Long> ids) {
        return settlementRecordService.cancelSettlement(ids);

    }
}
