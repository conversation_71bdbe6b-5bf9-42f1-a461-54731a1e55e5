package net.lab1024.sa.admin.module.business.mes.stock.out.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity.StkOutStockEntity;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.form.StkOutStockQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.vo.StkOutStockVO;

import java.util.List;

/**
 * 出库单 Dao
 *
 * <AUTHOR>
 * @Date 2025-02-09 19:58:18
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface StkOutStockDao extends BaseMapper<StkOutStockEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<StkOutStockVO> queryPage(Page page, @Param("queryForm") StkOutStockQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
