package net.lab1024.sa.admin.module.business.mes.base.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.lab1024.sa.admin.module.business.mes.base.constant.SizeAndColorConstant;
import net.lab1024.sa.admin.module.business.mes.base.dao.StyleColorDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.StyleColorEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.StyleColorAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.StyleColorUpdateForm;
import net.lab1024.sa.base.common.exception.BusinessException;
import org.springframework.stereotype.Service;

import static net.lab1024.sa.admin.module.business.mes.base.constant.SizeAndColorConstant.SPLIT_CHAR;

/**
 * 款式颜色表  Manager
 *
 * <AUTHOR>
 * @Date 2024-11-03 20:49:04
 * @Copyright zscbdic
 */
@Service
public class StyleColorManager extends ServiceImpl<StyleColorDao, StyleColorEntity> {


    /**
     * 新增前校验
     * @param addForm
     */
    public void addCheck(StyleColorAddForm addForm) {
        String styleColor = addForm.getStyleColor();
        if (styleColor.contains(SizeAndColorConstant.SPLIT_CHAR)) {
            throw new BusinessException("颜色不能包含" + SPLIT_CHAR);
        }

        long count = this.count(new LambdaQueryWrapper<StyleColorEntity>()
                .eq(StyleColorEntity::getStyleColor, styleColor));
        if (count > 0) {
            throw new BusinessException(styleColor+"已存在");
        }
    }

    /**
     * 修改前校验
     * @param updateForm
     */
    public void updateCheck(StyleColorUpdateForm updateForm) {
        String styleColor = updateForm.getStyleColor();
        if (styleColor.contains(SizeAndColorConstant.SPLIT_CHAR)) {
            throw new BusinessException("颜色不能包含" + SPLIT_CHAR);
        }

        long count = this.count(new LambdaQueryWrapper<StyleColorEntity>()
                .eq(StyleColorEntity::getStyleColor, styleColor)
                .ne(StyleColorEntity::getId, updateForm.getId()));
        if (count > 0) {
            throw new BusinessException(styleColor+"已存在");
        }
    }
}
