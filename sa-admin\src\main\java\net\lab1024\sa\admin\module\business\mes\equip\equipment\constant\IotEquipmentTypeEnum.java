package net.lab1024.sa.admin.module.business.mes.equip.equipment.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 物联网设备类型枚举
 */
@Getter
@AllArgsConstructor
public enum IotEquipmentTypeEnum implements BaseEnum {

    /**
     * 元一裁床
     */
    YUAN_YI_CUTTING_BED("YUAN_YI_CUTTING_BED", "元一裁床"),


    /**
     * 元一铺布机
     */
    YUAN_YI_FABRIC_MACHINE("YUAN_YI_FABRIC_MACHINE", "元一铺布机"),


    ;


    private String value;

    private String desc;
}
