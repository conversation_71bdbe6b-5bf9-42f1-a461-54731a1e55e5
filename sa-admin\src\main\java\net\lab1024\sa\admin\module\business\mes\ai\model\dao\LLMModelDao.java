package net.lab1024.sa.admin.module.business.mes.ai.model.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.mes.ai.model.domain.entity.LLMModelEntity;
import net.lab1024.sa.admin.module.business.mes.ai.model.domain.form.LLMModelQueryForm;
import net.lab1024.sa.admin.module.business.mes.ai.model.domain.vo.LLMModelVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 大模型表 Dao
 *
 * <AUTHOR>
 * @Date 2025-04-17 11:25:27
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface LLMModelDao extends BaseMapper<LLMModelEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<LLMModelVO> queryPage(Page page, @Param("queryForm") LLMModelQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
