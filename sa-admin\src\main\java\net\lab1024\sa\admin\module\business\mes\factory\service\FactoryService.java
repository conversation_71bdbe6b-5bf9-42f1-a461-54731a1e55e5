package net.lab1024.sa.admin.module.business.mes.factory.service;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import net.lab1024.sa.admin.module.business.mes.factory.dao.FactoryDao;
import net.lab1024.sa.admin.module.business.mes.factory.dao.WorkshopDao;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.FactoryEntity;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.WorkshopEntity;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.FactoryAddForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.FactoryQueryForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.FactoryUpdateForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.vo.FactoryVO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 工厂信息表 Service
 *
 * <AUTHOR>
 * @Date 2024-07-04 11:11:50
 * @Copyright zscbdic
 */

@Service
public class FactoryService {

    @Resource
    private FactoryDao factoryDao;

    @Resource
    private WorkshopDao workshopDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<FactoryVO> queryPage(FactoryQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<FactoryVO> list = factoryDao.queryPage(page, queryForm);
        PageResult<FactoryVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(FactoryAddForm addForm) {
        FactoryEntity factoryEntity = SmartBeanUtil.copy(addForm, FactoryEntity.class);
        factoryDao.insert(factoryEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(FactoryUpdateForm updateForm) {
        FactoryEntity factoryEntity = SmartBeanUtil.copy(updateForm, FactoryEntity.class);
        factoryDao.updateById(factoryEntity);
        return ResponseDTO.ok();
    }


    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id){
            return ResponseDTO.ok();
        }
        List<WorkshopEntity> workshopEntityList = workshopDao.selectList(new QueryWrapper<WorkshopEntity>().eq("factory_id", id));
        if(CollectionUtils.isNotEmpty(workshopEntityList)){
            return ResponseDTO.userErrorParam("删除失败,工厂下还存在未删除的车间");
        }
        factoryDao.updateDeleted(id,true);
        return ResponseDTO.ok();
    }

    /**
     * 下拉列表
     * @param
     * @return
     */
    public ResponseDTO<List<FactoryVO>> queryList() {
        List<FactoryEntity> factoryEntityList = factoryDao.selectList(new QueryWrapper<FactoryEntity>().eq("deleted_flag",false));
        List<FactoryVO> factoryVOList = SmartBeanUtil.copyList(factoryEntityList, FactoryVO.class);
        return ResponseDTO.ok(factoryVOList);
    }
}
