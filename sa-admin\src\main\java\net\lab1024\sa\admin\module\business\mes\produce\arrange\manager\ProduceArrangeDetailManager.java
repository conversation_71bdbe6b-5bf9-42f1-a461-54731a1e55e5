package net.lab1024.sa.admin.module.business.mes.produce.arrange.manager;

import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.entity.ProduceArrangeDetailEntity;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.dao.ProduceArrangeDetailDao;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 生产安排信息  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-22 18:44:38
 * @Copyright zscbdic
 */
@Service
public class ProduceArrangeDetailManager extends ServiceImpl<ProduceArrangeDetailDao, ProduceArrangeDetailEntity> {


}
