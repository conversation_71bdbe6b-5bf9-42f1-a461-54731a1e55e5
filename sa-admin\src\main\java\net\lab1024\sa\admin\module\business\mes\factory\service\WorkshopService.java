package net.lab1024.sa.admin.module.business.mes.factory.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.factory.dao.FactoryDao;
import net.lab1024.sa.admin.module.business.mes.factory.dao.ProduceTeamDao;
import net.lab1024.sa.admin.module.business.mes.factory.dao.WorkshopDao;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.FactoryEntity;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.ProduceTeamEntity;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.WorkshopEntity;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.WorkshopAddForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.WorkshopQuery;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.WorkshopQueryForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.WorkshopUpdateForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.vo.WorkshopVO;
import net.lab1024.sa.admin.module.system.employee.dao.EmployeeDao;
import net.lab1024.sa.admin.module.system.employee.domain.entity.EmployeeEntity;
import net.lab1024.sa.admin.module.system.employee.manager.EmployeeManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 车间信息 Service
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:31:51
 * @Copyright zscbdic
 */

@Service
public class WorkshopService {

    @Resource
    private WorkshopDao workshopDao;

    @Resource
    private ProduceTeamDao produceTeamDao;

    @Resource
    private EmployeeDao employeeDao;

    @Resource
    private FactoryDao factoryDao;

    @Resource
    private EmployeeManager employeeManager;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<WorkshopVO> queryPage(WorkshopQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<WorkshopVO> list = workshopDao.queryPage(page, queryForm);

        // 携带负责人名,工厂名
        for (WorkshopVO workshopVO : list) {
            Long managerId = workshopVO.getManagerId();
            EmployeeEntity employeeEntity = employeeDao.selectById(managerId);
            if (employeeEntity == null) {
//                workshopVO.setManager("发生错误，该负责人已被删除");
            } else {
                workshopVO.setManager(employeeEntity.getActualName());
            }
            FactoryEntity factoryEntity = factoryDao.selectById(workshopVO.getFactoryId());
            if (factoryEntity == null) {
                workshopVO.setFactoryName("发生错误，该工厂已被删除");
            } else {
                workshopVO.setFactoryName(factoryEntity.getName());
            }

        }
        PageResult<WorkshopVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(WorkshopAddForm addForm) {
        WorkshopEntity workshopEntity = SmartBeanUtil.copy(addForm, WorkshopEntity.class);
        workshopDao.insert(workshopEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(WorkshopUpdateForm updateForm) {
        WorkshopEntity workshopEntity = SmartBeanUtil.copy(updateForm, WorkshopEntity.class);
        workshopDao.updateById(workshopEntity);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    public ResponseDTO<String> batchDelete(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return ResponseDTO.ok();
        }

        workshopDao.batchUpdateDeleted(idList, true);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }
        List<ProduceTeamEntity> produceTeamEntityList = produceTeamDao.selectList(new QueryWrapper<ProduceTeamEntity>().eq("workshop_id", id));
        if (CollectionUtils.isNotEmpty(produceTeamEntityList)) {
            return ResponseDTO.userErrorParam("删除失败,车间下还存在未删除的生产小组");
        }
        workshopDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }

    /**
     * 下拉列表
     *
     * @param
     * @return
     */
    public ResponseDTO<List<WorkshopVO>> queryList(WorkshopQuery query) {
        // 查询所有车间
        List<WorkshopEntity> workshopEntityList = workshopDao.selectList(new LambdaQueryWrapper<WorkshopEntity>()
                .like(StrUtil.isNotBlank(query.getQueryKey()), WorkshopEntity::getName, query.getQueryKey()).or());
        if (CollUtil.isEmpty(workshopEntityList)) {
            return ResponseDTO.ok(Collections.emptyList());
        }

        // 查询所有负责人
        List<Long> managerIds = workshopEntityList.stream()
                .map(WorkshopEntity::getManagerId)
                .collect(Collectors.toList());
        Map<Long, String> empMap = null;
        if (CollUtil.isNotEmpty(managerIds)) {
            List<EmployeeEntity> employeeEntities = employeeDao.selectList(new LambdaQueryWrapper<EmployeeEntity>().in(EmployeeEntity::getEmployeeId, managerIds));
            empMap = employeeEntities.stream()
                    .collect(Collectors.toMap(EmployeeEntity::getEmployeeId, EmployeeEntity::getActualName));
        }

        List<WorkshopVO> workshopVOList = SmartBeanUtil.copyList(workshopEntityList, WorkshopVO.class);
        //如果没有负责人，直接返回
        if (CollUtil.isEmpty(empMap)) {
            return ResponseDTO.ok(workshopVOList);
        }

        for (WorkshopVO vo : workshopVOList) {
            vo.setManager((empMap.get(vo.getManagerId())));
        }
        return ResponseDTO.ok(workshopVOList);
    }
}
