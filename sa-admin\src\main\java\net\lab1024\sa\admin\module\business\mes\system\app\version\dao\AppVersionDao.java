package net.lab1024.sa.admin.module.business.mes.system.app.version.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.system.app.version.domain.entity.AppVersionEntity;
import net.lab1024.sa.admin.module.business.mes.system.app.version.domain.form.AppVersionQueryForm;
import net.lab1024.sa.admin.module.business.mes.system.app.version.domain.vo.AppVersionVO;

import java.util.List;

/**
 * app版本管理 Dao
 *
 * <AUTHOR>
 * @Date 2025-02-25 14:44:40
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface AppVersionDao extends BaseMapper<AppVersionEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<AppVersionVO> queryPage(Page page, @Param("queryForm") AppVersionQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
