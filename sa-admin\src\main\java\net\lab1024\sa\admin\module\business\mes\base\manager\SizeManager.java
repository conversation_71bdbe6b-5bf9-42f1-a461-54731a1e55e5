package net.lab1024.sa.admin.module.business.mes.base.manager;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.lab1024.sa.admin.module.business.mes.base.constant.SizeAndColorConstant;
import net.lab1024.sa.admin.module.business.mes.base.dao.SizeDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.SizeEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SizeAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SizeUpdateForm;
import net.lab1024.sa.base.common.exception.BusinessException;
import org.springframework.stereotype.Service;

import static net.lab1024.sa.admin.module.business.mes.base.constant.SizeAndColorConstant.SPLIT_CHAR;

/**
 * 尺码表  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-05 22:37:21
 * @Copyright zscbdic
 */
@Service
public class SizeManager extends ServiceImpl<SizeDao, SizeEntity> {


    /**
     * 添加校验
     *
     * @param addForm
     */
    public void addCheck(SizeAddForm addForm) {
        String sizeMessage = addForm.getSizeMessage();
        if (sizeMessage.contains(SizeAndColorConstant.SPLIT_CHAR)) {
            throw new BusinessException("尺码信息不能包含" + SPLIT_CHAR);
        }

        Long count = this.lambdaQuery().eq(SizeEntity::getSizeMessage, sizeMessage).count();
        if (count > 0) {
            throw new BusinessException("尺码"+sizeMessage+"已存在");
        }
    }

    /**
     * 修改校验
     *
     * @param updateForm
     */
    public void updateCheck(SizeUpdateForm updateForm) {
        String sizeMessage = updateForm.getSizeMessage();
        if (sizeMessage.contains(SizeAndColorConstant.SPLIT_CHAR)) {
            throw new BusinessException("尺码信息不能包含" + SPLIT_CHAR);
        }
        Long count = this.lambdaQuery().eq(SizeEntity::getSizeMessage, sizeMessage)
                .ne(SizeEntity::getId, updateForm.getId())
                .count();
        if (count > 0) {
            throw new BusinessException("尺码"+sizeMessage+"已存在");
        }
    }
}
