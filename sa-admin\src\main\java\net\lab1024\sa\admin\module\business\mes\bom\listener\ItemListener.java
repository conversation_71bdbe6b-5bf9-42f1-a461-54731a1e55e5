package net.lab1024.sa.admin.module.business.mes.bom.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.event.item.ItemDeleteCheckEvent;
import net.lab1024.sa.admin.module.business.mes.bom.dao.BomDao;
import net.lab1024.sa.admin.module.business.mes.bom.dao.BomDetailDao;
import net.lab1024.sa.admin.module.business.mes.bom.domain.entity.BomDetailEntity;
import net.lab1024.sa.admin.module.business.mes.bom.domain.entity.BomEntity;
import net.lab1024.sa.base.common.exception.BusinessException;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component("bomItemListener")
public class ItemListener {

    @Resource
    private BomDao bomDao;

    @Resource
    private BomDetailDao bomDetailDao;

    @EventListener(ItemDeleteCheckEvent.class)
    public void itemDeleteCheck(ItemDeleteCheckEvent event) {
        Long count = bomDao.selectCount(new LambdaQueryWrapper<BomEntity>()
                .eq(BomEntity::getItemId, event.getId()));
        if (count > 0) {
            throw new BusinessException("该物料正在被BOM使用，不能删除");
        }
        count = bomDetailDao.selectCount(new LambdaQueryWrapper<BomDetailEntity>()
                .eq(BomDetailEntity::getItemId, event.getId()));
        if (count > 0) {
            throw new BusinessException("该物料正在被BOM明细使用，不能删除");
        }
    }

}
