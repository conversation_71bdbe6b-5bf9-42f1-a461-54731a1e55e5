package net.lab1024.sa.admin.module.business.mes.report.stock.inout.controller;

import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import net.lab1024.sa.admin.module.business.mes.report.stock.inout.domain.form.ProInAndOutStockReportQueryForm;
import net.lab1024.sa.admin.module.business.mes.report.stock.inout.domain.vo.ProInAndOutStockReportVO;
import net.lab1024.sa.admin.module.business.mes.report.stock.inout.serivce.ProInAndOutStockReportService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 生产入库出库报表
 *
 * <AUTHOR>
 */
@RestController("")
public class ProInAndOutStockReportController {

    @Resource
    private ProInAndOutStockReportService proInAndOutStockReportService;

    /**
     * 生产出入库报表
     * @param queryForm
     * @return
     */
    @Operation(summary = "生产出入库报表分页查询")
    @PostMapping("/proInAndOutStockReport/summaryPage")
    public ResponseDTO<PageResult<ProInAndOutStockReportVO>> querySummaryPage(@RequestBody @Valid ProInAndOutStockReportQueryForm queryForm) {
        return proInAndOutStockReportService.querySummaryPage(queryForm);
    }

}
