package net.lab1024.sa.admin.module.business.mes.item.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 成衣信息表 实体类
 *
 * <AUTHOR>
 * @Date 2024-07-09 11:23:49
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_item_clothes")
public class ItemClothesEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 季度id
     */
    private Long seasonId;

    /**
     * 款式品类id
     */
    private Long styleId;

    /**
     * 物料id
     */
    private Long itemId;


    /**
     * 尺寸;sku信息
     */
    private String size;

    /**
     * 款式颜色;sku信息
     */
    private String styleColor;

    /**
     * 部件;sku信息
     */
    private String parts;



}
