package net.lab1024.sa.admin.module.business.mes.salary.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 工资字段值 列表VO
 *
 * <AUTHOR>
 * @Date 2024-11-25 20:28:37
 * @Copyright zscbdic
 */

@Data
public class WageFieldValueVO {

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 配置值id
     */
    private Long fieldValueId;

    /**
     * 字段值
     */
    private BigDecimal fieldValue;

    /**
     * 配置值
     */
//    private BigDecimal value;

    /**
     * 运算类型
     */
    private String type;



}
