package net.lab1024.sa.admin.module.business.mes.part.station.take.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import net.lab1024.sa.admin.module.business.mes.part.station.take.domain.form.PartStationStockTakeForm;
import net.lab1024.sa.admin.module.business.mes.part.station.take.service.PartStationStockTakeService;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 裁片驿站盘库 Controller
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:44:34
 * @Copyright zscbdic
 */
@RestController
@Tag(name = "")
public class PartStationStockTakeController {

    @Resource
    private PartStationStockTakeService partStationStockTakeService;

    /**
     * 裁片驿站盘库 <AUTHOR>
     * @param form
     * @return
     */
    @Operation(summary = "裁片驿站盘库 <AUTHOR>
    @PostMapping("/partStationStockTake/stockTake")
    public ResponseDTO<String> stockTake(@Valid @RequestBody PartStationStockTakeForm form) {
       return partStationStockTakeService.stockTake(form);
    }

}
