package net.lab1024.sa.admin.module.business.mes.bom.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 物料BOM表 实体类
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:25:22
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_bom")
public class BomEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * bom编号
     */
    private String bomNumber;

    /**
     * bom名称
     */
    private String bomName;

    /**
     * 版本号
     */
    private Integer versionNumber;

    /**
     * 物料id
     */
    private Long itemId;

    /**
     * 物料名称
     */
    private String itemName;

    /**
     * 物料编号
     */
    private String itemNumber;

    /**
     * 物料规格型号
     */
    private String itemModel;

    /**
     * 物料分类id
     */
    private Long itemTypeId;

    /**
     * 物料类型
     */
    private String itemCategory;

    /**
     * 物料单位id
     */
    private Long itemUnitId;

    /**
     * 物料单位名称
     */
    private String  itemUnitName;

    /**
     * 物料属性
     */
    private String itemAttribute;

    /**
     * 停用标识
     */
    private Boolean enableFlag;

}
