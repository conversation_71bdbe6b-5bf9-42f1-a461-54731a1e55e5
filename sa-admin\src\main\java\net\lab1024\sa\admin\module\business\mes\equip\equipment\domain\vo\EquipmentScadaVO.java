package net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 设备scada信息 列表VO
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:13:22
 * @Copyright zscbdic
 */

@Data
public class EquipmentScadaVO {






    @Schema(description = "设备ID")
    private Long equipmentId;

    @Schema(description = "是否物联网连接;0否,1是")
    private Boolean iotNetworkFlag;

    @Schema(description = "物联网平台;yuanyi元一")
    private String iotNetworkPlatform;

    @Schema(description = "设备ID(SCADA);保留字段")
    private String scadaEquipmentId;

    @Schema(description = "产品编码(SCADA);SCADA产品编码")
    private String productCode;

    @Schema(description = "设备编码(SCADA);SCADA设备编码")
    private String equipmentCode;

}
