package net.lab1024.sa.admin.module.business.mes.stock.in.controller;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.common.bill.constant.BillType;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockBillStatusEnum;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.form.StkInStockQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.form.StkOtherInStockAddForm;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.form.StkOtherInStockUpdateForm;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.vo.StkInStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.vo.StkOtherInStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.in.service.StkOtherInStockService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 其他入库单 Controller
 *
 * <AUTHOR>
 * @Date 2025-02-03 15:55:23
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class StkOtherInStockController {

    @Resource
    private StkOtherInStockService stkOtherInStockService;

    @Resource
    private SerialNumberService serialNumberService;


    /**
     * 分页查询
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/stkOtherInStock/queryPage")
    public ResponseDTO<PageResult<StkInStockVO>> queryPage(@RequestBody StkInStockQueryForm queryForm) {
        queryForm.setType(BillType.STOCK_OTHER_IN.getValue());
        return stkOtherInStockService.queryPage(queryForm);
    }


    /**
     * 添加
     *
     * @param form
     * @return
     */
    @PostMapping("/stkOtherInStock/add")
    public ResponseDTO<String> otherInStock(@RequestBody @Valid StkOtherInStockAddForm form) {
        form.setType(BillType.STOCK_OTHER_IN.getValue());
        if (StrUtil.isBlank(form.getStatus())) {
            form.setStatus(StockBillStatusEnum.UN_AUDIT.getValue());
        }
        if (StrUtil.isBlank(form.getNumber())) {
            form.setNumber(serialNumberService.generate(SerialNumberIdEnum.STOCK_IN_OTHER));
        }
        return stkOtherInStockService.add(form);
    }

    /**
     * 修改其他入库单
     *
     * @param form
     * @return
     */
    @PostMapping("/stkOtherInStock/update")
    public ResponseDTO<String> update(@RequestBody @Valid StkOtherInStockUpdateForm form) {
        return stkOtherInStockService.update(form);
    }

    /**
     * 根据id查询其他入库单
     *
     * @param id
     * @return
     */
    @GetMapping("stkOtherInStock/byId")
    public ResponseDTO<StkOtherInStockVO> queryById(@RequestParam("id") Long id) {
        return stkOtherInStockService.queryById(id);
    }


    /**
     * 修改单据状态
     *
     * @param id
     * @return
     */
    @GetMapping("stkOtherInStock/status")
    public ResponseDTO<String> updateStatus(@RequestParam("id") Long id) {
        return stkOtherInStockService.updateStatus(id);
    }

    /**
     * 删除单据
     * @param id
     * @return
     */
    @Operation(summary = "删除单据 <AUTHOR>
    @GetMapping("stkOtherInStock/delete")
    public ResponseDTO<String> delete(@RequestParam("id") Long id) {
        return stkOtherInStockService.delete(id);
    }
}
