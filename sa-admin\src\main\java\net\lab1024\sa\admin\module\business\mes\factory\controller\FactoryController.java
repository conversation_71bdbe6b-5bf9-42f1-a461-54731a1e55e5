package net.lab1024.sa.admin.module.business.mes.factory.controller;


import org.redisson.api.RedissonClient;
import org.redisson.client.RedisClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.FactoryAddForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.FactoryQueryForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.FactoryUpdateForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.vo.FactoryVO;
import net.lab1024.sa.admin.module.business.mes.factory.service.FactoryService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;


/**
 * 工厂信息 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-04 11:11:50
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "工厂信息接口")
public class FactoryController {

    @Resource
    private FactoryService factoryService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/factory/queryPage")
    public ResponseDTO<PageResult<FactoryVO>> queryPage(@RequestBody @Valid FactoryQueryForm queryForm) {
        return ResponseDTO.ok(factoryService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/factory/add")
    public ResponseDTO<String> add(@RequestBody @Valid FactoryAddForm addForm) {
        return factoryService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/factory/update")
    public ResponseDTO<String> update(@RequestBody @Valid FactoryUpdateForm updateForm) {
        return factoryService.update(updateForm);
    }


    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/factory/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return factoryService.delete(id);
    }

    /**
     * 下拉列表
     * @return
     */
    @Operation(summary = "下拉列表 <AUTHOR>
    @PostMapping("/factory/queryList")
    public ResponseDTO<List<FactoryVO>> queryList(){
        return factoryService.queryList();
    }
}
