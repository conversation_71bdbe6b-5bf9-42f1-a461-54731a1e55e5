package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 生产指令工序信息 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:24:08
 * @Copyright zscbdic
 */

@Data
public class ProduceInstructProcessVO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "指令单id")
    private Long orderId;

    @Schema(description = "序号")
    private Integer serialNumber;

    @Schema(description = "工序名称")
    private String name;

    @Schema(description = "部位")
    private String position;

    @Schema(description = "工序类型")
    private String processType;

    @Schema(description = "标准工时;单位")
    private Integer standardTime;

    @Schema(description = "工价一")
    private BigDecimal unitPrice1;

    @Schema(description = "工价二")
    private BigDecimal unitPrice2;

    @Schema(description = "工价三")
    private BigDecimal unitPrice3;

    @Schema(description = "工价四")
    private BigDecimal unitPrice4;

    @Schema(description = "工价五")
    private BigDecimal unitPrice5;

    @Schema(description = "工价六")
    private BigDecimal unitPrice6;

//    @Schema(description = "车间id")
//    private Long workshopId;

//    @Schema(description = "末道工序;0否 1是")
//    private Boolean endFlag;
//
//    @Schema(description = "是否审核;0不审核 1审核")
//    private Boolean auditFlag;
//
//    @Schema(description = "是否超时生产;0否 1是")
//    private Boolean overflowWorkFlag;
//
//    @Schema(description = "工序控制;0自制 1委外 2不限")
//    private String processControl;

//    @Schema(description = "sopId;保留")
//    private Long sopId;

//    /**
//     * 应生产数量
//     */
//    @Schema(description = "应生产数量")
//    private Integer shouldNum;
//
//    /**
//     * 已生产数量
//     */
//    @Schema(description = "已生产数量")
//    private Integer finishNum;

}
