package net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form;

import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.constant.IotEquipmentTypeEnum;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.constant.IotPlatformEnum;
import net.lab1024.sa.base.common.domain.PageParam;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;

/**
 * 设备scada信息 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:13:22
 * @Copyright zscbdic
 */

@Data
public class EquipmentScadaQueryForm extends PageParam{

    private String queryKey;

    private Boolean iotNetworkFlag;

    /**
     * 物联网平台;yuanyi元一
     */
    @CheckEnum(value = IotPlatformEnum.class, message = "物联网平台 值不合法",required = true)
    private String iotNetworkPlatform;

    /**
     * 对接设备类型
     */
    @CheckEnum(value = IotEquipmentTypeEnum.class, message = "对接设备类型 值不合法",required = false)
    private String iotEquipmentType;



}
