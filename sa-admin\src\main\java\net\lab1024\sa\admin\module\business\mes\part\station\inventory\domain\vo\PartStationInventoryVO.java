package net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 裁片驿站库存表 列表VO
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:44:34
 * @Copyright zscbdic
 */

@Data
public class PartStationInventoryVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

//    @Schema(description = "删除标识;0未删除，1删除")
//    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "菲票id")
    private Long feTicketId;

    @Schema(description = "货位id")
    private Long binId;

    @Schema(description = "货位编码")
    private String binCode;

    @Schema(description = "入库时间")
    private LocalDateTime stockInTime;

    @Schema(description = "最后盘库时间")
    private LocalDateTime lastCheckTime;


    //-------------------菲票------------------------------
    /**
     * 生产指令单编号
     */
    private String instructOrderNumber;

    /**
     * 生产指令单名称
     */
    private String instructOrderName;

    /**
     * 生产指令单id
     */
    private Long instructOrderId;

    /**
     * 裁床单id
     */
    private Long cutBedSheetId;

    /**
     * 裁床单编号
     */
    private String cutBedSheetNumber;

    /**
     * 裁床单名称
     */
    private String cutBedSheetName;

    /**
     * 物料id
     */
    private Long itemId;

    /**
     * 物料编号
     */
    private String itemNumber;

    /**
     * 规格型号
     */
    private String model;

    /**
     * 物料名称
     */
    private String itemName;

    /**
     * 物料单位id
     */
    private Long unitId;

    /**
     * 物料单位名称
     */
    private String unitName;

    /**
     * 物料属性;0面料，1其他，2成衣
     */
    private String attribute;

    /**
     * 物料类型;0半成品 1成品
     */
    private String category;

    /**
     * 扎号
     */
    private Integer tieNum;

    /**
     * 款式颜色
     */
    private String styleColor;

    /**
     * 尺码
     */
    private String size;

    /**
     * 床次
     */
    private Integer cutNum;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 缸号
     */
    private String lotNo;

    /**
     * 部位
     */
    private String positions;

    /**
     * 最后状态;保留
     */
    private String lastStatus;

    /**
     * 流转状态;保留
     */
    private String flowStatus;

}
