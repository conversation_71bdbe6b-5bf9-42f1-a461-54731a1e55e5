package net.lab1024.sa.admin.module.business.mes.base.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.base.dao.SeasonDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.SeasonEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.SeasonTreeVO;
import net.lab1024.sa.base.common.code.UserErrorCode;
import net.lab1024.sa.base.common.constant.StringConst;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 季度表  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-05 15:45:34
 * @Copyright zscbdic
 */
@Service
public class SeasonManager extends ServiceImpl<SeasonDao, SeasonEntity> {
    @Resource
    SeasonDao seasonDao;
    /**
     * 根据季度名称查询树表
     * @param parentId
     * @param queryKey
     * @return
     */
    public List<SeasonTreeVO> querySeasonTree(Long parentId, String queryKey) {
        //获取未删除的季度名称
        List<SeasonEntity> allEntityList = seasonDao.queryByKey(queryKey, false);
        //获取所有根节点
        List<SeasonEntity> rootList = allEntityList.stream()
                .filter(e -> e.getParentId().equals(parentId))
                .toList();
        //设置VO对象属性
        List<SeasonTreeVO> rootListVo = BeanUtil.copyToList(rootList, SeasonTreeVO.class);
        rootListVo.forEach(e->{
            e.setLabel(e.getSeasonName());
            e.setValue(e.getId());
            e.setFullName(e.getSeasonName());
        });
        queryAndSetSubType(rootListVo,allEntityList);
        return rootListVo;
    }

    /**
     * 递归查询子类
     * @param treeList
     * @param allTypeEntityList
     */
    private void queryAndSetSubType(List<SeasonTreeVO> treeList, List<SeasonEntity> allTypeEntityList){
        if (CollUtil.isEmpty(treeList)) {
            return;
        }
        //获取所有父ID
        List<Long> parentIds = treeList.stream().map(SeasonTreeVO::getId).toList();
        //得到子节点
        List<SeasonEntity> subEntityList = allTypeEntityList.stream()
                .filter(e -> parentIds.contains(e.getParentId()))
                .toList();
        //以父Id为key进行聚合
        Map<Long, List<SeasonEntity>> map = subEntityList.stream()
                .collect(Collectors.groupingBy(SeasonEntity::getParentId));
        //
        treeList.forEach(e->{
            List<SeasonEntity> subList = map.getOrDefault(e.getId(), new ArrayList<>());
            List<SeasonTreeVO> subListVo = BeanUtil.copyToList(subList, SeasonTreeVO.class);
            subListVo.forEach(item->{
                item.setLabel(item.getSeasonName());
                item.setValue(item.getId());
                item.setFullName(e.getFullName()+ StringConst.SEPARATOR_SLASH + item.getSeasonName());
            });
            //递归调用
            this.queryAndSetSubType(subListVo,allTypeEntityList);
            e.setChildren(subListVo);
        });
    }

    public ResponseDTO<String> checkSeasonType(SeasonEntity seasonEntity, boolean isUpdate) {
        //获取父级id
        Long parentId = seasonEntity.getParentId();
        //校验是否为根节点
        if (parentId != null) {
            //不是根节点
            if (Objects.equals(seasonEntity.getId(), parentId)) {
                return ResponseDTO.userErrorParam("父级类目与自己相同了");
            }
            //校验根节点是否存在
            if (!Objects.equals(parentId, NumberUtils.LONG_ZERO)) {
                SeasonEntity parentEntity = this.getById(parentId);
                Optional<SeasonEntity> optional = Optional.ofNullable(parentEntity);
                if (optional.isEmpty()) {
                    return ResponseDTO.error(UserErrorCode.DATA_NOT_EXIST, "父级分类不存在~");
                }
            }
        }else{
            //是根节点,parentID置0
            parentId = NumberUtils.LONG_ZERO;
        }

        //校验同父节点里，名称是否重复
        SeasonEntity se = new SeasonEntity();
        //设置校验条件
        se.setParentId(parentId);
        se.setSeasonName(seasonEntity.getSeasonName());
        se.setDeletedFlag(false);

        SeasonEntity isExist = seasonDao.selectOne(new QueryWrapper<>(se));
        if (isExist != null) {
            //存在
            if (!isUpdate) {
                return ResponseDTO.userErrorParam("同级下已存在相同类目~");
            }
            if (!(isExist.getId().equals(seasonEntity.getId()))) {
                return ResponseDTO.userErrorParam("同级下已存在相同类目~");
            }
        }
        return ResponseDTO.ok();
    }

    public List<Long> queryTypeSubId(List<Long> ids) {
        //空直接返回
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        //
        ArrayList<SeasonEntity> subIdLists = Lists.newArrayList();
        ids.forEach(e-> subIdLists.addAll(querySubType(e)));
        Map<Long, List<SeasonEntity>> collect = subIdLists.stream()
                .collect(Collectors.groupingBy(SeasonEntity::getId));
        // 递归查询子类
        ids = collect.values().stream()
                .flatMap(Collection::stream).map(SeasonEntity::getId).distinct().collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        ids.addAll(this.queryTypeSubId(ids));
        return ids;
    }

    public List<SeasonEntity> querySubType(Long parentId){
        return seasonDao.queryByParentId(Lists.newArrayList(parentId), false);
    }
}
