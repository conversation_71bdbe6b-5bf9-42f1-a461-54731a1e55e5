package net.lab1024.sa.admin.module.business.mes.part.station.turn.task.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.entity.PartStationTurnTaskEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.form.PartStationTurnTaskQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.vo.PartStationTurnTaskVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 驿站任务表 Dao
 *
 * <AUTHOR>
 * @Date 2025-03-14 20:13:46
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface PartStationTurnTaskDao extends BaseMapper<PartStationTurnTaskEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<PartStationTurnTaskVO> queryPage(Page page, @Param("queryForm") PartStationTurnTaskQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
