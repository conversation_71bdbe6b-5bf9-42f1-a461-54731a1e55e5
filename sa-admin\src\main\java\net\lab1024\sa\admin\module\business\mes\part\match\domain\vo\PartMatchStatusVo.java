package net.lab1024.sa.admin.module.business.mes.part.match.domain.vo;

import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.domain.vo.FeTicketVO;

import java.util.List;

@Data
public class PartMatchStatusVo {

    /**
     * 是否完成 true 完成 false 未完成
     */
    private Boolean complete;

    /**
     * 目标完整配扎菲票
     */
    private List<FeTicketVO> targetTickets;

    /**
     * 目标菲票ids
     */
    private List<Long> targetTicketIds;

    /**
     * 当前配扎菲票
     */
    private List<FeTicketVO> nowTickets;

    /**
     * 当前菲票ids
     */
    private List<Long> nowTicketIds;
}
