package net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 周转箱内容 新建表单
 *
 * <AUTHOR>
 * @Date 2025-03-14 15:03:20
 * @Copyright zscbdic
 */

@Data
public class PartStationTurnBoxInsideForm {

    @Schema(description = "周转箱ID", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "周转箱ID 不能为空")
    private Long turnBoxId;

    @Schema(description = "菲票ID", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "菲票ID 不能为空")
    private Long feTicketId;

}
