package net.lab1024.sa.admin.module.business.mes.ai.chat.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.mes.ai.chat.domain.form.AiChatMessageForm;
import net.lab1024.sa.admin.module.business.mes.ai.chat.domain.vo.AiChatMessageVO;
import net.lab1024.sa.admin.module.business.mes.ai.chat.domain.vo.AiChatVO;
import net.lab1024.sa.admin.module.business.mes.ai.core.assistant.ChatAssistant;
import net.lab1024.sa.admin.module.business.mes.ai.core.assistant.TransformEntityDTO;
import net.lab1024.sa.admin.module.business.mes.ai.core.config.AssistantConfig;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class ChatService {

    @Resource
    private ChatAssistant chatAssistant;

    @Resource
    private AssistantConfig assistantConfig;


    /**
     * 聊天
     *
     * @param form
     * @return
     */
    public Flux<ResponseDTO<AiChatVO>> chatStream(AiChatMessageForm form) {
        String content = form.getContent();

        Long userId = SmartRequestUtil.getRequestUserId();

        Flux<String> streamResponse = chatAssistant.chatStream(String.valueOf(userId), content);
        return streamResponse.map(chunk -> {
            String newContent = CharSequenceUtil.nullToDefault(chunk, "");
            AiChatVO.Message receive = new AiChatVO.Message()
                    .setContent(newContent)
                    .setType(MessageType.ASSISTANT.getValue());

            AiChatVO msgVo = new AiChatVO().setReceive(receive);

            return ResponseDTO.ok(msgVo);
        }).doOnError(throwable -> log.error("[chatStream][userId({}) form({}) 发生异常]", userId, form, throwable))
                .onErrorResume(error -> Flux.just(ResponseDTO.userErrorParam("对话生成异常!")))
                ;
    }

    /**
     * 聊天
     *
     * @param form
     * @return
     */
    public ResponseDTO<AiChatVO> chat(AiChatMessageForm form) {
        String reqContent = form.getContent();
        Long userId = SmartRequestUtil.getRequestUserId();

        try {
            String response = chatAssistant.chat(String.valueOf(userId), reqContent);

            AiChatVO.Message receive = new AiChatVO.Message()
                    .setContent(response)
                    .setType(MessageType.ASSISTANT.getValue());
            AiChatVO msgVo = new AiChatVO()
                    .setReceive(receive);

            return ResponseDTO.ok(msgVo);
        } catch (Exception e) {
            log.error("[chatStream][userId({}) form({}) 发生异常]", userId, form, e);
            return ResponseDTO.userErrorParam("对话生成异常!");
        }


    }

    /**
     * 清除当前聊天数据
     */
    public void clearChat() {
        Long userId = SmartRequestUtil.getRequestUserId();
        chatAssistant.clearChat(String.valueOf(userId));
    }


    public List<AiChatMessageVO> getHistory() {
        Long userId = SmartRequestUtil.getRequestUserId();
        List<Message> chatHistory = chatAssistant.getChatHistory(String.valueOf(userId));
        if (CollUtil.isEmpty(chatHistory)) {
            return Collections.emptyList();
        }
        return chatHistory.stream().map(e -> {
            AiChatMessageVO message = new AiChatMessageVO();
            message.setContent(e.getText());
            message.setType(e.getMessageType().getValue());
            return message;
        }).toList();

    }

    /**
     * 获取推荐提示
     *
     * @return
     */
    public ResponseDTO<List<String>> recommendPrompt() {
        AssistantConfig.Config config = assistantConfig.getConfig();
        if (Boolean.FALSE.equals(config.getUserRecommendPromptFlag())) {
            return ResponseDTO.ok(Collections.emptyList());
        }
        Long userId = SmartRequestUtil.getRequestUserId();
        String chatId = String.valueOf(userId);
        Long userRecommendPromptModelId = config.getUserRecommendPromptModelId();
        // 获取聊天记录
        List<Message> chatHistory = chatAssistant.getChatHistory(chatId);
        if (CollUtil.isEmpty(chatHistory)) {
            return ResponseDTO.ok(Collections.emptyList());
        }
        //获取最后一条用户消息
        Optional<Message> userMesOpt = chatHistory.stream().filter(e -> e.getMessageType().equals(MessageType.USER)).reduce((first, second) -> second);
        if (userMesOpt.isEmpty()) {
            return ResponseDTO.ok(Collections.emptyList());
        }
        // 构造请求体
        TransformEntityDTO transformEntityDTO = new TransformEntityDTO(userMesOpt.get().getText(), config.getUserRecommendSystemPrompt(), userRecommendPromptModelId, chatId);
        transformEntityDTO.setHistoryFlag(false);
        List<String> strings = chatAssistant.transformEntity(transformEntityDTO, new ParameterizedTypeReference<List<String>>() {
        });
        return ResponseDTO.ok(strings);
    }
}
