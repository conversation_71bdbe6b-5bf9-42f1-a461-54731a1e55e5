package net.lab1024.sa.admin.module.business.mes.part.station.turn.task.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.event.part.station.PartStationTurnTaskSubmitEvent;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.manager.PartStationTurnBoxManager;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskStatusEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskTypeEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.entity.PartStationTurnTaskEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.manager.PartStationTurnTaskManager;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationBinEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationWarehouseEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.manager.PartStationBinManager;
import net.lab1024.sa.base.common.enumeration.UserTypeEnum;
import net.lab1024.sa.base.config.AsyncConfig;
import net.lab1024.sa.base.module.support.message.constant.MessageTypeEnum;
import net.lab1024.sa.base.module.support.message.domain.MessageSendForm;
import net.lab1024.sa.base.module.support.message.service.MessageService;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Component("psPartStationTurnTaskListener")
public class PartStationTurnTaskListener {

    @Resource
    private MessageService messageService;

    @Resource
    private PartStationTurnTaskManager partStationTurnTaskManager;

    @Resource
    private PartStationBinManager partStationBinManager;

    @Resource
    private PartStationTurnBoxManager partStationTurnBoxManager;

    @Async(value = AsyncConfig.ASYNC_EXECUTOR_THREAD_NAME)
    @EventListener(PartStationTurnTaskSubmitEvent.class)
    public void handleSubmit(PartStationTurnTaskSubmitEvent event) {
        Long taskId = event.getTaskId();
        PartStationTurnTaskEntity task = partStationTurnTaskManager.getById(taskId);
        if (task == null) {
            return;
        }
        PartStationTurnBoxEntity box = partStationTurnBoxManager.getById(task.getTurnoverBoxId());
        if (box == null) {
            return;
        }
        Long startLocationId = task.getStartLocationId();
        PartStationBinEntity bin = partStationBinManager.getById(startLocationId);
        if (bin == null) {
            return;
        }
        PartStationWarehouseEntity warehouse = partStationBinManager.getWarehouseById(startLocationId);
        if (warehouse == null) {
            return;
        }
        String taskerIdsStr = warehouse.getTaskerIds();
        if (StrUtil.isBlank(taskerIdsStr)) {
            return;
        }
        List<Long> taskerIds = JSON.parseArray(taskerIdsStr, Long.class);
        if (CollUtil.isEmpty(taskerIds)) {
            return;
        }

        String content = """
                您有一个任务待处理，任务号【${taskNumber}】
                任务类型：${taskType}
                任务状态：${taskStatus}
                周转箱编号：${turnoverBoxNumber}
                起点货位：${startLocationNumber}
                起点货位描述：${startLocationDesc}
                时间：${taskTime}
                """;
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("taskNumber", task.getNumber());
        paramMap.put("taskType", Objects.requireNonNull(TurnTaskTypeEnum.getEnum(task.getType())).getDesc());
        paramMap.put("taskStatus", Objects.requireNonNull(TurnTaskStatusEnum.getByValue(task.getStatus())).getDesc());
        paramMap.put("turnoverBoxNumber", box.getNumber());
        paramMap.put("startLocationNumber", bin.getBinCode());
        paramMap.put("startLocationDesc", StrUtil.isBlank(bin.getBinDesc()) ? "暂无" : bin.getBinDesc());
        paramMap.put("taskTime", LocalDateTimeUtil.format(task.getSubmitTime(), "yyyy-MM-dd HH:mm:ss"));
        StringSubstitutor stringSubstitutor = new StringSubstitutor(paramMap);
        String contentStr = stringSubstitutor.replace(content);
        List<MessageSendForm> messages = taskerIds.stream().map(id -> {
            MessageSendForm messageSendForm = new MessageSendForm();
            messageSendForm.setMessageType(MessageTypeEnum.MAIL.getValue());
            messageSendForm.setReceiverUserType(UserTypeEnum.ADMIN_EMPLOYEE.getValue());
            messageSendForm.setReceiverUserId(id);
            messageSendForm.setTitle("裁片驿站助手 | 驿站任务");
            messageSendForm.setContent(contentStr);
            messageSendForm.setDataId(taskId);
            return messageSendForm;
        }).toList();
        messageService.sendMessage(messages);

    }
}
