package net.lab1024.sa.admin.module.business.mes.stock.warehouse.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkLocationAddForm;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkLocationQuery;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkLocationQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkLocationUpdateForm;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.vo.StkLocationVO;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.service.StkLocationService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 货位 Controller
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:14:20
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class StkLocationController {

    @Resource
    private StkLocationService stkLocationService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/stkLocation/queryPage")
    public ResponseDTO<PageResult<StkLocationVO>> queryPage(@RequestBody @Valid StkLocationQueryForm queryForm) {
        return ResponseDTO.ok(stkLocationService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/stkLocation/add")
    public ResponseDTO<String> add(@RequestBody @Valid StkLocationAddForm addForm) {
        return stkLocationService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/stkLocation/update")
    public ResponseDTO<String> update(@RequestBody @Valid StkLocationUpdateForm updateForm) {
        return stkLocationService.update(updateForm);
    }


    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/stkLocation/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return stkLocationService.delete(id);
    }


    /**
     * 查询列表
     * @param query
     * @return
     */
    @Operation(summary = "查询列表 <AUTHOR>
    @PostMapping("/stkLocation/queryList")
    public ResponseDTO<List<StkLocationVO>> queryList(@RequestBody @Valid StkLocationQuery query) {
        return stkLocationService.queryList(query);
    }
}
