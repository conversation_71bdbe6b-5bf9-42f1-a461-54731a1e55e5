package net.lab1024.sa.admin.module.business.mes.stock.in.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.event.stock.ProduceReturnMaterialEvent;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockBillStatusEnum;
import net.lab1024.sa.admin.module.business.mes.stock.in.dao.StkInStockDao;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.bo.StkInStockBO;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.entity.StkInStockDetailEntity;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.entity.StkInStockEntity;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.form.StkInStockQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.form.StkProdReturnMtrlAddForm;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.form.StkProdReturnMtrlUpdateForm;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.vo.StkInStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.vo.StkProdReturnMtrlVO;
import net.lab1024.sa.admin.module.business.mes.stock.in.manager.StkInStockDetailManager;
import net.lab1024.sa.admin.module.business.mes.stock.in.manager.StkInStockManager;
import net.lab1024.sa.admin.module.business.mes.stock.lot.manager.StkLotMasterManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class StkProdReturnMtrlService {

    @Resource
    private StkInStockManager stkInStockManager;

    @Resource
    private StkInStockDetailManager stkInStockDetailManager;

    @Resource
    private StkLotMasterManager stkLotMasterManager;

    @Resource
    private StkInStockService stkInStockService;

    @Resource
    private StkInStockDao stkInStockDao;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    public ResponseDTO<String> add(StkProdReturnMtrlAddForm form) {
        List<StkInStockDetailEntity> details = SmartBeanUtil.copyList(form.getDetails(), StkInStockDetailEntity.class);
        StkInStockEntity inStock = SmartBeanUtil.copy(form, StkInStockEntity.class);

        StkInStockBO stockBO = new StkInStockBO();
        stockBO.setStkInStock(inStock);
        stockBO.setDetails(details);

        if (StockBillStatusEnum.UN_AUDIT.getValue().equals(form.getStatus())) {
            //保存单据
            stkInStockService.saveBill(stockBO);
        } else if (StockBillStatusEnum.AUDIT.getValue().equals(form.getStatus())) {
            //保存并入库
            stkInStockService.saveAndStockIn(stockBO);
            pushProduceReturnMaterialEvent(stockBO);

        }
        return ResponseDTO.ok();
    }

    public ResponseDTO<String> updateStatus(Long id) {
        StkInStockEntity stockEntity = stkInStockManager.getById(id);
        if (stockEntity == null) {
            return ResponseDTO.userErrorParam("单据不存在");
        }
        String status = stockEntity.getStatus();
        stkInStockManager.updateBillStatusCheck(status);

        if (StockBillStatusEnum.UN_AUDIT.getValue().equals(status)) {
            List<StkInStockDetailEntity> detailEntities = stkInStockDetailManager.lambdaQuery()
                    .eq(StkInStockDetailEntity::getInStockId, id)
                    .list();
            StkInStockBO stockBO = new StkInStockBO();
            stockBO.setStkInStock(stockEntity);
            stockBO.setDetails(detailEntities);
            //入库
            stkInStockService.stockIn(stockBO);
            pushProduceReturnMaterialEvent(stockBO);

        }
        return ResponseDTO.ok();
    }

    public ResponseDTO<String> update(StkProdReturnMtrlUpdateForm form) {
        Long id = form.getId();
        StkInStockEntity stockEntity = stkInStockManager.getById(id);
        if (stockEntity == null) {
            return ResponseDTO.userErrorParam("单据不存在");
        }
        String status = stockEntity.getStatus();
        stkInStockManager.updateBillStatusCheck(status);



        List<StkInStockDetailEntity> details = SmartBeanUtil.copyList(form.getDetails(), StkInStockDetailEntity.class);
        StkInStockBO stockBO = new StkInStockBO();
        stockBO.setStkInStock(SmartBeanUtil.copy(form, StkInStockEntity.class));
        stockBO.setDetails(details);

        if(StockBillStatusEnum.AUDIT.getValue().equals(form.getStatus())){
            stkInStockService.updateAndStockIn(stockBO);
            pushProduceReturnMaterialEvent(stockBO);
        } else if (StockBillStatusEnum.UN_AUDIT.getValue().equals(form.getStatus())) {
            stkInStockService.updateBill(stockBO);
        }

        return ResponseDTO.ok();
    }

    public ResponseDTO<StkProdReturnMtrlVO> queryById(Long id) {
        StkInStockEntity stockEntity = stkInStockManager.getById(id);
        if (stockEntity == null) {
            return ResponseDTO.userErrorParam("单据不存在");
        }
        StkProdReturnMtrlVO vo = SmartBeanUtil.copy(stockEntity, StkProdReturnMtrlVO.class);
        List<StkInStockDetailEntity> detailEntities = stkInStockDetailManager.lambdaQuery()
                .eq(StkInStockDetailEntity::getInStockId, id)
                .list();
        List<StkProdReturnMtrlVO.DetailVO> detailVOS = SmartBeanUtil.copyList(detailEntities, StkProdReturnMtrlVO.DetailVO.class);
        vo.setDetails(detailVOS);
        return ResponseDTO.ok(vo);
    }

    public ResponseDTO<PageResult<StkInStockVO>> queryPage(StkInStockQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<StkInStockVO> list = stkInStockDao.queryPage(page, queryForm);
        PageResult<StkInStockVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return ResponseDTO.ok(pageResult);
    }



    public ResponseDTO<String> delete(Long id) {
        StkInStockEntity stkInStockEntity = stkInStockDao.selectById(id);
        if(stkInStockEntity==null){
            return ResponseDTO.userErrorParam("单据不存在");
        }
        stkInStockManager.updateBillStatusCheck(stkInStockEntity.getStatus());

        stkInStockManager.deleteBill(id);

        return ResponseDTO.ok();
    }


    /**
     * 推送领料事件
     *
     * @param stockBO
     */
    private void pushProduceReturnMaterialEvent(StkInStockBO stockBO) {
        //发送领料事件
        List<ProduceReturnMaterialEvent.Material> mList = stockBO.getDetails().stream().map(d -> {
            ProduceReturnMaterialEvent.Material m = new ProduceReturnMaterialEvent.Material();
            m.setMaterielId(d.getMaterielId());
            m.setQty(d.getQty());
            m.setProduceInstructOrderMaterielId(d.getOriginDetailId());
            return m;
        }).collect(Collectors.toList());
        eventPublisher.publishEvent(new ProduceReturnMaterialEvent(this, mList));
    }
}
