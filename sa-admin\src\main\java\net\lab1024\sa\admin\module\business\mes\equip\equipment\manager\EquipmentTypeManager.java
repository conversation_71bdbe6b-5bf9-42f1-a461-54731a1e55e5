package net.lab1024.sa.admin.module.business.mes.equip.equipment.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.dao.EquipmentDao;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.dao.EquipmentTypeDao;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.entity.EquipmentEntity;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.entity.EquipmentTypeEntity;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentTypeAddForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentTypeUpdateForm;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 设备类别  Manager
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:12:33
 * @Copyright zscbdic
 */
@Service
public class EquipmentTypeManager extends ServiceImpl<EquipmentTypeDao, EquipmentTypeEntity> {

    @Resource
    private EquipmentDao equipmentDao;

    /**
     * 添加校验
     *
     * @param addForm
     */
    public void addCheck(EquipmentTypeAddForm addForm) {
        String number = addForm.getNumber();
        long count = this.count(new LambdaQueryWrapper<EquipmentTypeEntity>()
                .eq(EquipmentTypeEntity::getNumber, number));
        if (count > 0) {
            throw new BusinessException("设备类别编号已存在");
        }
    }

    /**
     * 更新校验
     *
     * @param updateForm
     */
    public void updateCheck(EquipmentTypeUpdateForm updateForm) {
        String number = updateForm.getNumber();
        long count = this.count(new LambdaQueryWrapper<EquipmentTypeEntity>()
                .eq(EquipmentTypeEntity::getNumber, number)
                .ne(EquipmentTypeEntity::getId, updateForm.getId()));
        if (count > 0) {
            throw new BusinessException("设备类别编号已存在");
        }
    }

    /**
     * 删除校验
     *
     * @param idList
     */
    public void deleteCheck(List<Long> idList) {
        Long count = equipmentDao.selectCount(new LambdaQueryWrapper<EquipmentEntity>()
                .in(EquipmentEntity::getTypeId, idList));
        if (count > 0) {
            throw new BusinessException("设备类别存在设备，无法删除");
        }
    }

    /**
     * 删除校验
     *
     * @param id
     */
    public void deleteCheck(Long id) {
        this.deleteCheck(Lists.newArrayList(id));
    }
}
