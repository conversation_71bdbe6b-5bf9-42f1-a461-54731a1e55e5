package net.lab1024.sa.admin.module.business.mes.bom.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.mes.bom.domain.entity.BomDetailEntity;
import net.lab1024.sa.admin.module.business.mes.bom.domain.form.BomDetailQueryForm;
import net.lab1024.sa.admin.module.business.mes.bom.domain.vo.BomDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * BOM详情 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:25:50
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface BomDetailDao extends BaseMapper<BomDetailEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<BomDetailVO> queryPage(Page page, @Param("queryForm") BomDetailQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
}
