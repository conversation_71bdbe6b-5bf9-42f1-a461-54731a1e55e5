package net.lab1024.sa.admin.module.business.mes.base.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.mes.base.dao.SizeDao;
import net.lab1024.sa.admin.module.business.mes.base.dao.SizeTemplateDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.SizeEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.SizeTemplateEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.*;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.SizeInfoVO;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.SizeTemplateVO;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.SizeTemplateWithSizesVO;
import net.lab1024.sa.admin.module.business.mes.base.manager.SizeTemplateManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 尺寸模板表 Service
 *
 * <AUTHOR>
 * @Date 2024-07-05 15:45:35
 * @Copyright zscbdic
 */

@Service
@Slf4j
public class SizeTemplateService {

    @Resource
    private SizeTemplateDao sizeTemplateDao;

    @Resource
    private SizeTemplateManager sizeTemplateManager;

    @Resource
    private SizeDao sizeDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<SizeTemplateVO> queryPage(SizeTemplateQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<SizeTemplateVO> list = sizeTemplateDao.queryPage(page, queryForm);
        PageResult<SizeTemplateVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(SizeTemplateAddForm addForm) {
        //防重复校验

        SizeTemplateEntity templateEntity = new SizeTemplateEntity();
        templateEntity.setSizeCode(addForm.getSizeCode());
        templateEntity = sizeTemplateDao.selectOne(new QueryWrapper<>(templateEntity));
        if (templateEntity != null) {
            return ResponseDTO.userErrorParam("尺码编号重复了，请重新输入 ~");
        }
        SizeTemplateEntity sizeTemplateEntity = SmartBeanUtil.copy(addForm, SizeTemplateEntity.class);
        sizeTemplateDao.insert(sizeTemplateEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(SizeTemplateUpdateForm updateForm) {
        Long id = updateForm.getId();
        SizeTemplateEntity entity = sizeTemplateManager.query().eq("id", id).one();
        //优化
        Long count = sizeTemplateManager.query()
                .ne(entity.getSizeCode(), updateForm.getSizeCode())
                .eq("size_code", updateForm.getSizeCode())
                .count();
        if (count > 0) {
            return ResponseDTO.userErrorParam("尺码编号重复了，请重新输入 ~");
        }
        SizeTemplateEntity sizeTemplateEntity = SmartBeanUtil.copy(updateForm, SizeTemplateEntity.class);
        sizeTemplateDao.updateById(sizeTemplateEntity);
        return ResponseDTO.ok();
    }


    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }

        sizeTemplateManager.deleteAll(id);
        return ResponseDTO.ok();
    }

    //查询所有尺码信息
    public ResponseDTO<List<SizeInfoVO>> getSizeInfo(SizeTemplateQuery sizeTemplate) {

        List<SizeTemplateEntity> sizeTemplateEntityList = sizeTemplateManager.lambdaQuery()
                .like(StrUtil.isNotEmpty(sizeTemplate.getQueryKey()), SizeTemplateEntity::getTemplateName, sizeTemplate.getQueryKey())
                .like(StrUtil.isNotEmpty(sizeTemplate.getQueryKey()), SizeTemplateEntity::getSizeCode, sizeTemplate.getQueryKey())
                .list();
        if (CollectionUtil.isEmpty(sizeTemplateEntityList)) {
            return  ResponseDTO.ok(Collections.emptyList());
        }

        //对尺码模板列表进行遍历
        return sizeTemplateManager.iterList(sizeTemplateEntityList);

    }

    /**
     * @return 尺码模板下的尺码信息
     */
    public ResponseDTO<List<SizeTemplateWithSizesVO>> getSizeWithTemplateName(SizeInfoQueryForm queryForm) {
        //尺码模板信息
        LambdaQueryWrapper<SizeTemplateEntity> templateWrapper = new LambdaQueryWrapper<SizeTemplateEntity>()
                .select(SizeTemplateEntity::getId, SizeTemplateEntity::getTemplateName)
                .eq(StringUtil.isNotEmpty(queryForm.getTemplateName()), SizeTemplateEntity::getTemplateName, queryForm.getTemplateName());
        List<SizeTemplateEntity> sizeTemplateEntityList = sizeTemplateDao.selectList(templateWrapper);
        //尺码信息
        LambdaQueryWrapper<SizeEntity> sizeWrapper = new LambdaQueryWrapper<SizeEntity>()
                .select(SizeEntity::getId, SizeEntity::getTemplateId, SizeEntity::getSizeMessage)
                .eq(StringUtil.isNotEmpty(queryForm.getSizeMessage()), SizeEntity::getSizeMessage, queryForm.getSizeMessage());
        Map<Long, List<SizeEntity>> sizeMap = sizeDao.selectList(sizeWrapper).stream()
                .collect(Collectors.groupingBy(SizeEntity::getTemplateId));
        //模板信息拼接
        List<SizeTemplateWithSizesVO> result = sizeTemplateEntityList.stream()
                .map(template -> {
                    List<SizeEntity> list = sizeMap.getOrDefault(template.getId(), Collections.emptyList());
                    return new SizeTemplateWithSizesVO(template.getTemplateName(), list);
                }).collect(Collectors.toList());

        return ResponseDTO.ok(result);
    }
}
