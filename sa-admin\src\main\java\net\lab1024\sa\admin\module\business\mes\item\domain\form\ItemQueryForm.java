package net.lab1024.sa.admin.module.business.mes.item.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

import java.util.List;

/**
 * 主物料表 分页查询表单
 *
 * <AUTHOR>
 * @Date 2024-07-02 08:33:07
 * @Copyright zscbdic
 */

@Data
public class ItemQueryForm extends PageParam{

    /**
     * 关键字查询
     */
    @Schema(description = "关键字查询")
    private String queryKey;

    /**
     * 停用标识;0启用，1停用
     */
    @Schema(description = "停用标识;0启用，1停用")
    private Boolean enableFlag;

    /**
     * 属性;0面料，1其他，2成衣
     */
    @Schema(description = "属性;0面料，1辅料")
    private String attribute;

    /**
     * 属性集合
     */
    @Schema(description = "属性集合")
    private List<String> attributeList;


}
