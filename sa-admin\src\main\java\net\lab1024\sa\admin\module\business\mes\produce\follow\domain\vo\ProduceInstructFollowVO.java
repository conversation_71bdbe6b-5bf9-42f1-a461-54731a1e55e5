package net.lab1024.sa.admin.module.business.mes.produce.follow.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;
import net.lab1024.sa.base.common.json.serializer.FileKeyVoSerializer;

/**
 * 生产跟单 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-26 21:36:36
 * @Copyright zscbdic
 */

@Data
public class ProduceInstructFollowVO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "跟单员id")
    private List<Long> employeeId;

    @Schema(description = "跟单员姓名")
    private List<String> employeeName;

    @Schema(description = "委派人姓名")
    private String delegateName;

    @Schema(description = "委派人id")
    private Long delegateId;

    @Schema(description = "指令单id")
    private Long instructOrderId;

    @JsonSerialize(using = FileKeyVoSerializer.class)
    @Schema(description = "图片地址")
    private String imgUrl;

    /**
     * 客户id
     */
    @Schema(description = "客户id")
    private Long customerId;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")

    private String customerName;

    @Schema(description = "联系方式")
    private String telephone;

    /**
     * 单据编号
     */
    @Schema(description = "单据编号")
    private String instructNumber;

    @Schema(description = "优先级;0一般,1紧急,2非常紧急")
    private String priority;

    /**
     * 指令单名称
     */
    @Schema(description = "指令单名称")
    private String name;

    /**
     * 生产业务状态;0计划，1下达，2开工，3完工
     */
    @Schema(description = "生产业务状态;0计划，1下达，2开工，3完工")
    private String produceStatus;
//----------------------------基本信息-----------------

//    @Schema(description = "创建时间")
//    private LocalDateTime createTime;
//
//    @Schema(description = "创建人")
//    private String createBy;
//
//    @Schema(description = "更新时间")
//    private LocalDateTime updateTime;
//
//    @Schema(description = "更新人")
//    private String updateBy;

//    @Schema(description = "删除标识;0未删除，1删除")
//    private Integer deletedFlag;

//    @Schema(description = "备注")
//    private String remark;

}
