package net.lab1024.sa.admin.module.business.mes.process.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessLibraryAddForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessLibraryQueryForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessLibraryUpdateForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.vo.ProcessLibraryVO;
import net.lab1024.sa.admin.module.business.mes.process.service.ProcessLibraryService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 工序库 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-13 15:19:57
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "工序库接口")
public class ProcessLibraryController {

    @Resource
    private ProcessLibraryService processLibraryService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/processLibrary/queryPage")
    public ResponseDTO<PageResult<ProcessLibraryVO>> queryPage(@RequestBody @Valid ProcessLibraryQueryForm queryForm) {
        return ResponseDTO.ok(processLibraryService.queryPage(queryForm));
    }

    /**
     * 全查询
     * @return
     */
    @Operation(summary = "全查询 <AUTHOR>
    @GetMapping("/processLibrary/queryList")
    public ResponseDTO<List<ProcessLibraryVO>> queryList() {
        return ResponseDTO.ok(processLibraryService.queryList());
    }


    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/processLibrary/add")
    public ResponseDTO<String> add(@RequestBody @Valid ProcessLibraryAddForm addForm) {
        return processLibraryService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/processLibrary/update")
    public ResponseDTO<String> update(@RequestBody @Valid ProcessLibraryUpdateForm updateForm) {
        return processLibraryService.update(updateForm);
    }

    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/processLibrary/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return processLibraryService.delete(id);
    }

    /**
     * 单个查询
     * @param id
     * @return
     */
    @Operation(summary = "单个查询 <AUTHOR>
    @GetMapping("/processLibrary/{id}")
    public ResponseDTO<ProcessLibraryVO> queryById(@PathVariable Long id){
        return processLibraryService.queryById(id);
    }
}
