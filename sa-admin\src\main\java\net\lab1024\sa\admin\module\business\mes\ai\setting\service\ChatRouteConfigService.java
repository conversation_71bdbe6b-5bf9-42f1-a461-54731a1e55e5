package net.lab1024.sa.admin.module.business.mes.ai.setting.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.ai.core.config.ChatModelRouteConfig;
import net.lab1024.sa.admin.module.business.mes.ai.setting.domain.form.ChatRouteConfigUpdateForm;
import net.lab1024.sa.admin.module.business.mes.ai.setting.domain.vo.ChatRouteConfigVO;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ChatRouteConfigService {

    @Resource
    private ChatModelRouteConfig chatModelRouteConfig;

    public ChatRouteConfigVO getConfig() {
        ChatModelRouteConfig.Config config = chatModelRouteConfig.getConfig();
        ChatRouteConfigVO vo = SmartBeanUtil.copy(config, ChatRouteConfigVO.class);
        if(CollUtil.isNotEmpty(config.getItems())){
            List<ChatRouteConfigVO.Item> items = config.getItems().stream().map(e -> {
                ChatRouteConfigVO.Item item = new ChatRouteConfigVO.Item();
                item.setValue(e.getValue());
                item.setModelId(e.getModelId());
                return item;
            }).toList();
            vo.setItems(items);
        }
        return vo;
    }

    public void updateConfig(ChatRouteConfigUpdateForm form) {
        updateConfigCheck(form);
        ChatModelRouteConfig.Config config =  SmartBeanUtil.copy(form, ChatModelRouteConfig.Config.class);
        if(CollUtil.isNotEmpty(form.getItems())){
            List<ChatModelRouteConfig.Config.Item> configItems = form.getItems().stream().map(e -> {
                ChatModelRouteConfig.Config.Item item = new ChatModelRouteConfig.Config.Item();
                item.setValue(e.getValue());
                item.setModelId(e.getModelId());
                return item;
            }).toList();
            config.setItems(configItems);
        }

        chatModelRouteConfig.updateConfig(config);
    }

    /*
     * 校验配置
     */
    private void updateConfigCheck(ChatRouteConfigUpdateForm form){
        if(Boolean.FALSE.equals(form.getEnableFlag())){
            return;
        }

        if(CharSequenceUtil.isBlank(form.getPrompt())){
            throw new BusinessException("请输入提示语");
        }
        if(form.getModelId()==null){
            throw new BusinessException("请选择决策模型");
        }
        if(CollUtil.isEmpty(form.getItems())){
            throw new BusinessException("请配置路由");
        }
        for (ChatRouteConfigUpdateForm.Item item : form.getItems()) {
            Long modelId = item.getModelId();
            if(modelId==null){
                throw new BusinessException("请选择路由模型");
            }
            if(CharSequenceUtil.isBlank(item.getValue())){
                throw new BusinessException("请输入路由值");
            }
        }
    }
}
