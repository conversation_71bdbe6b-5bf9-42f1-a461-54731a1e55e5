package net.lab1024.sa.admin.module.business.mes.ai.core.assistant;

import lombok.Data;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;

/**
 * 模型转换实体
 */
@Data
public class TransformEntityDTO {

    private UserMessage userMessage;

    private SystemMessage systemMessage;

    private Long modelId;

    private Boolean historyFlag;

    private String chatId;

    public TransformEntityDTO(UserMessage userMessage, String sysMsg) {
        this.userMessage = userMessage;
        this.systemMessage = new SystemMessage(sysMsg);
        this.modelId = null;
        this.historyFlag = false;
        this.chatId = null;
    }

    public TransformEntityDTO(UserMessage userMessage, String sysMsg, Long modelId) {
        this.userMessage = userMessage;
        this.systemMessage = new SystemMessage(sysMsg);
        this.modelId = modelId;
        this.historyFlag = false;
        this.chatId = null;
    }

    public TransformEntityDTO(String userMsg, String sysMsg, Long modelId,String chatId) {
        this.userMessage = new UserMessage(userMsg);
        this.systemMessage = new SystemMessage(sysMsg);
        this.modelId = modelId;
        this.historyFlag = true;
        this.chatId = chatId;
    }

}
