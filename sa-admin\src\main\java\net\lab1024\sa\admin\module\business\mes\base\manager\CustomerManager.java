package net.lab1024.sa.admin.module.business.mes.base.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.base.dao.CustomerDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.CustomerEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 客户表  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-03 09:43:14
 * @Copyright zscbdic
 */
@Service
public class CustomerManager extends ServiceImpl<CustomerDao, CustomerEntity> {

    @Resource
    CustomerDao customerDao;

    /**
     * 根据客户id映射map
     * @return 客户信息map集合
     */
    public Map<Long,List<CustomerEntity>> processMap(){
        return customerDao.selectList(new QueryWrapper<>(null))
                .stream().collect(Collectors.groupingBy(CustomerEntity::getId));
    }
}
