package net.lab1024.sa.admin.module.business.mes.base.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 尺码表 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-05 22:37:21
 * @Copyright zscbdic
 */

@Data
public class SizeVO {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;


    /**
     * 模板id
     */
    @Schema(description = "关联t_sizing_template")
    private Long templateId;

    /**
     * 尺码信息（如：S码、M码、L码等）
     */
    @Schema(description = "尺码信息（如：S码、M码、L码等）")
    private String sizeMessage;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

}
