package net.lab1024.sa.admin.module.business.mes.common.crud.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

@Getter
@AllArgsConstructor
public enum CrudEnum implements BaseEnum {

    ADD("0", "新增"),

    BATCH_ADD("1", "批量新增"),

    UPDATE("2", "修改"),

    DELETE("3", "删除"),

    BATCH_DELETE("4", "批量删除");

    private final String value;

    private final String desc;
}
