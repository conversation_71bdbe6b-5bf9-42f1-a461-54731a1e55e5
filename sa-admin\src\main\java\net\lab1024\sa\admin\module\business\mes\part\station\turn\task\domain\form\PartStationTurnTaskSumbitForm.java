package net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskPriorityEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskTypeEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;

import java.time.LocalDateTime;

@Data
public class PartStationTurnTaskSumbitForm {

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "任务作用范围;human人")
    private String taskScope;

    @Schema(description = "任务编号")
    private String number;

    @Schema(description = "任务名称")
    private String name;

    @Schema(description = "任务优先级", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "任务优先级 不能为空")
    @CheckEnum(value = TurnTaskPriorityEnum.class, required = false, message = "任务优先级错误")
    private String priority;

    @Schema(description = "任务类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "任务类型 不能为空")
    @CheckEnum(value = TurnTaskTypeEnum.class, required = true, message = "任务类型错误")
    private String type;

    @Schema(description = "任务状态", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "任务状态 不能为空")
    private String status;

    @Schema(description = "是否自动派发 0:否 1:是")
    private Boolean autoDispatchFlag;

    @Schema(description = "起点货位ID")
    @NotNull(message = "起点货位ID 不能为空")
    private Long startLocationId;

    @Schema(description = "终点货位ID")
    private Long endLocationId;

    @Schema(description = "生产小组ID")
    private Long endProduceTeamId;


    @Schema(description = "周转箱ID")
    @NotNull(message = "周转箱ID 不能为空")
    private Long turnoverBoxId;

    @Schema(description = "提交时间")
    private LocalDateTime submitTime;

    @Schema(description = "提交人类型")
    private String submitterType;

    @Schema(description = "提交人ID")
    private String submitterId;

    @Schema(description = "提交人名称")
    private String submitterName;

    @Schema(description = "关联单据类型")
    private String bizType;

    @Schema(description = "关联单据ID")
    private Long bizId;

    @Schema(description = "关联单据详情序号")
    private Integer bizDetailSeq;

    @Schema(description = "关联单据详情ID")
    private Long bizDetailId;

    /**
     * 是否同步裁片收发记录
     */
    @Schema(description = "是否同步裁片收发记录")
    private Boolean syncPartDispatchFlag;
}
