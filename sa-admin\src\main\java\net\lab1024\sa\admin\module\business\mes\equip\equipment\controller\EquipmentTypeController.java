package net.lab1024.sa.admin.module.business.mes.equip.equipment.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentTypeAddForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentTypeQueryForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentTypeUpdateForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.vo.EquipmentTypeVO;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.service.EquipmentTypeService;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 设备类别 Controller
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:12:33
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class EquipmentTypeController {

    @Resource
    private EquipmentTypeService equipmentTypeService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/equipmentType/queryPage")
    public ResponseDTO<PageResult<EquipmentTypeVO>> queryPage(@RequestBody @Valid EquipmentTypeQueryForm queryForm) {
        return ResponseDTO.ok(equipmentTypeService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/equipmentType/add")
    public ResponseDTO<String> add(@RequestBody @Valid EquipmentTypeAddForm addForm) {
        return equipmentTypeService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/equipmentType/update")
    public ResponseDTO<String> update(@RequestBody @Valid EquipmentTypeUpdateForm updateForm) {
        return equipmentTypeService.update(updateForm);
    }

    /**
     * 批量删除
     * @param idList
     * @return
     */
    @Operation(summary = "批量删除 <AUTHOR>
    @PostMapping("/equipmentType/batchDelete")
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> idList) {
        return equipmentTypeService.batchDelete(idList);
    }

    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/equipmentType/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return equipmentTypeService.delete(id);
    }
}
