package net.lab1024.sa.admin.module.business.mes.stock.lot.manager;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.bo.StkInStockBO;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.entity.StkInStockDetailEntity;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.entity.StkInStockEntity;
import net.lab1024.sa.admin.module.business.mes.stock.lot.constant.StockDirectEnum;
import net.lab1024.sa.admin.module.business.mes.stock.lot.dao.StkLotMaterialTraceDao;
import net.lab1024.sa.admin.module.business.mes.stock.lot.domain.entity.StkLotMaterialTraceEntity;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.bo.StkOutStockBO;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity.StkOutStockDetailEntity;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity.StkOutStockEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.dao.StkWarehouseDao;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkWarehouseEntity;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 批号跟踪信息  Manager
 *
 * <AUTHOR>
 * @Date 2025-02-08 19:57:37
 * @Copyright zscbdic
 */
@Service
public class StkLotMaterialTraceManager extends ServiceImpl<StkLotMaterialTraceDao, StkLotMaterialTraceEntity> {

    @Resource
    private StkWarehouseDao stkWarehouseDao;

    /**
     * 解析日志
     *
     * @param bo
     * @param stockDirectEnum
     * @return
     */
    public List<StkLotMaterialTraceEntity> parseInLogs(StkInStockBO bo, StockDirectEnum stockDirectEnum) {
        StkInStockEntity stkInStock = bo.getStkInStock();
        List<StkInStockDetailEntity> details = bo.getDetails();

        StkWarehouseEntity warehouse = stkWarehouseDao.selectById(stkInStock.getWarehouseId());

        List<StkLotMaterialTraceEntity> logs = details.stream()
                .filter(d -> d.getLotId() != null)
                .map(d -> {
                    StkLotMaterialTraceEntity trace = new StkLotMaterialTraceEntity();
                    trace.setLotMasterId(d.getLotId());
                    trace.setStockDirect(stockDirectEnum.getValue());
                    trace.setBillType(stkInStock.getType());
                    trace.setBillId(stkInStock.getId());
                    trace.setBillNumber(stkInStock.getNumber());
                    trace.setBillTime(stkInStock.getInStockTime());
                    trace.setBillDetailSeq(d.getSeq());
                    trace.setBillDetailId(d.getId());
                    trace.setQty(d.getQty());
                    trace.setUnitId(d.getUnitId());
                    trace.setUnitName(d.getUnitName());

                    trace.setOrderType(d.getOriginOrderBillType());
                    trace.setOrderId(d.getOriginOrderBillId());
                    trace.setOrderNumber(d.getOriginOrderBillNumber());

                    trace.setOwnerType(stkInStock.getOwnerType());
                    trace.setOwnerId(stkInStock.getOwnerId());
                    trace.setOwnerName(stkInStock.getOwnerName());

                    trace.setWarehouseId(stkInStock.getWarehouseId());
                    trace.setWarehouseName(warehouse.getName());
                    trace.setWarehouseNumber(warehouse.getNumber());
                    trace.setLocationId(d.getLocationId());
                    trace.setLocationNumber(d.getLocationNumber());
                    return trace;

                }).collect(Collectors.toList());
        return logs;

    }


    public List<StkLotMaterialTraceEntity> parseOutLogs(StkOutStockBO bo, StockDirectEnum stockDirectEnum) {
        StkOutStockEntity stkInStock = bo.getStkOutStock();
        List<StkOutStockDetailEntity> details = bo.getDetails();

        StkWarehouseEntity warehouse = stkWarehouseDao.selectById(stkInStock.getWarehouseId());

        List<StkLotMaterialTraceEntity> logs = details.stream()
                .filter(d -> d.getLotId() != null)
                .map(d -> {
                    StkLotMaterialTraceEntity trace = new StkLotMaterialTraceEntity();
                    trace.setLotMasterId(d.getLotId());
                    trace.setStockDirect(stockDirectEnum.getValue());
                    trace.setBillType(stkInStock.getType());
                    trace.setBillId(stkInStock.getId());
                    trace.setBillNumber(stkInStock.getNumber());
                    trace.setBillTime(stkInStock.getOutStockTime());
                    trace.setBillDetailSeq(d.getSeq());
                    trace.setBillDetailId(d.getId());
                    trace.setQty(d.getQty());
                    trace.setUnitId(d.getUnitId());
                    trace.setUnitName(d.getUnitName());

                    trace.setOrderType(d.getOriginOrderBillType());
                    trace.setOrderId(d.getOriginOrderBillId());
                    trace.setOrderNumber(d.getOriginOrderBillNumber());

                    trace.setOwnerType(stkInStock.getOwnerType());
                    trace.setOwnerId(stkInStock.getOwnerId());
                    trace.setOwnerName(stkInStock.getOwnerName());

                    trace.setWarehouseId(stkInStock.getWarehouseId());
                    trace.setWarehouseName(warehouse.getName());
                    trace.setWarehouseNumber(warehouse.getNumber());
                    trace.setLocationId(d.getLocationId());
                    trace.setLocationNumber(d.getLocationNumber());
                    return trace;

                }).collect(Collectors.toList());
        return logs;

    }
}
