package net.lab1024.sa.admin.module.business.mes.bom.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.bom.domain.form.BomAddForm;
import net.lab1024.sa.admin.module.business.mes.bom.domain.form.BomQueryForm;
import net.lab1024.sa.admin.module.business.mes.bom.domain.form.BomUpdateForm;
import net.lab1024.sa.admin.module.business.mes.bom.domain.vo.BomVO;
import net.lab1024.sa.admin.module.business.mes.bom.service.BomService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 物料BOM表 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:25:22
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "bom管理接口")
public class BomController {

    @Resource
    private BomService bomService;

    @Resource
    private SerialNumberService serialNumberService;
    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/bom/queryPage")
    public ResponseDTO<PageResult<BomVO>> queryPage(@RequestBody @Valid BomQueryForm queryForm) {
        return ResponseDTO.ok(bomService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/bom/add")
    public ResponseDTO<String> add(@RequestBody @Valid BomAddForm addForm) {
        return bomService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/bom/update")
    public ResponseDTO<String> update(@RequestBody @Valid BomUpdateForm updateForm) {
        return bomService.update(updateForm);
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/bom/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return bomService.delete(id);
    }

    /**
     * 查询
     * @param id
     * @return
     */
    @Operation(summary = "查看详细信息")
    @GetMapping("/bom/{id}")
    public ResponseDTO<BomVO> queryById(@PathVariable("id") Long id){
        return bomService.queryById(id);
    }

//    /**
//     * 新建版本
//     * @param addForm
//     * @return
//     */
//    @Operation(summary = "新建版本")
//    @PostMapping("/bom/copy")
//    public ResponseDTO<String> copy(@RequestBody @Valid BomAddForm addForm){
//        return bomService.copy(addForm);
//    }

//    /**
//     * 根据id和版本号查询
//     * @param idAndVersionForm
//     * @return
//     */
//    @Operation(summary = "根据id和版本号查询")
//    @PostMapping("/bom/query")
//    public ResponseDTO<BomVO> queryByIdAndVersionNumber(@RequestBody @Valid IdAndVersionForm idAndVersionForm) {
//        return bomService.query(idAndVersionForm);
//    }

//    /**
//     * 根据bom编号查询所有版本号
//     * @param bomNumber
//     * @return
//     */
//    @Operation(summary = "根据bom编号查询所有版本号")
//    @GetMapping("/bom/query/{bomNumber}")
//    public ResponseDTO<List<Integer>> allVersionNumber(@PathVariable("bomNumber") String bomNumber){
//        return bomService.allVersionNumber(bomNumber);
//    }

    @Operation(summary = "自动生成BOM编号 <AUTHOR>
    @GetMapping("/bom/getBOMNo")
    public ResponseDTO<String> getBomNo() {
        return ResponseDTO.ok(serialNumberService.generate(SerialNumberIdEnum.BOM));
    }


    @Operation(summary = "下拉查询所有Bom <AUTHOR>
    @GetMapping("/bom/queryAll")
    public ResponseDTO<List<BomVO>> queryAll() {
        return ResponseDTO.ok(bomService.queryAll());
    }
}
