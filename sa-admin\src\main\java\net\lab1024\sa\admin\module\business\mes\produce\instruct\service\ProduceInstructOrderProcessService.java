package net.lab1024.sa.admin.module.business.mes.produce.instruct.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderProcessDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderProcessEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderProcessAddForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderProcessQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderProcessUpdateForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderProcessVO;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderVO;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructProcessVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 生产指令工序信息 Service
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:24:08
 * @Copyright zscbdic
 */

@Service
public class ProduceInstructOrderProcessService {

    @Resource
    private ProduceInstructOrderProcessDao produceInstructOrderProcessDao;

    @Resource
    private ProduceInstructOrderDao produceInstructOrderDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<ProduceInstructOrderProcessVO> queryPage(ProduceInstructOrderProcessQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ProduceInstructOrderProcessVO> list = produceInstructOrderProcessDao.queryPage(page, queryForm);
        PageResult<ProduceInstructOrderProcessVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(ProduceInstructOrderProcessAddForm addForm) {
        ProduceInstructOrderProcessEntity produceInstructOrderProcessEntity = SmartBeanUtil.copy(addForm, ProduceInstructOrderProcessEntity.class);
        produceInstructOrderProcessDao.insert(produceInstructOrderProcessEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(ProduceInstructOrderProcessUpdateForm updateForm) {
        ProduceInstructOrderProcessEntity produceInstructOrderProcessEntity = SmartBeanUtil.copy(updateForm, ProduceInstructOrderProcessEntity.class);
        produceInstructOrderProcessDao.updateById(produceInstructOrderProcessEntity);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    public ResponseDTO<String> batchDelete(List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return ResponseDTO.ok();
        }

        produceInstructOrderProcessDao.batchUpdateDeleted(idList, true);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Integer id) {
        if (null == id) {
            return ResponseDTO.ok();
        }

        produceInstructOrderProcessDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }

    public List<ProduceInstructOrderProcessVO> queryList(Long orderId) {
        List<ProduceInstructOrderProcessEntity> processEntities = produceInstructOrderProcessDao
                .selectList(new LambdaQueryWrapper<ProduceInstructOrderProcessEntity>()
                        .eq(ProduceInstructOrderProcessEntity::getOrderId, orderId));
        return SmartBeanUtil.copyList(processEntities, ProduceInstructOrderProcessVO.class);
    }

    public PageResult<ProduceInstructOrderVO> queryProduceInstructOrderPage(ProduceInstructOrderQueryForm queryForm) {

        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ProduceInstructOrderVO> list = produceInstructOrderDao.queryPage(page, queryForm);
        if (CollUtil.isEmpty(list)) {
            return SmartPageUtil.convert2PageResult(page, list);
        }

        List<Long> orderIds = list.stream().map(ProduceInstructOrderVO::getId).collect(Collectors.toList());
        List<ProduceInstructOrderProcessEntity> processEntityList = produceInstructOrderProcessDao.selectList(new LambdaQueryWrapper<ProduceInstructOrderProcessEntity>()
                .in(ProduceInstructOrderProcessEntity::getOrderId, orderIds)
                .orderByAsc(ProduceInstructOrderProcessEntity::getSerialNumber)
        );
        if (CollUtil.isEmpty(processEntityList)) {
            return SmartPageUtil.convert2PageResult(page, list);
        }

        Map<Long, List<ProduceInstructOrderProcessEntity>> processMap = processEntityList.stream()
                .collect(Collectors.groupingBy(ProduceInstructOrderProcessEntity::getOrderId));
        for (ProduceInstructOrderVO orderVO : list) {
            if (!processMap.containsKey(orderVO.getId())) {
                continue;
            }

            List<ProduceInstructOrderProcessEntity> processList = processMap.get(orderVO.getId());
            orderVO.setProcessList(SmartBeanUtil.copyList(processList, ProduceInstructOrderProcessVO.class));
        }
        return SmartPageUtil.convert2PageResult(page, list);
    }

    public ResponseDTO<List<ProduceInstructProcessVO>> getProcessInfoById(Long id) {
        //根据指令单id查找所属的工序
        List<ProduceInstructOrderProcessEntity> processEntities = produceInstructOrderProcessDao
                .selectList(new QueryWrapper<ProduceInstructOrderProcessEntity>()
                        .lambda().eq(ProduceInstructOrderProcessEntity::getOrderId, id));
        List<ProduceInstructProcessVO> collect = BeanUtil.copyToList(processEntities, ProduceInstructProcessVO.class);
        return ResponseDTO.ok(collect);

    }
}
