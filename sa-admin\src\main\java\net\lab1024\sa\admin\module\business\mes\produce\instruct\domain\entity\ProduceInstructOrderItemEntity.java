package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 指令单用料信息 实体类
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:24:54
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_produce_instruct_order_item")
public class ProduceInstructOrderItemEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 指令单id
     */
    private Long orderId;

    /**
     * 物料id
     */
    private Long itemId;

    /**
     * 物料名称
     */
    private String itemName;

    /**
     * 物料编号
     */
    private String itemNumber;

    /**
     * 物料规格型号
     */
    private String itemModel;

    /**
     * 物料分类id
     */
    private Long itemTypeId;

    /**
     * 物料类型
     */
    private String itemCategory;

    /**
     * 物料单位id
     */
    private Long itemUnitId;

    /**
     * 物料单位
     */
    private String itemUnitName;

    /**
     * 物料属性
     */
    private String itemAttribute;

    /**
     * 用料数量
     */
    private Double itemNum;


    /**
     * 单位用量
     */
    private Double dosage;

    /**
     * 单位损耗率
     */
    private Double loss;

    /**
     * 总用量
     */
    private Double totalDosage;

    /**
     * 发料数量
     */
    private Double giveQty;

    /**
     * 退料数量
     */
    private Double backQty;
}
