package net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.constant.IotEquipmentTypeEnum;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.constant.IotPlatformEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;

/**
 * 设备 更新表单
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:12:13
 * @Copyright zscbdic
 */

@Data
public class EquipmentUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "设备类别id")
    private Long typeId;

    @Schema(description = "设备编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "设备编号 不能为空")
    private String number;

    @Schema(description = "设备名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "设备名称 不能为空")
    private String name;

    @Schema(description = "规格")
    private String specification;

    @Schema(description = "型号")
    private String model;

    @Schema(description = "设备状态")
    private String status;

    @Schema(description = "图片 保留")
    private String imgs;

    @Schema(description = "车间id")
    private Long workshopId;

    //-----------------------------------------------------------

    @Schema(description = "是否物联网连接;0否,1是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否物联网连接;0否,1是 不能为空")
    private Boolean iotNetworkFlag;

    @Schema(description = "物联网平台;yuanyi元一")
    @CheckEnum(value = IotPlatformEnum.class, message = "物联网平台 值不合法",required = false)
    private String iotNetworkPlatform;

    /**
     * 对接设备类型
     */
    @CheckEnum(value = IotEquipmentTypeEnum.class, message = "对接设备类型 值不合法",required = false)
    private String iotEquipmentType;

    @Schema(description = "设备ID(SCADA);保留字段")
    private String scadaEquipmentId;

    @Schema(description = "产品编码(SCADA);SCADA产品编码")
    private String scadaProductCode;

    @Schema(description = "设备编码(SCADA);SCADA设备编码")
    private String scadaEquipmentCode;

}
