package net.lab1024.sa.admin.module.business.mes.ai.model.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.ai.model.dao.LLMModelDao;
import net.lab1024.sa.admin.module.business.mes.ai.model.domain.entity.LLMModelEntity;
import net.lab1024.sa.admin.module.business.mes.ai.model.domain.form.LLMModelAddForm;
import net.lab1024.sa.admin.module.business.mes.ai.model.domain.form.LLMModelQueryForm;
import net.lab1024.sa.admin.module.business.mes.ai.model.domain.form.LLMModelUpdateForm;
import net.lab1024.sa.admin.module.business.mes.ai.model.domain.vo.LLMModelVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 大模型表 Service
 *
 * <AUTHOR>
 * @Date 2025-04-17 11:25:27
 * @Copyright zscbdic
 */

@Service
public class LLMModelService {

    @Resource
    private LLMModelDao lLMModelDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<LLMModelVO> queryPage(LLMModelQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<LLMModelVO> list = lLMModelDao.queryPage(page, queryForm);
        return SmartPageUtil.convert2PageResult(page, list);
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(LLMModelAddForm addForm) {
        LLMModelEntity lLMModelEntity = SmartBeanUtil.copy(addForm, LLMModelEntity.class);
        lLMModelDao.insert(lLMModelEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(LLMModelUpdateForm updateForm) {
        LLMModelEntity lLMModelEntity = SmartBeanUtil.copy(updateForm, LLMModelEntity.class);
        lLMModelDao.updateById(lLMModelEntity);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    public ResponseDTO<String> batchDelete(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return ResponseDTO.ok();
        }

        lLMModelDao.batchUpdateDeleted(idList, true);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id){
            return ResponseDTO.ok();
        }

        lLMModelDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }

    /**
     * 列表查询
     * @return
     */
    public List<LLMModelVO> list() {
        List<LLMModelEntity> llmModelEntities = lLMModelDao.selectList(null);
        return SmartBeanUtil.copyList(llmModelEntities, LLMModelVO.class);
    }
}
