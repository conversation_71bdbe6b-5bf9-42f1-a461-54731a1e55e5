package net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

@Getter
@AllArgsConstructor
public enum TurnTaskStatusEnum implements BaseEnum {

    /**
     * 待领取
     */
    WAIT_GET("WAIT_GET", "待领取"),

    /**
     * 待开始
     */
    WAIT_START("WAIT_START", "待开始"),

    /**
     * 进行中
     */
    PROCESSING("PROCESSING", "进行中"),

    /**
     * 已完成
     */
    FINISH("FINISH", "已完成"),

    /**
     * 已取消
     */
    CANCEL("CANCEL", "已取消"),
    ;

    private final String value;

    private final String desc;

    public static TurnTaskStatusEnum getByValue(String value) {
        for (TurnTaskStatusEnum turnTaskStatusEnum : TurnTaskStatusEnum.values()) {
            if (turnTaskStatusEnum.getValue().equals(value)) {
                return turnTaskStatusEnum;
            }
        }
        return null;
    }
}
