package net.lab1024.sa.admin.module.business.mes.stock.inventory.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.dao.StkInventoryDao;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.entity.StkInventoryEntity;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.form.StkInventoryQuery;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.form.StkInventoryQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.vo.StkInventoryVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 即时库存 Service
 *
 * <AUTHOR>
 * @Date 2025-01-14 15:20:23
 * @Copyright zscbdic
 */

@Service
public class StkInventoryService {

    @Resource
    private StkInventoryDao stkInventoryDao;


    /**
     * 分页查询 即时库存
     *
     * @param queryForm
     * @return
     */
    public ResponseDTO<PageResult<StkInventoryVO>> queryInventoryPage(StkInventoryQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        Long pageNum = queryForm.getPageNum();
        Long pageSize = queryForm.getPageSize();
        Long offset = (pageNum - 1) * pageSize;

        List<StkInventoryVO> vos = stkInventoryDao.queryInventorPage(queryForm, offset, pageSize);
        Long total = stkInventoryDao.queryInventorPageCount(queryForm);
        page.setTotal(total);
        return ResponseDTO.ok(SmartPageUtil.convert2PageResult(page, vos));
    }

    /**
     * 多维库存分页查询
     *
     * @param queryForm
     * @return
     */
    public ResponseDTO<PageResult<StkInventoryVO>> queryMultiBalanceInventoryPage(StkInventoryQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        Long pageNum = queryForm.getPageNum();
        Long pageSize = queryForm.getPageSize();
        Long offset = (pageNum - 1) * pageSize;
        List<StkInventoryVO> vos = stkInventoryDao.queryMultiBalanceInventoryPage(queryForm, offset, pageSize);
        Long total = stkInventoryDao.queryMultiBalanceInventoryPageCount(queryForm);
        page.setTotal(total);
        return ResponseDTO.ok(SmartPageUtil.convert2PageResult(page, vos));
    }

    /**
     * 物料库存
     *
     * @param query
     * @return
     */
    public ResponseDTO<StkInventoryVO> queryMaterialInventory(StkInventoryQuery query) {
        Long warehouseId = query.getWarehouseId();
        Long locationId = query.getLocationId();
        Long lotId = query.getLotId();

        List<StkInventoryEntity> list = stkInventoryDao.selectList(new LambdaQueryWrapper<StkInventoryEntity>()
                .eq(StkInventoryEntity::getMaterielId, query.getMaterialId())
                .eq(warehouseId != null, StkInventoryEntity::getWarehouseId, warehouseId)
                .eq(locationId != null, StkInventoryEntity::getLocationId, locationId)
                .eq(lotId != null, StkInventoryEntity::getLotId, lotId));

        StkInventoryVO vo = new StkInventoryVO();
        vo.setMaterielId(query.getMaterialId());
        // 初始化库存数量为0
        BigDecimal totalQty = BigDecimal.ZERO;
        BigDecimal totalLockQty = BigDecimal.ZERO;
        BigDecimal totalAvbQty = BigDecimal.ZERO;
        BigDecimal totalPredictQty = BigDecimal.ZERO;

        if (CollUtil.isNotEmpty(list)) {
            // 遍历并累加库存数量
            for (StkInventoryEntity entity : list) {
                totalQty = totalQty.add(entity.getQty() != null ? entity.getQty() : BigDecimal.ZERO);
                totalLockQty = totalLockQty.add(entity.getLockQty() != null ? entity.getLockQty() : BigDecimal.ZERO);
                totalAvbQty = totalAvbQty.add(entity.getAvbQty() != null ? entity.getAvbQty() : BigDecimal.ZERO);
                totalPredictQty = totalPredictQty.add(entity.getPredictQty() != null ? entity.getPredictQty() : BigDecimal.ZERO);
            }
        }

        // 设置最终库存值
        vo.setQty(totalQty);
        vo.setLockQty(totalLockQty);
        vo.setAvbQty(totalAvbQty);
        vo.setPredictQty(totalPredictQty);

        return ResponseDTO.ok(vo);


    }
}
