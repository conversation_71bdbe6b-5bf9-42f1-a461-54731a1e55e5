package net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.form;

import net.lab1024.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 裁片收发日志 分页查询表单
 *
 * <AUTHOR>
 * @Date 2024-11-06 15:25:13
 * @Copyright zscbdic
 */

@Data
public class PartDispatchQueryForm extends PageParam{

    /**
     * 收或发
     */
    @Schema(description = "收或发")
    private String actionStatus;

    /**
     * 收发范围
     */
    @Schema(description = "收发范围")
    private String dispatchRange;

    /**
     * 物料编号
     */
    @Schema(description = "物料编号")
    private String itemNumber;

    /**
     * 生产指令单编号
     */
    @Schema(description = "生产指令单编号")
    private String instructNumber;

    /**
     * 款式颜色
     */
    @Schema(description = "款式颜色")
    private List<String> colors;

    /**
     * 尺码
     */
    @Schema(description = "尺码")
    private List<String> sizes;

}
