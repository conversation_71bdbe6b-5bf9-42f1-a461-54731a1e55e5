package net.lab1024.sa.admin.module.business.mes.stock.in.controller;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.common.bill.constant.BillType;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockBillStatusEnum;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.form.StkInStockQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.form.StkProdInStockAddForm;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.form.StkProdInStockUpdateForm;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.vo.StkInStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.vo.StkProdInStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.in.service.StkProdInStockService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 生产入库单 Controller
 *
 * <AUTHOR>
 * @Date 2025-02-03 15:55:23
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class StkProdInStockController {

    @Resource
    private StkProdInStockService stkProdInStockService;

    @Resource
    private SerialNumberService serialNumberService;


    /**
     * 分页查询
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/stkProdIn/queryPage")
    public ResponseDTO<PageResult<StkInStockVO>> queryPage(@RequestBody StkInStockQueryForm queryForm) {
        queryForm.setType(BillType.STOCK_PRODUCE_IN.getValue());
        return stkProdInStockService.queryPage(queryForm);
    }


    /**
     * 添加
     *
     * @param form
     * @return
     */
    @PostMapping("/stkProdIn/add")
    public ResponseDTO<String> otherInStock(@RequestBody @Valid StkProdInStockAddForm form) {
        form.setType(BillType.STOCK_PRODUCE_IN.getValue());
        if (StrUtil.isBlank(form.getStatus())) {
            form.setStatus(StockBillStatusEnum.UN_AUDIT.getValue());
        }
        if (StrUtil.isBlank(form.getNumber())) {
            form.setNumber(serialNumberService.generate(SerialNumberIdEnum.STOCK_IN_PRODUCE));
        }
        return stkProdInStockService.add(form);
    }

    /**
     * 修改
     *
     * @param form
     * @return
     */
    @PostMapping("/stkProdIn/update")
    public ResponseDTO<String> update(@RequestBody @Valid StkProdInStockUpdateForm form) {
        return stkProdInStockService.update(form);
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @GetMapping("stkProdIn/byId")
    public ResponseDTO<StkProdInStockVO> queryById(@RequestParam("id") Long id) {
        return stkProdInStockService.queryById(id);
    }


    /**
     * 修改单据状态
     *
     * @param id
     * @return
     */
    @GetMapping("stkProdIn/status")
    public ResponseDTO<String> updateStatus(@RequestParam("id") Long id) {
        return stkProdInStockService.updateStatus(id);
    }

    /**
     * 删除单据
     *
     * @param id
     * @return
     */
    @Operation(summary = "删除单据 <AUTHOR>
    @GetMapping("stkProdIn/delete")
    public ResponseDTO<String> delete(@RequestParam("id") Long id) {
        return stkProdInStockService.delete(id);
    }
}
