package net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.form;

import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

import java.time.LocalDate;

/**
 * 即时库存更新日志 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-01-15 11:20:19
 * @Copyright zscbdic
 */

@Data
public class StkInventoryLogQueryForm extends PageParam{

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库编号
     */
    private String warehouseNumber;

    /**
     * 物料id
     */
    private Long materielId;

    /**
     * 物料名称
     */
    private String materielName;


    /**
     * 物料spu编号
     */
    private String materielSpuNumber;

    /**
     * 物料sku编号
     */
    private String materielSkuNumber;


    /**
     * 批次编号
     */
    private String lotNumber;

    /**
     * 操作类型
     */
    private String optType;


    /**
     * 操作人名称
     */
    private String optUserName;

    /**
     * 操作时间（开始）
     */
    private LocalDate optTimeBegin;

    /**
     * 操作时间（结束）
     */
    private LocalDate optTimeEnd;

    /**
     * 业务单据类型
     */
    private String bizFormType;

    /**
     * 业务单据编号
     */
    private String bizFormNumber;



}
