package net.lab1024.sa.admin.module.business.mes.base.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.PartEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.PartQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.PartVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 部位表 Dao
 *
 * <AUTHOR>
 * @Date 2025-06-23 19:56:55
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface PartDao extends BaseMapper<PartEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<PartVO> queryPage(@Param("page") Page page, @Param("queryForm") PartQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
