package net.lab1024.sa.admin.module.business.mes.salary.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.salary.dao.SettlementDao;
import net.lab1024.sa.admin.module.business.mes.salary.domain.bo.SettlementBo;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.SettlementRecordDetailEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.SettlementRecordEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.SettlementForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.SettlementQueryForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.vo.SettlementVO;
import net.lab1024.sa.admin.module.business.mes.salary.manager.SettlementManager;
import net.lab1024.sa.admin.module.business.mes.work.domain.vo.WorkRecordVO;
import net.lab1024.sa.admin.module.system.department.dao.DepartmentDao;
import net.lab1024.sa.admin.module.system.department.domain.entity.DepartmentEntity;
import net.lab1024.sa.admin.module.system.employee.dao.EmployeeDao;
import net.lab1024.sa.admin.module.system.employee.domain.vo.EmployeeVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 计件结算Service
 */
@Service
public class SettlementService {

    @Resource
    private EmployeeDao employeeDao;

    @Resource
    private SettlementDao settlementDao;

    @Resource
    private SettlementManager settlementManager;

    @Resource
    private DepartmentDao departmentDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public ResponseDTO<PageResult<SettlementVO>> queryPage(SettlementQueryForm queryForm) {
        settlementManager.settlementWayCheck(queryForm);

        SettlementQueryForm query = settlementManager.buildQueryForm(queryForm);
        query.setQueryEmployeeIds(queryForm.getQueryEmployeeIds());
        Long pageNum = query.getPageNum();
        Long pageSize = query.getPageSize();
        Long offset = (pageNum - 1) * pageSize;
        // 查询结算数据
        List<SettlementVO> vos = settlementDao.queryPage(query, offset, pageSize);
        if(CollUtil.isEmpty(vos)){
            return ResponseDTO.ok(SmartPageUtil.emptyPage(queryForm.getPageNum(), queryForm.getPageSize()));
        }

        Long total = settlementDao.queryPageCount(query);
        PageResult<SettlementVO> result = new PageResult<>();

        //查询员工信息
        Map<Long, EmployeeVO> employeeMap;
        Map<Long, String> departmentMap;
        List<Long> employeeIds = vos.stream().map(SettlementVO::getEmployeeId).collect(Collectors.toList());
        List<EmployeeVO> employee = employeeDao.getEmployeeByIds(employeeIds);
        if (CollUtil.isNotEmpty(employee)) {
            //员工
            employeeMap = employee.stream().
                    collect(Collectors.toMap(EmployeeVO::getEmployeeId, employeeVO -> employeeVO));
            //部门
            List<Long> departmentId = employee.stream()
                    .map(EmployeeVO::getDepartmentId)
                    .collect(Collectors.toList());
            List<DepartmentEntity> departments = departmentDao.selectBatchIds(departmentId);
            if (CollUtil.isNotEmpty(departments)) {
                departmentMap = departments.stream()
                        .collect(Collectors.toMap(DepartmentEntity::getDepartmentId, DepartmentEntity::getName));
            } else {
                departmentMap = null;
            }
        } else {
            departmentMap = null;
            employeeMap = null;
        }

        if (CollUtil.isNotEmpty(employeeMap)) {
            vos.forEach(e -> {
                EmployeeVO employeeVO = employeeMap.get(e.getEmployeeId());
                e.setActualName(employeeVO.getActualName());
                e.setPhone(employeeVO.getPhone());
                e.setDepartmentId(employeeVO.getDepartmentId());
            });
        }

        if (CollUtil.isNotEmpty(departmentMap)) {
            vos.forEach(e -> {
                String departmentName = departmentMap.get(e.getDepartmentId());
                e.setDepartmentName(departmentName);
            });
        }

        result.setList(vos);
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);
        result.setTotal(total);
        result.setPages(total % pageSize == 0 ? total / pageSize : total / pageSize + 1);
        result.setEmptyFlag(CollUtil.isEmpty(vos));
        return ResponseDTO.ok(result);
    }

    /**
     * 结算明细
     *
     * @param queryForm
     * @return
     */
    public ResponseDTO<PageResult<WorkRecordVO>> querySettlementDetails(SettlementQueryForm queryForm, Long employeeId) {
        SettlementQueryForm query = settlementManager.buildQueryForm(queryForm);

        //只有一个元素的list
        query.setEmployeeIds(ListUtil.toList(employeeId));
        List<WorkRecordVO> vos = settlementDao.querySettlementDetails(query);
        PageResult<WorkRecordVO> pageResult = SmartPageUtil.subListPage(query.getPageNum().intValue(), query.getPageSize().intValue(), vos);
        return ResponseDTO.ok(pageResult);
    }

    public ResponseDTO<List<WorkRecordVO>> querySettlementDetailList(SettlementQueryForm queryForm, Long employeeId) {
        SettlementQueryForm query = settlementManager.buildQueryForm(queryForm);
        query.setEmployeeIds(ListUtil.toList(employeeId));
        List<WorkRecordVO> vos = settlementDao.querySettlementDetails(query);
        return ResponseDTO.ok(vos);
    }

    public ResponseDTO<String> settlement(SettlementForm form) {
        settlementManager.settlementWayCheck(form);
        SettlementQueryForm queryForm = settlementManager.buildQueryForm(form);
        // 未结算数据
        queryForm.setSettlementFlag(false);
        queryForm.setEmployeeIds(form.getEmployeeIds());
        List<WorkRecordVO> vos = settlementDao.querySettlementDetails(queryForm);
        if (CollUtil.isEmpty(vos)) {
            return ResponseDTO.userErrorParam("没有需要结算的数据");
        }
        // 根据员工id分组报工记录
        List<Long> workIds = vos.stream().map(WorkRecordVO::getId).collect(Collectors.toList());
        Map<Long, List<WorkRecordVO>> workRecordMap = vos.stream().collect(Collectors.groupingBy(WorkRecordVO::getWorkerId));
        //获取员工信息
        List<Long> employeeIds = form.getEmployeeIds();
        List<EmployeeVO> employee = employeeDao.getEmployeeByIds(employeeIds);
        if (CollUtil.isEmpty(employee)) {
            return ResponseDTO.userErrorParam("员工信息不存在");
        }
        Map<Long, EmployeeVO> employeeVoMap = employee.stream().collect(Collectors.toMap(EmployeeVO::getEmployeeId, employeeVO -> employeeVO));


        SettlementBo settlementBo = new SettlementBo();

        List<SettlementBo.RecordBo> recordBos = new ArrayList<>();
        for (Long employeeId : employeeIds) {
            if (!workRecordMap.containsKey(employeeId)) {
                // 没有报工数据
                continue;
            }
            if (!employeeVoMap.containsKey(employeeId)) {
                // 员工信息不存在
                continue;
            }
            EmployeeVO employeeVO = employeeVoMap.get(employeeId);

            List<WorkRecordVO> workRecordVOS = workRecordMap.get(employeeId);
            //封装结算记录主信息
            Integer totalNum = workRecordVOS.stream().mapToInt(WorkRecordVO::getWorkQuantity).sum();
            Integer totalCount = workRecordVOS.size();
            BigDecimal totalAmount = workRecordVOS.stream().map(w -> w.getPrice().multiply(BigDecimal.valueOf(w.getWorkQuantity()))).reduce(BigDecimal.ZERO, BigDecimal::add);
            SettlementRecordEntity entity = new SettlementRecordEntity(
                    employeeId, employeeVO.getActualName(),
                    form.getBelongDate(), form.getSettlementWay(),
                    totalNum, totalCount, totalAmount);
            SettlementBo.RecordBo bo = new SettlementBo.RecordBo();
            bo.setRecord(entity);
            //封装结算记录明细
            List<SettlementRecordDetailEntity> detailEntities = workRecordVOS.stream().map(w -> {
                SettlementRecordDetailEntity detailEntity = new SettlementRecordDetailEntity();
                detailEntity.setWorkRecordId(w.getId());
                return detailEntity;
            }).collect(Collectors.toList());
            //封装结算记录
            recordBos.add(new SettlementBo.RecordBo(entity, detailEntities));
        }

        //保存结算记录
        settlementBo.setSettlementRecords(recordBos);
        settlementBo.setWorkRecordIds(workIds);
        settlementManager.saveSettlement(settlementBo);
        return ResponseDTO.ok();
    }


}
