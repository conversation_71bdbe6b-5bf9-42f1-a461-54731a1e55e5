package net.lab1024.sa.admin.module.business.mes.process.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.process.dao.ProcessDao;
import net.lab1024.sa.admin.module.business.mes.process.dao.ProcessLibraryDetailsDao;
import net.lab1024.sa.admin.module.business.mes.process.domain.entity.ProcessEntity;
import net.lab1024.sa.admin.module.business.mes.process.domain.entity.ProcessLibraryDetailsEntity;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessAddForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessUpdateForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderProcessDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderProcessEntity;
import net.lab1024.sa.base.common.exception.BusinessException;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

/**
 * 工序信息  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-13 15:19:57
 * @Copyright zscbdic
 */
@Service
public class ProcessManager extends ServiceImpl<ProcessDao, ProcessEntity> {

    @Resource
    private ProduceInstructOrderProcessDao orderProcessDao;

    @Resource
    private ProcessLibraryDetailsDao processLibraryDetailsDao;

    @Resource
    private ApplicationEventPublisher publisher;

    public void addCheck(ProcessAddForm addForm) {
        String processNumber = addForm.getProcessNumber();
        Long processNumberCount = this.lambdaQuery()
                .eq(ProcessEntity::getProcessNumber, processNumber)
                .count();
        if (processNumberCount > 0) {
            throw new BusinessException("工序编号已存在");
        }

    }

    public void updateCheck(ProcessUpdateForm updateForm) {
        String processNumber = updateForm.getProcessNumber();
        Long processNumberCount = this.lambdaQuery()
                .eq(ProcessEntity::getProcessNumber, processNumber)
                .ne(ProcessEntity::getId, updateForm.getId())
                .count();
        if (processNumberCount > 0) {
            throw new BusinessException("工序编号已存在");
        }
    }

    /**
     * 删除校验
     *
     * @param id
     */
    public void deleteCheck(Long id) {
        Long count = orderProcessDao.selectCount(new LambdaQueryWrapper<ProduceInstructOrderProcessEntity>()
                .eq(ProduceInstructOrderProcessEntity::getProcessId, id));
        if (count > 0) {
            throw new BusinessException("该工序正在被指令单使用中，无法删除");
        }
        Long processCount = processLibraryDetailsDao.selectCount(new LambdaQueryWrapper<ProcessLibraryDetailsEntity>()
                .eq(ProcessLibraryDetailsEntity::getProcessId, id));
        if (processCount > 0) {
            throw new BusinessException("该工序正在被工序路线使用中，无法删除");
        }


    }
}
