package net.lab1024.sa.admin.module.business.mes.factory.service;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.factory.dao.ProduceTeamMemberDao;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.ProduceTeamMemberEntity;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.ProduceTeamMemberAddForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.ProduceTeamMemberQueryForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.ProduceTeamMemberUpdateForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.vo.ProduceTeamMemberVO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 生产小组成员 Service
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:33:31
 * @Copyright zscbdic
 */

@Service
public class ProduceTeamMemberService {

    @Resource
    private ProduceTeamMemberDao produceTeamMemberDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<ProduceTeamMemberVO> queryPage(ProduceTeamMemberQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ProduceTeamMemberVO> list = produceTeamMemberDao.queryPage(page, queryForm);
        PageResult<ProduceTeamMemberVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(ProduceTeamMemberAddForm addForm) {
        ProduceTeamMemberEntity produceTeamMemberEntity = SmartBeanUtil.copy(addForm, ProduceTeamMemberEntity.class);
        produceTeamMemberDao.insert(produceTeamMemberEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(ProduceTeamMemberUpdateForm updateForm) {
        ProduceTeamMemberEntity produceTeamMemberEntity = SmartBeanUtil.copy(updateForm, ProduceTeamMemberEntity.class);
        produceTeamMemberDao.updateById(produceTeamMemberEntity);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    public ResponseDTO<String> batchDelete(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return ResponseDTO.ok();
        }

        produceTeamMemberDao.batchUpdateDeleted(idList, true);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id){
            return ResponseDTO.ok();
        }

        produceTeamMemberDao.updateDeleted(id,true);
        return ResponseDTO.ok();
    }
}
