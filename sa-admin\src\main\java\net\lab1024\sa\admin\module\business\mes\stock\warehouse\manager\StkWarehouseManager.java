package net.lab1024.sa.admin.module.business.mes.stock.warehouse.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.dao.StkInventoryDao;
import net.lab1024.sa.admin.module.business.mes.stock.inventory.domain.entity.StkInventoryEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.dao.StkLocationDao;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.dao.StkRackDao;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.dao.StkWarehouseDao;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkLocationEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkRackEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkWarehouseEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkWarehouseAddForm;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form.StkWarehouseUpdateForm;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 仓库  Manager
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:13:18
 * @Copyright zscbdic
 */
@Service
public class StkWarehouseManager extends ServiceImpl<StkWarehouseDao, StkWarehouseEntity> {

    @Resource
    private StkInventoryDao stkInventoryDao;

    @Resource
    private StkRackDao stkRackDao;

    @Resource
    private StkLocationDao stkLocationDao;

    @Resource
    private StkWarehouseDao stkWarehouseDao;

    /**
     * 添加校验
     *
     * @param addForm
     */
    public void addCheck(StkWarehouseAddForm addForm) {
        String number = addForm.getNumber();
        long count = this.count(new LambdaQueryWrapper<StkWarehouseEntity>()
                .eq(StkWarehouseEntity::getNumber, number));
        if (count > 0) {
            throw new BusinessException("仓库编号已存在");
        }
    }

    /**
     * 更新校验
     *
     * @param updateForm
     */
    public void updateCheck(StkWarehouseUpdateForm updateForm) {
        String number = updateForm.getNumber();
        long count = this.count(new LambdaQueryWrapper<StkWarehouseEntity>()
                .eq(StkWarehouseEntity::getNumber, number)
                .ne(StkWarehouseEntity::getId, updateForm.getId()));
        if (count > 0) {
            throw new BusinessException("仓库编号已存在");
        }
    }

    /**
     * 删除校验
     *
     * @param id
     */
    public void deleteCheck(Long id) {
        Long count = stkInventoryDao.selectCount(new LambdaQueryWrapper<StkInventoryEntity>()
                .eq(StkInventoryEntity::getWarehouseId, id));
        if (count > 0) {
            throw new BusinessException("该仓库存在库存，无法删除");
        }
    }

    /**
     * 删除仓库
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteWarehouse(Long id) {
        // 删除仓库
        stkWarehouseDao.deleteById(id);
        // 删除库位
        List<StkRackEntity> racks = stkRackDao.selectList(new LambdaQueryWrapper<StkRackEntity>()
                .eq(StkRackEntity::getWarehouseId, id));
        if (CollUtil.isNotEmpty(racks)) {
            List<Long> rackIds = racks.stream().map(StkRackEntity::getId).collect(Collectors.toList());
            stkLocationDao.delete(new LambdaQueryWrapper<StkLocationEntity>()
                    .in(StkLocationEntity::getRackId, rackIds));
        }
        // 删除货架
        stkRackDao.delete(new LambdaQueryWrapper<StkRackEntity>()
                .eq(StkRackEntity::getWarehouseId, id));

    }

    /**
     * 是否启用库位
     *
     * @param id
     * @return
     */
    public boolean isOpenLocation(Long id) {
        StkWarehouseEntity warehouse = stkWarehouseDao.selectById(id);
        if (warehouse == null) {
            throw new BusinessException("仓库不存在");
        }
        return warehouse.getOpenLocationFlag();
    }

    /**
     * 启用库位校验
     *
     * @param warehouseId
     */
    public void openLocationCheck(Long warehouseId) {
        Long count = stkInventoryDao.selectCount(new LambdaQueryWrapper<StkInventoryEntity>()
                .eq(StkInventoryEntity::getWarehouseId, warehouseId));
        if (count > 0) {
            throw new BusinessException("该仓库存在库存，无法启用仓位");
        }
    }

    public void closeNegativeStockCheck(Long id) {
        Long count = stkInventoryDao.selectCount(new LambdaQueryWrapper<StkInventoryEntity>()
                .eq(StkInventoryEntity::getWarehouseId, id)
                .lt(StkInventoryEntity::getQty, 0));
        if (count > 0) {
            throw new BusinessException("该仓库存在负库存，无法关闭允许负库存限制");
        }
    }
}
