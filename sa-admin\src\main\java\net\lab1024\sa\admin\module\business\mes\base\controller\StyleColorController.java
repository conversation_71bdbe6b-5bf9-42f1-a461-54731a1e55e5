package net.lab1024.sa.admin.module.business.mes.base.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.StyleColorAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.StyleColorQueryAllForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.StyleColorQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.StyleColorUpdateForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.StyleColorVO;
import net.lab1024.sa.admin.module.business.mes.base.service.StyleColorService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 款式颜色表 Controller
 *
 * <AUTHOR>
 * @Date 2024-11-03 20:49:04
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class StyleColorController {

    @Resource
    private StyleColorService styleColorService;

    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/styleColor/queryPage")
    public ResponseDTO<PageResult<StyleColorVO>> queryPage(@RequestBody @Valid StyleColorQueryForm queryForm) {
        return ResponseDTO.ok(styleColorService.queryPage(queryForm));
    }

    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/styleColor/add")
    public ResponseDTO<String> add(@RequestBody @Valid StyleColorAddForm addForm) {
        return styleColorService.add(addForm);
    }

    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/styleColor/update")
    public ResponseDTO<String> update(@RequestBody @Valid StyleColorUpdateForm updateForm) {
        return styleColorService.update(updateForm);
    }

    @Operation(summary = "批量删除 <AUTHOR>
    @PostMapping("/styleColor/batchDelete")
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Integer> idList) {
        return styleColorService.batchDelete(idList);
    }

    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/styleColor/delete/{id}")
    public ResponseDTO<String> batchDelete(@PathVariable Integer id) {
        return styleColorService.delete(id);
    }

    @Operation(summary = "查询全部 <AUTHOR>
    @PostMapping("/styleColor/queryAll")
    public ResponseDTO<List<StyleColorVO>> queryAll(@RequestBody StyleColorQueryAllForm queryAllForm) {
        return ResponseDTO.ok(styleColorService.queryAll(queryAllForm));
    }
}
