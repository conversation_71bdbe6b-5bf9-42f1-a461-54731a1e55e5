package net.lab1024.sa.admin.module.business.mes.ai.model.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.ai.model.domain.form.LLMModelAddForm;
import net.lab1024.sa.admin.module.business.mes.ai.model.domain.form.LLMModelQueryForm;
import net.lab1024.sa.admin.module.business.mes.ai.model.domain.form.LLMModelUpdateForm;
import net.lab1024.sa.admin.module.business.mes.ai.model.domain.vo.LLMModelVO;
import net.lab1024.sa.admin.module.business.mes.ai.model.service.LLMModelService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 大模型表 Controller
 *
 * <AUTHOR>
 * @Date 2025-04-17 11:25:27
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "大模型表")
public class LLMModelController {

    @Resource
    private LLMModelService lLMModelService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/lLMModel/queryPage")
//    @SaCheckPermission("lLMModel:query")
    public ResponseDTO<PageResult<LLMModelVO>> queryPage(@RequestBody @Valid LLMModelQueryForm queryForm) {
        return ResponseDTO.ok(lLMModelService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/lLMModel/add")
//    @SaCheckPermission("lLMModel:add")
    public ResponseDTO<String> add(@RequestBody @Valid LLMModelAddForm addForm) {
        return lLMModelService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/lLMModel/update")
//    @SaCheckPermission("lLMModel:update")
    public ResponseDTO<String> update(@RequestBody @Valid LLMModelUpdateForm updateForm) {
        return lLMModelService.update(updateForm);
    }



    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/lLMModel/delete/{id}")
//    @SaCheckPermission("lLMModel:delete")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return lLMModelService.delete(id);
    }


    /**
     * 获取大模型列表
     * @return
     */
    @Operation(summary = "获取大模型列表 <AUTHOR>
    @PostMapping("/lLMModel/list")
    public ResponseDTO<List<LLMModelVO>> list() {
        return ResponseDTO.ok(lLMModelService.list());
    }
}
