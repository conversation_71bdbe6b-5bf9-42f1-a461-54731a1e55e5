package net.lab1024.sa.admin.module.business.mes.stock.out.controller;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.common.bill.constant.BillType;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockBillStatusEnum;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.form.StkOutStockQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.form.StkProdPickMtrlAddForm;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.form.StkProdPickMtrlUpdateForm;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.vo.StkOutStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.vo.StkProdPickMtrlVO;
import net.lab1024.sa.admin.module.business.mes.stock.out.service.StkProdPickMtrlService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 生产领料单 Controller
 *
 * <AUTHOR>
 * @Date 2025-02-03 15:55:23
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class StkProdPickMtrlController {

    @Resource
    private StkProdPickMtrlService stkProdPickMtrlService;

    @Resource
    private SerialNumberService serialNumberService;


    /**
     * 分页查询
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/stkProdPickMtrl/queryPage")
    public ResponseDTO<PageResult<StkOutStockVO>> queryPage(@RequestBody StkOutStockQueryForm queryForm) {
        queryForm.setType(BillType.STOCK_PRODUCE_PICK_MATERIAL_OUT.getValue());
        return stkProdPickMtrlService.queryPage(queryForm);
    }


    /**
     * 添加
     *
     * @param form
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/stkProdPickMtrl/add")
    public ResponseDTO<String> otherInStock(@RequestBody @Valid StkProdPickMtrlAddForm form) {
        form.setType(BillType.STOCK_PRODUCE_PICK_MATERIAL_OUT.getValue());
        if (StrUtil.isBlank(form.getStatus())) {
            form.setStatus(StockBillStatusEnum.UN_AUDIT.getValue());
        }
        if (StrUtil.isBlank(form.getNumber())) {
            form.setNumber(serialNumberService.generate(SerialNumberIdEnum.STOCK_OUT_PRODUCE_PICK_MATERIAL));
        }
        return stkProdPickMtrlService.add(form);
    }

    /**
     * 修改
     *
     * @param form
     * @return
     */
    @Operation(summary = "修改 <AUTHOR>
    @PostMapping("/stkProdPickMtrl/update")
    public ResponseDTO<String> update(@RequestBody @Valid StkProdPickMtrlUpdateForm form) {
        return stkProdPickMtrlService.update(form);
    }

    /**
     * 根据id
     *
     * @param id
     * @return
     */
    @Operation(summary = "根据id查询 <AUTHOR>
    @GetMapping("stkProdPickMtrl/byId")
    public ResponseDTO<StkProdPickMtrlVO> queryById(@RequestParam("id") Long id) {
        return stkProdPickMtrlService.queryById(id);
    }


    /**
     * 修改单据状态
     *
     * @param id
     * @return
     */
    @Operation(summary = "修改单据状态 <AUTHOR>
    @GetMapping("stkProdPickMtrl/status")
    public ResponseDTO<String> updateStatus(@RequestParam("id") Long id) {
        return stkProdPickMtrlService.updateStatus(id);
    }

    /*
    * 删除
     */
    @Operation(summary = "删除 <AUTHOR>
    @GetMapping("stkProdPickMtrl/delete")
    public ResponseDTO<String> delete(@RequestParam("id") Long id) {
        return stkProdPickMtrlService.delete(id);
    }
}
