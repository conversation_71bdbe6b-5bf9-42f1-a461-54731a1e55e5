package net.lab1024.sa.admin.event.part.station;


import lombok.Getter;
import lombok.Setter;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.constant.PartStationInventoryOptTypeEnum;
import org.springframework.context.ApplicationEvent;

import java.util.List;

@Getter
@Setter
public class PartStationInventoryOptEvent extends ApplicationEvent {

    private PartStationInventoryOptTypeEnum optType;

    private Long feTicketId;

    private List<Long> feTicketIds;

    private Long binId;

    private String optDesc;

    public PartStationInventoryOptEvent(Object source, PartStationInventoryOptTypeEnum optType, Long feTicketId, Long binId, String optDesc) {
        super(source);
        this.optType = optType;
        this.optDesc = optDesc;
        this.feTicketId = feTicketId;
        this.binId = binId;
    }

    public PartStationInventoryOptEvent(Object source, PartStationInventoryOptTypeEnum optType, List<Long> feTicketIds, Long binId, String optDesc) {
        super(source);
        this.optType = optType;
        this.optDesc = optDesc;
        this.feTicketIds = feTicketIds;
        this.binId = binId;
    }
}
