package net.lab1024.sa.admin.module.business.mes.salary.service;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.salary.dao.WageFieldDao;
import net.lab1024.sa.admin.module.business.mes.salary.dao.WageFieldValueDao;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.WageFieldEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.WageFieldValueEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.WageFieldAddForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.WageFieldQueryForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.WageFieldUpdateForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.vo.WageFieldVO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 工资字段 Service
 *
 * <AUTHOR>
 * @Date 2024-11-25 20:20:26
 * @Copyright zscbdic
 */

@Service
public class WageFieldService {

    @Resource
    private WageFieldDao wageFieldDao;

    @Resource
    private WageFieldValueDao wageFieldValueDao;



    /**
     * 添加
     */
    public ResponseDTO<String> add(WageFieldAddForm addForm) {
        WageFieldEntity wageFieldEntity = SmartBeanUtil.copy(addForm, WageFieldEntity.class);
        wageFieldDao.insert(wageFieldEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(WageFieldUpdateForm updateForm) {
        WageFieldEntity wageFieldEntity = SmartBeanUtil.copy(updateForm, WageFieldEntity.class);
        wageFieldDao.updateById(wageFieldEntity);
        return ResponseDTO.ok();
    }


    /**
     * 单个删除
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long id) {
        if (null == id){
            return ResponseDTO.ok();
        }

        wageFieldValueDao.delete(new LambdaQueryWrapper<WageFieldValueEntity>()
                .eq(WageFieldValueEntity::getFieldId,id));
        wageFieldDao.updateDeleted(id,true);
        return ResponseDTO.ok();
    }

    /**
     * 查询列表
     * @return
     */
    public ResponseDTO<List<WageFieldVO>> queryList() {
        List<WageFieldEntity> list = wageFieldDao.selectList(null);
        return ResponseDTO.ok(SmartBeanUtil.copyList(list, WageFieldVO.class));
    }
}
