package net.lab1024.sa.admin.module.business.mes.produce.instruct.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderProcessDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProcessCountVo;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class ProduceInstructOrderProcessStatsService {

    @Resource
    private ProduceInstructOrderProcessDao instructOrderProcessDao;

    /**
     * 工序统计
     * @return
     */
    public ResponseDTO<List<ProcessCountVo>> processCountStats(Date startTime, Date endTime) {
        DateTime bTime = DateUtil.beginOfDay(startTime);
        DateTime eTime = DateUtil.endOfDay(endTime);
        List<ProcessCountVo> list = instructOrderProcessDao.processCountStats(bTime, eTime);
        if(CollUtil.isEmpty(list)){
            return ResponseDTO.ok(Collections.emptyList());
        }

        list.forEach(item->{
            item.setOverflowNum(Math.max(item.getFinishNum() - item.getShouldNum(), 0));
        });
        return ResponseDTO.ok(list);
    }
}
