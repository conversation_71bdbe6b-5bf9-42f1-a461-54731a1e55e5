package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import lombok.Data;

import java.util.List;

/**
 * 裁片仓库表 新建表单
 *
 * <AUTHOR>
 * @Date 2024-10-06 16:48:11
 * @Copyright zscbdic
 */

@Data
public class PartStationWarehouseAddForm {

    @Schema(description = "仓库名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "仓库名称 不能为空")
    private String name;

    /**
     * 仓库编码
     */
    @Schema(description = "仓库编码")
    @NotNull(message = "仓库编码 不能为空")
    private String warehouseCode;

    @Schema(description = "仓库地址")
    private String address;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "仓库任务员")
    private List<Long> taskerIds;
}
