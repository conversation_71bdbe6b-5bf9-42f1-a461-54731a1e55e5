package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 裁片货架 实体类
 *
 * <AUTHOR>
 * @Date 2024-10-06 16:49:27
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_part_station_rack")
public class PartStationRackEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 所属仓库id
     */
    private Long warehouseId;

    /**
     * 货架编码
     */
    private String rackCode;

    /**
     * 位置
     */
    private String location;

}
