package net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.dto;

import lombok.Data;

@Data
public class PartStationBinInvDTO {

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 货架id
     */
    private Long rackId;

    /**
     * 货架编码
     */
    private String rackCode;

    /**
     * 库位id
     */
    private Long binId;

    /**
     * 库位编码
     */
    private String binCode;

    /**
     * 库位容量
     */
    private Integer binCapacity;

    /**
     * 库位库存
     */
    private Integer binInventory;

    /**
     * 使用率
     */
    private Double binUsageRate;




}
