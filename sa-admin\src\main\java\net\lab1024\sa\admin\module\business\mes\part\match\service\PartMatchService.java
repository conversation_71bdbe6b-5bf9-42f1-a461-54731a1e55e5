package net.lab1024.sa.admin.module.business.mes.part.match.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.mes.part.match.dao.PartMatchDao;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.part.match.domain.entity.PartMatchEntity;
import net.lab1024.sa.admin.module.business.mes.part.match.domain.form.PartMatchForm;
import net.lab1024.sa.admin.module.business.mes.part.match.domain.form.PartMatchQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.match.domain.vo.PartMatchPositionsStatusVo;
import net.lab1024.sa.admin.module.business.mes.part.match.domain.vo.PartMatchStatusListVo;
import net.lab1024.sa.admin.module.business.mes.part.match.domain.vo.PartMatchStatusVo;
import net.lab1024.sa.admin.module.business.mes.part.match.manager.PartMatchManager;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.domain.entity.FeTicketEntity;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.domain.vo.FeTicketVO;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.manager.FeTicketManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 裁片配扎情况 Service
 *
 * <AUTHOR>
 * @Date 2024-11-07 15:17:40
 * @Copyright zscbdic
 */

@Service
public class PartMatchService {

    @Resource
    private PartMatchDao partMatchDao;

    @Resource
    private PartMatchManager partMatchManager;

    @Resource
    private FeTicketManager feTicketManager;


    /**
     * 查询配扎情况
     *  通过指令单、款号、颜色、尺码、扎号查询，返回配扎情况
     * @return
     */
    public PageResult<PartMatchStatusListVo> queryStatus(PartMatchQueryForm queryForm){
        List<PartMatchStatusListVo> Vos = new ArrayList<>();
        // 根据 指令单、款号、颜色、尺码、扎号 进行分页查询feTicket
        IPage<FeTicketEntity> feTicketList = feTicketManager.queryFeTicketGroupByInstructAndTie(queryForm);
        Page<FeTicketEntity> page = (Page<FeTicketEntity>) SmartPageUtil.convert2PageQuery(queryForm);
        feTicketList.getRecords().stream().parallel().filter(f -> !f.getPositions().isEmpty()).forEach(
                    feTicket -> {
                        PartMatchStatusListVo po = new PartMatchStatusListVo();
                        // 设置数量
                        po.setNum(feTicket.getNum());
                        // 设置物料编码
                        po.setItemNumber(feTicket.getItemNumber());
                        // 车次
                        po.setCutNum(feTicket.getCutNum());
                        // 设置裁床单编号
                        po.setCutBedSheetNumber(feTicket.getCutBedSheetNumber());
                        // 设置数量
                        po.setNum(feTicket.getNum());
                        // 设置物料名称
                        po.setItemName(feTicket.getItemName());
                        // 设置尺码信息
                        po.setSize(feTicket.getSize());
                        // 设置颜色款式信息
                        po.setStyleColor(feTicket.getStyleColor());
                        // 设置款号信息
                        po.setItemName(feTicket.getItemName());
                        // 设置指令单信息
                        po.setInstructOrderNumber(feTicket.getInstructOrderNumber());
                        // 设置扎号信息
                        po.setTieNum(feTicket.getTieNum());
                        // 设置配扎情况
                        List<PartMatchPositionsStatusVo> matches = new ArrayList<>();

                        // 查询扎号相同的其他指令单
                        List<FeTicketEntity> feTicketEntities = feTicketManager.getSameTieTicket(feTicket.getId());
                        for (FeTicketEntity feTicketEntity : feTicketEntities){

                            PartMatchPositionsStatusVo partMatchPositionsStatusVo = new PartMatchPositionsStatusVo();
                            partMatchPositionsStatusVo.setPositionName(feTicketEntity.getPositions());
                            // 查询配扎情况
                            if (partMatchManager.isMatch(feTicketEntity.getId())){
                                // 已配对
                                partMatchPositionsStatusVo.setComplete(true);
                            }else{
                                // 未配对
                                partMatchPositionsStatusVo.setComplete(false);
                            }
                            // 添加部位的配对信息
                            matches.add(partMatchPositionsStatusVo);
                        }
                        po.setParts(matches);
                        Vos.add(po);
                    }
        );
        PageResult<PartMatchStatusListVo> pageResult = SmartPageUtil.convert2PageResult(page, Vos);
        pageResult.setTotal(feTicketList.getTotal());
        return pageResult;

    }


    /**
     * 配扎/配包
     * @param partMatchForm
     * @return
     */
    public ResponseDTO<String> match(PartMatchForm partMatchForm) {
        List<Long> ticketIds = partMatchForm.getTicketIds();
        boolean sameTie = feTicketManager.isSameTie(ticketIds);
        if (!sameTie) {
            return ResponseDTO.userErrorParam("请匹配同扎号的菲票");
        }

        List<FeTicketEntity> tickets = feTicketManager.getSameTieTicket(ticketIds.get(0));
        if(CollUtil.isEmpty(tickets)){
            return ResponseDTO.userErrorParam("没有找到相关菲票");
        }

        List<PartMatchEntity> list = ticketIds.stream().map(id -> {
            PartMatchEntity entity = new PartMatchEntity();
            entity.setTicketId(id);
            return entity;
        }).collect(Collectors.toList());

        List<Long> sameTieTicketIds = tickets.stream().map(FeTicketEntity::getId)
                .collect(Collectors.toList());
        partMatchManager.updateBatchData(sameTieTicketIds,list);

        return ResponseDTO.ok();

    }

    public ResponseDTO<PartMatchStatusVo> queryStatusByTicketId(Long ticketId) {
        List<FeTicketEntity> tickets = feTicketManager.getSameTieTicket(ticketId);
        if(CollUtil.isEmpty(tickets)){
            return ResponseDTO.userErrorParam("没有找到相关菲票");
        }
        //查询配扎情况
        List<Long> ticketIds = tickets.stream().map(FeTicketEntity::getId).collect(Collectors.toList());
        List<PartMatchEntity> matchs = partMatchDao.selectList(new LambdaQueryWrapper<PartMatchEntity>()
                .select(PartMatchEntity::getTicketId)
                .in(PartMatchEntity::getTicketId, ticketIds));
        //是否全部配对
        boolean complete = tickets.size() == matchs.size();
        if(CollUtil.isEmpty(matchs)){
            PartMatchStatusVo vo = new PartMatchStatusVo();
            vo.setComplete(complete);
            vo.setTargetTicketIds(ticketIds);
            vo.setTargetTickets(SmartBeanUtil.copyList(tickets, FeTicketVO.class));
            vo.setNowTicketIds(Collections.emptyList());
            vo.setNowTickets(Collections.emptyList());
            return ResponseDTO.ok(vo);
        }

        List<Long> nowTicketIds = matchs.stream()
                .map(PartMatchEntity::getTicketId)
                .collect(Collectors.toList());
        List<FeTicketEntity> nowTickets = tickets.stream().filter(t -> nowTicketIds.contains(t.getId()))
                .collect(Collectors.toList());
        PartMatchStatusVo vo = new PartMatchStatusVo();
        vo.setComplete(complete);
        vo.setTargetTicketIds(ticketIds);
        vo.setTargetTickets(SmartBeanUtil.copyList(tickets, FeTicketVO.class));
        vo.setNowTicketIds(nowTicketIds);
        vo.setNowTickets(SmartBeanUtil.copyList(nowTickets, FeTicketVO.class));
        return ResponseDTO.ok(vo);
    }
}
