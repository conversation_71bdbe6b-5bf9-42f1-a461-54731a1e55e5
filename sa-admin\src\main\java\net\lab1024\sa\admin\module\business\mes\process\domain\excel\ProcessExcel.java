package net.lab1024.sa.admin.module.business.mes.process.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 工序信息
 * <AUTHOR>
 */
@Data
public class ProcessExcel {

    /**
     * 工序编号
     */
    @NotNull(message = "工序编号不能为空")
    @ExcelProperty(value = "工序编号")
    private String processNumber;

    /**
     * 工序名称
     */
    @NotNull(message = "工序名称不能为空")
    @ExcelProperty(value = "工序名称")
    private String name;

    /**
     * 部位
     */
    @ExcelProperty(value = "部位")
    private String position;

    /**
     * 工序控制
     */
    @NotNull(message = "工序控制不能为空")
    @ExcelProperty(value = "工序控制")
    private String processControl;

    /**
     * 标准工时;单位
     */
    @Min(value = 0, message = "标准工时不能小于0")
    @ExcelProperty(value = "标准工时")
    private Integer standardTime;

    /**
     * 工价一
     */
    @NotNull(message = "工价一不能为空")
    @Min(value = 0, message = "工价一不能小于0")
    @ExcelProperty(value = "工价一")
    private BigDecimal unitPrice1;
}
