package net.lab1024.sa.admin.module.business.mes.part.station.inventory.job;

import cn.hutool.core.collection.CollUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.mes.part.station.anslyse.dao.PartStationAnalyseDao;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.dto.PartStationBinInvDTO;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.domain.dto.StoragePressureConfigDTO;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationWarehouseEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.manager.PartStationConfigManager;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.manager.PartStationWarehouseManager;
import net.lab1024.sa.base.common.enumeration.UserTypeEnum;
import net.lab1024.sa.base.module.support.job.core.SmartJob;
import net.lab1024.sa.base.module.support.message.constant.MessageTypeEnum;
import net.lab1024.sa.base.module.support.message.domain.MessageSendForm;
import net.lab1024.sa.base.module.support.message.service.MessageService;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;

/**
 * 存储压力检测
 */
@Slf4j
@Component
public class StoragePressureJob implements SmartJob {

    @Resource
    private PartStationAnalyseDao partStationAnalyseDao;

    @Resource
    private PartStationWarehouseManager partStationWarehouseManager;

    @Resource
    private PartStationConfigManager partStationConfigManager;

    @Resource
    private MessageService messageService;

    @Override
    public String run(String param) {
        checkStoragePressure();
        log.info("检查存储压力完成");
        return "检查存储压力完成";
    }

    private void checkStoragePressure() {
        StoragePressureConfigDTO pressureConfig = partStationConfigManager.getStoragePressureConfig();
        if (pressureConfig == null || !pressureConfig.getEnableFlag()) {
            return;
        }
        if (CollUtil.isEmpty(pressureConfig.getEmployeeIds())) {
            return;
        }

        List<PartStationWarehouseEntity> wList = partStationWarehouseManager.list();
        if (CollUtil.isEmpty(wList)) {
            return;
        }

        StringBuilder completeMessage = new StringBuilder();
        boolean isSend = false;
        // 遍历每个仓库
        for (PartStationWarehouseEntity warehouse : wList) {
            Long warehouseId = warehouse.getId();
            List<PartStationBinInvDTO> binInvDTOS = partStationAnalyseDao.queryPartStationBinInv(warehouseId);
            if (CollUtil.isEmpty(binInvDTOS)) {
                continue;
            }
            // 获取使用率大于配置的最大值
            List<PartStationBinInvDTO> maxTop3 = binInvDTOS.stream()
                    .filter(binInvDTO -> binInvDTO.getBinUsageRate() > pressureConfig.getMaxUsageRate())
                    .sorted(Comparator.comparingDouble(PartStationBinInvDTO::getBinUsageRate).reversed())
                    .limit(3)
                    .toList();
            if (CollUtil.isEmpty(maxTop3)) {
                continue;
            }
            // 获取使用率为0的货位
            isSend = true;
            List<PartStationBinInvDTO> minList = binInvDTOS.stream()
                    .filter(binInvDTO -> binInvDTO.getBinUsageRate() == 0)
                    .toList();

            StringBuilder warehouseStr = new StringBuilder(String.format("""
                    驿站仓库编号：%s
                    驿站仓库名称：%s
                    高压力货位TOP3
                    """, warehouse.getWarehouseCode(), warehouse.getName()));
            // 遍历每个货位
            for (PartStationBinInvDTO dto : maxTop3) {
                String binFormat = "- 货位编码[%s]：利用率%s%%（容量%s片，实际%s片）\n";
                String binStr = binFormat.formatted(dto.getBinCode(), dto.getBinUsageRate(), dto.getBinCapacity(), dto.getBinInventory());
                warehouseStr.append(binStr);
            }
            if (CollUtil.isNotEmpty(minList)) {
                //如果有异常空置货位
                String minFormat = String.format("异常空置货位(共%s个)\n", minList.size());
                warehouseStr.append(minFormat);
                for (int i = 0; i < minList.size(); i++) {
                    if (i == 3) {
                        warehouseStr.append("......\n");
                        break;
                    } else {
                        String binFormat = "- 货位编码[%s]：利用率%s%%（容量%s片，实际%s片）\n";
                        warehouseStr.append(binFormat.formatted(minList.get(i).getBinCode(), minList.get(i).getBinUsageRate(), minList.get(i).getBinCapacity(), minList.get(i).getBinInventory()));
                    }
                }
            }
            completeMessage.append(warehouseStr).append("\n");
        }

        if (!isSend) {
            return;
        }

        String message = String.format("""
                智能巡检发现以下裁片仓库存在货位高压率，请及时处理：
                
                %s
                系统提示：本消息由裁片驿站助手自动发送，无需回复。如需调整提醒规则，请在驿站设置中进行配置。
                """, completeMessage);

        //发送
        List<MessageSendForm> msgs = pressureConfig.getEmployeeIds().stream().map(id -> {
            MessageSendForm messageSendForm = new MessageSendForm();
            messageSendForm.setMessageType(MessageTypeEnum.MAIL.getValue());
            messageSendForm.setReceiverUserType(UserTypeEnum.ADMIN_EMPLOYEE.getValue());
            messageSendForm.setReceiverUserId(id);
            messageSendForm.setTitle("裁片驿站助手 | 智能巡检");
            messageSendForm.setContent(message);
            return messageSendForm;
        }).toList();
        messageService.sendMessage(msgs);

//        partStationAnalyseDao.queryPartStationBinInv();

    }
}
