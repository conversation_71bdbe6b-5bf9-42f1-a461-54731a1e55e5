package net.lab1024.sa.admin.module.business.mes.salary.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.SettlementRecordDetailEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.SettlementRecordEntity;

import java.util.List;

@Data
public class SettlementBo {

    /**
     * 结算记录
     */
    private List<RecordBo> settlementRecords;

    /**
     * 工单记录ids
     */
    private List<Long> workRecordIds;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RecordBo {
        /**
         * 结算记录
         */
        private SettlementRecordEntity record;
        /**
         * 结算记录详情
         */
        private List<SettlementRecordDetailEntity> details;
    }
}
