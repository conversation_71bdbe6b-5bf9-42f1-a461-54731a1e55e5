package net.lab1024.sa.admin.module.business.mes.factory.service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.factory.dao.ProduceTeamDao;
import net.lab1024.sa.admin.module.business.mes.factory.dao.ProduceTeamMemberDao;
import net.lab1024.sa.admin.module.business.mes.factory.dao.WorkshopDao;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.ProduceTeamEntity;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.ProduceTeamMemberEntity;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.WorkshopEntity;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.*;
import net.lab1024.sa.admin.module.business.mes.factory.domain.vo.ProduceTeamMemberVO;
import net.lab1024.sa.admin.module.business.mes.factory.domain.vo.ProduceTeamVO;
import net.lab1024.sa.admin.module.business.mes.factory.manager.ProduceTeamMemberManager;
import net.lab1024.sa.admin.module.system.employee.dao.EmployeeDao;
import net.lab1024.sa.admin.module.system.employee.domain.entity.EmployeeEntity;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 生产小组 Service
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:32:46
 * @Copyright zscbdic
 */

@Service
public class ProduceTeamService {

    @Resource
    private ProduceTeamDao produceTeamDao;
    @Resource
    private ProduceTeamMemberDao produceTeamMemberDao;
    @Resource
    private ProduceTeamMemberManager produceTeamMemberManager;
    @Resource
    private EmployeeDao employeeDao;
    @Resource
    private WorkshopDao workshopDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<ProduceTeamVO> queryPage(ProduceTeamQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ProduceTeamVO> list = produceTeamDao.queryPage(page, queryForm);
        for (ProduceTeamVO produceTeamVO : list) {
            //leader姓名
            Long leaderId = produceTeamVO.getLeaderId();
            EmployeeEntity employeeEntity = employeeDao.selectById(leaderId);
            if (employeeEntity==null){
//                produceTeamVO.setLeader("发生错误，该负责人已被删除");
            }
            else {
                produceTeamVO.setLeader(employeeEntity.getActualName());
            }

            Long workshopId = produceTeamVO.getWorkshopId();
            WorkshopEntity workshopEntity = workshopDao.selectById(workshopId);
            if(workshopEntity==null){
                produceTeamVO.setWorkshopName("发生错误，该车间已被删除");
            }
            else {
                produceTeamVO.setWorkshopName(workshopEntity.getName());
            }
        }
        PageResult<ProduceTeamVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(ProduceTeamAddForm addForm) {
        ProduceTeamEntity produceTeamEntity = SmartBeanUtil.copy(addForm, ProduceTeamEntity.class);

//      添加生产小组
        produceTeamDao.insert(produceTeamEntity);
        List<ProduceTeamMemberAddForm> produceTeamMemberAddFormList = addForm.getProduceTeamMemberAddFormList();
//        List<Integer> memberId = addForm.getMemberId();
        //若无具体小组成员，添加完成
        if (CollectionUtils.isEmpty(produceTeamMemberAddFormList)) {
            return ResponseDTO.ok();
        }

        //若有指定小组成员
        List<ProduceTeamMemberEntity> produceTeamMemberEntities = SmartBeanUtil.copyList(produceTeamMemberAddFormList, ProduceTeamMemberEntity.class);

        //返回添加小组的主键作为成员表中的小组id字段
        Long id = produceTeamEntity.getId();

        for (ProduceTeamMemberEntity produceTeamMemberEntity : produceTeamMemberEntities) {
            produceTeamMemberEntity.setTeamId(id);
        }
        //后添加小组成员
        produceTeamMemberManager.saveBatch(produceTeamMemberEntities);

        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */

    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(ProduceTeamUpdateForm updateForm) {
        ProduceTeamEntity produceTeamEntity = SmartBeanUtil.copy(updateForm, ProduceTeamEntity.class);

        Long id = produceTeamEntity.getId();

        //查询是否有该组id成员
        List<ProduceTeamMemberEntity> produceTeamMemberEntities = produceTeamMemberDao.selectList(new QueryWrapper<ProduceTeamMemberEntity>().eq("team_id", id));

        //若存在组员，先删除全部组员
        if (CollectionUtils.isNotEmpty(produceTeamMemberEntities)) {
            for (ProduceTeamMemberEntity produceTeamMemberEntity : produceTeamMemberEntities) {
                produceTeamMemberDao.updateDeleted(produceTeamMemberEntity.getId(), true);
            }
        }

        //需要加入的新组员
        List<ProduceTeamMemberAddForm> produceTeamMemberAddFormList = updateForm.getProduceTeamMemberAddFormList();

        List<ProduceTeamMemberEntity> produceAddTeamMemberList = SmartBeanUtil.copyList(produceTeamMemberAddFormList, ProduceTeamMemberEntity.class);

        for (ProduceTeamMemberEntity produceTeamMemberEntity : produceAddTeamMemberList) {
            produceTeamMemberEntity.setTeamId(id);
            produceTeamMemberDao.insert(produceTeamMemberEntity);
        }
        produceTeamDao.updateById(produceTeamEntity);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }
        //判断是否存该id的小组
        ProduceTeamEntity produceTeamEntity = produceTeamDao.selectById(id);
        if (produceTeamEntity == null) {
            ResponseDTO.userErrorParam("删除异常,不存在该数据");
        }


        //判断是否存在该小组id的组员
        List<ProduceTeamMemberEntity> produceTeamMemberEntities = produceTeamMemberDao.selectList(new QueryWrapper<ProduceTeamMemberEntity>().eq("team_id", id));

        //存在小组成员,首先删除小组成员
        if (CollectionUtils.isNotEmpty(produceTeamMemberEntities)) {
            for (ProduceTeamMemberEntity produceTeamMemberEntity : produceTeamMemberEntities) {
                produceTeamMemberDao.updateDeleted(produceTeamMemberEntity.getId(), true);
            }
        }

        //删除小组
        produceTeamDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }

    /**
     * id查询小组信息
     * @param id
     * @return
     */
    public ResponseDTO<ProduceTeamVO> getById(Long id) {
        if (id==null){
            return ResponseDTO.ok();
        }
        ProduceTeamEntity produceTeamEntity = produceTeamDao.selectById(id);

        ProduceTeamVO produceTeamVO = SmartBeanUtil.copy(produceTeamEntity, ProduceTeamVO.class);

        produceTeamVO.setLeader(employeeDao.selectById(produceTeamVO.getLeaderId()).getActualName());

        List<ProduceTeamMemberEntity> produceTeamMemberEntityList = produceTeamMemberDao
                .selectList(new QueryWrapper<ProduceTeamMemberEntity>().eq("team_id", id).eq("deleted_flag",false));

        List<ProduceTeamMemberVO> produceTeamMemberVOList = SmartBeanUtil.copyList(produceTeamMemberEntityList, ProduceTeamMemberVO.class);

        produceTeamMemberVOList.forEach(e->{
            e.setMember(employeeDao.selectById(e.getMemberId()).getActualName());
        });

        produceTeamVO.setProduceTeamMemberVOList(produceTeamMemberVOList);

        return ResponseDTO.ok(produceTeamVO);
    }

    /**
     * 查询所有小组信息
     * @return
     */
    public ResponseDTO<List<ProduceTeamVO>> queryList(ProduceTeamQuery query) {
        List<ProduceTeamEntity> teams = produceTeamDao.selectList(null);
        return ResponseDTO.ok(SmartBeanUtil.copyList(teams, ProduceTeamVO.class));
    }
}
