package net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * 裁片周转箱 实体类
 *
 * <AUTHOR>
 * @Date 2025-03-14 11:36:51
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_part_station_turn_box")
public class PartStationTurnBoxEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 周转箱编号
     */
    private String number;

    /**
     * 周转箱名称
     */
    private String name;

    /**
     * 容量
     */
    private Integer capacity;

    /**
     * 库内标识
     */
    private Boolean insideFlag;

    /**
     * 库内货位id
     */
    private Long insideLocationId;

}
