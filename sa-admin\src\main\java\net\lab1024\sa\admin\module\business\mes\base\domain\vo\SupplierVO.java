package net.lab1024.sa.admin.module.business.mes.base.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 供应商表 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-03 09:43:14
 * @Copyright zscbdic
 */

@Data
public class SupplierVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;


    @Schema(description = "备注")
    private String remark;

    @Schema(description = "供应商名称")
    private String name;

    @Schema(description = "供应商简称")
    private String fastName;

    @Schema(description = "联系人")
    private String contact;

    @Schema(description = "电话")
    private String telephone;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "供应商类型")
    private String type;

    @Schema(description = "结算方式")
    private String way;

    @Schema(description = "等级;5星最高，无半星")
    private Integer level;

    @Schema(description = "停用标识;0启用，1停用")
    private Boolean enableFlag;

}
