package net.lab1024.sa.admin.module.business.mes.base.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 单位表 实体类
 *
 * <AUTHOR>
 * @Date 2024-07-04 11:12:45
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_unit")
public class UnitEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Integer deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 单位编码
     */
    private String unitCode;

    /**
     * 单位名称
     */
    private String name;

}
