package net.lab1024.sa.admin.module.business.mes.equip.equipment.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentScadaQuery;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentScadaQueryForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.vo.EquipmentVO;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.service.EquipmentScadaService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 设备scada信息 Controller
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:13:22
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class EquipmentScadaController {

    @Resource
    private EquipmentScadaService equipmentScadaService;

    /**
     * 平台特定设备类别分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "获取平台特定设备类别分页查询")
    @PostMapping("/equipmentScada/queryScadaPage")
    public ResponseDTO<PageResult<EquipmentVO>> queryScadaPage(@RequestBody @Valid EquipmentScadaQueryForm queryForm) {
        return equipmentScadaService.queryScadaPage(queryForm);
    }

    /**
     * 平台特定设备类别查询列表
     * @param queryForm
     * @return
     */
    @Operation(summary = "获取平台特定设备类别查询列表")
    @PostMapping("/equipmentScada/queryScadaList")
    public ResponseDTO<List<EquipmentVO>> queryScadaList(@RequestBody @Valid EquipmentScadaQuery queryForm) {
        return equipmentScadaService.queryScadaList(queryForm);
    }



}
