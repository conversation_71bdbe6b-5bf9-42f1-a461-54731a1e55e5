package net.lab1024.sa.admin.module.business.mes.produce.instruct.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderClothesDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderClothesEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderClothesAddForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderClothesQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderClothesUpdateForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderClothesVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 指令单成衣信息 Service
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:25:32
 * @Copyright zscbdic
 */

@Service
public class ProduceInstructOrderClothesService {

    @Resource
    private ProduceInstructOrderClothesDao produceInstructOrderClothesDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<ProduceInstructOrderClothesVO> queryPage(ProduceInstructOrderClothesQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ProduceInstructOrderClothesVO> list = produceInstructOrderClothesDao.queryPage(page, queryForm);
        PageResult<ProduceInstructOrderClothesVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(ProduceInstructOrderClothesAddForm addForm) {
        ProduceInstructOrderClothesEntity produceInstructOrderClothesEntity = SmartBeanUtil.copy(addForm, ProduceInstructOrderClothesEntity.class);
        produceInstructOrderClothesDao.insert(produceInstructOrderClothesEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(ProduceInstructOrderClothesUpdateForm updateForm) {
        ProduceInstructOrderClothesEntity produceInstructOrderClothesEntity = SmartBeanUtil.copy(updateForm, ProduceInstructOrderClothesEntity.class);
        produceInstructOrderClothesDao.updateById(produceInstructOrderClothesEntity);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    public ResponseDTO<String> batchDelete(List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return ResponseDTO.ok();
        }

        produceInstructOrderClothesDao.batchUpdateDeleted(idList, true);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Integer id) {
        if (null == id){
            return ResponseDTO.ok();
        }

        produceInstructOrderClothesDao.updateDeleted(id,true);
        return ResponseDTO.ok();
    }

    /**
     * 获取指令单下所有成衣款式颜色
     *
     * @param id
     * @return
     */
    public List<String> getStyleColorList(Long id) {
        LambdaQueryWrapper<ProduceInstructOrderClothesEntity> lq = new LambdaQueryWrapper<>();
        lq.eq(ProduceInstructOrderClothesEntity::getOrderId, id);
        List<ProduceInstructOrderClothesEntity> entities = produceInstructOrderClothesDao.selectList(lq);
        if(CollUtil.isEmpty(entities)){
            return Collections.emptyList();
        }

        return entities.stream()
                .map(ProduceInstructOrderClothesEntity::getStyleColor)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取指令单下所有成衣尺码
     * @param id
     * @return
     */
    public List<String> getSizeList(Long id) {
        LambdaQueryWrapper<ProduceInstructOrderClothesEntity> lq = new LambdaQueryWrapper<>();
        lq.eq(ProduceInstructOrderClothesEntity::getOrderId, id);
        List<ProduceInstructOrderClothesEntity> entities = produceInstructOrderClothesDao.selectList(lq);
        if(CollUtil.isEmpty(entities)){
            return Collections.emptyList();
        }

        return entities.stream()
                .map(ProduceInstructOrderClothesEntity::getSize)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取指令单下所有成衣信息
     * @param id
     * @return
     */
    public List<ProduceInstructOrderClothesVO> list(Long id) {
        List<ProduceInstructOrderClothesEntity> clothes = produceInstructOrderClothesDao.selectList(new LambdaQueryWrapper<ProduceInstructOrderClothesEntity>()
                .eq(ProduceInstructOrderClothesEntity::getOrderId, id));
        return SmartBeanUtil.copyList(clothes, ProduceInstructOrderClothesVO.class);
    }
}
