package net.lab1024.sa.admin.module.business.mes.part.station.setting.annotation;

import net.lab1024.sa.admin.module.business.mes.part.station.setting.constant.StorageManageModeConfigEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 存储管理模式
 *
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface PartStationStoreManageMode {

    /**
     * 存储管理模式
     * @return
     */
    StorageManageModeConfigEnum mode() ;
}
