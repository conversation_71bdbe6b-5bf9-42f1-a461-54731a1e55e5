package net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 货位 新建表单
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:14:20
 * @Copyright zscbdic
 */

@Data
public class StkLocationAddForm {

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "货架id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "货架id 不能为空")
    private Long rackId;

    @Schema(description = "货位编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "货位编号 不能为空")
    private String number;

    @Schema(description = "拣货优先级;拣货优先级 保留")
    private Integer pickPriority;

}
