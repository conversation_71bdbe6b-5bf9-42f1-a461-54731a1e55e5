package net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 仓库 实体类
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:13:18
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_stk_warehouse")
public class StkWarehouseEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 仓库编号
     */
    private String number;

    /**
     * 仓库名称
     */
    private String name;

    /**
     * 仓库地址
     */
    private String address;

    /**
     * 负责人id
     */
    private Long principalId;

    /**
     * 负责人名称
     */
    private String principalName;

    /**
     * 负责人电话
     */
    private String tel;

    /**
     * 是否启用仓位管理;0否 1是
     */
    private Boolean openLocationFlag;

    /**
     * 允许锁库;0否 1是（保留字段）
     */
    private Boolean allowLockFlag;

    /**
     * 允许负库存;0否 1是
     */
    private Boolean allowNegativeFlag;

}
