package net.lab1024.sa.admin.module.business.mes.process.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.process.dao.ProcessLibraryDao;
import net.lab1024.sa.admin.module.business.mes.process.dao.ProcessLibraryDetailsDao;
import net.lab1024.sa.admin.module.business.mes.process.domain.entity.ProcessLibraryDetailsEntity;
import net.lab1024.sa.admin.module.business.mes.process.domain.entity.ProcessLibraryEntity;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessLibraryAddForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessLibraryDetailsAddForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessLibraryQueryForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessLibraryUpdateForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.vo.ProcessLibraryDetailsVO;
import net.lab1024.sa.admin.module.business.mes.process.domain.vo.ProcessLibraryVO;
import net.lab1024.sa.admin.module.business.mes.process.manager.ProcessLibraryManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工序库 Service
 *
 * <AUTHOR>
 * @Date 2024-07-13 15:19:57
 * @Copyright zscbdic
 */

@Service
public class ProcessLibraryService {

    @Resource
    private ProcessLibraryDao processLibraryDao;

    @Resource
    private ProcessLibraryDetailsDao processLibraryDetailsDao;

    @Resource
    private ProcessLibraryManager processLibraryManager;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<ProcessLibraryVO> queryPage(ProcessLibraryQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ProcessLibraryVO> list = processLibraryDao.queryPage(page, queryForm);
        PageResult<ProcessLibraryVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(ProcessLibraryAddForm addForm) {
        processLibraryManager.addCheck(addForm);


        ProcessLibraryEntity processLibraryEntity = SmartBeanUtil.copy(addForm, ProcessLibraryEntity.class);
        processLibraryDao.insert(processLibraryEntity);
        Long processLibraryEntityId = processLibraryEntity.getId();

        List<ProcessLibraryDetailsAddForm> processLibraryDetailsAddFormList = addForm.getProcessLibraryDetailsAddFormList();

        //排序
        processLibraryDetailsAddFormList = processLibraryDetailsAddFormList.stream().
                sorted(Comparator.comparing(ProcessLibraryDetailsAddForm::getSerialNumber)).collect(Collectors.toList());

        int length = processLibraryDetailsAddFormList.size();
        for (ProcessLibraryDetailsAddForm processLibraryDetailsAddForm : processLibraryDetailsAddFormList) {
            ProcessLibraryDetailsEntity processLibraryDetailsEntity = SmartBeanUtil.copy(processLibraryDetailsAddForm, ProcessLibraryDetailsEntity.class);
            processLibraryDetailsEntity.setProcessLibraryId(processLibraryEntityId);
            if (processLibraryDetailsEntity.getSerialNumber() == length) {
                processLibraryDetailsEntity.setEndFlag(true);
            } else {
                processLibraryDetailsEntity.setEndFlag(false);
            }
            processLibraryDetailsDao.insert(processLibraryDetailsEntity);
        }
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(ProcessLibraryUpdateForm updateForm) {
        processLibraryManager.updateCheck(updateForm);

        ProcessLibraryEntity processLibraryEntity = SmartBeanUtil.copy(updateForm, ProcessLibraryEntity.class);

        Long id = updateForm.getId();
        //删除原有库中的工序
        List<ProcessLibraryDetailsEntity> processLibraryDetailsEntityList = processLibraryDetailsDao.
                selectList(new LambdaQueryWrapper<ProcessLibraryDetailsEntity>().eq(ProcessLibraryDetailsEntity::getProcessLibraryId, id));

        if (CollectionUtils.isNotEmpty(processLibraryDetailsEntityList)) {
            processLibraryDetailsDao.deleteBatchIds(processLibraryDetailsEntityList.stream()
                    .map(ProcessLibraryDetailsEntity::getId)
                    .collect(Collectors.toList()));
        }

        //新增工序
        List<ProcessLibraryDetailsAddForm> processLibraryDetailsAddFormList = updateForm.getProcessLibraryDetailsAddFormList();
        //排序
        processLibraryDetailsAddFormList = processLibraryDetailsAddFormList.stream().
                sorted(Comparator.comparing(ProcessLibraryDetailsAddForm::getSerialNumber)).collect(Collectors.toList());

        int length = processLibraryDetailsAddFormList.size();
        for (ProcessLibraryDetailsAddForm processLibraryDetailsAddForm : processLibraryDetailsAddFormList) {
            ProcessLibraryDetailsEntity processLibraryDetailsEntity = SmartBeanUtil.copy(processLibraryDetailsAddForm, ProcessLibraryDetailsEntity.class);
            processLibraryDetailsEntity.setProcessLibraryId(id);
            if (processLibraryDetailsAddForm.getSerialNumber() == length) {
                processLibraryDetailsEntity.setEndFlag(true);
            } else {
                processLibraryDetailsEntity.setEndFlag(false);
            }
            processLibraryDetailsDao.insert(processLibraryDetailsEntity);
        }
        processLibraryDao.updateById(processLibraryEntity);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }
        List<ProcessLibraryDetailsEntity> processLibraryDetailsEntityList = processLibraryDetailsDao.
                selectList(new LambdaQueryWrapper<ProcessLibraryDetailsEntity>()
                        .eq(ProcessLibraryDetailsEntity::getProcessLibraryId, id)
                        .eq(ProcessLibraryDetailsEntity::getDeletedFlag, false));

        //首先删除库中的详细工序
        if (CollectionUtils.isNotEmpty(processLibraryDetailsEntityList)) {
            processLibraryDetailsEntityList.forEach(e -> {
                processLibraryDetailsDao.updateDeleted(e.getId(), true);
            });
        }
        //删除库
        processLibraryDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }

    /**
     * 单个查询,带详细工序
     *
     * @param id
     * @return
     */
    public ResponseDTO<ProcessLibraryVO> queryById(Long id) {

        if (id == null) {
            return ResponseDTO.ok();
        }

        ProcessLibraryEntity processLibraryEntity = processLibraryDao.selectById(id);
        ProcessLibraryVO processLibraryVO = SmartBeanUtil.copy(processLibraryEntity, ProcessLibraryVO.class);

        List<ProcessLibraryDetailsEntity> processLibraryDetailsEntityList = processLibraryDetailsDao.
                selectList(new LambdaQueryWrapper<ProcessLibraryDetailsEntity>()
                        .eq(ProcessLibraryDetailsEntity::getProcessLibraryId, id)
                        .eq(ProcessLibraryDetailsEntity::getDeletedFlag, false));

        List<ProcessLibraryDetailsVO> processLibraryDetailsVOList = SmartBeanUtil.copyList(processLibraryDetailsEntityList, ProcessLibraryDetailsVO.class);

        processLibraryDetailsVOList = processLibraryDetailsVOList.stream()
                .sorted(Comparator.comparing(ProcessLibraryDetailsVO::getSerialNumber))
                .collect(Collectors.toList());
        processLibraryVO.setProcessLibraryDetailsVOList(processLibraryDetailsVOList);

        return ResponseDTO.ok(processLibraryVO);
    }

    public List<ProcessLibraryVO> queryList() {
        List<ProcessLibraryEntity> libraryEntities = processLibraryDao.selectList(null);
        return BeanUtil.copyToList(libraryEntities, ProcessLibraryVO.class);
    }
}
