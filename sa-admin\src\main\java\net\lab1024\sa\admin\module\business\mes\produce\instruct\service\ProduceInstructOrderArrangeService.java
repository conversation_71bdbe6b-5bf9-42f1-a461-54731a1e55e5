package net.lab1024.sa.admin.module.business.mes.produce.instruct.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.event.produce.ProduceInstructOrderArrangeFinishEvent;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.constant.ProduceInstructOrderProduceStatusEnum;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderArrangeDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderArrangeEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderArrangeAddForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderArrangeQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderArrangeUpdateForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderArrangeVO;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.util.SmartRequestUtil;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 指令单安排信息 Service
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:26:34
 * @Copyright zscbdic
 */

@Service
public class ProduceInstructOrderArrangeService {

    @Resource
    private ProduceInstructOrderArrangeDao produceInstructOrderArrangeDao;

    @Resource
    private ProduceInstructOrderDao produceInstructOrderDao;

    @Resource
    private ApplicationEventPublisher eventPublisher;


    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<ProduceInstructOrderArrangeVO> queryPage(ProduceInstructOrderArrangeQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ProduceInstructOrderArrangeVO> list = produceInstructOrderArrangeDao.queryPage(page, queryForm);

        if (CollUtil.isEmpty(list)) {
            return SmartPageUtil.convert2PageResult(page, Collections.emptyList());
        }

        List<Long> orderIds = list.stream().map(ProduceInstructOrderArrangeVO::getOrderId).collect(Collectors.toList());
        List<ProduceInstructOrderEntity> orders = produceInstructOrderDao.selectBatchIds(orderIds);
        if (CollUtil.isEmpty(orders)) {
            return SmartPageUtil.convert2PageResult(page, Collections.emptyList());
        }
        Map<Long, ProduceInstructOrderEntity> orderMap = orders.stream()
                .collect(Collectors.toMap(ProduceInstructOrderEntity::getId, Function.identity()));
        for (ProduceInstructOrderArrangeVO vo : list) {
            if (orderMap.containsKey(vo.getOrderId())) {
                vo.setInstructNumber(orderMap.get(vo.getOrderId()).getInstructNumber());
                vo.setItemNumber(orderMap.get(vo.getOrderId()).getItemNumber());
                vo.setItemName(orderMap.get(vo.getOrderId()).getItemName());
            }
        }

        PageResult<ProduceInstructOrderArrangeVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(ProduceInstructOrderArrangeAddForm addForm) {
        ProduceInstructOrderArrangeEntity produceInstructOrderArrangeEntity = SmartBeanUtil.copy(addForm, ProduceInstructOrderArrangeEntity.class);
        produceInstructOrderArrangeDao.insert(produceInstructOrderArrangeEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(ProduceInstructOrderArrangeUpdateForm updateForm) {
        ProduceInstructOrderArrangeEntity produceInstructOrderArrangeEntity = SmartBeanUtil.copy(updateForm, ProduceInstructOrderArrangeEntity.class);
        produceInstructOrderArrangeDao.updateById(produceInstructOrderArrangeEntity);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    public ResponseDTO<String> batchDelete(List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return ResponseDTO.ok();
        }

        produceInstructOrderArrangeDao.batchUpdateDeleted(idList, true);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Integer id) {
        if (null == id) {
            return ResponseDTO.ok();
        }

        produceInstructOrderArrangeDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }

    /**
     * 查询指令单携带安排信息
     *
     * @param queryForm
     * @return
     */
    public PageResult<ProduceInstructOrderVO> queryInstructOrderWithArrangePage(ProduceInstructOrderQueryForm queryForm) {
        // 分页查询
//        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);

        LambdaQueryWrapper<ProduceInstructOrderEntity> lq = new LambdaQueryWrapper<ProduceInstructOrderEntity>()
                .ne(ProduceInstructOrderEntity::getProduceStatus, ProduceInstructOrderProduceStatusEnum.PLAN.getValue())
                .orderByDesc(ProduceInstructOrderEntity::getIssuedTime)
                .between(queryForm.getDeliverTimeBegin()!=null && queryForm.getDeliverTimeEnd()!=null, ProduceInstructOrderEntity::getDeliverTime, queryForm.getDeliverTimeBegin(), queryForm.getDeliverTimeEnd())
                .eq(StrUtil.isNotEmpty(queryForm.getProduceStatus()), ProduceInstructOrderEntity::getProduceStatus, queryForm.getProduceStatus())
                .eq(StrUtil.isNotEmpty(queryForm.getPriority()), ProduceInstructOrderEntity::getPriority, queryForm.getPriority())
                .and(StrUtil.isNotEmpty(queryForm.getQueryKey()), q -> {
                    q.like(ProduceInstructOrderEntity::getInstructNumber, queryForm.getQueryKey())
                            .or()
                            .like(ProduceInstructOrderEntity::getItemNumber, queryForm.getQueryKey());
                });

        Page<ProduceInstructOrderEntity> page = new Page<>(queryForm.getPageNum(), queryForm.getPageSize());
        produceInstructOrderDao.selectPage(page, lq);

        List<ProduceInstructOrderEntity> list = page.getRecords();
        // 查询指令单携带安排信息
        List<ProduceInstructOrderVO> vos = list.stream().map(e -> {
            List<ProduceInstructOrderArrangeEntity> arrangeEntities = produceInstructOrderArrangeDao
                    .selectList(new LambdaQueryWrapper<ProduceInstructOrderArrangeEntity>()
                            .eq(ProduceInstructOrderArrangeEntity::getOrderId, e.getId()));

            ProduceInstructOrderVO vo = SmartBeanUtil.copy(e, ProduceInstructOrderVO.class);

            vo.setArrangeList(SmartBeanUtil.copyList(arrangeEntities, ProduceInstructOrderArrangeVO.class));
            return vo;
        })
//                .filter(e->CollUtil.isNotEmpty(e.getArrangeList()))
                .collect(Collectors.toList());

        page.setTotal(produceInstructOrderDao.selectCount(lq));
        PageResult<ProduceInstructOrderVO> pageResult = SmartPageUtil.convert2PageResult(page, vos);
        return pageResult;
    }

    /**
     * 查询自己安排信息
     *
     * @param queryForm
     * @return
     */
    public PageResult<ProduceInstructOrderArrangeVO> querySelfArrangePage(ProduceInstructOrderArrangeQueryForm queryForm) {
        boolean queryKeyFlag = StrUtil.isNotBlank(queryForm.getQueryKey());
        //对指令单模糊查询，并且状态不是计划
        List<ProduceInstructOrderEntity> orderEntityList = produceInstructOrderDao.selectList(new LambdaQueryWrapper<ProduceInstructOrderEntity>()
                .ne(ProduceInstructOrderEntity::getProduceStatus, ProduceInstructOrderProduceStatusEnum.PLAN.getValue())
                .and(queryKeyFlag, wrapper -> {
                    wrapper.like(queryKeyFlag, ProduceInstructOrderEntity::getInstructNumber, queryForm.getQueryKey())
                            .or()
                            .like(queryKeyFlag, ProduceInstructOrderEntity::getName, queryForm.getQueryKey())
                            .or()
                            .like(queryKeyFlag, ProduceInstructOrderEntity::getCustomerName, queryForm.getQueryKey())
                            .or()
                            .like(queryKeyFlag, ProduceInstructOrderEntity::getItemNumber, queryForm.getQueryKey())
                            .or()
                            .like(queryKeyFlag, ProduceInstructOrderEntity::getItemName, queryForm.getQueryKey())
                            .ne(queryKeyFlag, ProduceInstructOrderEntity::getProduceStatus, ProduceInstructOrderProduceStatusEnum.PLAN.getValue());
                }));

        //如果没有指令单则直接返回
        if (CollUtil.isEmpty(orderEntityList)) {
            Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
            return SmartPageUtil.convert2PageResult(page, Collections.emptyList());
        }


        List<Long> orderIds = orderEntityList.stream()
                .map(ProduceInstructOrderEntity::getId)
                .collect(Collectors.toList());

        Map<Long, ProduceInstructOrderEntity> orderEntityMap = orderEntityList.stream()
                .collect(Collectors.toMap(ProduceInstructOrderEntity::getId, Function.identity()));

        //查询安排信息
        Long userId = SmartRequestUtil.getRequestUserId();
        Page<ProduceInstructOrderArrangeEntity> page = new Page<>(queryForm.getPageNum(), queryForm.getPageSize());
        produceInstructOrderArrangeDao.selectPage(page, new LambdaQueryWrapper<ProduceInstructOrderArrangeEntity>()
                .eq(ProduceInstructOrderArrangeEntity::getHeadId, userId)
                .eq(queryForm.getFinishFlag() != null, ProduceInstructOrderArrangeEntity::getFinishFlag, queryForm.getFinishFlag())
                .in(CollUtil.isNotEmpty(orderIds), ProduceInstructOrderArrangeEntity::getOrderId, orderIds)
                .orderByAsc(ProduceInstructOrderArrangeEntity::getPlanEndTime));

        List<ProduceInstructOrderArrangeVO> arrangeVOS = SmartBeanUtil.copyList(page.getRecords(), ProduceInstructOrderArrangeVO.class);

        for (ProduceInstructOrderArrangeVO vo : arrangeVOS) {
            if (orderEntityMap.containsKey(vo.getOrderId())) {
                ProduceInstructOrderEntity instructOrderEntity = orderEntityMap.get(vo.getOrderId());
                vo.setInstructNumber(instructOrderEntity.getInstructNumber());
                vo.setInstructName(instructOrderEntity.getName());
                vo.setCustomerId(instructOrderEntity.getCustomerId());
                vo.setCustomerName(instructOrderEntity.getCustomerName());
                vo.setImgUrl(instructOrderEntity.getImgUrl());
                vo.setProduceStatus(instructOrderEntity.getProduceStatus());
                vo.setPriority(instructOrderEntity.getPriority());
                vo.setDeliverTime(instructOrderEntity.getDeliverTime());
                vo.setIssuedTime(instructOrderEntity.getIssuedTime());
                vo.setItemId(instructOrderEntity.getItemId());
                vo.setItemName(instructOrderEntity.getItemName());
                vo.setItemNumber(instructOrderEntity.getItemNumber());
                vo.setModel(instructOrderEntity.getModel());
            }

        }

        return SmartPageUtil.convert2PageResult(page, arrangeVOS);
    }

    /**
     * 修改任务安排状态
     *
     * @param id
     * @return
     */
    public ResponseDTO<String> updateStatus(Long id) {
        ProduceInstructOrderArrangeEntity arrange = produceInstructOrderArrangeDao.selectById(id);
        if (arrange == null) {
            return ResponseDTO.userErrorParam("任务安排不存在");
        }

        Long orderId = arrange.getOrderId();
        ProduceInstructOrderEntity instructOrder = produceInstructOrderDao.selectById(orderId);
        if (instructOrder == null) {
            return ResponseDTO.userErrorParam("指令单不存在");
        }

        if (ProduceInstructOrderProduceStatusEnum.PLAN.getValue().equals(instructOrder.getProduceStatus())) {
            return ResponseDTO.userErrorParam("指令单计划中");
        }


        if (arrange.getFinishFlag()) {
            return ResponseDTO.userErrorParam("任务已经完成");
        }
        RequestUser user = SmartRequestUtil.getRequestUser();

        ProduceInstructOrderArrangeEntity updateEntity = new ProduceInstructOrderArrangeEntity();
        updateEntity.setId(id);
        updateEntity.setFinishId(user.getUserId());
        updateEntity.setFinishName(user.getUserName());
        updateEntity.setFinishFlag(true);
        produceInstructOrderArrangeDao.updateById(updateEntity);
        //发送事件
        eventPublisher.publishEvent(new ProduceInstructOrderArrangeFinishEvent(this, id));
        return ResponseDTO.ok();
    }
}
