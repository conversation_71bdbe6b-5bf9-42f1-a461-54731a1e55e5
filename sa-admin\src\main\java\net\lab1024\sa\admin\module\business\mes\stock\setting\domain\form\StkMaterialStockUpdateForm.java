package net.lab1024.sa.admin.module.business.mes.stock.setting.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 物料库存属性 更新表单
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:18:19
 * @Copyright zscbdic
 */

@Data
public class StkMaterialStockUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

//    @Schema(description = "物料id", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "物料id 不能为空")
//    private Long materialId;

    @Schema(description = "作用范围;ALL所有仓库 ONE 单一仓库", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "作用范围;ALL所有仓库 ONE 单一仓库 不能为空")
    private String scope;

    @Schema(description = "仓库限制;0停用 1启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "仓库限制;0停用 1启用 不能为空")
    private Boolean warehouseLimit;

    @Schema(description = "仓库id;保留")
    private Long warehouseId;

    @Schema(description = "仓位限制;0停用 1启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "仓位限制;0停用 1启用 不能为空")
    private Boolean locationLimit;

    @Schema(description = "仓位id;保留")
    private Long locationId;

    @Schema(description = "最小库存")
    private BigDecimal minStock;

    @Schema(description = "最大库存")
    private BigDecimal maxStock;

    @Schema(description = "安全库存")
    private BigDecimal safeStock;

    @Schema(description = "再订货点")
    private BigDecimal reorderGood;

    @Schema(description = "启用最小库存;0停用 1启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "启用最小库存;0停用 1启用 不能为空")
    private Boolean minStockFlag;

    @Schema(description = "启用最大库存;0停用 1启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "启用最大库存;0停用 1启用 不能为空")
    private Boolean maxStockFlag;

    @Schema(description = "启用安全库存;0停用 1启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "启用安全库存;0停用 1启用 不能为空")
    private Boolean safeStockFlag;

    @Schema(description = "启用再订货点;0停用 1启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "启用再订货点;0停用 1启用 不能为空")
    private Boolean reorderGoodFlag;

    @Schema(description = "批号管理;0停用 1启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "批号管理;0停用 1启用 不能为空")
    private Boolean lotManageFlag;

    @Schema(description = "SN管理;0停用 1启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "SN管理;0停用 1启用 不能为空")
    private Boolean snManageFlag;

}
