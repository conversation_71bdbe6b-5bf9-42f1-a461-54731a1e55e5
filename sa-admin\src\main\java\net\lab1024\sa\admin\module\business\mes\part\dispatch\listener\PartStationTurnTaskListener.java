package net.lab1024.sa.admin.module.business.mes.part.dispatch.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.event.part.station.PartStationTurnTaskFinishEvent;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.constant.ActionEnum;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.entity.PartDispatchLogEntity;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.entity.PartDispatchReceiveForm;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.form.PartDispatchSendForm;
import net.lab1024.sa.admin.module.business.mes.part.dispatch.manager.PartDispatchLogManager;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxInsideDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxInsideEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.manager.PartStationTurnBoxInsideManager;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskTypeEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.entity.PartStationTurnTaskEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.manager.PartStationTurnTaskManager;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import net.lab1024.sa.base.config.AsyncConfig;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;


@Component("partDispatchPartStationTurnTaskListener")
public class PartStationTurnTaskListener {

    @Resource
    private PartStationTurnTaskManager partStationTurnTaskManager;

    @Resource
    private PartDispatchLogManager partDispatchLogManager;

    @Resource
    private PartStationTurnBoxInsideManager partStationTurnBoxInsideManager;

    /**
     * 任务完成事件，同步裁片收发记录
     * @param event
     */
    @EventListener(PartStationTurnTaskFinishEvent.class)
    @Async(value = AsyncConfig.ASYNC_EXECUTOR_THREAD_NAME)
    public void finishEvent(PartStationTurnTaskFinishEvent event) {
        //校验
        PartStationTurnTaskFinishEvent.Payload payload = event.getPayload();
        if (payload == null) {
            return;
        }
        Long taskId = payload.getTaskId();
        PartStationTurnTaskEntity task = partStationTurnTaskManager.getById(taskId);
        if (task == null) {
            return;
        }
        if (!task.getSyncPartDispatchFlag() && TurnTaskTypeEnum.TO_WORKSHOP.getValue().equals(task.getType())) {
            return;
        }
        // 获取 周转箱内菲票
        List<PartStationTurnBoxInsideEntity> insideList = partStationTurnBoxInsideManager.lambdaQuery()
                .eq(PartStationTurnBoxInsideEntity::getTurnBoxId, task.getTurnoverBoxId())
                .list();
        if(CollUtil.isEmpty(insideList)){
            return;
        }
        List<Long> ticketIds = insideList.stream()
                .map(PartStationTurnBoxInsideEntity::getFeTicketId)
                .filter(Objects::nonNull)
                .toList();
        if(CollUtil.isEmpty(ticketIds)){
            return;
        }


        // 判断任务类型
        ActionEnum actionEnum = null;
        if (TurnTaskTypeEnum.TO_WORKSHOP.getValue().equals(task.getType())) {
            actionEnum = ActionEnum.SEND;
        } else if (TurnTaskTypeEnum.TO_PART_STATION.getValue().equals(task.getType())) {
            actionEnum = ActionEnum.RECEIVE;
        }
        if (actionEnum == null) {
            return;
        }

        //下发裁片
        if (ActionEnum.SEND.equals(actionEnum)) {
            Long produceTeamId = task.getEndProduceTeamId();
            if (produceTeamId == null) {
                return;
            }
            //转化为未下发的裁片
            List<PartDispatchSendForm> sendForms = ticketIds.stream().map(id -> {
                PartDispatchSendForm form = new PartDispatchSendForm();
                form.setTicketId(id);
                form.setProduceTeamId(produceTeamId);
                try {
                    partDispatchLogManager.sendCheck(form);
                } catch (BusinessException e) {
                    return null;
                }
                return form;
            }).filter(Objects::nonNull).toList();
            //如果没有需要下发的裁片，则不执行
            if (CollUtil.isEmpty(sendForms)) {
                return;
            }
            RequestUser user = SmartRequestUtil.getRequestUser();
            List<PartDispatchLogEntity> logList = partDispatchLogManager.transformToLog(sendForms, user);
            partDispatchLogManager.saveBatch(logList);

            return;
        } else if (ActionEnum.RECEIVE.equals(actionEnum)) {
            List<PartDispatchReceiveForm> logList = ticketIds.stream().map(id -> {
                PartDispatchReceiveForm form = new PartDispatchReceiveForm();
                form.setTicketId(id);
                try {
                    partDispatchLogManager.receiveCheck(form);
                } catch (BusinessException e) {
                    return null;
                }
                return form;
            }).filter(Objects::nonNull).toList();
            if (CollUtil.isEmpty(logList)) {
                return;
            }

            RequestUser user = SmartRequestUtil.getRequestUser();
            partDispatchLogManager.receive(logList, user);
            return;
        }

        return;


    }
}
