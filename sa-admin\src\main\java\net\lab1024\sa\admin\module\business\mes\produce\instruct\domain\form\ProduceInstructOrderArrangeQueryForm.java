package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

/**
 * 指令单安排信息 分页查询表单
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:26:34
 * @Copyright zscbdic
 */

@Data
public class ProduceInstructOrderArrangeQueryForm extends PageParam{

    @Schema(description = "指令单id")
    private Long orderId;

    @Schema(description = "关键字查询")
    private String queryKey;


    /**
     * 完成标识;0未完成，1完成
     */
    @Schema(description = "完成标识;0未完成，1完成")
    private Boolean finishFlag;
}
