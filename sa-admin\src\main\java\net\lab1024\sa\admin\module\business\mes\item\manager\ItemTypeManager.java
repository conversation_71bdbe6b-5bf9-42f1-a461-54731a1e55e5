package net.lab1024.sa.admin.module.business.mes.item.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.item.dao.ItemTypeDao;
import net.lab1024.sa.admin.module.business.mes.item.domain.entity.ItemTypeEntity;
import net.lab1024.sa.admin.module.business.mes.item.domain.vo.ItemTypeTreeVO;
import net.lab1024.sa.base.common.code.UserErrorCode;
import net.lab1024.sa.base.common.constant.StringConst;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 物料分类表  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-02 09:54:23
 * @Copyright zscbdic
 */
@Service
public class ItemTypeManager extends ServiceImpl<ItemTypeDao, ItemTypeEntity> {

    @Resource
    private ItemTypeDao itemTypeDao;

    public List<ItemTypeTreeVO> queryItemTypeTree(Long parentId, String queryKey) {
        List<ItemTypeEntity> allTypeEntityList = itemTypeDao.queryByKey(queryKey, false);
        //parentId为0说明改物料类型为根
        List<ItemTypeEntity> typeEntityList = allTypeEntityList.stream().filter(e -> e.getParentId().equals(parentId)).collect(Collectors.toList());
        List<ItemTypeTreeVO> treeList = SmartBeanUtil.copyList(typeEntityList, ItemTypeTreeVO.class);
        treeList.forEach(e -> {
            e.setLabel(e.getName());
            e.setValue(e.getId());
            e.setFullName(e.getName());
        });
        // 递归设置子类
        this.queryAndSetSubType(treeList, allTypeEntityList);
        return treeList;
    }

    /**
     *
     * @param treeList  parentId为0
     * @param allTypeEntityList  未删除的所有类型
     */
    private void queryAndSetSubType(List<ItemTypeTreeVO> treeList, List<ItemTypeEntity> allTypeEntityList) {
        if (CollectionUtils.isEmpty(treeList)) {
            return;
        }

        List<Long> parentIdList = treeList.stream().map(ItemTypeTreeVO::getValue).collect(Collectors.toList());
        //获得第二层
        List<ItemTypeEntity> typeEntityList = allTypeEntityList.stream().filter(e -> parentIdList.contains(e.getParentId())).collect(Collectors.toList());
        //根据父类别id聚合成map
        Map<Long, List<ItemTypeEntity>> typeSubMap = typeEntityList.stream().collect(Collectors.groupingBy(ItemTypeEntity::getParentId));
        //
        treeList.forEach(e -> {
            List<ItemTypeEntity> childrenEntityList = typeSubMap.getOrDefault(e.getValue(), Lists.newArrayList());
            List<ItemTypeTreeVO> childrenVOList = SmartBeanUtil.copyList(childrenEntityList, ItemTypeTreeVO.class);
            childrenVOList.forEach(item -> {
                item.setLabel(item.getName());
                item.setValue(item.getId());
                item.setFullName(e.getFullName() + StringConst.SEPARATOR_SLASH + item.getName());
            });
            // 递归查询
            this.queryAndSetSubType(childrenVOList, allTypeEntityList);
            e.setChildren(childrenVOList);
        });
    }


    /**
     * 新增/更新 类目时的 校验
     *
     */
    public ResponseDTO<String> checkItemType(ItemTypeEntity itemTypeEntity, boolean isUpdate) {
        // 校验父级是否存在
        Long parentId = itemTypeEntity.getParentId();

        if (null != parentId) {
            if (Objects.equals(itemTypeEntity.getId(), parentId)) {
                return ResponseDTO.userErrorParam("父级类目与自己相同了");
            }
                if (!Objects.equals(parentId, NumberUtils.LONG_ZERO)) {
                ItemTypeEntity parentEntity = this.getById(parentId);
                Optional<ItemTypeEntity> optional = Optional.ofNullable(parentEntity);;
                if (!optional.isPresent()) {
                    return ResponseDTO.error(UserErrorCode.DATA_NOT_EXIST, "父级分类不存在~");
                }
                ItemTypeEntity parent = optional.get();
            }

        } else {
            // 如果没有父类 使用默认父类
            parentId = NumberUtils.LONG_ZERO;
        }

        // 校验同父类下 名称是否重复
        ItemTypeEntity queryEntity = new ItemTypeEntity();
        queryEntity.setParentId(parentId);
        queryEntity.setName(itemTypeEntity.getName());
        queryEntity.setDeletedFlag(false);
        queryEntity = itemTypeDao.selectOne(new QueryWrapper<>(queryEntity));
        if (null != queryEntity) {
            if (isUpdate) {
                if (!Objects.equals(queryEntity.getId(), itemTypeEntity.getId())) {
                    return ResponseDTO.userErrorParam("同级下已存在相同类目~");
                }
            } else {
                return ResponseDTO.userErrorParam("同级下已存在相同类目~");
            }
        }
        return ResponseDTO.ok();
    }

    /**
     * 查询子类ids
     * @param ids
     * @return
     */
    public List<Long> queryTypeSubId(List<Long> ids){
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        //所有子类
        List<ItemTypeEntity> categoryEntityList = Lists.newArrayList();
        ids.forEach(e -> {
            categoryEntityList.addAll(querySubType(e));
        });
        Map<Long, List<ItemTypeEntity>> subTypeMap = categoryEntityList.stream().collect(Collectors.groupingBy(ItemTypeEntity::getId));
        // 递归查询子类
        ids = subTypeMap.values().stream()
                .flatMap(Collection::stream).map(ItemTypeEntity::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        ids.addAll(this.queryTypeSubId(ids));
        return ids;
    }

    public List<ItemTypeEntity> querySubType(Long parentId){
        return itemTypeDao.queryByParentId(Lists.newArrayList(parentId), false);
    }

    /**
     *
     * @return 以物料类型id为键，名称为值的Map
     */
    public Map<Long,String> processMap(){
        return query()
                .list().stream()
                .collect(Collectors.toMap(ItemTypeEntity::getId, ItemTypeEntity::getName));
    }
}
