package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 生产指令工序信息 实体类
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:24:08
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_produce_instruct_order_process")
public class ProduceInstructOrderProcessEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 指令单id
     */
    private Long orderId;

    /**
     * 序号
     */
    private Integer serialNumber;


    private Long processId;

    /**
     * 工序名称
     */
    private String name;

    /**
     * 部位
     */
    private String position;

    /**
     * 工序类型
     */
    private String processType;

    /**
     * 标准工时;单位
     */
    private Integer standardTime;

    /**
     * 工价一
     */
    private BigDecimal unitPrice1;

    /**
     * 工价二
     */
    private BigDecimal unitPrice2;

    /**
     * 工价三
     */
    private BigDecimal unitPrice3;

    /**
     * 工价四
     */
    private BigDecimal unitPrice4;

    /**
     * 工价五
     */
    private BigDecimal unitPrice5;

    /**
     * 工价六
     */
    private BigDecimal unitPrice6;

    /**
     * 车间id
     */
    private Long workshopId;

    /**
     * 车间名称
     */
    private String workshopName;

    /**
     * 末道工序;0否 1是
     */
    private Boolean endFlag;

    /**
     * 是否审核;0不审核 1审核
     */
    private Boolean auditFlag;

    /**
     * 是否超产;0否 1是
     */
    private Boolean overflowWorkFlag;

    /**
     * 工序控制;0自制 1委外 2不限
     */
    private String processControl;

    /**
     * sopId;保留
     */
    private Long sopId;

    /**
     * 应生产数量
     */
    private Integer shouldNum;

    /**
     * 已生产数量
     */
    private Integer finishNum;

}
