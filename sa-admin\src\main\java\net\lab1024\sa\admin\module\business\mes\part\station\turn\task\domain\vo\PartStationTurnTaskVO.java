package net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

/**
 * 驿站任务表 列表VO
 *
 * <AUTHOR>
 * @Date 2025-03-14 20:13:46
 * @Copyright zscbdic
 */

@Data
public class PartStationTurnTaskVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Integer deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "任务作用范围;human人")
    private String taskScope;

    @Schema(description = "任务编号")
    private String number;

    @Schema(description = "任务名称")
    private String name;

    @Schema(description = "任务优先级")
    private String priority;

    @Schema(description = "任务类型")
    private String type;

    @Schema(description = "任务状态")
    private String status;

    @Schema(description = "是否自动派发 0:否 1:是")
    private Boolean autoDispatchFlag;

    @Schema(description = "起点货位ID")
    private Long startLocationId;

    @Schema(description = "起点货位编号")
    private String startLocationNumber;

    @Schema(description = "起点货位描述")
    private String startLocationDesc;

    @Schema(description = "终点货位ID")
    private Long endLocationId;

    @Schema(description = "终点货位编号")
    private String endLocationNumber;

    @Schema(description = "终点货位描述")
    private String endLocationDesc;

    @Schema(description = "生产小组ID")
    private Long endProduceTeamId;

    @Schema(description = "生产小组名称")
    private String endProduceTeamName;

    @Schema(description = "菲票数组")
    private String ticketIds;

    /**
     * 菲票数量
     */
    @Schema(description = "菲票数量")
    private Integer ticketQty;

    /**
     * 裁片数量
     */
    @Schema(description = "裁片数量")
    private Integer partQty;

    @Schema(description = "周转箱ID")
    private Long turnoverBoxId;

    @Schema(description = "提交时间")
    private LocalDateTime submitTime;

    @Schema(description = "领取时间")
    private LocalDateTime getTime;

    @Schema(description = "开始时间")
    private LocalDateTime beginTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "提交人类型")
    private String submitterType;

    @Schema(description = "提交人ID")
    private String submitterId;

    @Schema(description = "提交人名称")
    private String submitterName;

    @Schema(description = "执行人类型")
    private String executorType;

    @Schema(description = "执行人ID")
    private String executorId;

    @Schema(description = "执行人名称")
    private String executorName;

    @Schema(description = "关联单据类型")
    private String bizType;

    @Schema(description = "关联单据ID")
    private Long bizId;

    @Schema(description = "关联单据详情序号")
    private Integer bizDetailSeq;

    @Schema(description = "关联单据详情ID")
    private Long bizDetailId;

    /**
     * 是否同步裁片收发记录
     */
    @Schema(description = "是否同步裁片收发记录")
    private Boolean syncPartDispatchFlag;

}
