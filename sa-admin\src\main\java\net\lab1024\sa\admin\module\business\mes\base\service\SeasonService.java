package net.lab1024.sa.admin.module.business.mes.base.service;

import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.base.dao.SeasonDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.SeasonEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SeasonAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SeasonQueryTreeForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SeasonUpdateForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.SeasonTreeVO;
import net.lab1024.sa.admin.module.business.mes.base.manager.SeasonManager;
import net.lab1024.sa.base.common.code.UserErrorCode;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 季度表 Service
 *
 * <AUTHOR>
 * @Date 2024-07-05 15:45:34
 * @Copyright zscbdic
 */

@Service
public class SeasonService {

    @Resource
    private SeasonDao seasonDao;

    @Resource
    private SeasonManager seasonManager;

    /**
     * 添加
     */
    public ResponseDTO<String> add(SeasonAddForm addForm) {
        SeasonEntity seasonEntity = SmartBeanUtil.copy(addForm, SeasonEntity.class);
        //进入校验
        ResponseDTO<String> res = seasonManager.checkSeasonType(seasonEntity, false);

        if (!res.getOk()) {
            return res;
        }
        // 没有父类则使用默认父类
        Long parentId = addForm.getParentId()==null?NumberUtils.LONG_ZERO: addForm.getParentId();
        seasonEntity.setParentId(parentId);
        seasonEntity.setDeletedFlag(false);
        seasonEntity.setSeasonName(addForm.getSeasonName());
        seasonDao.insert(seasonEntity);

        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(SeasonUpdateForm updateForm) {
        // 校验类目是否存在
        Long id = updateForm.getId();
        SeasonEntity originEntity = seasonManager.getById(id);
        if (originEntity==null) {
            return ResponseDTO.error(UserErrorCode.DATA_NOT_EXIST);
        }
        //前端没有传回parentID，需要自行设置
        SeasonEntity seasonEntity = SmartBeanUtil.copy(updateForm, SeasonEntity.class);
        seasonEntity.setParentId(originEntity.getParentId());
        ResponseDTO<String> responseDTO = seasonManager .checkSeasonType(seasonEntity, true);
        if (!responseDTO.getOk()) {
            return responseDTO;
        }
        seasonDao.updateById(seasonEntity);
        return ResponseDTO.ok();
    }


    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        SeasonEntity itemTypeEntity = seasonDao.selectById(id);
        if (itemTypeEntity==null) {
            return ResponseDTO.error(UserErrorCode.DATA_NOT_EXIST);
        }
        List<Long> subIds = seasonManager.queryTypeSubId(Lists.newArrayList(id));

        if (CollectionUtils.isNotEmpty(subIds)) {
            return ResponseDTO.userErrorParam("请先删除子级类目");
        }
        seasonDao.updateDeleted(id,true);
        return ResponseDTO.ok();
    }

    /**
     * 树形查询
     * @param queryForm 包含各季度名称和父id
     * @return
     */
    public ResponseDTO<List<SeasonTreeVO>> queryTree(SeasonQueryTreeForm queryForm) {
        if (null == queryForm.getParentId()) {
            queryForm.setParentId(NumberUtils.LONG_ZERO);
        }
        List<SeasonTreeVO> seasonTreeVO = seasonManager.querySeasonTree(queryForm.getParentId(), queryForm.getQueryKey());
        return ResponseDTO.ok(seasonTreeVO);
    }
}
