package net.lab1024.sa.admin.module.business.mes.equip.equipment.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.dao.EquipmentDao;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.dao.EquipmentScadaDao;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.bo.EquipmentBO;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.entity.EquipmentEntity;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.entity.EquipmentScadaEntity;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentAddForm;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.form.EquipmentUpdateForm;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;

/**
 * 设备  Manager
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:12:13
 * @Copyright zscbdic
 */
@Service
public class EquipmentManager extends ServiceImpl<EquipmentDao, EquipmentEntity> {

    @Resource
    private EquipmentScadaDao equipmentScadaDao;

    /**
     * 添加校验
     *
     * @param addForm
     */
    public void addCheck(EquipmentAddForm addForm) {
        String number = addForm.getNumber();
        long count = this.count(new LambdaQueryWrapper<EquipmentEntity>()
                .eq(EquipmentEntity::getNumber, number));
        if (count > 0) {
            throw new BusinessException("设备编号已存在");
        }
    }

    /**
     * 更新校验
     *
     * @param updateForm
     */
    public void updateCheck(EquipmentUpdateForm updateForm) {
        String number = updateForm.getNumber();
        long count = this.count(new LambdaQueryWrapper<EquipmentEntity>()
                .eq(EquipmentEntity::getNumber, number)
                .ne(EquipmentEntity::getId, updateForm.getId()));
        if (count > 0) {
            throw new BusinessException("设备编号已存在");
        }
    }

    /**
     * 保存设备信息
     *
     * @param equipmentBO
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveEquipment(EquipmentBO equipmentBO) {
        EquipmentScadaEntity equipmentScada = equipmentBO.getEquipmentScada();
        EquipmentEntity equipment = equipmentBO.getEquipment();

        this.save(equipment);
        equipmentScada.setEquipmentId(equipment.getId());
        equipmentScada.setId(null);
        equipmentScada.setEquipmentId(equipment.getId());
        equipmentScadaDao.insert(equipmentScada);
    }

    /**
     * 更新设备信息
     *
     * @param equipmentBO
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateEquipment(EquipmentBO equipmentBO) {
        EquipmentScadaEntity equipmentScada = equipmentBO.getEquipmentScada();
        EquipmentEntity equipment = equipmentBO.getEquipment();
        this.updateById(equipment);

        equipmentScada.setId(null);
        // 针对scada的2个下拉字段特定更新
        equipmentScadaDao.update(null,new LambdaUpdateWrapper<EquipmentScadaEntity>()
                .eq(EquipmentScadaEntity::getEquipmentId, equipment.getId())
                .set(EquipmentScadaEntity::getIotNetworkPlatform, equipmentScada.getIotNetworkPlatform())
                .set(EquipmentScadaEntity::getIotEquipmentType, equipmentScada.getIotEquipmentType()));
        equipmentScadaDao.update(equipmentScada, new LambdaQueryWrapper<EquipmentScadaEntity>()
                .eq(EquipmentScadaEntity::getEquipmentId, equipment.getId()));
    }

    /**
     * 删除设备信息
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteEquipment(Long id) {
        this.removeById(id);
        equipmentScadaDao.delete(new LambdaQueryWrapper<EquipmentScadaEntity>()
                .eq(EquipmentScadaEntity::getEquipmentId, id));
    }
}
