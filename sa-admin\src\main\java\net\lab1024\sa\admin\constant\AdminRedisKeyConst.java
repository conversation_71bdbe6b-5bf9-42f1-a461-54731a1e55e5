package net.lab1024.sa.admin.constant;

import net.lab1024.sa.base.constant.RedisKeyConst;

/**
 * redis key 常量类
 *
 * <AUTHOR>
 * @Date 2022-01-07 18:59:22
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright  <a href="https://1024lab.net">1024创新实验室</a>
 */
public class AdminRedisKeyConst extends RedisKeyConst {


    /**
     * 生产指令单锁
     */
    public static final String PRODUCE_INSTRUCT_ORDER_LOCK_KEY = "mes:lock:produce:instruct:order:";

    public static final String EMPLOYEE_APPLY_LOCK_KEY = "mes:lock:employee:apply:";
}
