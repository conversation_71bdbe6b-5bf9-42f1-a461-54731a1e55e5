package net.lab1024.sa.admin.module.business.mes.base.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 款式颜色表 新建表单
 *
 * <AUTHOR>
 * @Date 2024-11-03 20:49:04
 * @Copyright zscbdic
 */

@Data
public class StyleColorAddForm {

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "款式颜色", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "款式颜色 不能为空")
    private String styleColor;

}
