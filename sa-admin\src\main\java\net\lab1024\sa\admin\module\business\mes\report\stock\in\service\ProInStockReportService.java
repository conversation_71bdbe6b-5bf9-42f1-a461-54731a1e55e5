package net.lab1024.sa.admin.module.business.mes.report.stock.in.service;

import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.report.stock.in.domain.form.ProInStockReportQueryForm;
import net.lab1024.sa.admin.module.business.mes.report.stock.in.domain.vo.ProInStockReportVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

@Service
public class ProInStockReportService {

    /**
     * 生产入库汇总分页查询
     *
     * @param queryForm
     * @return
     */
    public ResponseDTO<PageResult<ProInStockReportVO>> querySummaryPage(ProInStockReportQueryForm queryForm) {
        return null;
    }
}
