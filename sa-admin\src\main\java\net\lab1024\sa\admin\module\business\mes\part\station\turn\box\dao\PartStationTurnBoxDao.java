package net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.form.PartStationTurnBoxQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.vo.PartStationTurnBoxVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 裁片周转箱 Dao
 *
 * <AUTHOR>
 * @Date 2025-03-14 11:36:51
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface PartStationTurnBoxDao extends BaseMapper<PartStationTurnBoxEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<PartStationTurnBoxVO> queryPage(Page page, @Param("queryForm") PartStationTurnBoxQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
