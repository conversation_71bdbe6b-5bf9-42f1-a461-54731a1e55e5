package net.lab1024.sa.admin.module.business.mes.process.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 工序库 更新表单
 *
 * <AUTHOR>
 * @Date 2024-07-13 15:19:57
 * @Copyright zscbdic
 */

@Data
public class ProcessLibraryUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "工艺库编号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "工艺库编号码 不能为空")
    private String processLibraryNumber;

    @Schema(description = "停用标识;0启用，1停用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "停用标识;0启用，1停用 不能为空")
    private Boolean enableFlag;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "名称 不能为空")
    private String name;

    @Schema(description = "详细工序更新")
    @Valid
    private List<ProcessLibraryDetailsAddForm> processLibraryDetailsAddFormList;

}
