package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 生产指令单 实体类
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:22:24
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_produce_instruct_order")
public class ProduceInstructOrderEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 单据编号
     */
    private String instructNumber;

    /**
     * 指令单名称
     */
    private String name;

    /**
     * 生产数量
     */
    private Integer produceNum;

    /**
     * 完成数量
     */
    private Integer finishNum;

    /**
     * 生产类型;0自产,1自裁委外，2整件委外
     */
    private String produceType;

    /**
     * 单据来源;0直接下达
     */
    private String orderOrigin;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 销售单号
     */
    private String salesOrderNumber;

    /**
     * 图片地址
     */
    private String imgUrl;

    /**
     * 计划开工日期
     */
    private LocalDate planStartTime;

    /**
     * 计划完工日期
     */
    private LocalDate planFinishTime;

    /**
     * 实际开工日期
     */
    private LocalDateTime realStartTime;

    /**
     * 实际完工日期
     */
    private LocalDateTime realFinishTime;

    /**
     * 生产业务状态;0计划，1下达，2开工，3完工
     */
    private String produceStatus;

    /**
     * 优先级;0一般,1紧急,2非常紧急
     */
    private String priority;

    /**
     * 交货日期
     */
    private LocalDate deliverTime;

    /**
     * 下达日期
     */
    private LocalDateTime issuedTime;

    /**
     * 物料id
     */
    private Long itemId;

    /**
     * 物料编号
     */
    private String itemNumber;

    /**
     * 规格型号
     */
    private String model;

    /**
     * 物料名称
     */
    private String itemName;

    /**
     * 物料类型;0半成品 1成品
     */
    private String category;

    /**
     * 物料属性;0面料，1其他，2成衣
     */
    private String attribute;

    /**
     * 单位id
     */
    private Long unitId;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 下达人
     */
    private Long issuerId;

    /**
     * 下达人名称
     */
    private String issuerName;

}
