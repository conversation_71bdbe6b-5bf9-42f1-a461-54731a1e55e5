package net.lab1024.sa.admin.module.business.mes.base.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SupplierAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SupplierQuery;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SupplierQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.SupplierUpdateForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.SupplierVO;
import net.lab1024.sa.admin.module.business.mes.base.service.SupplierService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商表 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-03 09:43:14
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class SupplierController {

    @Resource
    private SupplierService supplierService;

    /**
     * 查询所有
     * @param queryForm
     * @return
     */
    @PostMapping("/supplier/all")
    public ResponseDTO<List<SupplierVO>> all(@RequestBody @Valid SupplierQuery queryForm) {
        return ResponseDTO.ok(supplierService.queryAll(queryForm));
    }

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/supplier/queryPage")
    public ResponseDTO<PageResult<SupplierVO>> queryPage(@RequestBody @Valid SupplierQueryForm queryForm) {
        return ResponseDTO.ok(supplierService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/supplier/add")
    public ResponseDTO<String> add(@RequestBody @Valid SupplierAddForm addForm) {
        return supplierService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/supplier/update")
    public ResponseDTO<String> update(@RequestBody @Valid SupplierUpdateForm updateForm) {
        return supplierService.update(updateForm);
    }


    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/supplier/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return supplierService.delete(id);
    }
}
