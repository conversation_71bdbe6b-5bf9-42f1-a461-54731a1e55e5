package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 指令单安排信息 新建表单
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:26:34
 * @Copyright zscbdic
 */

@Data
public class ProduceInstructOrderArrangeAddForm {

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "序号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "序号 不能为空")
    @Min(value = 1, message = "序号不能小于1")
    private Integer serialNumber;

    @Schema(description = "节点名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "节点名称 不能为空")
    private String nodeName;

    @Schema(description = "负责人id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "负责人id 不能为空")
    private Long headId;

    /**
     * 计划开始时间 yyyy-MM-dd
     */
    @Schema(description = "计划开始时间")
    private LocalDate planBeginTime;

    /**
     * 计划结束时间 yyyy-MM-dd
     */
    @Schema(description = "计划结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "计划结束时间 不能为空")
    private LocalDate planEndTime;

}
