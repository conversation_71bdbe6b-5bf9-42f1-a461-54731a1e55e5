package net.lab1024.sa.admin.module.business.mes.produce.follow.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 生产跟单 实体类
 *
 * <AUTHOR>
 * @Date 2024-07-26 21:36:36
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_produce_instruct_follow")
public class ProduceInstructFollowEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic(value="0",delval="1")
    private Integer deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 跟单员id
     */
    private Long employeeId;

    /**
     * 指令单id
     */
    private Long instructOrderId;

    /**
     * 委派人id
     */
    private Long delegateId;

}
