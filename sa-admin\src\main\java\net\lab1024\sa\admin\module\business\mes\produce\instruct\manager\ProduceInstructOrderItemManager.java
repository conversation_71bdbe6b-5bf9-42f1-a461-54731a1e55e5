package net.lab1024.sa.admin.module.business.mes.produce.instruct.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.base.dao.UnitDao;
import net.lab1024.sa.admin.module.business.mes.item.dao.ItemDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderItemDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderItemEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderItemAddForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderItemUpdateForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderItemVO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 指令单用料信息  Manager
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:24:54
 * @Copyright zscbdic
 */
@Service
public class ProduceInstructOrderItemManager extends ServiceImpl<ProduceInstructOrderItemDao, ProduceInstructOrderItemEntity> {

    @Resource
    private UnitDao unitDao;

    @Resource
    private ItemDao itemDao;

    /**
     * 解析新增指令单用料信息
     *
     * @param itemList
     * @return
     */
    public List<ProduceInstructOrderItemEntity> parseAddForm(List<ProduceInstructOrderItemAddForm> itemList) {
        if (CollUtil.isEmpty(itemList)) {
            return Collections.emptyList();
        }

        //将相同物料信息进行合并
        return itemList.stream()
                .collect(Collectors.toMap(
                        ProduceInstructOrderItemAddForm::getItemId,
                        item -> item,
                        (existing, replacement) -> {
                            existing.setItemNum(existing.getItemNum() + replacement.getItemNum());
                            existing.setTotalDosage(existing.getTotalDosage() + replacement.getTotalDosage());
                            return existing;
                        }
                ))
                .values().stream().map(e -> SmartBeanUtil.copy(e, ProduceInstructOrderItemEntity.class)).collect(Collectors.toList());

    }

    /**
     * 解析更新指令单用料信息
     *
     * @param itemList
     * @return
     */
    public List<ProduceInstructOrderItemEntity> parseUpdateForm(List<ProduceInstructOrderItemUpdateForm> itemList) {
        if (CollUtil.isEmpty(itemList)) {
            return Collections.emptyList();
        }


        return itemList.stream()
                .collect(Collectors.toMap(
                        ProduceInstructOrderItemUpdateForm::getItemId,
                        item -> item,
                        (existing, replacement) -> {
                            existing.setItemNum(existing.getItemNum() + replacement.getItemNum());
                            existing.setTotalDosage(existing.getTotalDosage() + replacement.getTotalDosage());
                            return existing;
                        }
                ))
                .values().stream().map(e -> SmartBeanUtil.copy(e, ProduceInstructOrderItemEntity.class)).collect(Collectors.toList());
    }

    /**
     * 获取指令单用料信息
     *
     * @param orderId
     * @return
     */
    public List<ProduceInstructOrderItemVO> getItemInfo(Long orderId) {
        // 获取指令单用料信息
        List<ProduceInstructOrderItemEntity> itemEntityList = this.lambdaQuery().eq(ProduceInstructOrderItemEntity::getOrderId, orderId).list();
        if (CollUtil.isEmpty(itemEntityList)) {
            return Collections.emptyList();
        }

        // 获取物料信息
//        List<Long> itemIds = itemEntityList.stream().map(ProduceInstructOrderItemEntity::getItemId).distinct().collect(Collectors.toList());
//        List<ItemEntity> newItemEntityList = itemDao.selectList(new LambdaQueryWrapper<ItemEntity>().in(ItemEntity::getId, itemIds));
//        Map<Long, ItemEntity> itemMap;
//        if(CollUtil.isNotEmpty(newItemEntityList)){
//            itemMap = newItemEntityList.stream().collect(Collectors.toMap(ItemEntity::getId, e -> e));
//        } else {
//            itemMap = null;
//        }
        // 获取单位信息
//        List<Long> unitIds = itemEntityList.stream().map(ProduceInstructOrderItemEntity::getItemUnitId).distinct().collect(Collectors.toList());
//        List<UnitEntity> newUnitEntityList = unitDao.selectList(new LambdaQueryWrapper<UnitEntity>().in(UnitEntity::getId, unitIds));
//        Map<Long, UnitEntity> unitMap;
//        if(CollUtil.isNotEmpty(newUnitEntityList)){
//            unitMap = newUnitEntityList.stream().collect(Collectors.toMap(UnitEntity::getId, e -> e));
//        } else {
//            unitMap = null;
//        }

        // 封装VO
//        List<ProduceInstructOrderItemVO> vos = itemEntityList.stream().map(e -> {
//            ProduceInstructOrderItemVO itemVO = SmartBeanUtil.copy(e, ProduceInstructOrderItemVO.class);
//            // 物料信息
//            if (itemMap != null && itemMap.containsKey(e.getItemId())) {
//                itemVO.setItemAttribute(itemMap.get(e.getItemId()).getAttribute());
//                itemVO.setItemCategory(itemMap.get(e.getItemId()).getCategory());
//                itemVO.setItemModel(itemMap.get(e.getItemId()).getModel());
//                itemVO.setItemName(itemMap.get(e.getItemId()).getName());
//                itemVO.setItemNumber(itemMap.get(e.getItemId()).getNumber());
//            }
//            // 单位信息
//            if (unitMap != null && unitMap.containsKey(e.getItemUnitId())) {
//                itemVO.setItemUnitName(unitMap.get(e.getItemUnitId()).getName());
//            }
//            return itemVO;
//        }).collect(Collectors.toList());
        return SmartBeanUtil.copyList(itemEntityList, ProduceInstructOrderItemVO.class);
    }
}
