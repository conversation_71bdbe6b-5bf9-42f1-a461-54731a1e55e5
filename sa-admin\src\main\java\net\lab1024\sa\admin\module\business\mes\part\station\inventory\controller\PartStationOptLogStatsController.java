package net.lab1024.sa.admin.module.business.mes.part.station.inventory.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import net.lab1024.sa.admin.module.business.mes.common.vo.XyChartVO;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.constant.PartStationInventoryOptTypeEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.service.PartStationOptLogStatsService;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 裁片驿站操作日志统计 Controller
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:47:06
 * @Copyright zscbdic
 */
@RestController
@Tag(name = "")
public class PartStationOptLogStatsController {

    @Resource
    private PartStationOptLogStatsService partStationOptLogStatsService;

    /**
     * 出入库比率
     *
     * @param warehouseId 仓库ID 选填
     * @param beginTime   开始时间 yyyy-MM-dd
     * @param endTime     结束时间 yyyy-MM-dd
     * @return
     */
    @Operation(summary = "出入库比率")
    @GetMapping("/partStationOptLog/stats/inOutRatio")
    public ResponseDTO<Double> inOutRatio(@RequestParam(required = false, value = "warehouseId") Long warehouseId,
                                          @RequestParam(value = "beginTime") @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginTime,
                                          @RequestParam(value = "endTime") @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime) {
        return partStationOptLogStatsService.inOutRatio(warehouseId, beginTime, endTime);

    }


    /**
     * 出入库次数趋势 <AUTHOR>
     *
     * @param warehouseId 仓库ID 选填则统计该仓库，不填则统计所有仓库
     * @param beginTime   开始时间 (yyyy-MM-dd)
     * @param endTime     结束时间 (yyyy-MM-dd)
     * @return
     */
    @Operation(summary = "出入库次数趋势")
    @GetMapping("/partStationOptLog/stats/inOutCountTrend")
    public ResponseDTO<Map<String, List<XyChartVO<String, Long>>>> inventoryTrend(@RequestParam(required = false, value = "warehouseId") Long warehouseId,
                                                                                  @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginTime,
                                                                                  @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime) {

        return partStationOptLogStatsService.inOutCountTrend(warehouseId, beginTime, endTime);
    }

    /**
     * 出入库数量趋势 <AUTHOR>
     * @param warehouseId 选填则统计该仓库，不填则统计所有仓库
     * @param beginTime   开始时间 (yyyy-MM-dd)
     * @param endTime     结束时间 (yyyy-MM-dd)
     * @return
     */
    @Operation(summary = "出入库数量趋势")
    @GetMapping("/partStationOptLog/stats/inOutNumTrend")
    public ResponseDTO<Map<String, List<XyChartVO<String, Long>>>> inOutNumTrend(@RequestParam(required = false, value = "warehouseId") Long warehouseId,
                                                                                 @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginTime,
                                                                                 @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime) {
        return partStationOptLogStatsService.inOutNumTrend(warehouseId, beginTime, endTime);
    }

    /**
     * 入库的次数和件数 <AUTHOR>
     *
     * @param warehouseId 选填则统计该仓库，不填则统计所有仓库
     * @param beginTime   开始时间 (yyyy-MM-dd)
     * @param endTime     结束时间 (yyyy-MM-dd)
     * @return 次数和件数
     */
    @Operation(summary = "入库数量和次数")
    @GetMapping("/partStationOptLog/stats/inNum")
    public ResponseDTO<Map<String, Integer>> numOfIn(@RequestParam(required = false, value = "warehouseId") Long warehouseId,
                                                     @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginTime,
                                                     @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime) {
        return partStationOptLogStatsService.getNumByOptType(warehouseId, beginTime, endTime, PartStationInventoryOptTypeEnum.IN);
    }

    /**
     * 出库的次数和件数 <AUTHOR>
     *
     * @param warehouseId 选填则统计该仓库，不填则统计所有仓库
     * @param beginTime   开始时间 (yyyy-MM-dd)
     * @param endTime     结束时间 (yyyy-MM-dd)
     * @return 次数和件数
     */
    @Operation(summary = "出库数量和次数")
    @GetMapping("/partStationOptLog/stats/outNum")
    public ResponseDTO<Map<String, Integer>> numOfOut(@RequestParam(required = false, value = "warehouseId") Long warehouseId,
                                                      @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginTime,
                                                      @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime) {
        return partStationOptLogStatsService.getNumByOptType(warehouseId, beginTime, endTime, PartStationInventoryOptTypeEnum.OUT);
    }
}
