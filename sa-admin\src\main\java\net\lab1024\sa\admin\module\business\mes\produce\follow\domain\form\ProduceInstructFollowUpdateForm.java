package net.lab1024.sa.admin.module.business.mes.produce.follow.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 生产跟单 更新表单
 *
 * <AUTHOR>
 * @Date 2024-07-26 21:36:36
 * @Copyright zscbdic
 */

@Data
public class ProduceInstructFollowUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "主键 不能为空")
    private Long id;

    @Schema(description = "跟单员id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "跟单员id 不能为空")
    private List<Long> employeeId;

    @Schema(description = "指令单id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "指令单id 不能为空")
    private Long instructOrderId;

    @Schema(description = "委派人id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "委派人id 不能为空")
    private Long delegateId;

}
