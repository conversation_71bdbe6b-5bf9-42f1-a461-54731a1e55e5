package net.lab1024.sa.admin.module.business.mes.part.station.inventory.listener;

import cn.hutool.core.collection.CollUtil;
import net.lab1024.sa.admin.event.part.station.PartStationInventoryOptEvent;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.manager.PartStationOptLogManager;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import net.lab1024.sa.base.config.AsyncConfig;

import jakarta.annotation.Resource;

@Component
public class PartStationOptLogListener {


    @Resource
    private PartStationOptLogManager partStationOptLogManager;

    @Async(value = AsyncConfig.ASYNC_EXECUTOR_THREAD_NAME)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = PartStationInventoryOptEvent.class)
    public void afterCommit(PartStationInventoryOptEvent event) {
        RequestUser user = SmartRequestUtil.getRequestUser();
        if (user == null) {
            return;
        }
        Long userId = user.getUserId();
        String userName = user.getUserName();

        if (event.getFeTicketId() != null) {
            partStationOptLogManager.insertLog(
                    event.getFeTicketId(),
                    event.getBinId(),
                    userName,
                    userId,
                    event.getOptDesc(),
                    event.getOptType());
        } else if (CollUtil.isNotEmpty(event.getFeTicketIds())) {
            for (Long feTicketId : event.getFeTicketIds()) {
                partStationOptLogManager.insertLog(
                        feTicketId,
                        event.getBinId(),
                        userName,
                        userId,
                        event.getOptDesc(),
                        event.getOptType());
            }
        }


    }
}
