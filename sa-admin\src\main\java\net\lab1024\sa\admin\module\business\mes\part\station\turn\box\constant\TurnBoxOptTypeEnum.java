package net.lab1024.sa.admin.module.business.mes.part.station.turn.box.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

@Getter
@AllArgsConstructor
public enum TurnBoxOptTypeEnum implements BaseEnum {


    IN("IN","入箱"),
    OUT("OUT","出库"),
    MOVE("MOVE","移库"),

    ;
    private final String value;
    private final String desc;

}
