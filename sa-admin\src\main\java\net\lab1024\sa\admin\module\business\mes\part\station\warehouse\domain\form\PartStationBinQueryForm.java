package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form;

import net.lab1024.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 裁片货位 分页查询表单
 *
 * <AUTHOR>
 * @Date 2024-10-06 20:17:14
 * @Copyright zscbdic
 */

@Data
public class PartStationBinQueryForm extends PageParam{

    @Schema(description = "所属货架id")
    private Long rackId;

    @Schema(description = "编号查询")
    private String queryKey;

}
