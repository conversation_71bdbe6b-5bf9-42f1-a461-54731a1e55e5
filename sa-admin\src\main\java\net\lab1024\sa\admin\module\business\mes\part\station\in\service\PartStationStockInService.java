package net.lab1024.sa.admin.module.business.mes.part.station.in.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.lab1024.sa.admin.event.tailor.FeTicketTraceLogEvent;
import net.lab1024.sa.admin.module.business.mes.part.station.in.domain.form.PartStationStockInByBoxForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxInsideDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxInsideEntity;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.constant.FeTicketTraceLogTemplateEnum;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationBinDao;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.dao.PartStationInventoryDao;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationBinEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.entity.PartStationInventoryEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.in.domain.form.PartStationStockInByTicketForm;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.manager.PartStationInventoryManager;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.dao.FeTicketDao;
import net.lab1024.sa.admin.module.business.mes.tailor.ticket.domain.entity.FeTicketEntity;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;

import java.util.List;

@Service
public class PartStationStockInService {

    @Resource
    private FeTicketDao feTicketDao;

    @Resource
    private PartStationTurnBoxDao partStationTurnBoxDao;

    @Resource
    private PartStationTurnBoxInsideDao partStationTurnBoxInsideDao;

    @Resource
    private PartStationBinDao partStationBinDao;

    @Resource
    private PartStationInventoryManager partStationInventoryManager;

    @Resource
    private PartStationInventoryDao partStationInventoryDao;

    @Resource
    private ApplicationEventPublisher eventPublisher;


    /**
     * 入库
     *
     * @param form
     * @return
     */
    public ResponseDTO<String> stockIn(PartStationStockInByTicketForm form) {
        Long binId = form.getBinId();
        Long feTicketId = form.getFeTicketId();

        FeTicketEntity ticket = feTicketDao.selectById(feTicketId);
        if (ticket == null) {
            return ResponseDTO.userErrorParam("菲票不存在");
        }

        PartStationBinEntity bin = partStationBinDao.selectById(binId);
        if (bin == null) {
            return ResponseDTO.userErrorParam("库位不存在");
        }

        Long count = partStationInventoryDao.selectCount(new LambdaQueryWrapper<PartStationInventoryEntity>()
                .eq(PartStationInventoryEntity::getFeTicketId, feTicketId));
        if (count > 0) {
            return ResponseDTO.userErrorParam("该菲票已入库");
        }

        String optDesc = String.format("裁片已放入 库位【%s】", bin.getBinCode());
        partStationInventoryManager.sockIn(feTicketId, binId, optDesc);

        return ResponseDTO.ok("入库成功");
    }

    /**
     * 入库
     *
     * @param form
     * @return
     */
    public ResponseDTO<String> stockInByBox(PartStationStockInByBoxForm form) {
        Long binId = form.getBinId();
        Long turnBoxId = form.getTurnBoxId();

        PartStationBinEntity bin = partStationBinDao.selectById(binId);
        if (bin == null) {
            return ResponseDTO.userErrorParam("库位不存在");
        }
        PartStationTurnBoxEntity box = partStationTurnBoxDao.selectById(turnBoxId);
        if (box == null) {
            return ResponseDTO.userErrorParam("周转箱不存在");
        }
        if (box.getInsideFlag()) {
            return ResponseDTO.userErrorParam("该周转箱已入库");
        }

        List<Long> ticketIds = partStationTurnBoxInsideDao.selectList(new LambdaQueryWrapper<PartStationTurnBoxInsideEntity>()
                        .eq(PartStationTurnBoxInsideEntity::getTurnBoxId, turnBoxId))
                .stream()
                .map(PartStationTurnBoxInsideEntity::getFeTicketId)
                .toList();
        if (CollUtil.isNotEmpty(ticketIds)) {
            Long count = partStationInventoryDao.selectCount(new LambdaQueryWrapper<PartStationInventoryEntity>()
                    .in(PartStationInventoryEntity::getFeTicketId, ticketIds));
            if (count > 0) {
                return ResponseDTO.userErrorParam("存在入库菲票");
            }
        }
        String optDesc = String.format("裁片已放入 库位【%s】", bin.getBinCode());

        partStationInventoryManager.stockInByBox(ticketIds, turnBoxId, binId, optDesc);

        return ResponseDTO.ok("入库成功");

    }
}
