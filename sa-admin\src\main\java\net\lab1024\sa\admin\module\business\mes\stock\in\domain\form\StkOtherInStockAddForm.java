package net.lab1024.sa.admin.module.business.mes.stock.in.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockBillStatusEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 其他入库单
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StkOtherInStockAddForm extends StkInStockAddForm{

    /**
     * 单据来源类型
     */
    @Schema(description = "单据来源类型")
    private String originType;

    /**
     * 单据来源ID
     */
    @Schema(description = "单据来源ID")
    private Long originId;

    /**
     * 单据来源编号
     */
    @Schema(description = "单据来源编号")
    private String originNumber;

    /**
     * 单据类型
     */
    @Schema(description = "单据类型", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "单据类型 不能为空")
//    @CheckEnum(value = BillType.class,required = true,message = "单据类型错误")
    private String type;

    /**
     * 单据方式
     */
    @Schema(description = "单据方式")
    private String way;

    /**
     * 单据状态
     */
    @Schema(description = "单据状态", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank(message = "单据状态; 不能为空")
    @CheckEnum(value = StockBillStatusEnum.class,required = true,message = "单据状态错误")
    private String status;

    @Valid
    @NotEmpty(message = "入库单详情 不能为空")
    private List<DetailAddForm> details;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DetailAddForm extends StkInStockDetailAddForm{

        @Schema(description = "单据来源详情类型")
        private String originDetailType;

        @Schema(description = "单据来源详情ID")
        private Long originDetailId;

        @Schema(description = "单据来源详情行号")
        private Integer originDetailSeq;
    }
}
