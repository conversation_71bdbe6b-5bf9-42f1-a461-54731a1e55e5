package net.lab1024.sa.admin.module.business.mes.salary.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.salary.domain.entity.SettlementRecordEntity;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.SettlementRecordQueryForm;
import net.lab1024.sa.admin.module.business.mes.salary.domain.vo.SettlementRecordVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 薪酬结算记录 Dao
 *
 * <AUTHOR>
 * @Date 2024-11-20 20:48:59
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface SettlementRecordDao extends BaseMapper<SettlementRecordEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<SettlementRecordVO> queryPage(Page page, @Param("queryForm") SettlementRecordQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
