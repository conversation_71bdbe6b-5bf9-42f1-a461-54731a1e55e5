package net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 货位 实体类
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:14:20
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_stk_location")
public class StkLocationEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 货架id
     */
    private Long rackId;

    /**
     * 货位编号
     */
    private String number;

    /**
     * 拣货优先级;拣货优先级 保留
     */
    private Integer pickPriority;

}
