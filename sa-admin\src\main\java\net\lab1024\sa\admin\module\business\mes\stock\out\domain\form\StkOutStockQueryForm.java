package net.lab1024.sa.admin.module.business.mes.stock.out.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

import java.time.LocalDate;

/**
 * 出库单 分页查询表单
 *
 * <AUTHOR>
 * @Date 2025-02-09 19:58:18
 * @Copyright zscbdic
 */

@Data
public class StkOutStockQueryForm extends PageParam{

    @Schema(description = "单据来源类型")
    private String originType;

    @Schema(description = "单据类型")
    private String type;

    @Schema(description = "单据方式")
    private String way;

    @Schema(description = "关键字查询")
    private String queryKey;

    @Schema(description = "单据状态")
    private String status;

    @Schema(description = "出库时间")
    private LocalDate outStockTimeBegin;

    @Schema(description = "出库时间")
    private LocalDate outStockTimeEnd;

}
