package net.lab1024.sa.admin.module.business.mes.ai.tool.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.ai.tool.dao.LLMToolRoleDao;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.entity.LLMToolEntity;
import net.lab1024.sa.admin.module.business.mes.ai.tool.domain.entity.LLMToolRoleEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 大模型工具角色权限表  Manager
 *
 * <AUTHOR>
 * @Date 2025-04-06 15:15:25
 * @Copyright zscbdic
 */
@Service
public class LLMToolRoleManager extends ServiceImpl<LLMToolRoleDao, LLMToolRoleEntity> {

    @Resource
    private LLMToolRoleDao llmToolRoleDao;

    /**
     * 更新角色工具权限
     *
     * @param roleId
     * @param roleToolEntityList
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateRoleTool(Long roleId, List<LLMToolRoleEntity> roleToolEntityList) {
        // 根据角色ID删除菜单权限
        llmToolRoleDao.delete(new LambdaQueryWrapper<LLMToolRoleEntity>().eq(LLMToolRoleEntity::getRoleId, roleId));
        // 批量添加菜单权限
        llmToolRoleDao.insert(roleToolEntityList);
    }

    /**
     * 获取工具列表
     * @param roleIdList
     * @param administratorFlag
     * @return
     */
    public Object[] getToolList(List<Long> roleIdList, Boolean administratorFlag) {
        //管理员返回所有工具
        if(Boolean.TRUE.equals(administratorFlag)){
            List<LLMToolEntity> toolEntityList = llmToolRoleDao.selectToolListByRoleIdList(true, Lists.newArrayList(),false);
            if(CollectionUtils.isEmpty(toolEntityList)){
                return Collections.emptyList().toArray();
            }
            List<Object> toolBeans = toolEntityList.stream().map(e -> {
                String beanName = e.getBeanName();
                try {
                    return SpringUtil.getBean(beanName);
                } catch (NoSuchBeanDefinitionException exception) {
                    return null;
                }
            }).filter(Objects::nonNull).toList();
            if(CollectionUtils.isNotEmpty(toolBeans)){
                return toolBeans.toArray();
            }
            return Collections.emptyList().toArray();
        }
        //非管理员 无角色 返回空
        if (CollectionUtils.isEmpty(roleIdList)) {
            return Collections.emptyList().toArray();
        }
        List<LLMToolEntity> toolEntityList = llmToolRoleDao.selectToolListByRoleIdList(true,roleIdList,false);
        if(CollectionUtils.isEmpty(toolEntityList)){
            return Collections.emptyList().toArray();
        }
        List<Object> toolBeans = toolEntityList.stream().map(e -> {
                    String beanName = e.getBeanName();
                    try {
                        return SpringUtil.getBean(beanName);
                    } catch (NoSuchBeanDefinitionException exception) {
                        return null;
                    }
                }).filter(Objects::nonNull)
                .toList();
        if(CollectionUtils.isNotEmpty(toolBeans)){
            return toolBeans.toArray();
        }
        return Collections.emptyList().toArray();
    }
}
