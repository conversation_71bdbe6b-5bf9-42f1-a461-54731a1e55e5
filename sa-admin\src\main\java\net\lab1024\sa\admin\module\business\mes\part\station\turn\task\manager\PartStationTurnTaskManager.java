package net.lab1024.sa.admin.module.business.mes.part.station.turn.task.manager;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskStatusEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.entity.PartStationTurnTaskEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.dao.PartStationTurnTaskDao;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao.PartStationBinDao;
import net.lab1024.sa.base.common.exception.BusinessException;
import org.springframework.stereotype.Service;

/**
 * 驿站任务表  Manager
 *
 * <AUTHOR>
 * @Date 2025-03-14 20:13:46
 * @Copyright zscbdic
 */
@Service
public class PartStationTurnTaskManager extends ServiceImpl<PartStationTurnTaskDao, PartStationTurnTaskEntity> {

    @Resource
    private PartStationBinDao partStationBinDao;

    @Resource
    private PartStationTurnTaskDao partStationTurnTaskDao;


    /**
     * 校验周转箱可用性
     *
     * @param turnoverBoxId
     */
    public void checkBoxAvailable(Long turnoverBoxId) {

        Long count = partStationTurnTaskDao.selectCount(new LambdaQueryWrapper<PartStationTurnTaskEntity>()
                .eq(PartStationTurnTaskEntity::getTurnoverBoxId, turnoverBoxId)
                .in(PartStationTurnTaskEntity::getStatus,
                        TurnTaskStatusEnum.WAIT_GET.getValue(),
                        TurnTaskStatusEnum.WAIT_START.getValue(),
                        TurnTaskStatusEnum.PROCESSING.getValue()));
        if (count > 0) {
            throw new BusinessException("周转箱不可用,该周转箱有任务在处理中");
        }
    }

    /**
     * 校验任务编号可用性
     *
     * @param number
     */
    public void checkNumber(String number) {
        if (StrUtil.isBlank(number)) {
            throw new BusinessException("任务编号不能为空");
        }

        Long count = partStationTurnTaskDao.selectCount(new LambdaQueryWrapper<PartStationTurnTaskEntity>()
                .eq(PartStationTurnTaskEntity::getNumber, number));
        if (count > 0) {
            throw new BusinessException("任务编号已存在");
        }
    }
}
