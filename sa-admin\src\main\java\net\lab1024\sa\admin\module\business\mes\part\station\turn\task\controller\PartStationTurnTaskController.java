package net.lab1024.sa.admin.module.business.mes.part.station.turn.task.controller;

import cn.hutool.core.util.StrUtil;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskExecutorTypeEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskPriorityEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskScopeEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskStatusEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.form.*;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.vo.PartStationTurnTaskVO;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.service.PartStationTurnTaskService;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;

import java.time.LocalDateTime;

/**
 * 驿站任务表 Controller
 *
 * <AUTHOR>
 * @Date 2025-03-14 20:13:46
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "驿站任务表")
public class PartStationTurnTaskController {

    @Resource
    private PartStationTurnTaskService partStationTurnTaskService;

    @Resource
    private SerialNumberService serialNumberService;

    /**
     * 分页查询 <AUTHOR>
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/partStationTurnTask/queryPage")
//    @SaCheckPermission("partStationTurnTask:query")
    public ResponseDTO<PageResult<PartStationTurnTaskVO>> queryPage(@RequestBody @Valid PartStationTurnTaskQueryForm queryForm) {
        return ResponseDTO.ok(partStationTurnTaskService.queryPage(queryForm));
    }

    /**
     * 分页查询 我的任务 <AUTHOR>
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 我的任务 <AUTHOR>
    @PostMapping("/partStationTurnTask/queryMyTaskPage")
//    @SaCheckPermission("partStationTurnTask:query")
    public ResponseDTO<PageResult<PartStationTurnTaskVO>> queryPageMy(@RequestBody @Valid PartStationTurnTaskQueryForm queryForm) {
        Long userId = SmartRequestUtil.getRequestUser().getUserId();
        queryForm.setExecutorId(String.valueOf(userId));
        return ResponseDTO.ok(partStationTurnTaskService.queryPage(queryForm));
    }

    /**
     * 批量删除 <AUTHOR>
     * @param idList
     * @return
     */
    @Operation(summary = "批量删除 <AUTHOR>
    @PostMapping("/partStationTurnTask/batchDelete")
//    @SaCheckPermission("partStationTurnTask:delete")
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> idList) {
        return partStationTurnTaskService.batchDelete(idList);
    }

    /**
     * 单个删除 <AUTHOR>
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/partStationTurnTask/delete/{id}")
//    @SaCheckPermission("partStationTurnTask:delete")
    public ResponseDTO<String> batchDelete(@PathVariable Long id) {
        return partStationTurnTaskService.delete(id);
    }



}
