package net.lab1024.sa.admin.module.business.mes.common.bill.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 单据类型
 */
@Getter
@AllArgsConstructor
public enum BillType implements BaseEnum {

    /**
     * 其他入库单
     */
    STOCK_OTHER_IN("STOCK_OTHER_IN", "其他入库单"),

    /**
     * 其他出库单
     */
    STOCK_OTHER_OUT("STOCK_OTHER_OUT", "其他出库单"),

    /**
     * 生产领料单
     */
    STOCK_PRODUCE_PICK_MATERIAL_OUT("STOCK_PICK_MATERIAL_OUT", "生产领料单"),

    /**
     * 生产退料单
     */
    STOCK_PRODUCE_RETURN_MATERIAL_IN("STOCK_RETURN_MATERIAL_IN", "生产退料单"),

    /**
     * 生产入库单
     */
    STOCK_PRODUCE_IN("STOCK_PRODUCE_IN", "生产入库单"),

    /**
     * 生产退库单
     */
    STOCK_PRODUCE_OUT("STOCK_PRODUCE_OUT", "生产退库单"),

    /**
     * 生产用料清单
     */
    PRODUCE_ORDER_NEED_MATERIAL("PRODUCE_NEED_MATERIAL", "生产用料清单"),


    /**
     * 生产指令单
     */
    PRODUCE_INSTRUCT_ORDER("PRODUCE_INSTRUCT_ORDER", "生产指令单"),

    /**
     * 生产成品清单
     */
    PRODUCE_INSTRUCT_ORDER_CLOTHES("PRODUCE_INSTRUCT_ORDER_CLOTHES", "生产成品清单"),

    /**
     * 裁床单
     */
    CUT_BED_SHEET("CUT_BED_SHEET", "裁床单"),


    ;
    private String value;

    private String desc;
}
