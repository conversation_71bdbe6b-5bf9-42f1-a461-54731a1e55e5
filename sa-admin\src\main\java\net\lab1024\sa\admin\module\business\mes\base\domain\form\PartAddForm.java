package net.lab1024.sa.admin.module.business.mes.base.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 部位表 新建表单
 *
 * <AUTHOR>
 * @Date 2025-06-23 19:56:55
 * @Copyright zscbdic
 */

@Data
public class PartAddForm {

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "部位名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "部位名称 不能为空")
    private String name;

}