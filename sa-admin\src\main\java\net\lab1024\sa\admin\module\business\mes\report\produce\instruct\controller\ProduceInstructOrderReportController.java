package net.lab1024.sa.admin.module.business.mes.report.produce.instruct.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.ProduceInstructOrderSummaryVO;
import net.lab1024.sa.admin.module.business.mes.report.produce.instruct.service.ProduceInstructOrderReportService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 生产指令单报表 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:22:24
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "生产指令单报表")
public class ProduceInstructOrderReportController {

    @Resource
    private ProduceInstructOrderReportService produceInstructOrderReportService;

    /**
     * 执行汇总
     *
     * @param queryForm
     * @return
     */
    @Operation(summary = "执行汇总")
    @PostMapping("/produceInstructOrderReport/executeSummary")
    public ResponseDTO<PageResult<ProduceInstructOrderSummaryVO>> executeSummary(@RequestBody @Valid ProduceInstructOrderQueryForm queryForm) {
        return produceInstructOrderReportService.executeSummary(queryForm);
    }


    /**
     * 执行汇总详情
     * @param produceInstructOrderId
     * @return
     */
    @Operation(summary = "执行汇总详情")
    @GetMapping("/produceInstructOrderReport/executeDetail/{produceInstructOrderId}")
    public ResponseDTO<List<ProduceInstructOrderSummaryVO.DetailVO>> executeDetail(@PathVariable("produceInstructOrderId") Long produceInstructOrderId) {
        return produceInstructOrderReportService.executeDetail(produceInstructOrderId);
    }
}
