package net.lab1024.sa.admin.module.business.mes.part.station.setting.aspect;

import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.annotation.PartStationStoreManageMode;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.constant.StorageManageModeConfigEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.domain.dto.StorageManageModeConfigDTO;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.manager.PartStationConfigManager;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.constant.PartStationConfigConstant;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.config.ConfigService;
import net.lab1024.sa.base.module.support.config.domain.ConfigVO;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 裁片驿站模式切面
 */
@Slf4j
@Aspect
@Component
public class PartStationStoreManageModeAspect {



    @Resource
    private PartStationConfigManager partStationConfigManager;


    @Pointcut("@annotation(net.lab1024.sa.admin.module.business.mes.part.station.setting.annotation.PartStationStoreManageMode)")
    public void pointCut() {

    }

    @Around("pointCut()")
    public Object aroundAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 获取方法上的注解
        PartStationStoreManageMode annotation = method.getAnnotation(PartStationStoreManageMode.class);
        StorageManageModeConfigEnum mode = annotation.mode();

        // 在目标方法执行前的逻辑
//        System.out.println("Before method execution. Storage management mode: " + mode);

        StorageManageModeConfigDTO modeConfig = partStationConfigManager.getStorageManageModeConfig();
        if(modeConfig == null || StrUtil.isBlank(modeConfig.getManageMode()) || !modeConfig.getManageMode().equals(mode.getValue())){
            return ResponseDTO.userErrorParam("当前模式不可用");
        }

        Object result = null;
        try {

            result = joinPoint.proceed();
        } catch (Throwable throwable) {
            log.error("", throwable);
            throw throwable;
        }
        return result;

    }
}
