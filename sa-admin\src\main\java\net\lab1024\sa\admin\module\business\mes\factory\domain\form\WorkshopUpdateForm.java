package net.lab1024.sa.admin.module.business.mes.factory.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 车间信息 更新表单
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:31:51
 * @Copyright zscbdic
 */

@Data
public class WorkshopUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "所属工厂id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "所属工厂id 不能为空")
    private Long factoryId;

    @Schema(description = "车间名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "车间名称 不能为空")
    private String name;

    @Schema(description = "车间位置")
    private String location;

    @Schema(description = "负责人id")
    private Long managerId;

}
