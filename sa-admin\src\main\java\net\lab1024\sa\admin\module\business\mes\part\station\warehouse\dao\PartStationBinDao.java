package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao;

import java.util.List;

import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.dto.PartStationBinDTO;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationBinEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationBinQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.vo.PartStationBinVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 裁片货位 Dao
 *
 * <AUTHOR>
 * @Date 2024-10-06 20:17:14
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface PartStationBinDao extends BaseMapper<PartStationBinEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<PartStationBinVO> queryPage(Page page, @Param("queryForm") PartStationBinQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

    /**
     * 根据id查询 货位相关信息
     * @param ids
     * @return
     */
    List<PartStationBinDTO> queryBinByIds(@Param("ids") List<Long> ids);

}
