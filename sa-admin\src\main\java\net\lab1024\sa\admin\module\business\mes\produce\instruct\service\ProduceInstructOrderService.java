package net.lab1024.sa.admin.module.business.mes.produce.instruct.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.event.produce.ProduceInstructOrderDeleteCheckEvent;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.constant.ProduceInstructOrderProduceStatusEnum;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderProcessDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.bo.ProduceInstructOrderBo;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.*;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ChangePriorityForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderAddForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form.ProduceInstructOrderUpdateForm;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo.*;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.manager.*;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.common.util.SmartRequestUtil;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 生产指令单 Service
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:22:24
 * @Copyright zscbdic
 */

@Service
@Slf4j
public class ProduceInstructOrderService {

    @Resource
    private ProduceInstructOrderDao produceInstructOrderDao;

    @Resource
    private ProduceInstructOrderManager orderManager;

    @Resource
    private ProduceInstructOrderItemManager orderItemManager;

    @Resource
    private ProduceInstructOrderArrangeManager orderArrangeManager;

    @Resource
    private ProduceInstructOrderClothesManager orderClothesManager;

    @Resource
    private ProduceInstructOrderProcessManager orderProcessManager;
//
    @Resource
    private ProduceInstructOrderProcessDao produceInstructOrderProcessDao;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<ProduceInstructOrderVO> queryPage(ProduceInstructOrderQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ProduceInstructOrderVO> list = produceInstructOrderDao.queryPage(page, queryForm);
        if(CollUtil.isEmpty(list)){
            return SmartPageUtil.convert2PageResult(page, list);
        }

        List<Long> orderIds = list.stream().map(ProduceInstructOrderVO::getId).collect(Collectors.toList());
        List<ProduceInstructOrderProcessEntity> processEntityList = orderProcessManager.lambdaQuery()
                .in(ProduceInstructOrderProcessEntity::getOrderId, orderIds)
                .list();
        if(CollUtil.isEmpty(processEntityList)){
            return SmartPageUtil.convert2PageResult(page, list);
        }
        Map<Long, List<ProduceInstructOrderProcessEntity>> processMap = processEntityList.stream()
                .collect(Collectors.groupingBy(ProduceInstructOrderProcessEntity::getOrderId));
        for (ProduceInstructOrderVO orderVO : list) {
            if(!processMap.containsKey(orderVO.getId())){
                continue;
            }

            List<ProduceInstructOrderProcessEntity> processList = processMap.get(orderVO.getId());
            int allFinishNum = processList.stream().mapToInt(ProduceInstructOrderProcessEntity::getFinishNum).sum();
            orderVO.setAllFinishNum(allFinishNum);
            int allShouldNum = processList.stream().mapToInt(ProduceInstructOrderProcessEntity::getShouldNum).sum();
            orderVO.setAllShouldNum(allShouldNum);
        }


        return SmartPageUtil.convert2PageResult(page, list);
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(ProduceInstructOrderAddForm addForm) {
        orderManager.addCheck(addForm);

        //处理指令单信息
        ProduceInstructOrderEntity orderEntity = BeanUtil
                .copyProperties(addForm, ProduceInstructOrderEntity.class, "itemList", "processList", "clothesList", "arrangeList");
        orderEntity.setProduceStatus(ProduceInstructOrderProduceStatusEnum.PLAN.getValue());
        //处理指令单物料信息
        List<ProduceInstructOrderItemEntity> itemList = orderItemManager.parseAddForm(addForm.getItemList());
        //处理指令单工序信息
        List<ProduceInstructOrderProcessEntity> processList = orderProcessManager.parseAddForm(addForm.getProcessList());
        //处理指令单成衣信息
        List<ProduceInstructOrderClothesEntity> clothesList = orderClothesManager.parseAddForm(addForm.getClothesList());
        //处理指令单安排信息
        List<ProduceInstructOrderArrangeEntity> arrangeList = orderArrangeManager.parseAddForm(addForm.getArrangeList());

        int produceNum = clothesList.stream().mapToInt(ProduceInstructOrderClothesEntity::getNum).sum();
        orderEntity.setProduceNum(produceNum);
        ProduceInstructOrderBo addBo = new ProduceInstructOrderBo();
        addBo.setOrder(orderEntity);
        addBo.setItemList(itemList);
        addBo.setProcessList(processList);
        addBo.setClothesList(clothesList);
        addBo.setArrangeList(arrangeList);
        orderManager.saveInstructOrderInfo(addBo);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(ProduceInstructOrderUpdateForm updateForm) {
        ProduceInstructOrderEntity oldOrder = orderManager.getById(updateForm.getId());
        if (oldOrder == null) {
            return ResponseDTO.userErrorParam("指令单不存在");
        }
        orderManager.updateCheck(updateForm);

        ProduceInstructOrderEntity orderEntity = BeanUtil.copyProperties(updateForm, ProduceInstructOrderEntity.class, "itemList", "processList", "clothesList", "arrangeList");
        //处理指令单物料信息
        List<ProduceInstructOrderItemEntity> itemList = orderItemManager.parseUpdateForm(updateForm.getItemList());
        //处理指令单工序信息
        List<ProduceInstructOrderProcessEntity> processList = orderProcessManager.parseUpdateForm(updateForm.getProcessList());
        //处理指令单成衣信息
        List<ProduceInstructOrderClothesEntity> clothesList = orderClothesManager.parseUpdateForm(updateForm.getClothesList());
        //处理指令单安排信息
        List<ProduceInstructOrderArrangeEntity> arrangeList = orderArrangeManager.parseUpdateForm(updateForm.getArrangeList());

        int produceNum = clothesList.stream().mapToInt(ProduceInstructOrderClothesEntity::getNum).sum();
        orderEntity.setProduceNum(produceNum);
        ProduceInstructOrderBo updateBo = new ProduceInstructOrderBo();
        updateBo.setOrder(orderEntity);
        updateBo.setItemList(itemList);
        updateBo.setProcessList(processList);
        updateBo.setClothesList(clothesList);
        updateBo.setArrangeList(arrangeList);
        orderManager.updateInstructOrderInfo(updateBo);
        return ResponseDTO.ok();
    }


    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }
        ProduceInstructOrderEntity order = orderManager.getById(id);
        if (order == null) {
            return ResponseDTO.userErrorParam("指令单不存在");
        }

        orderManager.deleteCheck(order);

        eventPublisher.publishEvent(new ProduceInstructOrderDeleteCheckEvent(this,id));

        orderManager.deletedInstructOrderInfo(id);
        return ResponseDTO.ok();
    }

    /**
     * 获取详情
     */
    public ResponseDTO<ProduceInstructOrderVO> get(Long id) {
        //指令单信息
        ProduceInstructOrderVO orderVO = orderManager.getOrderMainInfo(id);
        //指令单物料信息
        List<ProduceInstructOrderItemVO> itemList = orderItemManager.getItemInfo(id);
        //指令单安排信息
        List<ProduceInstructOrderArrangeVO> arrangeList = orderArrangeManager.getArrangeInfo(id);
        //指令单工序信息
        List<ProduceInstructOrderProcessVO> processList = orderProcessManager.getProcessInfo(id);
        //指令单成衣信息
        List<ProduceInstructOrderClothesVO> clothesList = orderClothesManager.getClothesInfo(id);

        orderVO.setItemList(itemList);
        orderVO.setArrangeList(arrangeList);
        orderVO.setProcessList(processList);
        orderVO.setClothesList(clothesList);

        return ResponseDTO.ok(orderVO);
    }


    /**
     * 单个下达
     *
     * @param id
     * @return
     */
    public ResponseDTO<String> issued(Long id) {
        ProduceInstructOrderEntity entity = orderManager.getById(id);
        if (entity == null) {
            return ResponseDTO.userErrorParam("指令单不存在");
        }
        if (!ProduceInstructOrderProduceStatusEnum.PLAN.getValue().equals(entity.getProduceStatus())) {
            return ResponseDTO.userErrorParam("指令单状态不为计划");
        }
        //校验
        orderManager.issuedCheck(id);

        //计算生产数量
        List<ProduceInstructOrderClothesEntity> clothesEntities = orderClothesManager.lambdaQuery()
                .eq(ProduceInstructOrderClothesEntity::getOrderId, id)
                .list();
        int sum = clothesEntities.stream()
                .mapToInt(ProduceInstructOrderClothesEntity::getNum)
                .sum();

        RequestUser user = SmartRequestUtil.getRequestUser();

        //更新指令单状态
        orderManager.lambdaUpdate()
                .eq(ProduceInstructOrderEntity::getId, id)
                .eq(ProduceInstructOrderEntity::getProduceStatus, ProduceInstructOrderProduceStatusEnum.PLAN.getValue())
                .set(ProduceInstructOrderEntity::getProduceStatus, ProduceInstructOrderProduceStatusEnum.ISSUED.getValue())
                .set(ProduceInstructOrderEntity::getIssuedTime, LocalDateTime.now())
                .set(ProduceInstructOrderEntity::getIssuerId, user.getUserId())
                .set(ProduceInstructOrderEntity::getIssuerName, user.getUserName())
                .set(ProduceInstructOrderEntity::getProduceNum, sum)
                .update();


        return ResponseDTO.ok();
    }

    /**
     * 反下达
     * @param id
     * @return
     */
    public ResponseDTO<String> reIssued(Long id) {
        ProduceInstructOrderEntity entity = orderManager.getById(id);
        if (entity == null) {
            return ResponseDTO.userErrorParam("指令单不存在");
        }
        if (!ProduceInstructOrderProduceStatusEnum.ISSUED.getValue().equals(entity.getProduceStatus())) {
            return ResponseDTO.userErrorParam("指令单状态不为下达");
        }

        //更新指令单状态
        orderManager.lambdaUpdate()
                .eq(ProduceInstructOrderEntity::getId, id)
                .set(ProduceInstructOrderEntity::getProduceStatus, ProduceInstructOrderProduceStatusEnum.PLAN.getValue())
                .set(ProduceInstructOrderEntity::getIssuedTime, null)
                .set(ProduceInstructOrderEntity::getIssuerId, null)
                .set(ProduceInstructOrderEntity::getIssuerName, null)
                .update();
        return ResponseDTO.ok();

    }

    /**
     * 获取指令单基础信息
     * @param id
     * @return
     */
    public ResponseDTO<ProduceInstructOrderVO> getBaseInfo(Long id) {
        ProduceInstructOrderEntity order = orderManager.getById(id);
        ProduceInstructOrderVO orderVO = BeanUtil.copyProperties(order, ProduceInstructOrderVO.class);
        return ResponseDTO.ok(orderVO);
    }

    /**
     * 修改指令优先级
     * @param changePriorityForm
     * @return
     */
    public ResponseDTO<String> changePriority(ChangePriorityForm changePriorityForm) {
        orderManager.changePriority(changePriorityForm);
        return ResponseDTO.ok();
    }

    /**
     * 强制完成
     * @param ids
     * @return
     */
    public ResponseDTO<String> forceComplete(ValidateList<Long> ids) {
        orderManager.lambdaUpdate()
                .set(ProduceInstructOrderEntity::getProduceStatus, ProduceInstructOrderProduceStatusEnum.FINISH.getValue())
                .in(ProduceInstructOrderEntity::getId, ids)
                .in(ProduceInstructOrderEntity::getProduceStatus, ProduceInstructOrderProduceStatusEnum.START.getValue(), ProduceInstructOrderProduceStatusEnum.ISSUED.getValue())
                .update();
        return ResponseDTO.ok();
    }

    /**
     *
     * @return id，指令单编号，物料编号
     */
    public ResponseDTO<List<ProduceInstructOrderVO>> getInsOrItemNumber() {
        List<ProduceInstructOrderEntity> list = produceInstructOrderDao
                .selectList(new LambdaQueryWrapper<ProduceInstructOrderEntity>()
                .select(ProduceInstructOrderEntity::getId,
                        ProduceInstructOrderEntity::getInstructNumber,
                        ProduceInstructOrderEntity::getItemNumber));
        List<ProduceInstructOrderVO> result = SmartBeanUtil.copyList(list, ProduceInstructOrderVO.class);
        return ResponseDTO.ok(result);
    }


}
