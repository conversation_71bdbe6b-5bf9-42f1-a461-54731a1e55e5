package net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 驿站任务表 实体类
 *
 * <AUTHOR>
 * @Date 2025-03-14 20:13:46
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_part_station_turn_task")
public class PartStationTurnTaskEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Integer deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 任务作用范围;human人
     */
    private String taskScope;

    /**
     * 任务编号
     */
    private String number;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务优先级
     */
    private String priority;

    /**
     * 任务类型
     */
    private String type;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 是否自动派发 0:否 1:是
     */
    private Boolean autoDispatchFlag;

    /**
     * 起点货位ID
     */
    private Long startLocationId;

    /**
     * 终点货位ID
     */
    private Long endLocationId;

    /**
     * 终点生产小组ID
     */
    private Long endProduceTeamId;

    /**
     * 菲票数组
     */
    private String ticketIds;

    /**
     * 菲票数量
     */
    private Integer ticketQty;

    /**
     * 裁片数量
     */
    private Integer partQty;

    /**
     * 周转箱ID
     */
    private Long turnoverBoxId;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 领取时间
     */
    private LocalDateTime getTime;

    /**
     * 开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 提交人类型
     */
    private String submitterType;

    /**
     * 提交人ID
     */
    private String submitterId;

    /**
     * 提交人名称
     */
    private String submitterName;

    /**
     * 执行人类型
     */
    private String executorType;

    /**
     * 执行人ID
     */
    private String executorId;

    /**
     * 执行人名称
     */
    private String executorName;

    /**
     * 关联单据类型
     */
    private String bizType;

    /**
     * 关联单据ID
     */
    private Long bizId;

    /**
     * 关联单据详情序号
     */
    private Integer bizDetailSeq;

    /**
     * 关联单据详情ID
     */
    private Long bizDetailId;

    /**
     * 是否同步裁片收发记录
     */
    private Boolean syncPartDispatchFlag;

}
