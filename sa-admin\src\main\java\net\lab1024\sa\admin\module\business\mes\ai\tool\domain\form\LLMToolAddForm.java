package net.lab1024.sa.admin.module.business.mes.ai.tool.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 大模型工具表 新建表单
 *
 * <AUTHOR>
 * @Date 2025-04-05 14:27:51
 * @Copyright zscbdic
 */

@Data
public class LLMToolAddForm {

    @Schema(description = "bean名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "bean名称 不能为空")
    private String beanName;

    @Schema(description = "工具描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "工具描述 不能为空")
    private String description;

    @Schema(description = "启用状态;0禁用 1启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "启用状态;0禁用 1启用 不能为空")
    private Boolean enableFlag;

    @Schema(description = "工具类型")
    @NotBlank(message = "工具类型 不能为空")
    private String type;

}
