package net.lab1024.sa.admin.module.business.mes.item.controller;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.common.excel.utils.ExcelUtils;
import net.lab1024.sa.admin.module.business.mes.item.constant.ItemAttributeEnum;
import net.lab1024.sa.admin.module.business.mes.item.constant.ItemCategoryEnum;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemClothAddForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemClothQueryForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.form.ItemClothUpdateForm;
import net.lab1024.sa.admin.module.business.mes.item.domain.vo.ItemClothVO;
import net.lab1024.sa.admin.module.business.mes.item.service.ItemClothService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 布料信息 Controller
 */
@RestController
public class ItemClothController {

    @Resource
    private ItemClothService itemClothService;
    @Resource
    private SerialNumberService serialNumberService;


    /**
     * 布料添加
     *
     * @param addForm
     * @return
     */
    @Operation(summary = "添加")
    @PostMapping("/item/cloth/add")
    public ResponseDTO<String> add(@RequestBody @Valid ItemClothAddForm addForm) {
        addForm.setAttribute(ItemAttributeEnum.CLOTH.getValue());
        addForm.setCategory(ItemCategoryEnum.FINISHED_PRODUCT.getValue());
        if (StrUtil.isEmpty(addForm.getNumber())) {
            addForm.setNumber(serialNumberService.generate(SerialNumberIdEnum.ITEM));
        }
        return itemClothService.add(addForm);
    }

    /**
     * 布料更新
     *
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新")
    @PostMapping("/item/cloth/update")
    public ResponseDTO<String> update(@RequestBody @Valid ItemClothUpdateForm updateForm) {
        updateForm.setAttribute(ItemAttributeEnum.CLOTH.getValue());
        updateForm.setCategory(ItemCategoryEnum.FINISHED_PRODUCT.getValue());
        return itemClothService.update(updateForm);
    }

    /**
     * 布料详情
     */
    @Operation(summary = "详情")
    @GetMapping("/item/cloth/byId")
    public ResponseDTO<ItemClothVO> getById(@RequestParam("id") Long id) {
        return itemClothService.getById(id);
    }

    /**
     * 布料分页
     *
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页")
    @PostMapping("/item/cloth/queryPage")
    public ResponseDTO<PageResult<ItemClothVO>> queryPage(@RequestBody @Valid ItemClothQueryForm queryForm) {
        return itemClothService.queryPage(queryForm);
    }

    /**
     * 布料删除
     *
     * @param id
     * @return
     */
    @Operation(summary = "删除")
    @GetMapping("/item/cloth/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return itemClothService.delete(id);
    }




    /**
     * 下载导入模板
     *
     * @param response
     * @throws IOException
     */
    @Operation(summary = "下载导入模板")
    @GetMapping("/item/cloth/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        ExcelUtils.excelTemplateExport(response, "file-template/excel/item/import_item_cloth_template.xlsx", "导入布料模板.xlsx");
    }

    /**
     * 导入
     *
     * @param file
     * @return
     */
    @Operation(summary = "导入")
    @PostMapping("/item/cloth/import")
    public ResponseDTO<String> importExcel(@RequestParam("file") MultipartFile file) {
        return itemClothService.importExcel(file);
    }

    /**
     * 获取布料二维码内容
     * @param clothId
     * @return
     */
    @Operation(summary = "布料二维码内容 <AUTHOR>
    @GetMapping("/item/cloth/getQrCodeContent")
    public ResponseDTO<String> getQrCodeContent(@RequestParam("clothId") String clothId) {
        return itemClothService.getQrCodeContent(clothId);
    }
}
