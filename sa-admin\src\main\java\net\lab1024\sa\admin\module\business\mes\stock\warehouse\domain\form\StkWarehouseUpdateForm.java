package net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 仓库 更新表单
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:13:18
 * @Copyright zscbdic
 */

@Data
public class StkWarehouseUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "仓库编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "仓库编号 不能为空")
    private String number;

    @Schema(description = "仓库名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "仓库名称 不能为空")
    private String name;

    @Schema(description = "仓库地址")
    private String address;

    @Schema(description = "负责人id")
    private Long principalId;

    @Schema(description = "负责人名称")
    private String principalName;

    @Schema(description = "负责人电话")
    private String tel;

    @Schema(description = "是否启用仓位管理", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否启用仓位管理;0否 1是 不能为空")
    private Boolean openLocationFlag;

//    /**
//     * 允许锁库;0否 1是（保留字段）
//     */
//    private Boolean allowLockFlag;

    /**
     * 允许负库存;0否 1是
     */
    @NotNull(message = "允许负库存 不能为空")
    @Schema(description = "允许负库存;0否 1是")
    private Boolean allowNegativeFlag;


}
