package net.lab1024.sa.admin.module.business.mes.process.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import net.lab1024.sa.admin.module.business.mes.common.excel.utils.ExcelUtils;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessAddForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessQueryForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.form.ProcessUpdateForm;
import net.lab1024.sa.admin.module.business.mes.process.domain.vo.ProcessVO;
import net.lab1024.sa.admin.module.business.mes.process.service.ProcessService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;

/**
 * 工序信息 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-13 15:19:57
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "工序信息接口")
public class ProcessController {

    @Resource
    private ProcessService processService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/process/queryPage")
    public ResponseDTO<PageResult<ProcessVO>> queryPage(@RequestBody @Valid ProcessQueryForm queryForm) {
        return ResponseDTO.ok(processService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/process/add")
    public ResponseDTO<String> add(@RequestBody @Valid ProcessAddForm addForm) {
        return processService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/process/update")
    public ResponseDTO<String> update(@RequestBody @Valid ProcessUpdateForm updateForm) {
        return processService.update(updateForm);
    }

    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/process/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return processService.delete(id);
    }


    /**
     * 下载导入模板
     * @param response
     * @throws IOException
     */
    @Operation(summary = "下载导入模板")
    @GetMapping("/process/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        //excel/process/import_process_template.xlsx
        //下载模板文件
        ExcelUtils.excelTemplateExport(response, "file-template/excel/process/import_process_template.xlsx", "导入工序模板.xlsx");
    }

    /**
     * 导入
     * @param file 文件名称
     * @return
     */
    @PostMapping("/process/import")
    public ResponseDTO<String> importExcel(@RequestParam("file") MultipartFile file) {
        return processService.importExcel(file);
    }
}
