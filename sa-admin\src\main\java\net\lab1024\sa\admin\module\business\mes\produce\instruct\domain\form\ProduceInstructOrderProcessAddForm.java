package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 生产指令工序信息 新建表单
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:24:08
 * @Copyright zscbdic
 */

@Data
public class ProduceInstructOrderProcessAddForm {


    @Schema(description = "序号")
    @NotNull(message = "序号 不能为空")
    @Min(value = 1, message = "序号不能小于1")
    private Integer serialNumber;

    @Schema(description = "工序id")
    @NotNull(message = "工序id不能为空")
    private Long processId;

    @NotNull(message = "工序名称 不能为空")
    @Schema(description = "工序名称")
    private String name;

    @Schema(description = "部位")
    private String position;

    @Schema(description = "工序类型")
    private String processType;

    @Schema(description = "标准工时;单位")
    @Min(value = 1, message = "标准工时不能小于1")
    private Integer standardTime;

    @Schema(description = "工价一")
    @Min(value = 0, message = "工价一不能小于0")
    private BigDecimal unitPrice1;

    @Schema(description = "工价二")
    @Min(value = 0, message = "工价二不能小于0")
    private BigDecimal unitPrice2;

    @Schema(description = "工价三")
    @Min(value = 0, message = "工价三不能小于0")
    private BigDecimal unitPrice3;

    @Schema(description = "工价四")
    @Min(value = 0, message = "工价四不能小于0")
    private BigDecimal unitPrice4;

    @Schema(description = "工价五")
    @Min(value = 0, message = "工价五不能小于0")
    private BigDecimal unitPrice5;

    @Schema(description = "工价六")
    @Min(value = 0, message = "工价六不能小于0")
    private BigDecimal unitPrice6;

    @Schema(description = "车间id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "车间id 不能为空")
    private Long workshopId;

    /**
     * 车间id
     */
//    @Schema(description = "车间名称", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "车间名称 不能为空")
    private String workshopName;

    @Schema(description = "是否审核;0不审核 1审核", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否审核;0不审核 1审核 不能为空")
    private Boolean auditFlag;

    /**
     * 是否超产;0否 1是
     */
    @NotNull(message = "是否超产;0否 1是 不能为空")
    @Schema(description = "是否超产;0否 1是", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean overflowWorkFlag;

    @Schema(description = "工序控制;0自制 1委外 2不限", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "工序控制;0自制 1委外 2不限 不能为空")
    private String processControl;

    @Schema(description = "sopId;保留")
    private Long sopId;

    /**
     * 应生产数量
     */
    @NotNull(message = "应生产数量 不能为空")
    @Min(value = 1, message = "应生产数量不能小于1")
    @Schema(description = "应生产数量")
    private Integer shouldNum;

}
