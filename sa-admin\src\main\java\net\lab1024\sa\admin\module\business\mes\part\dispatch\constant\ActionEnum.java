package net.lab1024.sa.admin.module.business.mes.part.dispatch.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

@AllArgsConstructor
@Getter
public enum ActionEnum implements BaseEnum {

    /**
     * 收
     */
    RECEIVE("1","收"),
    /**
     * 发
     */
    SEND("2","发"),

    /**
     * 无数据
     */
    NONE("3","无");



    private String value;

    private String desc;

    public static ActionEnum getByValue(String value) {
        for (ActionEnum actionEnum : ActionEnum.values()) {
            if (actionEnum.getValue().equals(value)) {
                return actionEnum;
            }
        }
        return null;
    }
}
