package net.lab1024.sa.admin.module.business.mes.part.station.inventory.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.entity.PartStationInventoryEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.form.PartStationInventoryQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.vo.PartStationInventoryVO;

import java.util.List;

/**
 * 裁片驿站库存表 Dao
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:44:34
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface PartStationInventoryDao extends BaseMapper<PartStationInventoryEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<PartStationInventoryVO> queryPage(Page page, @Param("queryForm") PartStationInventoryQueryForm queryForm);

//    /**
//     * 更新删除状态
//     */
//    long updateDeleted(@Param("id") Long id, @Param("deletedFlag") boolean deletedFlag);
//
//    /**
//     * 批量更新删除状态
//     */
//    void batchUpdateDeleted(@Param("idList") List<Long> idList, @Param("deletedFlag") boolean deletedFlag);

    List<PartStationInventoryVO> querySummary(@Param("queryForm") PartStationInventoryQueryForm queryForm, @Param("offset") Long offset, @Param("limit") Long limit);

    Long querySummaryTotal(@Param("queryForm") PartStationInventoryQueryForm queryForm);
}
