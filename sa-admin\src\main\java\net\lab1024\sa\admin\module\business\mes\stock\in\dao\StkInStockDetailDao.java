package net.lab1024.sa.admin.module.business.mes.stock.in.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.entity.StkInStockDetailEntity;

import java.util.List;

/**
 * 入库单详情 Dao
 *
 * <AUTHOR>
 * @Date 2025-02-03 15:55:23
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface StkInStockDetailDao extends BaseMapper<StkInStockDetailEntity> {


    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);
    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList")List<Long> idList,@Param("deletedFlag")boolean deletedFlag);

}
