package net.lab1024.sa.admin.module.business.mes.stock.out.manager;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.stock.out.dao.StkOutStockDetailDao;
import net.lab1024.sa.admin.module.business.mes.stock.out.domain.entity.StkOutStockDetailEntity;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.dao.StkWarehouseDao;
import net.lab1024.sa.admin.module.business.mes.stock.warehouse.domain.entity.StkWarehouseEntity;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 出库单详情  Manager
 *
 * <AUTHOR>
 * @Date 2025-02-09 19:59:35
 * @Copyright zscbdic
 */
@Service
public class StkOutStockDetailManager extends ServiceImpl<StkOutStockDetailDao, StkOutStockDetailEntity> {

    @Resource
    private StkWarehouseDao stkWarehouseDao;

    /**
     * 检查是否需要库位
     * @param warehouseId
     * @param details
     */
    public void checkNeedLocation(Long warehouseId, List<StkOutStockDetailEntity> details) {
        StkWarehouseEntity warehouse = stkWarehouseDao.selectById(warehouseId);
        if (warehouse == null) {
            throw new BusinessException("仓库不存在");
        }
        if (Boolean.TRUE.equals(warehouse.getOpenLocationFlag())) {
            details.forEach(e -> {
                if (e.getLocationId() == null) {
                    throw new BusinessException("第" + e.getSeq() + "行请选择库位");
                }
            });
        } else {
            details.forEach(e -> {
                // 没有库位，则设置为空
                e.setLocationId(null);
                e.setLocationNumber(null);
            });
        }
    }
}

