package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

import java.time.LocalDate;
import java.util.List;

/**
 * 生产指令单 分页查询表单
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:22:24
 * @Copyright zscbdic
 */

@Data
public class ProduceInstructOrderQueryForm extends PageParam{

    @Schema(description = "关键字查询")
    private String queryKey;

    @Schema(description = "交货日期")
    private LocalDate deliverTimeBegin;

    @Schema(description = "交货日期")
    private LocalDate deliverTimeEnd;

    @Schema(description = "下发时间")
    private LocalDate issuedTimeBegin;

    @Schema(description = "下发时间")
    private LocalDate issuedTimeEnd;

    @Schema(description = "生产类型;0自产,1自裁委外，2整件委外")
    private String produceType;

    @Schema(description = "生产业务状态;0计划，1下达，2开工，3完工")
    private String produceStatus;

    @Schema(description = "优先级;0一般,1紧急,2非常紧急")
    private String priority;

    @Schema(description = "物料编号")
    private String itemNumber;

    @Schema(description = "指令单编号")
    private String instructNumber;

    @Schema(description = "生产业务状态(多选)")
    private List<String> produceStatusList;



}
