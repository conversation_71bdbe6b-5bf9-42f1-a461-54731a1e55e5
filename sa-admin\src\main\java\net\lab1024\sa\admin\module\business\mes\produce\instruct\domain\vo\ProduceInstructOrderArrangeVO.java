package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 指令单安排信息 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:26:34
 * @Copyright zscbdic
 */

@Data
public class ProduceInstructOrderArrangeVO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "指令单id")
    private Long orderId;

    @Schema(description = "序号")
    private Integer serialNumber;

    @Schema(description = "节点名称")
    private String nodeName;

    @Schema(description = "负责人id")
    private Long headId;

    @Schema(description = "负责人名称")
    private String headName;

    @Schema(description = "计划开始时间")
    private LocalDate planBeginTime;

    @Schema(description = "计划结束时间")
    private LocalDate planEndTime;

    @Schema(description = "实际开始时间")
    private LocalDateTime realBeginTime;

    @Schema(description = "实际结束时间")
    private LocalDateTime realEndTime;

    @Schema(description = "完成人id")
    private Long finishId;

    @Schema(description = "完成人名称")
    private String finishName;

    @Schema(description = "末道节点;0否 1是")
    private Boolean endFlag;

    @Schema(description = "完成标识;0未完成，1完成")
    private Boolean finishFlag;


    /**
     * 指令单编号
     */
    @Schema(description = "指令单编号")
    private String instructNumber;

    /**
     * 指令单名称
     */
    private String instructName;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;


    /**
     * 图片地址
     */
    private String imgUrl;


    /**
     * 生产业务状态;0计划，1下达，2开工，3完工
     */
    private String produceStatus;

    /**
     * 优先级;0一般,1紧急,2非常紧急
     */
    private String priority;

    /**
     * 交货日期
     */
    private LocalDate deliverTime;

    /**
     * 下达日期
     */
    private LocalDateTime issuedTime;


    /**
     * 物料id
     */
    private Long itemId;

    /**
     * 物料编号
     */
    private String itemNumber;

    /**
     * 规格型号
     */
    private String model;

    /**
     * 物料名称
     */
    private String itemName;

}
