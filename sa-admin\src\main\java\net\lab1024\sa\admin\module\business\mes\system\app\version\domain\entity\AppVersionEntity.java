package net.lab1024.sa.admin.module.business.mes.system.app.version.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * app版本管理 实体类
 *
 * <AUTHOR>
 * @Date 2025-02-25 14:44:40
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_app_version")
public class AppVersionEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * app_id
     */
    private String appId;

    /**
     * app名称
     */
    private String appName;


    /**
     * 安卓下载链接
     */
    private String androidUrl;

    /**
     * 版本号排序
     */
    private Double versionSort;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 描述
     */
    private String versionDescription;

    /**
     * 包类型;0是整包升级，1是wgt升级
     */
    private String packageType;

    /**
     * 是否强制更新;0否1是
     */
    private Boolean forceUpdate;

    /**
     * 状态;published（已发布）、draft（草稿）
     */
    private String status;

    /**
     * 发布日期
     */
    private LocalDateTime releaseDate;

}
