package net.lab1024.sa.admin.module.business.mes.ai.model.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import net.lab1024.sa.admin.module.business.mes.ai.model.constant.ModelTypeEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;

/**
 * 大模型表 更新表单
 *
 * <AUTHOR>
 * @Date 2025-04-17 11:25:27
 * @Copyright zscbdic
 */

@Data
public class LLMModelUpdateForm {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "主键 不能为空")
    private Long id;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "模型类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "模型类型 不能为空")
    @CheckEnum(required = true,value = ModelTypeEnum.class, message = "模型类型 枚举值错误")
    private String modelType;

    @Schema(description = "模型优先级", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "模型优先级")
    private Integer priority;

    @Schema(description = "模型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "模型名称 不能为空")
    private String modelName;

    @Schema(description = "模型别名")
    private String modelNickname;

    @Schema(description = "启用标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "启用标识 不能为空")
    private Boolean enableFlag;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "请求地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "请求地址 不能为空")
    private String baseUrl;

    @Schema(description = "api_key", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "api_key 不能为空")
    private String apiKey;

    @Schema(description = "工具调用能力", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "工具调用能力 不能为空")
    private Boolean useToolFlag;

    @Schema(description = "知识库调用能力", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "知识库调用能力 不能为空")
    private Boolean knowledgeUseFlag;

    @Schema(description = "视觉调用能力", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "视觉调用能力 不能为空")
    private Boolean visionUseFlag;

}
