package net.lab1024.sa.admin.module.business.mes.salary.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 薪酬结算记录详情 列表VO
 *
 * <AUTHOR>
 * @Date 2024-11-20 20:49:55
 * @Copyright zscbdic
 */

@Data
public class SettlementRecordDetailVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "主表id")
    private Long mainId;

    @Schema(description = "报工记录id")
    private Long workRecordId;

}
