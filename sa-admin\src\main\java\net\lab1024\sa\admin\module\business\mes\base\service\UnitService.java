package net.lab1024.sa.admin.module.business.mes.base.service;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.event.base.UnitDeleteCheckEvent;
import net.lab1024.sa.admin.module.business.mes.base.dao.UnitDao;
import net.lab1024.sa.admin.module.business.mes.base.domain.entity.UnitEntity;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.UnitAddForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.UnitQuery;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.UnitQueryForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.UnitUpdateForm;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.UnitVO;
import net.lab1024.sa.admin.module.business.mes.base.manager.UnitManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 单位表 Service
 *
 * <AUTHOR>
 * @Date 2024-07-04 11:12:45
 * @Copyright zscbdic
 */
@Slf4j
@Service
public class UnitService {

    @Resource
    private UnitDao unitDao;


    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private UnitManager unitManager;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<UnitVO> queryPage(UnitQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<UnitVO> list = unitDao.queryPage(page, queryForm);
        PageResult<UnitVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(UnitAddForm addForm) {
        unitManager.addCheck(addForm);

        UnitEntity unitEntity = SmartBeanUtil.copy(addForm, UnitEntity.class);

        unitDao.insert(unitEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(UnitUpdateForm updateForm) {
        unitManager.updateCheck(updateForm);

        UnitEntity unitEntity = SmartBeanUtil.copy(updateForm, UnitEntity.class);
        unitDao.updateById(unitEntity);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    public ResponseDTO<String> batchDelete(List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return ResponseDTO.ok();
        }
        unitDao.batchUpdateDeleted(idList, true);
        return ResponseDTO.ok();
    }

    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }
        //校验业务
        eventPublisher.publishEvent(new UnitDeleteCheckEvent(this, id));
//        if((!((boolean) request.getAttribute("deleteFlag")))){
//            log.info("service delete");
//            unitDao.updateDeleted(id, true);
//            return ResponseDTO.ok();
//        }
        unitManager.removeById(id);
        return ResponseDTO.ok();


    }

    /**
     * 查询关键字
     *
     * @param queryForm
     * @return List
     */

    public List<UnitVO> queryAll(UnitQuery queryForm) {
        List<UnitEntity> unitEntityList = unitDao.selectList(new LambdaQueryWrapper<UnitEntity>()
                .like(StrUtil.isNotBlank(queryForm.getQueryKey()), UnitEntity::getName, queryForm.getQueryKey()));
        List<UnitVO> unitVOS = SmartBeanUtil.copyList(unitEntityList, UnitVO.class);
        return unitVOS;
    }


    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    public UnitVO getById(Long id) {
        UnitEntity unitEntity = unitManager.getById(id);
        return SmartBeanUtil.copy(unitEntity, UnitVO.class);
    }
}
