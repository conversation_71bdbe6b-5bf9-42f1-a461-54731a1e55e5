package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 指令单用料信息 列表VO
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:24:54
 * @Copyright zscbdic
 */

@Data
public class ProduceInstructOrderItemVO {

    /**
     * 生产用料数据id
     */
    @Schema(description = "id")
    private Long id;


    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

//    @Schema(description = "删除标识;0未删除，1删除")
//    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    /**
     * 生产指令单id
     */
    @Schema(description = "指令单id")
    private Long orderId;

    /**
     * 生产指令单编号
     */
    @Schema(description = "指令单编号")
    private String orderNumber;

    /**
     * 生产指令单状态
     */
    @Schema(description = "指令单状态")
    private String orderProduceStatus;

    /**
     * 物料(产品)id
     */
    @Schema(description = "指令单物料id")
    private Long orderItemId;

    /*
     * 物料编号(产品)
     */
    @Schema(description = "指令单物料编号")
    private String orderItemNumber;

    /**
     * 物料名称(产品)
     */
    @Schema(description = "指令单物料名称")
    private String orderItemName;

    /**
     * 用料物料id
     */
    @Schema(description = "物料id")
    private Long itemId;

    /**
     * 用料物料名称
     */
    @Schema(description = "物料名称")
    private String itemName;

    /**
     * 用料物料spu编号
     */
    @Schema(description = "物料编号")
    private String itemNumber;

    /**
     * 用料物料sku编号
     */
    @Schema(description = "物料sku编号")
    private String itemSkuNumber;

    /**
     * 用料物料规格型号
     */
    @Schema(description = "物料规格型号")
    private String itemModel;


    @Schema(description = "物料分类id")
    private Long itemTypeId;

    @Schema(description = "物料类型")
    private String itemCategory;

    /**
     * 用料物料单位id
     */
    @Schema(description = "物料单位id")
    private Long itemUnitId;

    /**
     * 用料物料单位
     */
    @Schema(description = "物料单位")
    private String itemUnitName;

    @Schema(description = "物料属性")
    private String itemAttribute;

    /**
     * 用料数量
     */
    @Schema(description = "用料数量")
    private Double itemNum;

    /**
     * 单位用量
     */
    @Schema(description = "单位用量")
    private Double dosage;

    /**
     * 损耗率
     */
    @Schema(description = "单位损耗率")
    private Double loss;

    /**
     * 总用量
     */
    @Schema(description = "总用量")
    private Double totalDosage;

    /**
     * 发料数量
     */
    @Schema(description = "发料数量")
    private Double giveQty;

    /**
     * 退料数量
     */
    @Schema(description = "退料数量")
    private Double backQty;
}
