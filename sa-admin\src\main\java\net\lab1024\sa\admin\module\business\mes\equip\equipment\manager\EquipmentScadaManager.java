package net.lab1024.sa.admin.module.business.mes.equip.equipment.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.constant.IotEquipmentTypeEnum;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.constant.IotPlatformEnum;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.dao.EquipmentScadaDao;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.entity.EquipmentScadaEntity;
import net.lab1024.sa.base.common.exception.BusinessException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

/**
 * 设备scada信息  Manager
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:13:22
 * @Copyright zscbdic
 */
@Service
public class EquipmentScadaManager extends ServiceImpl<EquipmentScadaDao, EquipmentScadaEntity> {


    /**
     * 校验
     *
     * @param equipmentScada
     */
    public void check(EquipmentScadaEntity equipmentScada) {
        Boolean iotNetworkFlag = equipmentScada.getIotNetworkFlag();
        if (!iotNetworkFlag) {
            return;
        }
        if (StrUtil.isBlank(equipmentScada.getIotNetworkPlatform())) {
            throw new BusinessException("开启设备物联，物联网平台不能为空");
        }

        if (StrUtil.isBlank(equipmentScada.getScadaEquipmentCode())) {
            throw new BusinessException("Scada设备编码不能为空");
        }
        if (StrUtil.isBlank(equipmentScada.getScadaProductCode())) {
            throw new BusinessException("Scada产品编码不能为空");
        }

        if (IotPlatformEnum.YUAN_YI_IOT.getValue().equals(equipmentScada.getIotNetworkPlatform())) {
            ArrayList<String> supportType = CollUtil.newArrayList(
                    IotEquipmentTypeEnum.YUAN_YI_CUTTING_BED.getValue(),
                    IotEquipmentTypeEnum.YUAN_YI_FABRIC_MACHINE.getValue());
            boolean support = supportType.contains(equipmentScada.getIotEquipmentType());
            if (StrUtil.isNotBlank(equipmentScada.getIotEquipmentType()) && !support) {
                throw new BusinessException("请正确选择元一物联平台支持设备类型");
            }

        }

    }


}
