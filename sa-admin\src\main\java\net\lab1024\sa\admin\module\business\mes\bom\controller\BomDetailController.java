package net.lab1024.sa.admin.module.business.mes.bom.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.bom.domain.form.BomDetailQueryForm;
import net.lab1024.sa.admin.module.business.mes.bom.domain.vo.BomDetailVO;
import net.lab1024.sa.admin.module.business.mes.bom.service.BomDetailService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * BOM详情 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:25:50
 * @Copyright zscbdic
 */

@RestController
@Tag(name ="")
public class BomDetailController {

    @Resource
    private BomDetailService bomDetailService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/bomDetail/queryPage")
    public ResponseDTO<PageResult<BomDetailVO>> queryPage(@RequestBody @Valid BomDetailQueryForm queryForm) {
        return ResponseDTO.ok(bomDetailService.queryPage(queryForm));
    }

//    @Operation(summary = "添加 <AUTHOR>
//    @PostMapping("/bomDetail/add")
//    public ResponseDTO<String> add(@RequestBody @Valid BomDetailAddForm addForm) {
//        return bomDetailService.add(addForm);
//    }
//
//    @Operation(summary = "更新 <AUTHOR>
//    @PostMapping("/bomDetail/update")
//    public ResponseDTO<String> update(@RequestBody @Valid BomDetailUpdateForm updateForm) {
//        return bomDetailService.update(updateForm);
//    }
//
//    @Operation(summary = "单个删除 <AUTHOR>
//    @GetMapping("/bomDetail/delete/{id}")
//    public ResponseDTO<String> delete(@PathVariable Long id) {
//        return bomDetailService.delete(id);
//    }


    /**
     * 获取bom内物料列表
     * @param bomId
     * @return
     */
    @Operation(summary = "获取bom内物料列表 <AUTHOR>
    @GetMapping("/bomDetail/getBomDetailList/{bomId}")
    public ResponseDTO<List<BomDetailVO>> getBomDetailList(@PathVariable("bomId")  Long bomId) {
        return bomDetailService.getBomDetailList(bomId);
    }
}
