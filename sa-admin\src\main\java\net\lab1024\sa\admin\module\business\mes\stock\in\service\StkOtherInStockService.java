package net.lab1024.sa.admin.module.business.mes.stock.in.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import net.lab1024.sa.admin.module.business.mes.stock.constant.StockBillStatusEnum;
import net.lab1024.sa.admin.module.business.mes.stock.in.dao.StkInStockDao;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.bo.StkInStockBO;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.entity.StkInStockDetailEntity;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.entity.StkInStockEntity;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.form.StkInStockQueryForm;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.form.StkOtherInStockAddForm;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.form.StkOtherInStockUpdateForm;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.vo.StkInStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.in.domain.vo.StkOtherInStockVO;
import net.lab1024.sa.admin.module.business.mes.stock.in.manager.StkInStockDetailManager;
import net.lab1024.sa.admin.module.business.mes.stock.in.manager.StkInStockManager;
import net.lab1024.sa.admin.module.business.mes.stock.lot.manager.StkLotMasterManager;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 其他入库单 Service
 *
 * <AUTHOR>
 * @Date 2025-02-03 15:54:07
 * @Copyright zscbdic
 */
@Service
public class StkOtherInStockService {

    @Resource
    private StkInStockManager stkInStockManager;

    @Resource
    private StkInStockDetailManager stkInStockDetailManager;

    @Resource
    private StkLotMasterManager stkLotMasterManager;

    @Resource
    private StkInStockService stkInStockService;

    @Resource
    private StkInStockDao stkInStockDao;

    /**
     * 其他入库
     *
     * @param form
     */
    public ResponseDTO<String> add(StkOtherInStockAddForm form) {

        List<StkInStockDetailEntity> details = SmartBeanUtil.copyList(form.getDetails(), StkInStockDetailEntity.class);
        StkInStockEntity inStock = SmartBeanUtil.copy(form, StkInStockEntity.class);

        StkInStockBO stockBO = new StkInStockBO();
        stockBO.setStkInStock(inStock);
        stockBO.setDetails(details);

        if (StockBillStatusEnum.UN_AUDIT.getValue().equals(form.getStatus())) {
            //保存单据
            stkInStockService.saveBill(stockBO);
        } else if (StockBillStatusEnum.AUDIT.getValue().equals(form.getStatus())) {
            //保存并入库
            stkInStockService.saveAndStockIn(stockBO);

        }
        return ResponseDTO.ok();
    }

    /**
     * 修改单据状态
     *
     * @param id
     * @return
     */
    public ResponseDTO<String> updateStatus(Long id) {
        StkInStockEntity stockEntity = stkInStockManager.getById(id);
        if (stockEntity == null) {
            return ResponseDTO.userErrorParam("单据不存在");
        }
        String status = stockEntity.getStatus();
        stkInStockManager.updateBillStatusCheck(status);

        if (StockBillStatusEnum.UN_AUDIT.getValue().equals(status)) {
            List<StkInStockDetailEntity> detailEntities = stkInStockDetailManager.lambdaQuery()
                    .eq(StkInStockDetailEntity::getInStockId, id)
                    .list();
            StkInStockBO stockBO = new StkInStockBO();
            stockBO.setStkInStock(stockEntity);
            stockBO.setDetails(detailEntities);
            //入库
            stkInStockService.stockIn(stockBO);

        }
        return ResponseDTO.ok();
    }

    /**
     * 修改其他入库单
     *
     * @param form
     * @return
     */
    public ResponseDTO<String> update(StkOtherInStockUpdateForm form) {
        Long id = form.getId();
        StkInStockEntity stockEntity = stkInStockManager.getById(id);
        if (stockEntity == null) {
            return ResponseDTO.userErrorParam("单据不存在");
        }
        String status = stockEntity.getStatus();
        stkInStockManager.updateBillStatusCheck(status);



        List<StkInStockDetailEntity> details = SmartBeanUtil.copyList(form.getDetails(), StkInStockDetailEntity.class);
        StkInStockBO stockBO = new StkInStockBO();
        stockBO.setStkInStock(SmartBeanUtil.copy(form, StkInStockEntity.class));
        stockBO.setDetails(details);

        if(StockBillStatusEnum.AUDIT.getValue().equals(form.getStatus())){
            stkInStockService.updateAndStockIn(stockBO);
        } else if (StockBillStatusEnum.UN_AUDIT.getValue().equals(form.getStatus())) {
            stkInStockService.updateBill(stockBO);
        }

        return ResponseDTO.ok();
    }


    /**
     * 查询其他入库单
     *
     * @param id
     * @return
     */
    public ResponseDTO<StkOtherInStockVO> queryById(Long id) {
        StkInStockEntity stockEntity = stkInStockManager.getById(id);
        if (stockEntity == null) {
            return ResponseDTO.userErrorParam("单据不存在");
        }
        StkOtherInStockVO vo = SmartBeanUtil.copy(stockEntity, StkOtherInStockVO.class);
        List<StkInStockDetailEntity> detailEntities = stkInStockDetailManager.lambdaQuery()
                .eq(StkInStockDetailEntity::getInStockId, id)
                .list();
        List<StkOtherInStockVO.DetailVO> detailVOS = SmartBeanUtil.copyList(detailEntities, StkOtherInStockVO.DetailVO.class);
        vo.setDetails(detailVOS);
        return ResponseDTO.ok(vo);

    }

    /**
     * 分页查询其他入库单
     *
     * @param queryForm
     * @return
     */
    public ResponseDTO<PageResult<StkInStockVO>> queryPage(StkInStockQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<StkInStockVO> list = stkInStockDao.queryPage(page, queryForm);
        PageResult<StkInStockVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 删除其他入库单
     * @param id
     * @return
     */
    public ResponseDTO<String> delete(Long id) {
        StkInStockEntity stkInStockEntity = stkInStockDao.selectById(id);
        if(stkInStockEntity==null){
            return ResponseDTO.userErrorParam("单据不存在");
        }
        stkInStockManager.updateBillStatusCheck(stkInStockEntity.getStatus());

        stkInStockManager.deleteBill(id);

        return ResponseDTO.ok();
    }
}
