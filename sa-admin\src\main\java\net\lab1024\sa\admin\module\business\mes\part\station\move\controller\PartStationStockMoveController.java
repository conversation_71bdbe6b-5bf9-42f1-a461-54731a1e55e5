package net.lab1024.sa.admin.module.business.mes.part.station.move.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.admin.module.business.mes.part.station.move.domain.form.PartStationStockMoveByBoxForm;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.annotation.PartStationStoreManageMode;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.constant.StorageManageModeConfigEnum;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import net.lab1024.sa.admin.module.business.mes.part.station.move.domain.form.PartStationStockMoveByTicketForm;
import net.lab1024.sa.admin.module.business.mes.part.station.move.service.PartStationStockMoveService;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 裁片驿站移库 Controller
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:44:34
 * @Copyright zscbdic
 */
@RestController
@Tag(name = "")
public class PartStationStockMoveController {

    @Resource
    private PartStationStockMoveService partStationStockMoveService;

    /**
     * 单扎裁片移库 <AUTHOR>
     * @param form
     * @return
     */
    @Operation(summary = "单扎裁片移库 <AUTHOR>
    @PostMapping("/partStationStockMove/stockMove")
    @PartStationStoreManageMode(mode = StorageManageModeConfigEnum.FE_TICKET_MANAGE_MODE)
    public ResponseDTO<String> stockMove(@RequestBody @Valid PartStationStockMoveByTicketForm form) {
        return partStationStockMoveService.stockMove(form);
    }

    /**
     * 裁片移库（周转箱） <AUTHOR>
     * @param form
     * @return
     */
    @Operation(summary = "裁片移库（周转箱） <AUTHOR>
    @PostMapping("/partStationStockMove/stockMoveByBox")
    @PartStationStoreManageMode(mode = StorageManageModeConfigEnum.TURN_BOX_MANAGE_MODE)
    public ResponseDTO<String> stockMoveByBox(@RequestBody @Valid PartStationStockMoveByBoxForm form) {
        return partStationStockMoveService.stockMoveByBox(form);
    }
}
