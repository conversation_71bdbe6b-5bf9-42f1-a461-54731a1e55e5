package net.lab1024.sa.admin.module.business.mes.part.dispatch.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 裁片收发日志 列表VO
 *
 * <AUTHOR>
 * @Date 2024-11-06 15:25:13
 * @Copyright zscbdic
 */

@Data
public class PartDispatchLogVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "菲票id")
    private Long ticketId;

    @Schema(description = "生产指令单编号")
    private String instructOrderNumber;

    @Schema(description = "生产指令单id")
    private Long instructOrderId;

    @Schema(description = "裁床单id")
    private Long cutBedSheetId;

    @Schema(description = "裁床单编号")
    private String cutBedSheetNumber;

    @Schema(description = "物料id")
    private Long itemId;

    @Schema(description = "物料编号")
    private String itemNumber;

    @Schema(description = "规格型号")
    private String model;

    @Schema(description = "物料名称")
    private String itemName;

    @Schema(description = "物料单位id")
    private Long unitId;

    @Schema(description = "物料单位名称")
    private String unitName;

    @Schema(description = "扎号")
    private Integer tieNum;

    @Schema(description = "款式颜色")
    private String styleColor;

    @Schema(description = "尺码")
    private String size;

    @Schema(description = "床次")
    private Integer cutNum;

    @Schema(description = "数量")
    private Integer num;

    @Schema(description = "缸号")
    private String lotNo;

    @Schema(description = "部位")
    private String positions;

    @Schema(description = "收发范围")
    private String dispatchRange;

    @Schema(description = "收或发")
    private String actionStatus;

    @Schema(description = "供应商id")
    private Long supplierId;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "工厂id")
    private Long factoryId;

    @Schema(description = "工厂名称")
    private String factoryName;

    @Schema(description = "车间id")
    private Long workshopId;

    @Schema(description = "车间名称")
    private String workshopName;

    @Schema(description = "生产组id")
    private Long produceTeamId;

    @Schema(description = "生产组名称")
    private String produceTeamName;

    /**
     * 下发时间
     */
    @Schema(description = "下发时间")
    private LocalDateTime sendTime;

    /**
     * 下发人id
     */
    @Schema(description = "下发人id")
    private Long senderId;

    /**
     * 下发人名称
     */
    @Schema(description = "下发人名称")
    private String senderName;

    /**
     * 下发描述
     */
    @Schema(description = "下发描述")
    private String sendDesc;

    /**
     * 回收时间
     */
    @Schema(description = "回收时间")
    private LocalDateTime receiveTime;

    /**
     * 回收人id
     */
    @Schema(description = "回收人id")
    private Long receiverId;

    /**
     * 回收人名称
     */
    @Schema(description = "回收人名称")
    private String receiverName;

    /**
     * 回收描述
     */
    @Schema(description = "回收描述")
    private String receiveDesc;

}
