package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 裁片货位 新建表单
 *
 * <AUTHOR>
 * @Date 2024-10-06 20:17:14
 * @Copyright zscbdic
 */

@Data
public class PartStationBinAddForm {

    @Schema(description = "所属货架id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "所属货架id 不能为空")
    private Long rackId;

    @Schema(description = "库位编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "库位编码 不能为空")
    private String binCode;

    @Schema(description = "库位描述")
    private String binDesc;

    @Schema(description = "容量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "容量 不能为空")
    private Integer capacity;

}
