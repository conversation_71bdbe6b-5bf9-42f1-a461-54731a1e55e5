package net.lab1024.sa.admin.module.business.mes.ai.core.route;

import cn.hutool.core.collection.CollUtil;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.mes.ai.core.config.ChatModelRouteConfig;
import net.lab1024.sa.admin.module.business.mes.ai.core.constant.AiPlatformEnum;
import net.lab1024.sa.admin.module.business.mes.ai.core.exception.NoMatchModelException;
import net.lab1024.sa.admin.module.business.mes.ai.core.factory.AiModelFactory;
import net.lab1024.sa.admin.module.business.mes.ai.model.constant.ModelTypeEnum;
import net.lab1024.sa.admin.module.business.mes.ai.model.domain.entity.LLMModelEntity;
import net.lab1024.sa.admin.module.business.mes.ai.model.manager.LLMModelManager;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * LLM 模型路由
 */
@Slf4j
@Component
public class ChatModelRoute {

    @Resource
    private LLMModelManager llmModelManager;

    @Resource
    private ChatModelRouteConfig chatModelRouteConfig;

    public ChatModel selectModel(UserMessage userMessage) {
        return selectModel(new RouteSelectDTO(null, userMessage));
    }

    public ChatModel selectModel(RouteSelectDTO routeSelectDTO) {
        UserMessage userMessage = routeSelectDTO.getUserMessage();
        Long modelId = routeSelectDTO.getModelId();

        //指定模型ID，直接选择该模型
        if (modelId != null) {
            LLMModelEntity llmModel = llmModelManager.getById(modelId);
            if (llmModel == null) {
                log.error("模型ID不存在");
                throw new NoMatchModelException("模型ID不存在");
            }
            // 检查模型是否可用
            llmModelManager.checkUseValid(llmModel);
            return AiModelFactory.getOrCreateChatModel(AiPlatformEnum.OPENAI, llmModel.getModelName(), llmModel.getApiKey(), llmModel.getBaseUrl());
        }
        //--------------------------------------------------

        // 有图片，选择视觉模型
        if (CollUtil.isNotEmpty(userMessage.getMedia()) && userMessage.getMedia().stream().allMatch(media -> "image".equals(media.getMimeType().getType()))) {
            return selectVisualModel(userMessage);
        }
        //----------------------------------------------------


        ChatModelRouteConfig.Config config = chatModelRouteConfig.getConfig();
        // 未启用，返回优先级最高的模型
        if (Boolean.TRUE.equals(!config.getEnableFlag()) || CollUtil.isEmpty(config.getItems())) {
            return fallbackSelectModel();
        }
        // 启用，根据配置选择决策模型
        Long decisionModelId = config.getModelId();
        LLMModelEntity llmModel = llmModelManager.getById(decisionModelId);
        if (llmModel == null) {
            log.error("模型ID不存在");
            throw new NoMatchModelException("模型ID不存在");
        }
        // 检查模型是否可用
        llmModelManager.checkUseValid(llmModel);
        ChatModel model = AiModelFactory.getOrCreateChatModel(AiPlatformEnum.OPENAI, llmModel.getModelName(), llmModel.getApiKey(), llmModel.getBaseUrl());
        SystemMessage systemMessage = new SystemMessage(buildPrompt(config.getPrompt(), config.getItems()));
        String call = model.call(systemMessage, userMessage);
        BeanOutputConverter<Integer> beanOutputConverter = new BeanOutputConverter<>(Integer.class);
        // 匹配模型序号
        try {
            Integer modelIndex = beanOutputConverter.convert(call);
            if (modelIndex == null || modelIndex <= 0 || modelIndex > config.getItems().size()) {
                throw new NoMatchModelException("匹配模型完成，匹配结果异常");
            }
            LLMModelEntity modelEntity = llmModelManager.getById(config.getItems().get(modelIndex - 1).getModelId());
            if (modelEntity == null) {
                throw new NoMatchModelException("匹配模型完成，模型ID不存在");
            }
            // 检查模型是否可用
            llmModelManager.checkUseValid(llmModel);
            log.info("模型路由匹配成功，模型：{}", modelEntity.getModelName());
            return AiModelFactory.getOrCreateChatModel(AiPlatformEnum.OPENAI, modelEntity.getModelName(), modelEntity.getApiKey(), modelEntity.getBaseUrl());
        } catch (NoMatchModelException e) {
            log.error("匹配模型完成，模型ID不存在");
            return fallbackSelectModel();
        } catch (Exception e) {
            log.error("匹配模型完成，匹配结果异常");
            return fallbackSelectModel();
        }

    }

    public ChatModel selectVisualModel(UserMessage userMessage) {
        LLMModelEntity llmModel = llmModelManager.lambdaQuery()
                .eq(LLMModelEntity::getModelType, ModelTypeEnum.CONVERSATION.getValue())
                .eq(LLMModelEntity::getEnableFlag, true)
                .eq(LLMModelEntity::getVisionUseFlag, true)
                .orderByAsc(LLMModelEntity::getPriority)
                .last("limit 1")
                .one();
        if (llmModel == null) {
            log.error("无视觉理解模型可用");
            throw new NoMatchModelException("无视觉理解模型可用");
        }
        // 检查模型是否可用
        llmModelManager.checkUseValid(llmModel);
        return AiModelFactory.getOrCreateChatModel(AiPlatformEnum.OPENAI, llmModel.getModelName(), llmModel.getApiKey(), llmModel.getBaseUrl());
    }

    private String buildPrompt(String systemPrompt, List<ChatModelRouteConfig.Config.Item> items) {

        StringBuilder optionPrompt = new StringBuilder("\n##以下是意图列表");
        for (int i = 0; i < items.size(); i++) {
            ChatModelRouteConfig.Config.Item item = items.get(i);
            String temp = """
                    -------------------
                    序号:%s
                    意图描述:%s
                    意图值:%s
                    --------------------
                    """;
            optionPrompt.append(String.format(temp, i + 1, item.getValue(), item.getModelId()));
        }
        String format = String.format("""
                %s
                %s
                """, systemPrompt, optionPrompt);
        log.info("构建模型路由系统提示:{}", format);
        return format;
    }


    /**
     * 默认选择优先级最高的模型
     *
     * @return
     */
    private ChatModel fallbackSelectModel() {
        LLMModelEntity llmModel = llmModelManager.lambdaQuery()
                .eq(LLMModelEntity::getModelType, ModelTypeEnum.CONVERSATION.getValue())
                .eq(LLMModelEntity::getEnableFlag, true)
                .orderByAsc(LLMModelEntity::getPriority)
                .last("limit 1")
                .one();
        if (llmModel == null) {
            log.error("没有可用的模型");
            throw new NoMatchModelException("没有可用的模型");
        }
        // 检查模型是否可用
        llmModelManager.checkUseValid(llmModel);
        log.debug("未启用模型路由，优先级最高的模型：{}", llmModel.getModelName());
        return AiModelFactory.getOrCreateChatModel(AiPlatformEnum.OPENAI, llmModel.getModelName(), llmModel.getApiKey(), llmModel.getBaseUrl());
    }


    /**
     * 路由选择
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RouteSelectDTO {

        private Long modelId;

        private UserMessage userMessage;
    }
}
