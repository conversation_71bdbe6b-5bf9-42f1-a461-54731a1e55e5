package net.lab1024.sa.admin.module.business.mes.produce.instruct.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.event.item.ItemDeleteCheckEvent;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderClothesDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.dao.ProduceInstructOrderItemDao;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderClothesEntity;
import net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.entity.ProduceInstructOrderItemEntity;
import net.lab1024.sa.base.common.exception.BusinessException;

import jakarta.annotation.Resource;

@Component("produceItemListener")
public class ItemListener {


    @Resource
    private ProduceInstructOrderDao produceInstructOrderDao;

    @Resource
    private ProduceInstructOrderItemDao produceInstructOrderItemDao;

    @Resource
    private ProduceInstructOrderClothesDao produceInstructOrderClothesDao;

    @EventListener(ItemDeleteCheckEvent.class)
    public void itemDeleteCheck(ItemDeleteCheckEvent event) {
        Long id = event.getId();

        Long count = produceInstructOrderItemDao.selectCount(new LambdaQueryWrapper<ProduceInstructOrderItemEntity>()
                .eq(ProduceInstructOrderItemEntity::getItemId, id));
        if (count > 0) {
            throw new BusinessException("指令单物料用料清单使用中，无法删除");
        }
        count = produceInstructOrderClothesDao.selectCount(new LambdaQueryWrapper<ProduceInstructOrderClothesEntity>()
                .eq(ProduceInstructOrderClothesEntity::getItemId, id));
        if (count > 0) {
            throw new BusinessException("指令单生产成衣清单使用中，无法删除");
        }

    }

}
