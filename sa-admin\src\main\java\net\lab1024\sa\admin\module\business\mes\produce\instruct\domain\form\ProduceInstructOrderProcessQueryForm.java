package net.lab1024.sa.admin.module.business.mes.produce.instruct.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

/**
 * 生产指令工序信息 分页查询表单
 *
 * <AUTHOR>
 * @Date 2024-07-10 10:24:08
 * @Copyright zscbdic
 */

@Data
public class ProduceInstructOrderProcessQueryForm extends PageParam{

    @Schema(description = "关键字查询")
    private String queryKey;

}
