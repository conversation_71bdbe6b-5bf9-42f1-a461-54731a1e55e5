package net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设备类别 列表VO
 *
 * <AUTHOR>
 * @Date 2025-02-09 20:12:33
 * @Copyright zscbdic
 */

@Data
public class EquipmentTypeVO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "设备类别编号")
    private String number;

    @Schema(description = "设备类别名称")
    private String name;

}
