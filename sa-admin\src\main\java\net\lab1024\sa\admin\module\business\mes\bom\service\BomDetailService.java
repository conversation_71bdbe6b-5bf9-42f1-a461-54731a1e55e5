package net.lab1024.sa.admin.module.business.mes.bom.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.bom.dao.BomDetailDao;
import net.lab1024.sa.admin.module.business.mes.bom.domain.entity.BomDetailEntity;
import net.lab1024.sa.admin.module.business.mes.bom.domain.form.BomDetailAddForm;
import net.lab1024.sa.admin.module.business.mes.bom.domain.form.BomDetailQueryForm;
import net.lab1024.sa.admin.module.business.mes.bom.domain.form.BomDetailUpdateForm;
import net.lab1024.sa.admin.module.business.mes.bom.domain.vo.BomDetailVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * BOM详情 Service
 *
 * <AUTHOR>
 * @Date 2024-07-09 10:25:50
 * @Copyright zscbdic
 */

@Service
public class BomDetailService {

    @Resource
    private BomDetailDao bomDetailDao;

    /**
     * 分页查询
     *
     * @param queryForm
     * @return
     */
    public PageResult<BomDetailVO> queryPage(BomDetailQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<BomDetailVO> list = bomDetailDao.queryPage(page, queryForm);
        PageResult<BomDetailVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 添加
     */
    public ResponseDTO<String> add(BomDetailAddForm addForm) {
        BomDetailEntity bomDetailEntity = SmartBeanUtil.copy(addForm, BomDetailEntity.class);
        bomDetailDao.insert(bomDetailEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新
     *
     * @param updateForm
     * @return
     */
    public ResponseDTO<String> update(BomDetailUpdateForm updateForm) {
        BomDetailEntity bomDetailEntity = SmartBeanUtil.copy(updateForm, BomDetailEntity.class);
        bomDetailDao.updateById(bomDetailEntity);
        return ResponseDTO.ok();
    }


    /**
     * 单个删除
     */
    public ResponseDTO<String> delete(Long id) {
        if (null == id) {
            return ResponseDTO.ok();
        }

        bomDetailDao.updateDeleted(id, true);
        return ResponseDTO.ok();
    }

    /**
     * 获取BOM详情
     *
     * @param bomId bomId
     * @return
     */
    public ResponseDTO<List<BomDetailVO>> getBomDetailList(Long bomId) {
        List<BomDetailEntity> entityList = bomDetailDao.selectList(new LambdaQueryWrapper<BomDetailEntity>()
                .eq(BomDetailEntity::getBomId, bomId));
        if (CollUtil.isEmpty(entityList)) {
            return ResponseDTO.ok(Collections.emptyList());
        }

        return ResponseDTO.ok(SmartBeanUtil.copyList(entityList, BomDetailVO.class));

    }
}
