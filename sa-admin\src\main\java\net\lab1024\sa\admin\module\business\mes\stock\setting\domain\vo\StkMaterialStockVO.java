package net.lab1024.sa.admin.module.business.mes.stock.setting.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 物料库存属性 列表VO
 *
 * <AUTHOR>
 * @Date 2025-02-05 15:18:19
 * @Copyright zscbdic
 */

@Data
public class StkMaterialStockVO {


    @Schema(description = "主键")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "删除标识;0未删除，1删除")
    private Boolean deletedFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "物料id")
    private Long materialId;

    @Schema(description = "作用范围;ALL所有仓库 ONE 单一仓库")
    private String scope;

    @Schema(description = "仓库限制;0停用 1启用")
    private Boolean warehouseLimit;

    @Schema(description = "仓库id;保留")
    private Long warehouseId;

    @Schema(description = "仓位限制;0停用 1启用")
    private Boolean locationLimit;

    @Schema(description = "仓位id;保留")
    private Long locationId;

    @Schema(description = "最小库存")
    private BigDecimal minStock;

    @Schema(description = "最大库存")
    private BigDecimal maxStock;

    @Schema(description = "安全库存")
    private BigDecimal safeStock;

    @Schema(description = "再订货点")
    private BigDecimal reorderGood;

    @Schema(description = "启用最小库存;0停用 1启用")
    private Boolean minStockFlag;

    @Schema(description = "启用最大库存;0停用 1启用")
    private Boolean maxStockFlag;

    @Schema(description = "启用安全库存;0停用 1启用")
    private Boolean safeStockFlag;

    @Schema(description = "启用再订货点;0停用 1启用")
    private Boolean reorderGoodFlag;

    @Schema(description = "批号管理;0停用 1启用")
    private Boolean lotManageFlag;

    @Schema(description = "SN管理;0停用 1启用")
    private Boolean snManageFlag;

    @Schema(description = "物料SPU编号")
    private String materielSpuNumber;

    @Schema(description = "物料SKU编号")
    private String materielSkuNumber;

    @Schema(description = "物料名称")
    private String materielName;

    @Schema(description = "物料型号")
    private String materielModel;

}
