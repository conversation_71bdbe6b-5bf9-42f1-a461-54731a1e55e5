package net.lab1024.sa.admin.module.business.mes.ai.model.manager;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.ai.core.config.ChatModelRouteConfig;
import net.lab1024.sa.admin.module.business.mes.ai.core.exception.NoMatchModelException;
import net.lab1024.sa.admin.module.business.mes.ai.model.dao.LLMModelDao;
import net.lab1024.sa.admin.module.business.mes.ai.model.domain.entity.LLMModelEntity;
import org.springframework.stereotype.Service;

/**
 * 大模型表  Manager
 *
 * <AUTHOR>
 * @Date 2025-04-17 11:25:27
 * @Copyright zscbdic
 */
@Service
public class LLMModelManager extends ServiceImpl<LLMModelDao, LLMModelEntity> {

    @Resource
    private ChatModelRouteConfig chatModelRouteConfig;

    public void checkUseValid(LLMModelEntity entity) {
        Boolean enableFlag = entity.getEnableFlag();
        if (enableFlag == null || !enableFlag) {
            throw new NoMatchModelException("模型未启用");
        }
    }

    public void deleteCheck(Long id) {
        ChatModelRouteConfig.Config config = chatModelRouteConfig.getConfig();
        if (config.getModelId().equals(id)) {
            throw new NoMatchModelException("路由决策模型正在被使用，无法删除");
        }
        boolean useFlag = config.getItems().stream().anyMatch(item -> item.getModelId().equals(id));
        if (useFlag) {
            throw new NoMatchModelException("路由模型正在被使用，无法删除");
        }
    }


}
