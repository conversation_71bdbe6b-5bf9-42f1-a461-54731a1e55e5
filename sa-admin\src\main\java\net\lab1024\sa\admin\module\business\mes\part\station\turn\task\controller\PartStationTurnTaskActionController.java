package net.lab1024.sa.admin.module.business.mes.part.station.turn.task.controller;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskExecutorTypeEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskPriorityEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskScopeEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.constant.TurnTaskStatusEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.form.PartStationTurnTaskBeginForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.form.PartStationTurnTaskFinishForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.form.PartStationTurnTaskSumbitForm;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.task.service.PartStationTurnTaskActionService;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

/**
 * 驿站任务操作 接口
 */
@RestController
public class PartStationTurnTaskActionController {

    @Resource
    private PartStationTurnTaskActionService partStationTurnTaskActionService;

    @Resource
    private SerialNumberService serialNumberService;

    /**
     * 提交任务
     *
     * @param form
     * @return
     */
    @Operation(summary = "提交任务 @cjm")
    @PostMapping("/partStationTurnTask/submit")
    public ResponseDTO<Long> submit(@RequestBody @Valid PartStationTurnTaskSumbitForm form) {
        form.setTaskScope(TurnTaskScopeEnum.ARTIFICIAL.getValue());
        form.setStatus(TurnTaskStatusEnum.WAIT_GET.getValue());
        form.setAutoDispatchFlag(false);
        form.setSubmitTime(LocalDateTime.now());
        RequestUser user = SmartRequestUtil.getRequestUser();
        form.setSubmitterType(TurnTaskExecutorTypeEnum.HUMAN.getValue());
        form.setSubmitterId(String.valueOf(user.getUserId()));
        form.setSubmitterName(user.getUserName());

        if (StrUtil.isBlank(form.getPriority())) {
            form.setPriority(TurnTaskPriorityEnum.LOW.getValue());
        }
        if (form.getSyncPartDispatchFlag() == null) {
            form.setSyncPartDispatchFlag(false);
        }
        if (StrUtil.isBlank(form.getNumber())) {
            form.setNumber(serialNumberService.generate(SerialNumberIdEnum.PART_STATION_TURN_TASK));
        }
        return partStationTurnTaskActionService.submitTask(form);
    }

    /**
     * 取消任务
     *
     * @param idList
     * @return
     */
    @Operation(summary = "取消任务 @cjm")
    @PostMapping("/partStationTurnTask/cancel")
    public ResponseDTO<String> cancel(@RequestBody ValidateList<Long> idList) {
        return partStationTurnTaskActionService.cancelTask(idList);
    }

    /**
     * 领取任务
     *
     * @param id
     * @return
     */
    @Operation(summary = "领取任务 <AUTHOR>
    @GetMapping("/partStationTurnTask/get/{id}")
    public ResponseDTO<String> get(@PathVariable Long id) {
        RequestUser user = SmartRequestUtil.getRequestUser();
        return partStationTurnTaskActionService.getTask(id, TurnTaskExecutorTypeEnum.HUMAN, String.valueOf(user.getUserId()), user.getUserName());
    }

    /**
     * 放弃任务
     *
     * @param id
     * @return
     */
    @Operation(summary = "放弃任务 <AUTHOR>
    @GetMapping("/partStationTurnTask/giveUp/{id}")
    public ResponseDTO<String> giveUp(@PathVariable Long id) {
        RequestUser user = SmartRequestUtil.getRequestUser();
        return partStationTurnTaskActionService.giveUpTask(id, TurnTaskExecutorTypeEnum.HUMAN, String.valueOf(user.getUserId()), user.getUserName());
    }


    /**
     * 开始任务
     *
     * @param form
     * @return
     */
    @Operation(summary = "开始任务")
    @PostMapping("/partStationTurnTask/begin")
    public ResponseDTO<String> begin(@RequestBody @Valid PartStationTurnTaskBeginForm form) {
        return partStationTurnTaskActionService.beginTask(form);
    }

    /**
     * 完成任务
     *
     * @param form
     * @return
     */
    @Operation(summary = "完成任务")
    @PostMapping("/partStationTurnTask/finish")
    public ResponseDTO<String> finish(@RequestBody @Valid PartStationTurnTaskFinishForm form) {
        return partStationTurnTaskActionService.finishTask(form);
    }
}
