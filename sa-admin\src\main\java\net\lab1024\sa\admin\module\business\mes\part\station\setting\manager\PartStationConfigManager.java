package net.lab1024.sa.admin.module.business.mes.part.station.setting.manager;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.business.mes.part.station.inventory.dao.PartStationInventoryDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.dao.PartStationTurnBoxDao;
import net.lab1024.sa.admin.module.business.mes.part.station.turn.box.domain.entity.PartStationTurnBoxEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.constant.PartStationConfigConstant;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.constant.StorageManageModeConfigEnum;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.domain.dto.StorageManageModeConfigDTO;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.domain.dto.StoragePressureConfigDTO;
import net.lab1024.sa.admin.module.business.mes.part.station.setting.domain.dto.StorageTimeConfigDTO;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.module.support.config.ConfigService;
import net.lab1024.sa.base.module.support.config.domain.ConfigAddForm;
import net.lab1024.sa.base.module.support.config.domain.ConfigUpdateForm;
import net.lab1024.sa.base.module.support.config.domain.ConfigVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;

@Service
public class PartStationConfigManager {

    @Resource
    private ConfigService configService;

    @Resource
    private PartStationTurnBoxDao partStationTurnBoxDao;

    @Resource
    private PartStationInventoryDao partStationInventoryDao;

    /**
     * 获取存储时间配置
     * @return
     */
    public StorageTimeConfigDTO getStorageTimeConfig() {

        ConfigVO config = configService.getConfig(PartStationConfigConstant.STORAGE_TIME_CONFIG_KEY);
        if(config == null || StrUtil.isBlank(config.getConfigValue())){
            StorageTimeConfigDTO configDTO = new StorageTimeConfigDTO();
            configDTO.setEnableFlag(false);
            configDTO.setMinDay(1);
            configDTO.setMaxDay(1);
            configDTO.setEmployeeIds(Collections.emptyList());

            ConfigAddForm addForm = new ConfigAddForm();
            addForm.setConfigKey(PartStationConfigConstant.STORAGE_TIME_CONFIG_KEY);
            addForm.setConfigValue(JSON.toJSONString(configDTO));
            addForm.setConfigName("裁片驿站配置-存储时间配置");
            addForm.setRemark("存储时间配置");
            configService.add(addForm);

            return configDTO;
        }
        String configValue = config.getConfigValue();
        StorageTimeConfigDTO configDTO = JSON.parseObject(configValue, StorageTimeConfigDTO.class);
        configDTO.setConfigId(config.getConfigId());
        return configDTO;
    }

    /**
     * 更新存储时间配置
     * @param storageTimeConfigDTO
     */
    public void updateStorageTimeConfig(StorageTimeConfigDTO storageTimeConfigDTO) {
        if(storageTimeConfigDTO.getMinDay()> storageTimeConfigDTO.getMaxDay()){
            throw new BusinessException("建议存放时间不能大于最大存放时间");
        }

        ConfigUpdateForm configUpdateForm = new ConfigUpdateForm();
        configUpdateForm.setConfigKey(PartStationConfigConstant.STORAGE_TIME_CONFIG_KEY);
        configUpdateForm.setConfigValue(JSON.toJSONString(storageTimeConfigDTO));
        configUpdateForm.setRemark("存储时间配置");
        configUpdateForm.setConfigName("裁片驿站配置-存储时间配置");
        configUpdateForm.setConfigId(storageTimeConfigDTO.getConfigId());
        configService.updateConfig(configUpdateForm);
    }


    /**
     * 获取存储压力配置
     * @return
     */
    public StoragePressureConfigDTO getStoragePressureConfig() {
        ConfigVO config = configService.getConfig(PartStationConfigConstant.STORAGE_PRESSURE_CONFIG_KEY);
        if(config == null || StrUtil.isBlank(config.getConfigValue())){
            StoragePressureConfigDTO configDTO = new StoragePressureConfigDTO();
            configDTO.setEnableFlag(false);
            configDTO.setEmployeeIds(Collections.emptyList());
            configDTO.setMaxUsageRate(85);

            ConfigAddForm addForm = new ConfigAddForm();
            addForm.setConfigKey(PartStationConfigConstant.STORAGE_PRESSURE_CONFIG_KEY);
            addForm.setConfigValue(JSON.toJSONString(configDTO));
            addForm.setConfigName("裁片驿站配置-存储压力配置");
            addForm.setRemark("存储压力配置");
            configService.add(addForm);

            return configDTO;
        }
        String configValue = config.getConfigValue();
        StoragePressureConfigDTO configDTO = JSON.parseObject(configValue, StoragePressureConfigDTO.class);
        configDTO.setConfigId(config.getConfigId());
        return configDTO;
    }

    /**
     * 更新存储压力配置
     * @param storagePressureConfigDTO
     */
    public void updateStoragePressureConfig(StoragePressureConfigDTO storagePressureConfigDTO) {
        ConfigUpdateForm configUpdateForm = new ConfigUpdateForm();
        configUpdateForm.setConfigKey(PartStationConfigConstant.STORAGE_PRESSURE_CONFIG_KEY);
        configUpdateForm.setConfigValue(JSON.toJSONString(storagePressureConfigDTO));
        configUpdateForm.setRemark("存储压力配置");
        configUpdateForm.setConfigName("裁片驿站配置-存储压力配置");
        configUpdateForm.setConfigId(storagePressureConfigDTO.getConfigId());
        configService.updateConfig(configUpdateForm);
    }


    /**
     * 获取存储管理模式配置
     * @return
     */
    public StorageManageModeConfigDTO getStorageManageModeConfig() {
        ConfigVO config = configService.getConfig(PartStationConfigConstant.STORAGE_MANAGE_MODE_CONFIG_KEY);
        if(config == null || StrUtil.isBlank(config.getConfigValue())){
            StorageManageModeConfigDTO configDTO = new StorageManageModeConfigDTO();
            configDTO.setManageMode(StorageManageModeConfigEnum.FE_TICKET_MANAGE_MODE.getValue());

            ConfigAddForm addForm = new ConfigAddForm();
            addForm.setConfigKey(PartStationConfigConstant.STORAGE_MANAGE_MODE_CONFIG_KEY);
            addForm.setConfigValue(JSON.toJSONString(configDTO));
            addForm.setConfigName("裁片驿站配置-存储管理模式配置");
            addForm.setRemark("存储管理模式配置");
            configService.add(addForm);
            return configDTO;
        }
        String configValue = config.getConfigValue();
        StorageManageModeConfigDTO configDTO = JSON.parseObject(configValue, StorageManageModeConfigDTO.class);
        configDTO.setConfigId(config.getConfigId());
        return configDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateStorageManageModeConfig(StorageManageModeConfigDTO storageManageModeConfigDTO) {
        StorageManageModeConfigDTO modeConfig = this.getStorageManageModeConfig();
        if(!modeConfig.getManageMode().equals(storageManageModeConfigDTO.getManageMode())){
            partStationInventoryDao.delete(null);
            partStationTurnBoxDao.update(new LambdaUpdateWrapper<PartStationTurnBoxEntity>()
                    .set(PartStationTurnBoxEntity::getInsideFlag, false)
                    .set(PartStationTurnBoxEntity::getInsideLocationId, null));
        }

        ConfigUpdateForm configUpdateForm = new ConfigUpdateForm();
        configUpdateForm.setConfigKey(PartStationConfigConstant.STORAGE_MANAGE_MODE_CONFIG_KEY);
        configUpdateForm.setConfigValue(JSON.toJSONString(storageManageModeConfigDTO));
        configUpdateForm.setRemark("存储管理模式配置");
        configUpdateForm.setConfigName("裁片驿站配置-存储管理模式配置");
        configUpdateForm.setConfigId(storageManageModeConfigDTO.getConfigId());
        configService.updateConfig(configUpdateForm);
    }
}
