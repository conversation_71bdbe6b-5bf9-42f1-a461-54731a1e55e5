package net.lab1024.sa.admin.module.business.mes.part.station.inventory.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * 裁片驿站操作日志 实体类
 *
 * <AUTHOR>
 * @Date 2024-10-07 19:47:06
 * @Copyright zscbdic
 */

@Data
@TableName("t_mes_part_station_opt_log")
public class PartStationOptLogEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 删除标识;0未删除，1删除
     */
    @TableLogic
    private Boolean deletedFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 菲票id
     */
    private Long feTicketId;

    /**
     * 指令单号
     */
    private String instructOrderNumber;

    /**
     * 指令单id
     */
    private Long instructOrderId;

    /**
     * 裁床单号
     */
    private String cutBedSheetNumber;

    /**
     * 裁床单id
     */
    private Long cutBedSheetId;

    /**
     * 床次
     */
    private Integer cutNum;

    /**
     * 物料id
     */
    private Long itemId;

    /**
     * 物料编号
     */
    private String itemNumber;

    /**
     * 物料名称
     */
    private String itemName;

    /**
     * 扎号
     */
    private Integer tieNum;

    /**
     * 款式颜色
     */
    private String styleColor;

    /**
     * 尺寸
     */
    private String size;

    /**
     * 部位
     */
    private String positions;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 操作类型
     */
    private String optType;

    /**
     * 操作描述
     */
    private String optDesc;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 操作时间
     */
    private LocalDateTime optTime;

    /**
     * 货位id
     */
    private Long binId;

    /**
     * 货位编码
     */
    private String binCode;

    /**
     * 货架id
     */
    private Long rackId;

    /**
     * 货架编码
     */
    private String rackCode;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

}
