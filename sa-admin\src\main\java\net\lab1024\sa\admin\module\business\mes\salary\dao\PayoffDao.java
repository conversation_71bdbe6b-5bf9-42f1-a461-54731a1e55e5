package net.lab1024.sa.admin.module.business.mes.salary.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import net.lab1024.sa.admin.module.business.mes.salary.domain.dto.PayoffDto;
import net.lab1024.sa.admin.module.business.mes.salary.domain.form.PayoffQueryForm;

import java.util.List;

@Mapper
public interface PayoffDao {

    /**
     * 分页查询
     * @param queryForm
     * @param offset
     * @param limit
     * @return
     */

    List<PayoffDto> queryPage(@Param("queryForm") PayoffQueryForm queryForm,@Param("offset") Long offset,@Param("limit") Long limit);

    /**
     * 查询总条数
     * @param queryForm
     * @return
     */
    Long queryPageCount(@Param("queryForm") PayoffQueryForm queryForm);
}
