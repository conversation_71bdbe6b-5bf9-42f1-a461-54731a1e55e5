package net.lab1024.sa.admin.module.business.mes.produce.arrange.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import net.lab1024.sa.admin.module.business.mes.process.domain.vo.ProcessLibraryVO;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.form.ProduceArrangeAddForm;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.form.ProduceArrangeQueryForm;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.form.ProduceArrangeUpdateForm;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.domain.vo.ProduceArrangeVO;
import net.lab1024.sa.admin.module.business.mes.produce.arrange.service.ProduceArrangeService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 生产安排信息 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-22 18:44:38
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "生产安排信息接口")
public class ProduceArrangeController {

    @Resource
    private ProduceArrangeService produceArrangeService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/produceArrange/queryPage")
    public ResponseDTO<PageResult<ProduceArrangeVO>> queryPage(@RequestBody @Valid ProduceArrangeQueryForm queryForm) {
        return ResponseDTO.ok(produceArrangeService.queryPage(queryForm));
    }

    /**
     * 全查询
     * @return
     */
    @Operation(summary = "全查询 <AUTHOR>
    @GetMapping("/produceArrange/queryList")
    public ResponseDTO<List<ProcessLibraryVO>> queryList() {
        return ResponseDTO.ok(produceArrangeService.queryList());
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/produceArrange/add")
    public ResponseDTO<String> add(@RequestBody @Valid ProduceArrangeAddForm addForm) {
        return produceArrangeService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/produceArrange/update")
    public ResponseDTO<String> update(@RequestBody @Valid ProduceArrangeUpdateForm updateForm) {
        return produceArrangeService.update(updateForm);
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/produceArrange/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return produceArrangeService.delete(id);
    }


    /**
     * 查询详细生产安排
     * @param id
     * @return
     */
    @Operation(summary = "查询详细生产安排 <AUTHOR>
    @GetMapping("/produceArrange/{id}")
    public ResponseDTO<ProduceArrangeVO> queryById(@PathVariable("id") Long id){
        return produceArrangeService.queryById(id);
    }
}
