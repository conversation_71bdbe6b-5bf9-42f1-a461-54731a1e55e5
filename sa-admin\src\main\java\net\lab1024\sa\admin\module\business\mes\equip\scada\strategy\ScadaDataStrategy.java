package net.lab1024.sa.admin.module.business.mes.equip.scada.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.dao.EquipmentDao;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.dao.EquipmentScadaDao;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.dao.EquipmentTypeDao;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.entity.EquipmentEntity;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.entity.EquipmentScadaEntity;
import net.lab1024.sa.admin.module.business.mes.equip.equipment.domain.entity.EquipmentTypeEntity;
import net.lab1024.sa.admin.module.business.mes.equip.scada.constant.EquipmentScadaDataRedisConstant;
import net.lab1024.sa.admin.module.business.mes.equip.scada.domain.dto.EquipScadaDataDTO;
import net.lab1024.sa.admin.module.business.mes.equip.scada.domain.dto.EquipScadaPropertyDTO;
import net.lab1024.sa.admin.module.business.mes.equip.scada.domain.vo.EquipmentScadaDataVO;
import net.lab1024.sa.admin.module.business.mes.equip.scada.domain.vo.EquipmentScadaPropertyVO;
import net.lab1024.sa.admin.module.business.mes.factory.dao.WorkshopDao;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.WorkshopEntity;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Scada数据获取策略优化版本，支持缓存部分命中时合并查询结果
 */
@Component
public abstract class ScadaDataStrategy implements InitializingBean {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private EquipmentTypeDao equipmentTypeDao;

    @Resource
    private EquipmentDao equipmentDao;

    @Resource
    private EquipmentScadaDao equipmentScadaDao;

    @Resource
    private WorkshopDao workshopDao;

    /**
     * 查询SCADA数据
     *
     * @param equipIds
     * @return
     */
    public List<EquipmentScadaDataVO> queryScadaData(List<Long> equipIds) {
        if (CollUtil.isEmpty(equipIds)) {
            return Collections.emptyList();
        }

        // 生成缓存键并批量获取
        List<String> keys = equipIds.stream()
                .map(id -> EquipmentScadaDataRedisConstant.EQUIPMENT_SCADA_DATA_KEY + id)
                .collect(Collectors.toList());
        List<String> cachedJsons = stringRedisTemplate.opsForValue().multiGet(keys);

        // 分离命中和未命中的设备
        List<EquipmentScadaDataVO> result = new ArrayList<>(equipIds.size());
        List<Long> missedIds = new ArrayList<>();

        for (int i = 0; i < equipIds.size(); i++) {
            Long equipId = equipIds.get(i);
            String json = (cachedJsons != null && i < cachedJsons.size()) ? cachedJsons.get(i) : null;

            if (StrUtil.isNotBlank(json)) {
                result.add(JSONObject.parseObject(json, EquipmentScadaDataVO.class));
            } else {
                missedIds.add(equipId);
            }
        }

        // 处理未命中缓存的设备
        if (!missedIds.isEmpty()) {
            List<EquipmentScadaDataVO> missedVos = queryAndProcessEquipmentScadaDataVO(missedIds);
            result.addAll(missedVos);
        }

        // 按原始顺序排序（可选，根据业务需求）
        return result;
    }


    /**
     * 查询SCADA设备属性
     *
     * @param equipId
     * @return
     */
    public EquipmentScadaPropertyVO queryScadaProperties(Long equipId) {

        EquipmentScadaEntity scadaEntity = equipmentScadaDao.selectOne(new LambdaQueryWrapper<EquipmentScadaEntity>()
                .eq(EquipmentScadaEntity::getEquipmentId, equipId)
                .last("limit 1"));
        if (scadaEntity == null) {
            return null;
        }
        EquipScadaPropertyDTO query = new EquipScadaPropertyDTO();
        query.setScadaEquipmentId(scadaEntity.getScadaEquipmentId());
        query.setScadaProductCode(scadaEntity.getScadaProductCode());
        query.setScadaEquipmentCode(scadaEntity.getScadaEquipmentCode());

        EquipScadaPropertyDTO dto = requestScadaProperty(query);

        EquipmentScadaPropertyVO vo = new EquipmentScadaPropertyVO();
        vo.setEquipmentId(equipId);
        vo.setScadaEquipmentId(dto.getScadaEquipmentId());
        vo.setScadaProductCode(dto.getScadaProductCode());
        vo.setScadaEquipmentCode(dto.getScadaEquipmentCode());
        vo.setLastRequestTime(new Date());
        if (CollUtil.isEmpty(dto.getProperties())) {
            return vo;
        }
        List<EquipmentScadaPropertyVO.Property> properties = dto.getProperties().stream().map(p -> {
            EquipmentScadaPropertyVO.Property property = new EquipmentScadaPropertyVO.Property();
            property.setFieldKey(p.getFieldKey());
            property.setFieldName(p.getFieldName());
            property.setFieldValueStr(p.getFieldValueStr());
            property.setDataType(p.getDataType());
            property.setUnitName(p.getUnitName());
            property.setUpdateTime(p.getUpdateTime());
            property.setRequestTime(p.getRequestTime());
            return property;
        }).collect(Collectors.toList());
        vo.setProperties(properties);

        return vo;

    }


    public EquipmentScadaPropertyVO queryScadaPropertyObj(Long equipId) {
        EquipmentScadaEntity scadaEntity = equipmentScadaDao.selectOne(new LambdaQueryWrapper<EquipmentScadaEntity>()
                .eq(EquipmentScadaEntity::getEquipmentId, equipId)
                .last("limit 1"));
        if (scadaEntity == null) {
            return null;
        }
        EquipScadaPropertyDTO query = new EquipScadaPropertyDTO();
        query.setScadaEquipmentId(scadaEntity.getScadaEquipmentId());
        query.setScadaProductCode(scadaEntity.getScadaProductCode());
        query.setScadaEquipmentCode(scadaEntity.getScadaEquipmentCode());

        EquipScadaPropertyDTO dto = requestScadaProperty(query);

        EquipmentScadaPropertyVO vo = new EquipmentScadaPropertyVO();
        vo.setEquipmentId(equipId);
        vo.setScadaEquipmentId(dto.getScadaEquipmentId());
        vo.setScadaProductCode(dto.getScadaProductCode());
        vo.setScadaEquipmentCode(dto.getScadaEquipmentCode());
        vo.setLastRequestTime(new Date());
        if (CollUtil.isEmpty(dto.getProperties())) {
            return vo;
        }
        JSONObject propertyObj = new JSONObject();
        for (EquipScadaPropertyDTO.Property property : dto.getProperties()) {
            JSONObject valueObj = new JSONObject();
            valueObj.put("fieldKey", property.getFieldKey());
            valueObj.put("fieldName", property.getFieldName());
            valueObj.put("fieldValueStr", property.getFieldValueStr());
            valueObj.put("unitName", property.getUnitName());
            valueObj.put("dataType", property.getDataType());
            valueObj.put("requestTime", property.getRequestTime());
            valueObj.put("updateTime", property.getUpdateTime());

            propertyObj.put(property.getFieldKey(), valueObj);

        }
        vo.setPropertyObj(propertyObj);
        return vo;

    }

    /**
     * 处理未命中缓存的设备
     *
     * @param missedIds
     * @return
     */
    protected List<EquipmentScadaDataVO> queryAndProcessEquipmentScadaDataVO(List<Long> missedIds) {
        // 查询设备基础信息
        List<EquipmentScadaDataVO> vos = queryEquipData(missedIds);

        // 查询并填充SCADA实时数据
        queryAndSetScadaData(vos);


        // 缓存处理
        vos.forEach(vo -> {
            vo.setLastScadaDataUpdateTime(new Date());
            String key = EquipmentScadaDataRedisConstant.EQUIPMENT_SCADA_DATA_KEY + vo.getEquipmentId();
            stringRedisTemplate.opsForValue().set(
                    key,
                    JSONObject.toJSONString(vo),
                    EquipmentScadaDataRedisConstant.EQUIPMENT_SCADA_DATA_TIMEOUT,
                    TimeUnit.SECONDS
            );
        });

        return vos;
    }

    /**
     * 查询设备基础信息
     *
     * @param equipIds
     * @return
     */
    protected List<EquipmentScadaDataVO> queryEquipData(List<Long> equipIds) {
        // 批量查询设备基本信息
        List<EquipmentEntity> equipments = equipmentDao.selectBatchIds(equipIds);
        if (CollUtil.isEmpty(equipments)) {
            return Collections.emptyList();
        }

        // 预加载设备类型信息
        Set<Long> typeIds = equipments.stream()
                .map(EquipmentEntity::getTypeId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Map<Long, EquipmentTypeEntity> typeMap = CollUtil.isEmpty(typeIds) ? Collections.emptyMap() :
                equipmentTypeDao.selectBatchIds(typeIds).stream()
                        .collect(Collectors.toMap(EquipmentTypeEntity::getId, t -> t));

        // 预加载SCADA配置信息
        List<EquipmentScadaEntity> scadaList = equipmentScadaDao.selectList(
                new LambdaQueryWrapper<EquipmentScadaEntity>()
                        .in(EquipmentScadaEntity::getEquipmentId, equipIds)
                        .eq(EquipmentScadaEntity::getIotNetworkFlag, true)
        );
        Map<Long, EquipmentScadaEntity> scadaMap = scadaList.stream()
                .collect(Collectors.toMap(EquipmentScadaEntity::getEquipmentId, s -> s));

        //车间
        List<Long> workshopIds = equipments.stream()
                .map(EquipmentEntity::getWorkshopId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<Long, WorkshopEntity> workshopMap = CollUtil.isEmpty(workshopIds) ? Collections.emptyMap() :
                workshopDao.selectList(new LambdaQueryWrapper<WorkshopEntity>()
                                .in(WorkshopEntity::getId, workshopIds)).stream()
                        .collect(Collectors.toMap(WorkshopEntity::getId, w -> w));

        // 组装VO对象
        return equipments.stream().map(e -> {
            EquipmentScadaDataVO vo = new EquipmentScadaDataVO();
            vo.setEquipmentId(e.getId());
            vo.setEquipmentNumber(e.getNumber());
            vo.setEquipmentName(e.getName());
            vo.setEquipmentStatus(e.getStatus());

            // 处理设备类型信息
            if (e.getTypeId() != null && CollUtil.isNotEmpty(typeMap)) {
                EquipmentTypeEntity type = typeMap.get(e.getTypeId());
                if (type != null) {
                    vo.setEquipmentTypeId(type.getId());  // 修复设备类型ID错误
                    vo.setEquipmentTypeName(type.getName());
                }
            }

            // 处理SCADA配置信息
            EquipmentScadaEntity scada = scadaMap.get(e.getId());
            if (scada != null) {
                vo.setScadaPlatform(scada.getIotNetworkPlatform());
                vo.setScadaEquipmentCode(scada.getScadaEquipmentCode());
                vo.setScadaProductCode(scada.getScadaProductCode());
                vo.setScadaEquipmentId(scada.getScadaEquipmentId());
                vo.setScadaEquipmentType(scada.getIotEquipmentType());
            }

            //处理车间信息
            if (e.getWorkshopId() != null && CollUtil.isNotEmpty(workshopMap)) {
                WorkshopEntity workshop = workshopMap.get(e.getWorkshopId());
                if (workshop != null) {
                    vo.setWorkShopId(workshop.getId());
                    vo.setWorkShopName(workshop.getName());
                }
            }

            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 查询并设置SCADA实时数据
     *
     * @param vos
     */
    protected void queryAndSetScadaData(List<EquipmentScadaDataVO> vos) {
        // 构建平台查询参数
        List<EquipScadaDataDTO> queryParams = vos.stream()
                .map(vo -> {
                    EquipScadaDataDTO dto = new EquipScadaDataDTO();
                    dto.setScadaEquipmentId(vo.getScadaEquipmentId());
                    dto.setScadaProductCode(vo.getScadaProductCode());
                    dto.setScadaEquipmentCode(vo.getScadaEquipmentCode());
                    return dto;
                })
                .collect(Collectors.toList());

        // 请求平台数据并建立映射关系
        Map<String, EquipScadaDataDTO> platformDataMap = requestScadaData(queryParams).stream()
                .collect(Collectors.toMap(
                        dto -> generatePlatformDataMapKey(
                                dto.getScadaEquipmentId(),
                                dto.getScadaProductCode(),
                                dto.getScadaEquipmentCode()
                        ),
                        dto -> dto
                ));

        // 更新VO实时数据
        vos.forEach(vo -> {
            String key = generatePlatformDataMapKey(
                    vo.getScadaEquipmentId(),
                    vo.getScadaProductCode(),
                    vo.getScadaEquipmentCode()
            );

            EquipScadaDataDTO dto = platformDataMap.get(key);
            Date now = new Date();
            if (dto != null) {
                vo.setLastRequestTime(now);
                vo.setEquipmentOnlineStatus(dto.getEquipmentOnlineStatus());
                vo.setEquipmentRunStatus(dto.getEquipmentRunStatus());
                vo.setEquipmentScadaRunStatus(dto.getEquipmentScadaRunStatus());
                vo.setEquipmentScadaOnlineStatus(dto.getEquipmentScadaOnlineStatus());
                vo.setLastScadaDataUpdateTime(dto.getLastScadaDataUpdateTime());
                vo.setLastScadaEquipmentDownTime(dto.getLastScadaEquipmentDownTime());
                vo.setLastScadaEquipmentUpTime(dto.getLastScadaEquipmentUpTime());
            } else {
                vo.setEquipmentOnlineStatus("暂无");
                vo.setEquipmentRunStatus("暂无");
                vo.setLastScadaDataUpdateTime(null);
            }
        });
    }

    /**
     * 生成平台数据映射key
     *
     * @param scadaEquipmentId
     * @param productCode
     * @param equipmentCode
     * @return
     */
    private String generatePlatformDataMapKey(String scadaEquipmentId, String productCode, String equipmentCode) {
        return StrUtil.isNotBlank(scadaEquipmentId) ?
                scadaEquipmentId :
                StrUtil.nullToEmpty(productCode) + StrUtil.nullToEmpty(equipmentCode);
    }

    /**
     * 请求SCADA平台数据
     *
     * @param queryParams
     * @return
     */
    protected abstract List<EquipScadaDataDTO> requestScadaData(List<EquipScadaDataDTO> queryParams);

    /**
     * 请求SCADA平台设备属性
     *
     * @param queryParam
     * @return
     */
    protected abstract EquipScadaPropertyDTO requestScadaProperty(EquipScadaPropertyDTO queryParam);
}
