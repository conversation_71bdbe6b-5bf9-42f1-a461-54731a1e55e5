package net.lab1024.sa.admin.module.business.mes.factory.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.factory.domain.entity.FactoryEntity;
import net.lab1024.sa.admin.module.business.mes.factory.domain.form.FactoryQueryForm;
import net.lab1024.sa.admin.module.business.mes.factory.domain.vo.FactoryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 工厂信息表 Dao
 *
 * <AUTHOR>
 * @Date 2024-07-04 11:11:50
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface FactoryDao extends BaseMapper<FactoryEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<FactoryVO> queryPage(Page page, @Param("queryForm") FactoryQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);

}
