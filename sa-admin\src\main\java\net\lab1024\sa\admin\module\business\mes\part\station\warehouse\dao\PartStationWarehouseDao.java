package net.lab1024.sa.admin.module.business.mes.part.station.warehouse.dao;

import java.util.List;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.entity.PartStationWarehouseEntity;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.form.PartStationWarehouseQueryForm;
import net.lab1024.sa.admin.module.business.mes.part.station.warehouse.domain.vo.PartStationWarehouseVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 裁片仓库表 Dao
 *
 * <AUTHOR>
 * @Date 2024-10-06 16:48:11
 * @Copyright zscbdic
 */

@Mapper
@Component
public interface PartStationWarehouseDao extends BaseMapper<PartStationWarehouseEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<PartStationWarehouseVO> queryPage(Page page, @Param("queryForm") PartStationWarehouseQueryForm queryForm);

    /**
     * 更新删除状态
     */
    long updateDeleted(@Param("id")Long id,@Param("deletedFlag")boolean deletedFlag);


    /**
     * 查询仓库内货位id
     * @param id
     * @return
     */
    List<Long> queryBinIds(@Param("id") Long id);

}
