package net.lab1024.sa.admin.module.business.mes.part.station.turn.task.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 驿站任务表 新建表单
 *
 * <AUTHOR>
 * @Date 2025-03-14 20:13:46
 * @Copyright zscbdic
 */

@Data
public class PartStationTurnTaskAddForm {

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "任务作用范围;human人")
    private String taskScope;

    @Schema(description = "任务编号")
    private String number;

    @Schema(description = "任务名称")
    private String name;

    @Schema(description = "任务优先级", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "任务优先级 不能为空")
    private String priority;

    @Schema(description = "任务类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "任务类型 不能为空")
    private String type;

    @Schema(description = "任务状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "任务状态 不能为空")
    private String status;

    @Schema(description = "是否自动派发 0:否 1:是")
    private Integer autoDispatchFlag;

    @Schema(description = "起点货位ID")
    private Long startLocationId;

    @Schema(description = "终点货位ID")
    private Long endLocationId;

    @Schema(description = "菲票数组")
    private String ticketIds;

    @Schema(description = "周转箱ID")
    private Long turnoverBoxId;

    @Schema(description = "提交时间")
    private LocalDateTime submitTime;

    @Schema(description = "领取时间")
    private LocalDateTime getTime;

    @Schema(description = "开始时间")
    private LocalDateTime beginTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "提交人类型")
    private String submitterType;

    @Schema(description = "提交人ID")
    private String submitterId;

    @Schema(description = "提交人名称")
    private String submitterName;

    @Schema(description = "执行人类型")
    private String executorType;

    @Schema(description = "执行人ID")
    private String executorId;

    @Schema(description = "执行人名称")
    private String executorName;

    @Schema(description = "关联单据类型")
    private String bizType;

    @Schema(description = "关联单据ID")
    private Long bizId;

    @Schema(description = "关联单据详情序号")
    private Integer bizDetailSeq;

    @Schema(description = "关联单据详情ID")
    private Long bizDetailId;

}