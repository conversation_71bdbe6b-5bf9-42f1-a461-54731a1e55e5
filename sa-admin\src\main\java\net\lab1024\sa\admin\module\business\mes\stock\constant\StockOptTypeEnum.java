package net.lab1024.sa.admin.module.business.mes.stock.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 库存单据单操作类型枚举
 */
@Getter
@AllArgsConstructor
public enum StockOptTypeEnum implements BaseEnum {

    /**
     * 入库
     */
    IN("IN", "入库"),

    /**
     * 出库
     */
    OUT("OUT", "出库"),

    /**
     * 盘库
     */
    TAKE("TAKE", "盘库"),

    /**
     * 移库
     */
    MOVE("MOVE", "移库"),

    ;


    private final String value;

    private final String desc;
}
