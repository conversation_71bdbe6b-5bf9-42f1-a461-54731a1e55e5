package net.lab1024.sa.admin.module.business.mes.base.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.mes.base.domain.form.*;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.SizeInfoVO;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.SizeTemplateVO;
import net.lab1024.sa.admin.module.business.mes.base.domain.vo.SizeTemplateWithSizesVO;
import net.lab1024.sa.admin.module.business.mes.base.service.SizeTemplateService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 尺寸模板表 Controller
 *
 * <AUTHOR>
 * @Date 2024-07-05 15:45:35
 * @Copyright zscbdic
 */

@RestController
@Tag(name = "")
public class SizeTemplateController {

    @Resource
    private SizeTemplateService sizeTemplateService;

    /**
     * 分页查询
     * @param queryForm
     * @return
     */
    @Operation(summary = "分页查询 <AUTHOR>
    @PostMapping("/sizeTemplate/queryPage")
    public ResponseDTO<PageResult<SizeTemplateVO>> queryPage(@RequestBody @Valid SizeTemplateQueryForm queryForm) {
        return ResponseDTO.ok(sizeTemplateService.queryPage(queryForm));
    }

    /**
     * 添加
     * @param addForm
     * @return
     */
    @Operation(summary = "添加 <AUTHOR>
    @PostMapping("/sizeTemplate/add")
    public ResponseDTO<String> add(@RequestBody @Valid SizeTemplateAddForm addForm) {
        return sizeTemplateService.add(addForm);
    }

    /**
     * 更新
     * @param updateForm
     * @return
     */
    @Operation(summary = "更新 <AUTHOR>
    @PostMapping("/sizeTemplate/update")
    public ResponseDTO<String> update(@RequestBody @Valid SizeTemplateUpdateForm updateForm) {
        return sizeTemplateService.update(updateForm);
    }

    /**
     * 单个删除
     * @param id
     * @return
     */
    @Operation(summary = "单个删除 <AUTHOR>
    @GetMapping("/sizeTemplate/delete/{id}")
    public ResponseDTO<String> delete(@PathVariable Long id) {
        return sizeTemplateService.delete(id);
    }

    /**
     * 尺码模板信息下拉查询
     */
    @Operation(summary = "尺码模板信息下拉查询 <AUTHOR>
    @PostMapping("/sizeTemplate/sizeInfo")
    public ResponseDTO<List<SizeInfoVO>> querySizeInfo(@RequestBody @Valid SizeTemplateQuery sizeTemplateQuery ) {
        return sizeTemplateService.getSizeInfo(sizeTemplateQuery);
    }

    /**
     * 尺码信息查询
     */
    @Operation(summary = "尺码信息查询 <AUTHOR>
    @PostMapping("/sizeTemplate/sizeInfoWithTemplate")
    public ResponseDTO<List<SizeTemplateWithSizesVO>> getSizeWithTemplateName(@RequestBody SizeInfoQueryForm queryForm) {
        return sizeTemplateService.getSizeWithTemplateName(queryForm);
    }

}
